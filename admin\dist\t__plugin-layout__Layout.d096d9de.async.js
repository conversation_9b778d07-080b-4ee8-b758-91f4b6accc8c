(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6301],{16254:function(ie){ie.exports=ct,ie.exports.parse=T,ie.exports.compile=u,ie.exports.tokensToFunction=x,ie.exports.tokensToRegExp=Ue;var se="/",v="./",w=new RegExp(["(\\\\.)","(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?"].join("|"),"g");function T(M,O){for(var j=[],_=0,$=0,L="",Y=O&&O.delimiter||se,J=O&&O.delimiters||v,R=!1,D;(D=w.exec(M))!==null;){var X=D[0],U=D[1],F=D.index;if(L+=M.slice($,F),$=F+X.length,U){L+=U[1],R=!0;continue}var k="",Ae=M[$],dt=D[2],pt=D[3],vt=D[4],ve=D[5];if(!R&&L.length){var Ne=L.length-1;J.indexOf(L[Ne])>-1&&(k=L[Ne],L=L.slice(0,Ne))}L&&(j.push(L),L="",R=!1);var Ge=k!==""&&Ae!==void 0&&Ae!==k,Qt=ve==="+"||ve==="*",mt=ve==="?"||ve==="*",gt=k||Y,Ve=pt||vt;j.push({name:dt||_++,prefix:k,delimiter:gt,optional:mt,repeat:Qt,partial:Ge,pattern:Ve?ye(Ve):"[^"+V(gt)+"]+?"})}return(L||$<M.length)&&j.push(L+M.substr($)),j}function u(M,O){return x(T(M,O))}function x(M){for(var O=new Array(M.length),j=0;j<M.length;j++)typeof M[j]=="object"&&(O[j]=new RegExp("^(?:"+M[j].pattern+")$"));return function(_,$){for(var L="",Y=$&&$.encode||encodeURIComponent,J=0;J<M.length;J++){var R=M[J];if(typeof R=="string"){L+=R;continue}var D=_?_[R.name]:void 0,X;if(Array.isArray(D)){if(!R.repeat)throw new TypeError('Expected "'+R.name+'" to not repeat, but got array');if(D.length===0){if(R.optional)continue;throw new TypeError('Expected "'+R.name+'" to not be empty')}for(var U=0;U<D.length;U++){if(X=Y(D[U],R),!O[J].test(X))throw new TypeError('Expected all "'+R.name+'" to match "'+R.pattern+'"');L+=(U===0?R.prefix:R.delimiter)+X}continue}if(typeof D=="string"||typeof D=="number"||typeof D=="boolean"){if(X=Y(String(D),R),!O[J].test(X))throw new TypeError('Expected "'+R.name+'" to match "'+R.pattern+'", but got "'+X+'"');L+=R.prefix+X;continue}if(R.optional){R.partial&&(L+=R.prefix);continue}throw new TypeError('Expected "'+R.name+'" to be '+(R.repeat?"an array":"a string"))}return L}}function V(M){return M.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function ye(M){return M.replace(/([=!:$/()])/g,"\\$1")}function Te(M){return M&&M.sensitive?"":"i"}function Yt(M,O){if(!O)return M;var j=M.source.match(/\((?!\?)/g);if(j)for(var _=0;_<j.length;_++)O.push({name:_,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,pattern:null});return M}function be(M,O,j){for(var _=[],$=0;$<M.length;$++)_.push(ct(M[$],O,j).source);return new RegExp("(?:"+_.join("|")+")",Te(j))}function Jt(M,O,j){return Ue(T(M,j),O,j)}function Ue(M,O,j){j=j||{};for(var _=j.strict,$=j.start!==!1,L=j.end!==!1,Y=V(j.delimiter||se),J=j.delimiters||v,R=[].concat(j.endsWith||[]).map(V).concat("$").join("|"),D=$?"^":"",X=M.length===0,U=0;U<M.length;U++){var F=M[U];if(typeof F=="string")D+=V(F),X=U===M.length-1&&J.indexOf(F[F.length-1])>-1;else{var k=F.repeat?"(?:"+F.pattern+")(?:"+V(F.delimiter)+"(?:"+F.pattern+"))*":F.pattern;O&&O.push(F),F.optional?F.partial?D+=V(F.prefix)+"("+k+")?":D+="(?:"+V(F.prefix)+"("+k+"))?":D+=V(F.prefix)+"("+k+")"}}return L?(_||(D+="(?:"+Y+")?"),D+=R==="$"?"$":"(?="+R+")"):(_||(D+="(?:"+Y+"(?="+R+"))?"),X||(D+="(?="+Y+"|"+R+")")),new RegExp(D,Te(j))}function ct(M,O,j){return M instanceof RegExp?Yt(M,O):Array.isArray(M)?be(M,O,j):Jt(M,O,j)}},7700:function(){},2828:function(){},17124:function(){},43361:function(){},17212:function(){},18067:function(){},58490:function(ie,se,v){"use strict";v.r(se),v.d(se,{default:function(){return Ll}});var w=v(11849),T=v(2824),u=v(67294),x=v(21010),V=v(79004),ye=v(93224),Te=v(73727),Yt=v(24793),be=v(97183),Jt=v(84305),Ue=v(69224),ct=v(7700),M=v(94184),O=v.n(M),j=v(42473),_=v.n(j),$=v(21770),L=v(38069),Y=v(56725),J=v(12044);function R(t,e){var r=typeof t.pageName=="string"?t.title:e;(0,u.useEffect)(function(){(0,J.Z)()&&r&&(document.title=r)},[t.title,r])}var D=R,X=v(97435),U=v(40821),F=v(62991),k=v.n(F),Ae=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function dt(t,e){return!!(t===e||Ae(t)&&Ae(e))}function pt(t,e){if(t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(!dt(t[r],e[r]))return!1;return!0}function vt(t,e){e===void 0&&(e=pt);var r,n=[],a,i=!1;function o(){for(var l=[],s=0;s<arguments.length;s++)l[s]=arguments[s];return i&&r===this&&e(l,n)||(a=t.apply(this,l),i=!0,r=this,n=l),a}return o}var ve=vt;function Ne(t){for(var e=[],r=0;r<t.length;){var n=t[r];if(n==="*"||n==="+"||n==="?"){e.push({type:"MODIFIER",index:r,value:t[r++]});continue}if(n==="\\"){e.push({type:"ESCAPED_CHAR",index:r++,value:t[r++]});continue}if(n==="{"){e.push({type:"OPEN",index:r,value:t[r++]});continue}if(n==="}"){e.push({type:"CLOSE",index:r,value:t[r++]});continue}if(n===":"){for(var a="",i=r+1;i<t.length;){var o=t.charCodeAt(i);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||o===95){a+=t[i++];continue}break}if(!a)throw new TypeError("Missing parameter name at "+r);e.push({type:"NAME",index:r,value:a}),r=i;continue}if(n==="("){var l=1,s="",i=r+1;if(t[i]==="?")throw new TypeError('Pattern cannot start with "?" at '+i);for(;i<t.length;){if(t[i]==="\\"){s+=t[i++]+t[i++];continue}if(t[i]===")"){if(l--,l===0){i++;break}}else if(t[i]==="("&&(l++,t[i+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+i);s+=t[i++]}if(l)throw new TypeError("Unbalanced pattern at "+r);if(!s)throw new TypeError("Missing pattern at "+r);e.push({type:"PATTERN",index:r,value:s}),r=i;continue}e.push({type:"CHAR",index:r,value:t[r++]})}return e.push({type:"END",index:r,value:""}),e}function Ge(t,e){e===void 0&&(e={});for(var r=Ne(t),n=e.prefixes,a=n===void 0?"./":n,i="[^"+xe(e.delimiter||"/#?")+"]+?",o=[],l=0,s=0,f="",c=function(B){if(s<r.length&&r[s].type===B)return r[s++].value},d=function(B){var K=c(B);if(K!==void 0)return K;var W=r[s],N=W.type,re=W.index;throw new TypeError("Unexpected "+N+" at "+re+", expected "+B)},p=function(){for(var B="",K;K=c("CHAR")||c("ESCAPED_CHAR");)B+=K;return B};s<r.length;){var g=c("CHAR"),b=c("NAME"),P=c("PATTERN");if(b||P){var m=g||"";a.indexOf(m)===-1&&(f+=m,m=""),f&&(o.push(f),f=""),o.push({name:b||l++,prefix:m,suffix:"",pattern:P||i,modifier:c("MODIFIER")||""});continue}var y=g||c("ESCAPED_CHAR");if(y){f+=y;continue}f&&(o.push(f),f="");var h=c("OPEN");if(h){var m=p(),S=c("NAME")||"",E=c("PATTERN")||"",H=p();d("CLOSE"),o.push({name:S||(E?l++:""),pattern:S&&!E?i:E,prefix:m,suffix:H,modifier:c("MODIFIER")||""});continue}d("END")}return o}function Qt(t,e){return mt(Ge(t,e),e)}function mt(t,e){e===void 0&&(e={});var r=ht(e),n=e.encode,a=n===void 0?function(s){return s}:n,i=e.validate,o=i===void 0?!0:i,l=t.map(function(s){if(typeof s=="object")return new RegExp("^(?:"+s.pattern+")$",r)});return function(s){for(var f="",c=0;c<t.length;c++){var d=t[c];if(typeof d=="string"){f+=d;continue}var p=s?s[d.name]:void 0,g=d.modifier==="?"||d.modifier==="*",b=d.modifier==="*"||d.modifier==="+";if(Array.isArray(p)){if(!b)throw new TypeError('Expected "'+d.name+'" to not repeat, but got an array');if(p.length===0){if(g)continue;throw new TypeError('Expected "'+d.name+'" to not be empty')}for(var P=0;P<p.length;P++){var m=a(p[P],d);if(o&&!l[c].test(m))throw new TypeError('Expected all "'+d.name+'" to match "'+d.pattern+'", but got "'+m+'"');f+=d.prefix+m+d.suffix}continue}if(typeof p=="string"||typeof p=="number"){var m=a(String(p),d);if(o&&!l[c].test(m))throw new TypeError('Expected "'+d.name+'" to match "'+d.pattern+'", but got "'+m+'"');f+=d.prefix+m+d.suffix;continue}if(!g){var y=b?"an array":"a string";throw new TypeError('Expected "'+d.name+'" to be '+y)}}return f}}function gt(t,e){var r=[],n=Oe(t,r,e);return Ve(n,r,e)}function Ve(t,e,r){r===void 0&&(r={});var n=r.decode,a=n===void 0?function(i){return i}:n;return function(i){var o=t.exec(i);if(!o)return!1;for(var l=o[0],s=o.index,f=Object.create(null),c=function(p){if(o[p]===void 0)return"continue";var g=e[p-1];g.modifier==="*"||g.modifier==="+"?f[g.name]=o[p].split(g.prefix+g.suffix).map(function(b){return a(b,g)}):f[g.name]=a(o[p],g)},d=1;d<o.length;d++)c(d);return{path:l,index:s,params:f}}}function xe(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function ht(t){return t&&t.sensitive?"":"i"}function bn(t,e){if(!e)return t;var r=t.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)e.push({name:n,prefix:"",suffix:"",modifier:"",pattern:""});return t}function xn(t,e,r){var n=t.map(function(a){return Oe(a,e,r).source});return new RegExp("(?:"+n.join("|")+")",ht(r))}function On(t,e,r){return Cn(Ge(t,r),e,r)}function Cn(t,e,r){r===void 0&&(r={});for(var n=r.strict,a=n===void 0?!1:n,i=r.start,o=i===void 0?!0:i,l=r.end,s=l===void 0?!0:l,f=r.encode,c=f===void 0?function(B){return B}:f,d="["+xe(r.endsWith||"")+"]|$",p="["+xe(r.delimiter||"/#?")+"]",g=o?"^":"",b=0,P=t;b<P.length;b++){var m=P[b];if(typeof m=="string")g+=xe(c(m));else{var y=xe(c(m.prefix)),h=xe(c(m.suffix));if(m.pattern)if(e&&e.push(m),y||h)if(m.modifier==="+"||m.modifier==="*"){var S=m.modifier==="*"?"?":"";g+="(?:"+y+"((?:"+m.pattern+")(?:"+h+y+"(?:"+m.pattern+"))*)"+h+")"+S}else g+="(?:"+y+"("+m.pattern+")"+h+")"+m.modifier;else g+="("+m.pattern+")"+m.modifier;else g+="(?:"+y+h+")"+m.modifier}}if(s)a||(g+=p+"?"),g+=r.endsWith?"(?="+d+")":"$";else{var E=t[t.length-1],H=typeof E=="string"?p.indexOf(E[E.length-1])>-1:E===void 0;a||(g+="(?:"+p+"(?="+d+"))?"),H||(g+="(?="+p+"|"+d+")")}return new RegExp(g,ht(r))}function Oe(t,e,r){return t instanceof RegExp?bn(t,e):Array.isArray(t)?xn(t,e,r):On(t,e,r)}function te(t,e){return e>>>t|e<<32-t}function Pn(t,e,r){return t&e^~t&r}function Mn(t,e,r){return t&e^t&r^e&r}function wn(t){return te(2,t)^te(13,t)^te(22,t)}function En(t){return te(6,t)^te(11,t)^te(25,t)}function Sn(t){return te(7,t)^te(18,t)^t>>>3}function jn(t){return te(17,t)^te(19,t)^t>>>10}function Rn(t,e){return t[e&15]+=jn(t[e+14&15])+t[e+9&15]+Sn(t[e+1&15])}var Tn=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],A,G,Z,An="0123456789abcdef";function kt(t,e){var r=(t&65535)+(e&65535),n=(t>>16)+(e>>16)+(r>>16);return n<<16|r&65535}function Nn(){A=new Array(8),G=new Array(2),Z=new Array(64),G[0]=G[1]=0,A[0]=1779033703,A[1]=3144134277,A[2]=1013904242,A[3]=2773480762,A[4]=1359893119,A[5]=2600822924,A[6]=528734635,A[7]=1541459225}function yt(){var t,e,r,n,a,i,o,l,s,f,c=new Array(16);t=A[0],e=A[1],r=A[2],n=A[3],a=A[4],i=A[5],o=A[6],l=A[7];for(var d=0;d<16;d++)c[d]=Z[(d<<2)+3]|Z[(d<<2)+2]<<8|Z[(d<<2)+1]<<16|Z[d<<2]<<24;for(var p=0;p<64;p++)s=l+En(a)+Pn(a,i,o)+Tn[p],p<16?s+=c[p]:s+=Rn(c,p),f=wn(t)+Mn(t,e,r),l=o,o=i,i=a,a=kt(n,s),n=r,r=e,e=t,t=kt(s,f);A[0]+=t,A[1]+=e,A[2]+=r,A[3]+=n,A[4]+=a,A[5]+=i,A[6]+=o,A[7]+=l}function Dn(t,e){var r,n,a=0;n=G[0]>>3&63;var i=e&63;for((G[0]+=e<<3)<e<<3&&G[1]++,G[1]+=e>>29,r=0;r+63<e;r+=64){for(var o=n;o<64;o++)Z[o]=t.charCodeAt(a++);yt(),n=0}for(var l=0;l<i;l++)Z[l]=t.charCodeAt(a++)}function In(){var t=G[0]>>3&63;if(Z[t++]=128,t<=56)for(var e=t;e<56;e++)Z[e]=0;else{for(var r=t;r<64;r++)Z[r]=0;yt();for(var n=0;n<56;n++)Z[n]=0}Z[56]=G[1]>>>24&255,Z[57]=G[1]>>>16&255,Z[58]=G[1]>>>8&255,Z[59]=G[1]&255,Z[60]=G[0]>>>24&255,Z[61]=G[0]>>>16&255,Z[62]=G[0]>>>8&255,Z[63]=G[0]&255,yt()}function ql(){for(var t=0,e=new Array(32),r=0;r<8;r++)e[t++]=A[r]>>>24&255,e[t++]=A[r]>>>16&255,e[t++]=A[r]>>>8&255,e[t++]=A[r]&255;return e}function Hn(){for(var t=new String,e=0;e<8;e++)for(var r=28;r>=0;r-=4)t+=An.charAt(A[e]>>>r&15);return t}function Ln(t){return Nn(),Dn(t,t.length),In(),Hn()}var Bn=Ln;function Xe(t){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Xe=function(r){return typeof r}:Xe=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Xe(t)}function Fn(t,e){return Wn(t)||zn(t,e)||xt(t,e)||Zn()}function Zn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zn(t,e){if(!(typeof Symbol=="undefined"||!(Symbol.iterator in Object(t)))){var r=[],n=!0,a=!1,i=void 0;try{for(var o=t[Symbol.iterator](),l;!(n=(l=o.next()).done)&&(r.push(l.value),!(e&&r.length===e));n=!0);}catch(s){a=!0,i=s}finally{try{!n&&o.return!=null&&o.return()}finally{if(a)throw i}}return r}}function Wn(t){if(Array.isArray(t))return t}function Kn(t,e){var r;if(typeof Symbol=="undefined"||t[Symbol.iterator]==null){if(Array.isArray(t)||(r=xt(t))||e&&t&&typeof t.length=="number"){r&&(t=r);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(f){throw f},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,o=!1,l;return{s:function(){r=t[Symbol.iterator]()},n:function(){var f=r.next();return i=f.done,f},e:function(f){o=!0,l=f},f:function(){try{!i&&r.return!=null&&r.return()}finally{if(o)throw l}}}}function _n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function qt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function $n(t,e,r){return e&&qt(t.prototype,e),r&&qt(t,r),t}function Un(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&De(t,e)}function Gn(t){var e=er();return function(){var n=Ie(t),a;if(e){var i=Ie(this).constructor;a=Reflect.construct(n,arguments,i)}else a=n.apply(this,arguments);return Vn(this,a)}}function Vn(t,e){return e&&(Xe(e)==="object"||typeof e=="function")?e:Xn(t)}function Xn(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function bt(t){var e=typeof Map=="function"?new Map:void 0;return bt=function(n){if(n===null||!Yn(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(n))return e.get(n);e.set(n,a)}function a(){return Ye(n,arguments,Ie(this).constructor)}return a.prototype=Object.create(n.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),De(a,n)},bt(t)}function Ye(t,e,r){return er()?Ye=Reflect.construct:Ye=function(a,i,o){var l=[null];l.push.apply(l,i);var s=Function.bind.apply(a,l),f=new s;return o&&De(f,o.prototype),f},Ye.apply(null,arguments)}function er(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}function Yn(t){return Function.toString.call(t).indexOf("[native code]")!==-1}function De(t,e){return De=Object.setPrototypeOf||function(n,a){return n.__proto__=a,n},De(t,e)}function Ie(t){return Ie=Object.setPrototypeOf?Object.getPrototypeOf:function(r){return r.__proto__||Object.getPrototypeOf(r)},Ie(t)}function tr(t){return kn(t)||Qn(t)||xt(t)||Jn()}function Jn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xt(t,e){if(!!t){if(typeof t=="string")return Ot(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ot(t,e)}}function Qn(t){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(t))return Array.from(t)}function kn(t){if(Array.isArray(t))return Ot(t)}function Ot(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function qn(t,e){if(t==null)return{};var r=ea(t,e),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(a=0;a<i.length;a++)n=i[a],!(e.indexOf(n)>=0)&&(!Object.prototype.propertyIsEnumerable.call(t,n)||(r[n]=t[n]))}return r}function ea(t,e){if(t==null)return{};var r={},n=Object.keys(t),a,i;for(i=0;i<n.length;i++)a=n[i],!(e.indexOf(a)>=0)&&(r[a]=t[a]);return r}function rr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?rr(Object(r),!0).forEach(function(n){ta(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function ta(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function He(t){return t.split("?")[0].split("#")[0]}var Ct=function(e){if(!e.startsWith("http"))return!1;try{var r=new URL(e);return!!r}catch(n){return!1}},ra=function(e){var r=e.path;if(!r||r==="/")try{return"/".concat(Bn(JSON.stringify(e)))}catch(n){}return r&&He(r)},na=function(e,r){var n=e.name,a=e.locale;return"locale"in e&&a===!1||!n?!1:e.locale||"".concat(r,".").concat(n)},nr=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return(e||r).startsWith("/")||Ct(e)?e:"/".concat(r,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},aa=function(e,r){var n=e.menu,a=n===void 0?{}:n,i=e.indexRoute,o=e.path,l=o===void 0?"":o,s=e.routes,f=a.name,c=f===void 0?e.name:f,d=a.icon,p=d===void 0?e.icon:d,g=a.hideChildren,b=g===void 0?e.hideChildren:g,P=a.flatMenu,m=P===void 0?e.flatMenu:P,y=i&&Object.keys(i).join(",")!=="redirect"?[z({path:l,menu:a},i)].concat(s||[]):s,h=z({},e);if(c&&(h.name=c),p&&(h.icon=p),y&&y.length){if(b)return delete h.routes,h;var S=Pt(z(z({},r),{},{data:y}),e);if(m)return S;h.routes=S}return h};function Pt(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},r=t.data,n=t.formatMessage,a=t.parentName,i=t.locale;return!r||!Array.isArray(r)?[]:r.filter(function(o){return o?o.routes||o.children||o.path||o.layout?!0:(o.redirect||o.unaccessible,!1):!1}).filter(function(o){var l,s;return o.unaccessible&&delete o.name,(o==null||(l=o.menu)===null||l===void 0?void 0:l.name)||(o==null?void 0:o.flatMenu)||(o==null||(s=o.menu)===null||s===void 0?void 0:s.flatMenu)?!0:o.menu!==!1}).map(function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"};o.children&&!o.routes&&(o.routes=o.children,delete o.children);var l=nr(o.path,e?e.path:"/"),s=o.name,f=na(o,a||"menu"),c=f!==!1&&i!==!1&&n&&f?n({id:f,defaultMessage:s}):s,d=e.pro_layout_parentKeys,p=d===void 0?[]:d,g=e.routes,b=e.children,P=e.icon,m=e.flatMenu,y=e.indexRoute,h=qn(e,["pro_layout_parentKeys","routes","children","icon","flatMenu","indexRoute"]),S=z(z(z({},h),{},{menu:void 0},o),{},{path:l,locale:f,key:o.key||ra(z(z({},o),{},{path:l})),pro_layout_parentKeys:Array.from(new Set([].concat(tr(o.parentKeys||[]),tr(p),["/".concat(e.key||"").replace(/\/\//g,"/").replace(/\/\//g,"/")]))).filter(function(H){return H&&H!=="/"})});if(c?S.name=c:delete S.name,S.menu===void 0&&delete S.menu,o.routes){var E=Pt(z(z({},t),{},{data:o.routes,parentName:f||""}),S);S.routes=E&&E.length>0?E:void 0,S.routes||delete S.routes}return aa(S,t)}).flat(1)}var ia=ve(Pt,k()),oa=function t(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.filter(function(r){return r&&(r.name||r.routes)&&!r.hideInMenu&&!r.redirect}).map(function(r){var n=z({},r);if(n.children&&!n.routes&&(n.routes=r.children),n.routes&&Array.isArray(n.routes)&&!n.hideChildrenInMenu&&n.routes.some(function(i){return i&&!!i.name})){var a=t(n.routes);if(a.length)return z(z({},n),{},{routes:a})}return z(z({},r),{},{routes:void 0})}).filter(function(r){return r})},la=function(t){Un(r,t);var e=Gn(r);function r(){return _n(this,r),e.apply(this,arguments)}return $n(r,[{key:"get",value:function(a){var i;try{var o=Kn(this.entries()),l;try{for(o.s();!(l=o.n()).done;){var s=Fn(l.value,2),f=s[0],c=s[1],d=He(f);if(!Ct(f)&&Oe(d,[]).test(a)){i=c;break}}}catch(p){o.e(p)}finally{o.f()}}catch(p){i=void 0}return i}}]),r}(bt(Map)),ua=function(e){var r=new la,n=function a(i,o){i.forEach(function(l){l&&l.routes&&a(l.routes,l);var s=nr(l.path,o?o.path:"/");r.set(He(s),l)})};return n(e),r},sa=ve(ua,k()),fa=function t(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(function(r){if(r.routes&&Array.isArray(r.routes)&&r.routes.length>0){var n=t(r.routes);if(n.length)return z(z({},r),{},{routes:n})}var a=z({},r);return delete a.routes,a}).filter(function(r){return r})},ca=function(e,r,n,a){var i=ia({data:e,formatMessage:n,locale:r}),o=a?fa(i):oa(i),l=sa(i);return{breadcrumb:l,menuData:o}},ar=ca;function ir(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Je(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?ir(Object(r),!0).forEach(function(n){da(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ir(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function da(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var pa=function t(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r={};return e.forEach(function(n){!n||!n.key||(r[He(n.path||n.key||"/")]=Je({},n),r[n.key||n.path||"/"]=Je({},n),n.routes&&(r=Je(Je({},r),t(n.routes))))}),r},or=pa,va=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;return e.filter(function(a){if(a==="/"&&r==="/")return!0;if(a!=="/"&&a!=="/*"&&a&&!Ct(a)){var i=He(a);try{if(n&&Oe("".concat(i)).test(r)||Oe("".concat(i),[]).test(r)||Oe("".concat(i,"/(.*)")).test(r))return!0}catch(o){}}return!1}).sort(function(a,i){return a===r?10:i===r?-10:a.substr(1).split("/").length-i.substr(1).split("/").length})},ma=function(e,r,n,a){var i=or(r),o=Object.keys(i),l=va(o,e||"/",a);return!l||l.length<1?[]:(n||(l=[l[l.length-1]]),l.map(function(s){var f=i[s]||{pro_layout_parentKeys:"",key:""},c=new Map,d=(f.pro_layout_parentKeys||[]).map(function(p){return c.has(p)?null:(c.set(p,!0),i[p])}).filter(function(p){return p});return f.key&&d.push(f),d}).flat(1))},lr=ma,eu=v(2828),tu=v(17124),ru=v(30887),oe=v(99210),ga=v(1351),ha=v(76629),nu=v(43361),au=v(65056),iu=v(18067),q=v(96156),ee=v(22122),ya=v(90484),ba=function(e){var r=e.prefixCls,n=e.className,a=e.width,i=e.style;return u.createElement("h3",{className:O()(r,n),style:(0,ee.Z)({width:a},i)})},xa=ba,Oa=v(85061),Ca=function(e){var r=function(f){var c=e.width,d=e.rows,p=d===void 0?2:d;if(Array.isArray(c))return c[f];if(p-1===f)return c},n=e.prefixCls,a=e.className,i=e.style,o=e.rows,l=(0,Oa.Z)(Array(o)).map(function(s,f){return u.createElement("li",{key:f,style:{width:r(f)}})});return u.createElement("ul",{className:O()(n,a),style:i},l)},Pa=Ca,Le=v(65632),Ma=function(e){var r,n,a=e.prefixCls,i=e.className,o=e.style,l=e.size,s=e.shape,f=O()((r={},(0,q.Z)(r,"".concat(a,"-lg"),l==="large"),(0,q.Z)(r,"".concat(a,"-sm"),l==="small"),r)),c=O()((n={},(0,q.Z)(n,"".concat(a,"-circle"),s==="circle"),(0,q.Z)(n,"".concat(a,"-square"),s==="square"),(0,q.Z)(n,"".concat(a,"-round"),s==="round"),n)),d=typeof l=="number"?{width:l,height:l,lineHeight:"".concat(l,"px")}:{};return u.createElement("span",{className:O()(a,f,c,i),style:(0,ee.Z)((0,ee.Z)({},d),o)})},Qe=Ma,Mt=v(10366),ur=function(e){var r=function(a){var i=a.getPrefixCls,o=e.prefixCls,l=e.className,s=e.active,f=i("skeleton",o),c=(0,Mt.Z)(e,["prefixCls","className"]),d=O()(f,"".concat(f,"-element"),(0,q.Z)({},"".concat(f,"-active"),s),l);return u.createElement("div",{className:d},u.createElement(Qe,(0,ee.Z)({prefixCls:"".concat(f,"-avatar")},c)))};return u.createElement(Le.C,null,r)};ur.defaultProps={size:"default",shape:"circle"};var wa=ur,sr=function(e){var r=function(a){var i=a.getPrefixCls,o=e.prefixCls,l=e.className,s=e.active,f=i("skeleton",o),c=(0,Mt.Z)(e,["prefixCls"]),d=O()(f,"".concat(f,"-element"),(0,q.Z)({},"".concat(f,"-active"),s),l);return u.createElement("div",{className:d},u.createElement(Qe,(0,ee.Z)({prefixCls:"".concat(f,"-button")},c)))};return u.createElement(Le.C,null,r)};sr.defaultProps={size:"default"};var Ea=sr,fr=function(e){var r=function(a){var i=a.getPrefixCls,o=e.prefixCls,l=e.className,s=e.active,f=i("skeleton",o),c=(0,Mt.Z)(e,["prefixCls"]),d=O()(f,"".concat(f,"-element"),(0,q.Z)({},"".concat(f,"-active"),s),l);return u.createElement("div",{className:d},u.createElement(Qe,(0,ee.Z)({prefixCls:"".concat(f,"-input")},c)))};return u.createElement(Le.C,null,r)};fr.defaultProps={size:"default"};var Sa=fr,ja="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",Ra=function(e){var r=function(a){var i=a.getPrefixCls,o=e.prefixCls,l=e.className,s=e.style,f=i("skeleton",o),c=O()(f,"".concat(f,"-element"),l);return u.createElement("div",{className:c},u.createElement("div",{className:O()("".concat(f,"-image"),l),style:s},u.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:"".concat(f,"-image-svg")},u.createElement("path",{d:ja,className:"".concat(f,"-image-path")}))))};return u.createElement(Le.C,null,r)},Ta=Ra;function wt(t){return t&&(0,ya.Z)(t)==="object"?t:{}}function Aa(t,e){return t&&!e?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function Na(t,e){return!t&&e?{width:"38%"}:t&&e?{width:"50%"}:{}}function Da(t,e){var r={};return(!t||!e)&&(r.width="61%"),!t&&e?r.rows=3:r.rows=2,r}var Ce=function(e){var r=function(a){var i=a.getPrefixCls,o=a.direction,l=e.prefixCls,s=e.loading,f=e.className,c=e.children,d=e.avatar,p=e.title,g=e.paragraph,b=e.active,P=e.round,m=i("skeleton",l);if(s||!("loading"in e)){var y,h=!!d,S=!!p,E=!!g,H;if(h){var B=(0,ee.Z)((0,ee.Z)({prefixCls:"".concat(m,"-avatar")},Aa(S,E)),wt(d));H=u.createElement("div",{className:"".concat(m,"-header")},u.createElement(Qe,B))}var K;if(S||E){var W;if(S){var N=(0,ee.Z)((0,ee.Z)({prefixCls:"".concat(m,"-title")},Na(h,E)),wt(p));W=u.createElement(xa,N)}var re;if(E){var le=(0,ee.Z)((0,ee.Z)({prefixCls:"".concat(m,"-paragraph")},Da(h,S)),wt(g));re=u.createElement(Pa,le)}K=u.createElement("div",{className:"".concat(m,"-content")},W,re)}var ce=O()(m,(y={},(0,q.Z)(y,"".concat(m,"-with-avatar"),h),(0,q.Z)(y,"".concat(m,"-active"),b),(0,q.Z)(y,"".concat(m,"-rtl"),o==="rtl"),(0,q.Z)(y,"".concat(m,"-round"),P),y),f);return u.createElement("div",{className:ce},H,K)}return c};return u.createElement(Le.C,null,r)};Ce.defaultProps={avatar:!1,title:!0,paragraph:!0},Ce.Button=Ea,Ce.Avatar=wa,Ce.Input=Sa,Ce.Image=Ta;var Ia=Ce,Ha=Ia,cr=v(91321),La=v(16165),Ba=function(e){if(!e.startsWith("http"))return!1;try{var r=new URL(e);return!!r}catch(n){return!1}},dr=Ba;function Fa(t){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(t)}var Za=Fa,za={navTheme:"dark",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!1,headerHeight:48,iconfontUrl:"",primaryColor:"daybreak",splitMenus:!1},pr=za;function vr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Et(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?vr(Object(r),!0).forEach(function(n){Wa(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Wa(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var mr=function t(e){return(e||[]).reduce(function(r,n){if(n.key&&r.push(n.key),n.routes){var a=r.concat(t(n.routes)||[]);return a}return r},[])},St={daybreak:"daybreak","#1890ff":"daybreak","#F5222D":"dust","#FA541C":"volcano","#FAAD14":"sunset","#13C2C2":"cyan","#52C41A":"green","#2F54EB":"geekblue","#722ED1":"purple"},Ka=function(e){return Object.keys(e).reduce(function(r,n){return r[e[n]]=n,r},{})};function ou(t){return t&&St[t]?St[t]:void 0}function lu(t){var e=Ka(St);return t&&e[t]?e[t]:t}function ke(t){return t.map(function(e){var r=Et({},e);if(!r.name||r.hideInMenu)return null;if(r&&(r==null?void 0:r.routes)){if(!r.hideChildrenInMenu&&r.routes.some(function(n){return n&&n.name&&!n.hideInMenu}))return Et(Et({},e),{},{routes:ke(r.routes)});delete r.routes}return r}).filter(function(e){return e})}var _a=v(57186);function $a(t,e){return Xa(t)||Va(t,e)||Ga(t,e)||Ua()}function Ua(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ga(t,e){if(!!t){if(typeof t=="string")return gr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gr(t,e)}}function gr(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Va(t,e){var r=t==null?null:typeof Symbol!="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],a=!0,i=!1,o,l;try{for(r=r.call(t);!(a=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));a=!0);}catch(s){i=!0,l=s}finally{try{!a&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function Xa(t){if(Array.isArray(t))return t}function Ya(){var t=(0,u.useState)([]),e=$a(t,2),r=e[0],n=e[1];return{flatMenuKeys:r,setFlatMenuKeys:n}}var Ja=(0,_a.f)(Ya),qe=Ja;function jt(){return jt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},jt.apply(this,arguments)}function hr(t){return qa(t)||ka(t)||yr(t)||Qa()}function Qa(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ka(t){if(typeof Symbol!="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function qa(t){if(Array.isArray(t))return Rt(t)}function et(t,e){return ri(t)||ti(t,e)||yr(t,e)||ei()}function ei(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yr(t,e){if(!!t){if(typeof t=="string")return Rt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Rt(t,e)}}function Rt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ti(t,e){var r=t==null?null:typeof Symbol!="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],a=!0,i=!1,o,l;try{for(r=r.call(t);!(a=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));a=!0);}catch(s){i=!0,l=s}finally{try{!a&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function ri(t){if(Array.isArray(t))return t}function br(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function tt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?br(Object(r),!0).forEach(function(n){ni(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):br(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function ni(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ai(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var ii=oe.Z.SubMenu,oi=oe.Z.ItemGroup,xr=(0,cr.Z)({scriptUrl:pr.iconfontUrl}),Or=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"icon-";if(typeof e=="string"&&e!==""){if(dr(e)||Za(e))return u.createElement(La.Z,{component:function(){return u.createElement("img",{src:e,alt:"icon",className:"ant-pro-sider-menu-icon"})}});if(e.startsWith(r))return u.createElement(xr,{type:e})}return e},li=function t(e){var r=this;ai(this,t),this.props=void 0,this.getNavMenuItems=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],a=arguments.length>1?arguments[1]:void 0;return n.map(function(i){return r.getSubMenuOrItem(i,a)}).filter(function(i){return i})},this.getSubMenuOrItem=function(n,a){if(Array.isArray(n.routes)&&n&&n.routes.length>0){var i=r.getIntlName(n),o=r.props,l=o.subMenuItemRender,s=o.prefixCls,f=o.menu,c=o.iconPrefixes,d=n.icon?u.createElement("span",{className:"".concat(s,"-menu-item"),title:i},!a&&Or(n.icon,c),u.createElement("span",{className:"".concat(s,"-menu-item-title")},i)):u.createElement("span",{className:"".concat(s,"-menu-item"),title:i},i),p=l?l(tt(tt({},n),{},{isUrl:!1}),d):d,g=(f==null?void 0:f.type)==="group"?oi:ii;return u.createElement(g,{title:p,key:n.key||n.path,onTitleClick:n.onTitleClick},r.getNavMenuItems(n.routes,!0))}return u.createElement(oe.Z.Item,{disabled:n.disabled,key:n.key||n.path,onClick:n.onTitleClick},r.getMenuItemPath(n,a))},this.getIntlName=function(n){var a=n.name,i=n.locale,o=r.props,l=o.menu,s=o.formatMessage;return i&&(l==null?void 0:l.locale)!==!1?s==null?void 0:s({id:i,defaultMessage:a}):a},this.getMenuItemPath=function(n,a){var i=r.conversionPath(n.path||"/"),o=r.props,l=o.location,s=l===void 0?{pathname:"/"}:l,f=o.isMobile,c=o.onCollapse,d=o.menuItemRender,p=o.iconPrefixes,g=r.getIntlName(n),b=r.props.prefixCls,P=a?null:Or(n.icon,p),m=u.createElement("span",{className:"".concat(b,"-menu-item")},P,u.createElement("span",{className:"".concat(b,"-menu-item-title")},g)),y=dr(i);if(y&&(m=u.createElement("span",{title:g,onClick:function(){var E,H;(E=window)===null||E===void 0||(H=E.open)===null||H===void 0||H.call(E,i)},className:"".concat(b,"-menu-item ").concat(b,"-menu-item-link")},P,u.createElement("span",{className:"".concat(b,"-menu-item-title")},g))),d){var h=tt(tt({},n),{},{isUrl:y,itemPath:i,isMobile:f,replace:i===s.pathname,onClick:function(){return c&&c(!0)},children:void 0});return d(h,m,r.props)}return m},this.conversionPath=function(n){return n&&n.indexOf("http")===0?n:"/".concat(n||"").replace(/\/+/g,"/")},this.props=e},ui=function(e,r){var n=r.layout,a=r.collapsed,i={};return e&&!a&&["side","mix"].includes(n||"mix")&&(i={openKeys:e}),i},Cr=function(e){var r=e.theme,n=e.mode,a=e.className,i=e.handleOpenChange,o=e.style,l=e.menuData,s=e.menu,f=e.matchMenuKeys,c=e.iconfontUrl,d=e.collapsed,p=e.selectedKeys,g=e.onSelect,b=e.openKeys,P=(0,u.useRef)([]),m=qe.useContainer(),y=m.flatMenuKeys,h=(0,Y.Z)(s==null?void 0:s.defaultOpenAll),S=et(h,2),E=S[0],H=S[1],B=(0,Y.Z)(function(){return(s==null?void 0:s.defaultOpenAll)?mr(l)||[]:b===!1?!1:[]},{value:b===!1?void 0:b,onChange:i}),K=et(B,2),W=K[0],N=K[1],re=(0,Y.Z)([],{value:p,onChange:g?function(he){g&&he&&g(he)}:void 0}),le=et(re,2),ce=le[0],ue=le[1];(0,u.useEffect)(function(){(s==null?void 0:s.defaultOpenAll)||b===!1||y.length||f&&(N(f),ue(f))},[f.join("-")]),(0,u.useEffect)(function(){c&&(xr=(0,cr.Z)({scriptUrl:c}))},[c]),(0,u.useEffect)(function(){if(f.join("-")!==(ce||[]).join("-")&&ue(f),!E&&b!==!1&&f.join("-")!==(W||[]).join("-")){var he=f;(s==null?void 0:s.autoClose)===!1&&(he=Array.from(new Set([].concat(hr(f),hr(W||[]))))),N(he)}else(s==null?void 0:s.ignoreFlatMenu)&&E?N(mr(l)):y.length>0&&H(!1)},[f.join("-"),d]);var Me=(0,u.useMemo)(function(){return ui(W,e)},[W&&W.join(","),e.layout,e.collapsed]),we=(0,u.useState)(function(){return new li(e)}),ge=et(we,1),ne=ge[0];if(s==null?void 0:s.loading)return u.createElement("div",{style:(n==null?void 0:n.includes("inline"))?{padding:24}:{marginTop:16}},u.createElement(Ha,{active:!0,title:!1,paragraph:{rows:(n==null?void 0:n.includes("inline"))?6:1}}));var ae=O()(a,{"top-nav-menu":n==="horizontal"});ne.props=e,e.openKeys===!1&&!e.handleOpenChange&&(P.current=f);var de=e.postMenuData?e.postMenuData(l):l;return de&&(de==null?void 0:de.length)<1?null:u.createElement(oe.Z,jt({},Me,{key:"Menu",mode:n,inlineIndent:16,defaultOpenKeys:P.current,theme:r,selectedKeys:ce,style:o,className:ae,onOpenChange:N},e.menuProps),ne.getNavMenuItems(de,!1))};Cr.defaultProps={postMenuData:function(e){return e||[]}};var Pr=Cr;function Mr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function wr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Mr(Object(r),!0).forEach(function(n){Pe(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Mr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Tt(){return Tt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Tt.apply(this,arguments)}function Pe(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var si=be.Z.Sider,Er=function(e){return typeof e=="string"?u.createElement("img",{src:e,alt:"logo"}):typeof e=="function"?e():e},At=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"menuHeaderRender",n=e.logo,a=e.title,i=e.layout,o=e[r||""];if(o===!1)return null;var l=Er(n),s=u.createElement("h1",null,a!=null?a:"Ant Design Pro");return o?o(l,e.collapsed?null:s,e):i==="mix"&&r==="menuHeaderRender"?null:u.createElement("a",null,l,e.collapsed?null:s)},Sr=function(e){return e?u.createElement(ga.Z,null):u.createElement(ha.Z,null)},fi=function(e){var r,n=e.collapsed,a=e.fixSiderbar,i=e.menuFooterRender,o=e.onCollapse,l=e.theme,s=e.siderWidth,f=e.isMobile,c=e.onMenuHeaderClick,d=e.breakpoint,p=d===void 0?"lg":d,g=e.style,b=e.layout,P=e.menuExtraRender,m=P===void 0?!1:P,y=e.collapsedButtonRender,h=y===void 0?Sr:y,S=e.links,E=e.menuContentRender,H=e.prefixCls,B=e.onOpenChange,K=e.headerHeight,W=e.logoStyle,N="".concat(H,"-sider"),re=qe.useContainer(),le=re.flatMenuKeys,ce=O()("".concat(N),(r={},Pe(r,"".concat(N,"-fixed"),a),Pe(r,"".concat(N,"-layout-").concat(b),b&&!f),Pe(r,"".concat(N,"-light"),l==="light"),r)),ue=At(e),Me=m&&m(e),we=E!==!1&&le&&u.createElement(Pr,Tt({},e,{key:"base-menu",mode:"inline",handleOpenChange:B,style:{width:"100%"},className:"".concat(N,"-menu")})),ge=E?E(e,we):we;return u.createElement(u.Fragment,null,a&&u.createElement("div",{style:wr({width:n?48:s,overflow:"hidden",flex:"0 0 ".concat(n?48:s,"px"),maxWidth:n?48:s,minWidth:n?48:s,transition:"background-color 0.3s, min-width 0.3s, max-width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"},g)}),u.createElement(si,{collapsible:!0,trigger:null,collapsed:n,breakpoint:p===!1?void 0:p,onCollapse:function(ae){f||o==null||o(ae)},collapsedWidth:48,style:wr({overflow:"hidden",paddingTop:b==="mix"&&!f?K:void 0},g),width:s,theme:l,className:ce},ue&&u.createElement("div",{className:O()("".concat(N,"-logo"),Pe({},"".concat(N,"-collapsed"),n)),onClick:b!=="mix"?c:void 0,id:"logo",style:W},ue),Me&&u.createElement("div",{className:"".concat(N,"-extra ").concat(!ue&&"".concat(N,"-extra-no-logo"))},Me),u.createElement("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"}},ge),u.createElement("div",{className:"".concat(N,"-links")},u.createElement(oe.Z,{theme:l,inlineIndent:16,className:"".concat(N,"-link-menu"),selectedKeys:[],openKeys:[],mode:"inline"},(S||[]).map(function(ne,ae){return u.createElement(oe.Z.Item,{className:"".concat(N,"-link"),key:ae},ne)}),h&&!f&&u.createElement(oe.Z.Item,{className:"".concat(N,"-collapsed-button"),title:!1,key:"collapsed",onClick:function(){o&&o(!n)}},h(n)))),i&&u.createElement("div",{className:O()("".concat(N,"-footer"),Pe({},"".concat(N,"-footer-collapsed"),!n))},i(e))))},jr=fi,Rr=v(28991),ci=v(6610),di=v(5991),pi=v(10379),vi=v(54070),mi=v(34203),gi=v(50344),Tr=v(80334),Ar=v(42550),hi=v(91033),yi="rc-observer-key",Nr=function(t){(0,pi.Z)(r,t);var e=(0,vi.Z)(r);function r(){var n;return(0,ci.Z)(this,r),n=e.apply(this,arguments),n.resizeObserver=null,n.childNode=null,n.currentElement=null,n.state={width:0,height:0,offsetHeight:0,offsetWidth:0},n.onResize=function(a){var i=n.props.onResize,o=a[0].target,l=o.getBoundingClientRect(),s=l.width,f=l.height,c=o.offsetWidth,d=o.offsetHeight,p=Math.floor(s),g=Math.floor(f);if(n.state.width!==p||n.state.height!==g||n.state.offsetWidth!==c||n.state.offsetHeight!==d){var b={width:p,height:g,offsetWidth:c,offsetHeight:d};n.setState(b),i&&Promise.resolve().then(function(){i((0,Rr.Z)((0,Rr.Z)({},b),{},{offsetWidth:c,offsetHeight:d}))})}},n.setChildNode=function(a){n.childNode=a},n}return(0,di.Z)(r,[{key:"componentDidMount",value:function(){this.onComponentUpdated()}},{key:"componentDidUpdate",value:function(){this.onComponentUpdated()}},{key:"componentWillUnmount",value:function(){this.destroyObserver()}},{key:"onComponentUpdated",value:function(){var a=this.props.disabled;if(a){this.destroyObserver();return}var i=(0,mi.Z)(this.childNode||this),o=i!==this.currentElement;o&&(this.destroyObserver(),this.currentElement=i),!this.resizeObserver&&i&&(this.resizeObserver=new hi.Z(this.onResize),this.resizeObserver.observe(i))}},{key:"destroyObserver",value:function(){this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)}},{key:"render",value:function(){var a=this.props.children,i=(0,gi.Z)(a);if(i.length>1)(0,Tr.ZP)(!1,"Find more than one child node with `children` in ResizeObserver. Will only observe first one.");else if(i.length===0)return(0,Tr.ZP)(!1,"`children` of ResizeObserver is empty. Nothing is in observe."),null;var o=i[0];if(u.isValidElement(o)&&(0,Ar.Yr)(o)){var l=o.ref;i[0]=u.cloneElement(o,{ref:(0,Ar.sQ)(l,this.setChildNode)})}return i.length===1?i[0]:i.map(function(s,f){return!u.isValidElement(s)||"key"in s&&s.key!==null?s:u.cloneElement(s,{key:"".concat(yi,"-").concat(f)})})}}]),r}(u.Component);Nr.displayName="ResizeObserver";var bi=Nr,uu=v(17212),xi=["rightContentRender"];function rt(){return rt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},rt.apply(this,arguments)}function Dr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Nt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Dr(Object(r),!0).forEach(function(n){Oi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Dr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Oi(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ci(t,e){return Ei(t)||wi(t,e)||Mi(t,e)||Pi()}function Pi(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Mi(t,e){if(!!t){if(typeof t=="string")return Ir(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ir(t,e)}}function Ir(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function wi(t,e){var r=t==null?null:typeof Symbol!="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],a=!0,i=!1,o,l;try{for(r=r.call(t);!(a=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));a=!0);}catch(s){i=!0,l=s}finally{try{!a&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function Ei(t){if(Array.isArray(t))return t}function Si(t,e){if(t==null)return{};var r=ji(t,e),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(a=0;a<i.length;a++)n=i[a],!(e.indexOf(n)>=0)&&(!Object.prototype.propertyIsEnumerable.call(t,n)||(r[n]=t[n]))}return r}function ji(t,e){if(t==null)return{};var r={},n=Object.keys(t),a,i;for(i=0;i<n.length;i++)a=n[i],!(e.indexOf(a)>=0)&&(r[a]=t[a]);return r}var Ri=function(e){var r=e.rightContentRender,n=Si(e,xi),a=(0,u.useState)("auto"),i=Ci(a,2),o=i[0],l=i[1];return u.createElement("div",{style:{minWidth:o}},u.createElement("div",{style:{paddingRight:8}},u.createElement(bi,{onResize:function(f){var c=f.width;l(c)}},r&&u.createElement("div",null,r(Nt({},n))))))},Ti=function(e){var r=(0,u.useRef)(null),n=e.theme,a=e.onMenuHeaderClick,i=e.contentWidth,o=e.rightContentRender,l=e.className,s=e.style,f=e.layout,c="".concat(e.prefixCls||"ant-pro","-top-nav-header"),d=At(Nt(Nt({},e),{},{collapsed:!1}),f==="mix"?"headerTitleRender":void 0),p=O()(c,l,{light:n==="light"});return u.createElement("div",{className:p,style:s},u.createElement("div",{ref:r,className:"".concat(c,"-main ").concat(i==="Fixed"?"wide":"")},d&&u.createElement("div",{className:"".concat(c,"-main-left"),onClick:a},u.createElement("div",{className:"".concat(c,"-logo"),key:"logo",id:"logo"},d)),u.createElement("div",{style:{flex:1},className:"".concat(c,"-menu")},u.createElement(Pr,rt({},e,e.menuProps))),o&&u.createElement(Ri,rt({rightContentRender:o},e))))},Hr=Ti;function Dt(){return Dt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Dt.apply(this,arguments)}function Lr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Be(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Lr(Object(r),!0).forEach(function(n){Br(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Lr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Br(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ai=function(e,r){return e===!1?null:e?e(r,null):r},Ni=function(e){var r=e.isMobile,n=e.logo,a=e.collapsed,i=e.onCollapse,o=e.collapsedButtonRender,l=o===void 0?Sr:o,s=e.rightContentRender,f=e.menuHeaderRender,c=e.onMenuHeaderClick,d=e.className,p=e.style,g=e.layout,b=e.children,P=e.headerTheme,m=P===void 0?"dark":P,y=e.splitMenus,h=e.menuData,S=e.prefixCls,E="".concat(S,"-global-header"),H=O()(d,E,Br({},"".concat(E,"-layout-").concat(g),g&&m==="dark"));if(g==="mix"&&!r&&y){var B=(h||[]).map(function(N){return Be(Be({},N),{},{children:void 0,routes:void 0})}),K=ke(B);return u.createElement(Hr,Dt({mode:"horizontal"},e,{splitMenus:!1,menuData:K,theme:m}))}var W=u.createElement("span",{className:"".concat(E,"-logo"),key:"logo"},u.createElement("a",null,Er(n)));return u.createElement("div",{className:H,style:Be({},p)},r&&Ai(f,W),r&&l&&u.createElement("span",{className:"".concat(E,"-collapsed-button"),onClick:function(){i&&i(!a)}},l(a)),g==="mix"&&!r&&u.createElement(u.Fragment,null,u.createElement("div",{className:"".concat(E,"-logo"),onClick:c},At(Be(Be({},e),{},{collapsed:!1}),"headerTitleRender"))),u.createElement("div",{style:{flex:1}},b),s&&s(e))},Di=Ni;function nt(t){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?nt=function(r){return typeof r}:nt=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},nt(t)}function Fr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Ii(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Fr(Object(r),!0).forEach(function(n){Fe(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Fr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Fe(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function at(){return at=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},at.apply(this,arguments)}function Hi(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Zr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Li(t,e,r){return e&&Zr(t.prototype,e),r&&Zr(t,r),t}function Bi(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&It(t,e)}function It(t,e){return It=Object.setPrototypeOf||function(n,a){return n.__proto__=a,n},It(t,e)}function Fi(t){var e=Wi();return function(){var n=it(t),a;if(e){var i=it(this).constructor;a=Reflect.construct(n,arguments,i)}else a=n.apply(this,arguments);return Zi(this,a)}}function Zi(t,e){if(e&&(nt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return zi(t)}function zi(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Wi(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}function it(t){return it=Object.setPrototypeOf?Object.getPrototypeOf:function(r){return r.__proto__||Object.getPrototypeOf(r)},it(t)}var zr=be.Z.Header,Ki=function(t){Bi(r,t);var e=Fi(r);function r(){var n;Hi(this,r);for(var a=arguments.length,i=new Array(a),o=0;o<a;o++)i[o]=arguments[o];return n=e.call.apply(e,[this].concat(i)),n.renderContent=function(){var l=n.props,s=l.isMobile,f=l.onCollapse,c=l.navTheme,d=l.layout,p=l.headerRender,g=l.headerContentRender,b=d==="top",P=ke(n.props.menuData||[]),m=u.createElement(Di,at({onCollapse:f},n.props,{menuData:P}),g&&g(n.props));return b&&!s&&(m=u.createElement(Hr,at({theme:c,mode:"horizontal",onCollapse:f},n.props,{menuData:P}))),p&&typeof p=="function"?p(n.props,m):m},n}return Li(r,[{key:"render",value:function(){var a,i=this.props,o=i.fixedHeader,l=i.layout,s=i.className,f=i.style,c=i.navTheme,d=i.collapsed,p=i.siderWidth,g=i.hasSiderMenu,b=i.isMobile,P=i.prefixCls,m=i.headerHeight,y=o||l==="mix",h=l==="top",S=y&&g&&!h&&!b,E=O()(s,(a={},Fe(a,"".concat(P,"-fixed-header"),y),Fe(a,"".concat(P,"-fixed-header-action"),!d),Fe(a,"".concat(P,"-top-menu"),h),Fe(a,"".concat(P,"-header-").concat(c),c&&l!=="mix"),a)),H=l!=="mix"&&S?"calc(100% - ".concat(d?48:p,"px)"):"100%",B=y?0:void 0;return u.createElement(u.Fragment,null,y&&u.createElement(zr,{style:{height:m,lineHeight:"".concat(m,"px"),background:"transparent"}}),u.createElement(zr,{style:Ii({padding:0,height:m,lineHeight:"".concat(m,"px"),width:H,zIndex:l==="mix"?100:19,right:B},f),className:E},this.renderContent()))}}]),r}(u.Component),_i=Ki,$i=v(16254),Ht=v.n($i);function Ui(t){return Yi(t)||Xi(t)||Vi(t)||Gi()}function Gi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Vi(t,e){if(!!t){if(typeof t=="string")return Lt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lt(t,e)}}function Xi(t){if(typeof Symbol!="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Yi(t){if(Array.isArray(t))return Lt(t)}function Lt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Ji=function(e,r,n){if(n){var a=Ui(n.keys()).find(function(o){return Ht()(o).test(e)});if(a)return n.get(a)}if(r){var i=Object.keys(r).find(function(o){return Ht()(o).test(e)});if(i)return r[i]}return{path:""}},Wr=function(e,r){var n=e.pathname,a=n===void 0?"/":n,i=e.breadcrumb,o=e.breadcrumbMap,l=e.formatMessage,s=e.title,f=e.menu,c=f===void 0?{locale:!1}:f,d=r?"":s||"",p=Ji(a,i,o);if(!p)return{title:d,id:"",pageName:d};var g=p.name;return c.locale!==!1&&p.locale&&l&&(g=l({id:p.locale||"",defaultMessage:p.name})),g?r||!s?{title:g,id:p.locale||"",pageName:g}:{title:"".concat(g," - ").concat(s),id:p.locale||"",pageName:g}:{title:d,id:p.locale||"",pageName:d}},su=function(e,r){return Wr(e,r).title},fu=null,Qi={"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.daybreak":"\u62C2\u6653\u84DD\uFF08\u9ED8\u8BA4\uFF09","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"};function Kr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function ki(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Kr(Object(r),!0).forEach(function(n){qi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Kr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function qi(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var eo=ki({},Qi),to={"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"};function _r(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function ro(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?_r(Object(r),!0).forEach(function(n){no(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_r(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function no(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ao=ro({},to),io={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark style","app.setting.pagestyle.light":"Light style","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.daybreak":"Daybreak Blue (default)","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"};function $r(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function oo(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?$r(Object(r),!0).forEach(function(n){lo(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$r(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function lo(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var uo=oo({},io),so={"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.daybreak":"Blu cielo mattutino (default)","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xE0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xE8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"};function Ur(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function fo(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ur(Object(r),!0).forEach(function(n){co(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ur(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function co(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var po=fo({},so),vo={"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.daybreak":"Daybreak Blue (default)","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."};function Gr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function mo(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Gr(Object(r),!0).forEach(function(n){go(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Gr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function go(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ho=mo({},vo),Bt={"zh-CN":eo,"zh-TW":ao,"en-US":uo,"it-IT":po,"ko-KR":ho},yo=function(){if(!(0,J.Z)())return"zh-CN";var e=window.localStorage.getItem("umi_locale");return e||window.g_locale||navigator.language},bo=function(){var t=yo();return Bt[t]?Bt[t]:Bt["zh-CN"]},xo=v(63485),Vr=v(64335),cu=v(57338),Oo=v(97532);function ot(){return ot=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ot.apply(this,arguments)}function Xr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function Co(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Xr(Object(r),!0).forEach(function(n){Po(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Xr(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Po(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Mo=function(e){var r=e.isMobile,n=e.menuData,a=e.siderWidth,i=e.collapsed,o=e.onCollapse,l=e.style,s=e.className,f=e.hide,c=e.getContainer,d=e.prefixCls,p=e.matchMenuKeys,g=qe.useContainer(),b=g.setFlatMenuKeys;(0,u.useEffect)(function(){if(!(!n||n.length<1)){var m=or(n);b(Object.keys(m))}},[p.join("-")]),(0,u.useEffect)(function(){r===!0&&(o==null||o(!0))},[r]);var P=(0,X.Z)(e,["className","style"]);return f?null:r?u.createElement(Oo.Z,{visible:!i,placement:"left",className:O()("".concat(d,"-drawer-sider"),s),onClose:function(){return o==null?void 0:o(!0)},style:Co({padding:0,height:"100vh"},l),getContainer:c,width:a,bodyStyle:{height:"100vh",padding:0,display:"flex",flexDirection:"row"}},u.createElement(jr,ot({},P,{className:O()("".concat(d,"-sider"),s),collapsed:r?!1:i,splitMenus:!1}))):u.createElement(jr,ot({className:O()("".concat(d,"-sider"),s)},P,{style:l}))},Ft=Mo;function wo(t){if(!t||t==="/")return["/"];var e=t.split("/").filter(function(r){return r});return e.map(function(r,n){return"/".concat(e.slice(0,n+1).join("/"))})}var Eo=function(e){var r=e.breadcrumbName,n=e.path;return u.createElement("a",{href:n},r)},So=function(e,r){var n=r.formatMessage,a=r.menu;return e.locale&&n&&(a==null?void 0:a.locale)!==!1?n({id:e.locale,defaultMessage:e.name}):e.name},jo=function(e,r){var n=e.get(r);if(!n){var a=Array.from(e.keys())||[],i=a.find(function(o){return Ht()(o.replace("?","")).test(r)});i&&(n=e.get(i))}return n||{path:""}},Ro=function(e){var r=e.location,n=e.breadcrumbMap;return{location:r,breadcrumbMap:n}},To=function(e,r,n){var a=wo(e==null?void 0:e.pathname),i=a.map(function(o){var l=jo(r,o),s=So(l,n),f=l.hideInBreadcrumb;return s&&!f?{path:o,breadcrumbName:s,component:l.component}:{path:"",breadcrumbName:""}}).filter(function(o){return o&&o.path});return i},Ao=function(e){var r=Ro(e),n=r.location,a=r.breadcrumbMap;return n&&n.pathname&&a?To(n,a,e):[]},No=function(e,r){var n=e.breadcrumbRender,a=e.itemRender,i=r.breadcrumbProps||{},o=i.minLength,l=o===void 0?2:o,s=Ao(e),f=a||Eo,c=s;return n&&(c=n(c)||[]),(c&&c.length<l||n===!1)&&(c=void 0),{routes:c,itemRender:f}};function Do(t,e){return Lo(t)||Ho(t,e)||Yr(t,e)||Io()}function Io(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ho(t,e){var r=t==null?null:typeof Symbol!="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],a=!0,i=!1,o,l;try{for(r=r.call(t);!(a=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));a=!0);}catch(s){i=!0,l=s}finally{try{!a&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function Lo(t){if(Array.isArray(t))return t}function Bo(t){return zo(t)||Zo(t)||Yr(t)||Fo()}function Fo(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yr(t,e){if(!!t){if(typeof t=="string")return Zt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Zt(t,e)}}function Zo(t){if(typeof Symbol!="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function zo(t){if(Array.isArray(t))return Zt(t)}function Zt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Wo(t){return Bo(t).reduce(function(e,r){var n=Do(r,2),a=n[0],i=n[1];return e[a]=i,e},{})}var Ko=function t(e,r,n,a){var i=ar(e,(r==null?void 0:r.locale)||!1,n,!0),o=i.menuData,l=i.breadcrumb;return a?t(a(o),r,n,void 0):{breadcrumb:Wo(l),breadcrumbMap:l,menuData:o}},_o=Ko,$o=v(83832),Uo=v(91200),Go=v(78164),Vo=function(e){var r=e.style,n=e.className,a=e.children,i=e.ErrorBoundary||Go.Z;return u.createElement(Uo.oK,null,u.createElement(i,null,u.createElement(be.Z.Content,{className:n,style:r},a)))},Xo=Vo,Yo=function(e){var r=["sidemenu","topmenu"];return r.includes(e)?e==null?void 0:e.replace("menu",""):e},Jo=Yo,Qo=v(51812);function lt(t){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?lt=function(r){return typeof r}:lt=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},lt(t)}function ko(t,e){return rl(t)||tl(t,e)||el(t,e)||qo()}function qo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function el(t,e){if(!!t){if(typeof t=="string")return Jr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jr(t,e)}}function Jr(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function tl(t,e){var r=t==null?null:typeof Symbol!="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],a=!0,i=!1,o,l;try{for(r=r.call(t);!(a=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));a=!0);}catch(s){i=!0,l=s}finally{try{!a&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function rl(t){if(Array.isArray(t))return t}var nl=function(e){var r=(0,u.useState)({}),n=ko(r,2),a=n[0],i=n[1];return(0,u.useEffect)(function(){i((0,Qo.Z)({layout:lt(e.layout)!=="object"?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar,headerTheme:e.headerTheme}))},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar,e.headerTheme]),a},al=nl,il=v(30939),ol=["id","defaultMessage"],ll=["fixSiderbar","navTheme","layout"];function Qr(t,e,r,n,a,i,o){try{var l=t[i](o),s=l.value}catch(f){r(f);return}l.done?e(s):Promise.resolve(s).then(n,a)}function ul(t){return function(){var e=this,r=arguments;return new Promise(function(n,a){var i=t.apply(e,r);function o(s){Qr(i,n,a,o,l,"next",s)}function l(s){Qr(i,n,a,o,l,"throw",s)}o(void 0)})}}function kr(t,e){if(t==null)return{};var r=sl(t,e),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(a=0;a<i.length;a++)n=i[a],!(e.indexOf(n)>=0)&&(!Object.prototype.propertyIsEnumerable.call(t,n)||(r[n]=t[n]))}return r}function sl(t,e){if(t==null)return{};var r={},n=Object.keys(t),a,i;for(i=0;i<n.length;i++)a=n[i],!(e.indexOf(a)>=0)&&(r[a]=t[a]);return r}function Ze(t,e){return pl(t)||dl(t,e)||cl(t,e)||fl()}function fl(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cl(t,e){if(!!t){if(typeof t=="string")return qr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qr(t,e)}}function qr(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function dl(t,e){var r=t==null?null:typeof Symbol!="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],a=!0,i=!1,o,l;try{for(r=r.call(t);!(a=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));a=!0);}catch(s){i=!0,l=s}finally{try{!a&&r.return!=null&&r.return()}finally{if(i)throw l}}return n}}function pl(t){if(Array.isArray(t))return t}function en(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,n)}return r}function I(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?en(Object(r),!0).forEach(function(n){fe(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function fe(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function me(){return me=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},me.apply(this,arguments)}var tn=0,vl=function(e,r){return e.headerRender===!1||e.pure?null:u.createElement(_i,me({matchMenuKeys:r},e))},ml=function(e){return e.footerRender===!1||e.pure?null:e.footerRender?e.footerRender(I({},e),u.createElement(xo.Z,null)):null},gl=function(e,r){var n=e.layout,a=e.isMobile,i=e.openKeys,o=e.splitMenus,l=e.menuRender;if(e.menuRender===!1||e.pure)return null;var s=e.menuData;if(o&&(i!==!1||n==="mix")&&!a){var f=Ze(r,1),c=f[0];if(c){var d,p;s=((d=e.menuData)===null||d===void 0||(p=d.find(function(P){return P.key===c}))===null||p===void 0?void 0:p.routes)||[]}else s=[]}var g=ke(s||[]);if(g&&(g==null?void 0:g.length)<1&&o)return null;if(n==="top"&&!a)return u.createElement(Ft,me({matchMenuKeys:r},e,{hide:!0}));if(l){var b=u.createElement(Ft,me({matchMenuKeys:r},e,{menuData:g}));return l(e,b)}return u.createElement(Ft,me({matchMenuKeys:r},e,{menuData:g}))},hl=function(e,r){var n=r.pageTitleRender,a=Wr(e);if(n===!1)return{title:r.title||"",id:"",pageName:""};if(n){var i=n(e,a.title,a);if(typeof i=="string")return I(I({},a),{},{title:i});_()(typeof i=="string","pro-layout: renderPageTitle return value should be a string")}return a},yl=function(e,r,n){return e?r?48:n:0},rn=function(e){var r,n,a,i,o=e||{},l=o.children,s=o.onCollapse,f=o.location,c=f===void 0?{pathname:"/"}:f,d=o.contentStyle,p=o.route,g=o.defaultCollapsed,b=o.style,P=o.disableContentMargin,m=o.siderWidth,y=m===void 0?208:m,h=o.menu,S=o.isChildrenLayout,E=o.menuDataRender,H=o.actionRef,B=o.formatMessage,K=o.loading,W=(0,u.useContext)(Ue.ZP.ConfigContext),N=(r=e.prefixCls)!==null&&r!==void 0?r:W.getPrefixCls("pro"),re=(0,Y.Z)(!1,{value:h==null?void 0:h.loading,onChange:h==null?void 0:h.onLoadingChange}),le=Ze(re,2),ce=le[0],ue=le[1],Me=(0,u.useState)(function(){return tn+=1,"pro-layout-".concat(tn)}),we=Ze(Me,1),ge=we[0],ne=(0,u.useCallback)(function(Q){var Se=Q.id,ft=Q.defaultMessage,Xt=kr(Q,ol);if(B)return B(I({id:Se,defaultMessage:ft},Xt));var je=bo();return je[Se]?je[Se]:ft},[B]),ae=(0,u.useMemo)(function(){return(h==null?void 0:h.params)?[ge,h==null?void 0:h.params]:[ge]},[ge,(0,il.P)(h==null?void 0:h.params)]),de=(0,u.useRef)(void 0),he=(0,U.ZP)(ae,function(){var Q=ul(regeneratorRuntime.mark(function Se(ft,Xt){var je,yn;return regeneratorRuntime.wrap(function(Re){for(;;)switch(Re.prev=Re.next){case 0:return ue(!0),Re.next=3,h==null||(je=h.request)===null||je===void 0?void 0:je.call(h,Xt||{},(p==null?void 0:p.routes)||[]);case 3:return yn=Re.sent,ue(!1),Re.abrupt("return",yn);case 6:case"end":return Re.stop()}},Se)}));return function(Se,ft){return Q.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),Wt=he.data;de.current=Wt,(0,u.useEffect)(function(){!de.current||(0,U.JG)(ae)},[ae]);var Bl=(0,u.useMemo)(function(){return _o(Wt||(p==null?void 0:p.routes)||[],h,ne,E)},[ne,h,E,Wt,p==null?void 0:p.routes]),Kt=Bl||{},ln=Kt.breadcrumb,Fl=ln===void 0?{}:ln,un=Kt.breadcrumbMap,sn=Kt.menuData,ze=sn===void 0?[]:sn;H&&(h==null?void 0:h.request)&&(H.current={reload:function(){(0,U.JG)(ae)}});var We=(0,u.useMemo)(function(){return lr(c.pathname||"/",ze||[],!0)},[c.pathname,ze]),_t=(0,u.useMemo)(function(){return Array.from(new Set(We.map(function(Q){return Q.key||Q.path||""})))},[We]),fn=We[We.length-1]||{},cn=al(fn),ut=I(I({},e),cn),Zl=ut.fixSiderbar,dn=ut.navTheme,zl=ut.layout,Wl=kr(ut,ll),Ke=Jo(zl),st=(0,L.ZP)(),_e=(st==="sm"||st==="xs")&&!e.disableMobile,Kl=Ke!=="top"&&!_e,_l=(0,$.Z)(function(){return g||!1},{value:e.collapsed,onChange:s}),pn=Ze(_l,2),$e=pn[0],vn=pn[1],Ee=(0,X.Z)(I(I(I({prefixCls:N},e),{},{siderWidth:y},cn),{},{formatMessage:ne,breadcrumb:Fl,menu:I(I({},h),{},{loading:ce}),layout:Ke}),["className","style","breadcrumbRender"]),$t=hl(I(I({pathname:c.pathname},Ee),{},{breadcrumbMap:un}),e),$l=No(I(I({},Ee),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:un}),e),Ut=gl(I(I({},Ee),{},{menuData:ze,onCollapse:vn,isMobile:_e,theme:(dn||"dark").toLocaleLowerCase().includes("dark")?"dark":"light",collapsed:$e}),_t),Gt=vl(I(I({},Ee),{},{hasSiderMenu:!!Ut,menuData:ze,isMobile:_e,collapsed:$e,onCollapse:vn,theme:(dn||"dark").toLocaleLowerCase().includes("dark")?"dark":"light"}),_t),mn=ml(I({isMobile:_e,collapsed:$e},Ee)),Ul=(0,u.useContext)(Vr.Z),Gl=Ul.isChildrenLayout,Vt=S!==void 0?S:Gl,pe="".concat(N,"-basicLayout"),Vl=O()(e.className,"ant-design-pro",pe,(n={},fe(n,"screen-".concat(st),st),fe(n,"".concat(pe,"-top-menu"),Ke==="top"),fe(n,"".concat(pe,"-is-children"),Vt),fe(n,"".concat(pe,"-fix-siderbar"),Zl),fe(n,"".concat(pe,"-").concat(Ke),Ke),n)),Xl=yl(!!Kl,$e,y),gn={position:"relative"};(Vt||d&&d.minHeight)&&(gn.minHeight=0);var Yl=O()("".concat(pe,"-content"),(a={},fe(a,"".concat(pe,"-has-header"),Gt),fe(a,"".concat(pe,"-content-disable-margin"),P),a));(0,u.useEffect)(function(){var Q;(Q=e.onPageChange)===null||Q===void 0||Q.call(e,e.location)},[c.pathname,(i=c.pathname)===null||i===void 0?void 0:i.search]);var Jl=(0,u.useState)(!1),hn=Ze(Jl,2),Ql=hn[0],kl=hn[1];return D($t,e.title||!1),u.createElement(qe.Provider,null,u.createElement(Vr.Z.Provider,{value:I(I({},Ee),{},{breadcrumb:$l,menuData:ze,isMobile:_e,collapsed:$e,isChildrenLayout:!0,title:$t.pageName,hasSiderMenu:!!Ut,hasHeader:!!Gt,siderWidth:Xl,hasFooter:!!mn,hasFooterToolbar:Ql,setHasFooterToolbar:kl,pageTitleInfo:$t,matchMenus:We,matchMenuKeys:_t,currentMenu:fn})},e.pure?l:u.createElement("div",{className:Vl},u.createElement(be.Z,{style:I({minHeight:"100%"},b)},Ut,u.createElement("div",{style:gn,className:W.getPrefixCls("layout")},Gt,u.createElement(Xo,me({isChildrenLayout:Vt},Wl,{className:Yl,style:d}),K?u.createElement($o.Z,null):l),mn)))))},bl=function(){return u.createElement("svg",{width:"32px",height:"32px",viewBox:"0 0 200 200"},u.createElement("defs",null,u.createElement("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1"},u.createElement("stop",{stopColor:"#4285EB",offset:"0%"}),u.createElement("stop",{stopColor:"#2EC7FF",offset:"100%"})),u.createElement("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2"},u.createElement("stop",{stopColor:"#29CDFF",offset:"0%"}),u.createElement("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),u.createElement("stop",{stopColor:"#0A60FF",offset:"100%"})),u.createElement("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3"},u.createElement("stop",{stopColor:"#FA816E",offset:"0%"}),u.createElement("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),u.createElement("stop",{stopColor:"#F51D2C",offset:"100%"})),u.createElement("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4"},u.createElement("stop",{stopColor:"#FA8E7D",offset:"0%"}),u.createElement("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),u.createElement("stop",{stopColor:"#F51D2C",offset:"100%"}))),u.createElement("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd"},u.createElement("g",{transform:"translate(-20.000000, -20.000000)"},u.createElement("g",{transform:"translate(20.000000, 20.000000)"},u.createElement("g",null,u.createElement("g",{fillRule:"nonzero"},u.createElement("g",null,u.createElement("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),u.createElement("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})),u.createElement("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})),u.createElement("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"}))))))};rn.defaultProps=I(I({logo:u.createElement(bl,null)},pr),{},{location:(0,J.Z)()?window.location:void 0});var xl=rn,Ol=v(8292),du=Ol.ZP,Cl=xl,pu=v(59250),Pl=v(13013),vu=v(20228),Ml=v(11382),mu=v(94233),wl=v(51890),El=v(55035),C=v(85893);function zt(t,e,r,n){if(t.rightRender)return t.rightRender(r,n,t);var a=(0,C.jsx)(oe.Z,{className:"umi-plugin-layout-menu",children:(0,C.jsxs)(oe.Z.Item,{onClick:function(){return t.logout&&(t==null?void 0:t.logout(r))},children:[(0,C.jsx)(El.Z,{}),"\u9000\u51FA\u767B\u5F55"]},"logout")}),i=(0,C.jsxs)("span",{className:"umi-plugin-layout-action",children:[(0,C.jsx)(wl.C,{size:"small",className:"umi-plugin-layout-avatar",src:(r==null?void 0:r.avatar)||"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",alt:"avatar"}),(0,C.jsx)("span",{className:"umi-plugin-layout-name",children:r==null?void 0:r.name})]});return e?(0,C.jsx)("div",{className:"umi-plugin-layout-right",children:(0,C.jsx)(Ml.Z,{size:"small",style:{marginLeft:8,marginRight:8}})}):(0,C.jsxs)("div",{className:"umi-plugin-layout-right anticon",children:[t.logout?(0,C.jsx)(Pl.Z,{overlay:a,overlayClassName:"umi-plugin-layout-container",children:i}):i,x.pD&&(0,C.jsx)(x.pD,{})]})}var gu=v(57106),nn=v(6129),hu=v(57663),an=v(71577);function on(){x.m8.push("/")}var Sl=function(){return(0,C.jsx)(nn.ZP,{status:"404",title:"404",subTitle:"\u62B1\u6B49\uFF0C\u4F60\u8BBF\u95EE\u7684\u9875\u9762\u4E0D\u5B58\u5728",extra:(0,C.jsx)(an.Z,{type:"primary",onClick:on,children:"\u8FD4\u56DE\u9996\u9875"})})},jl=function(){return(0,C.jsx)(nn.ZP,{status:"403",title:"403",subTitle:"\u62B1\u6B49\uFF0C\u4F60\u65E0\u6743\u8BBF\u95EE\u8BE5\u9875\u9762",extra:(0,C.jsx)(an.Z,{type:"primary",onClick:on,children:"\u8FD4\u56DE\u9996\u9875"})})},Rl=function(e){var r=e.children,n=e.currentPathConfig;return n?n.unAccessible||n.unaccessible?e.unAccessible||(0,C.jsx)(jl,{}):r:e.noFound||(0,C.jsx)(Sl,{})},Tl=function(){return(0,C.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 200 200",children:[(0,C.jsxs)("defs",{children:[(0,C.jsxs)("linearGradient",{id:"linearGradient-1",x1:"62.102%",x2:"108.197%",y1:"0%",y2:"37.864%",children:[(0,C.jsx)("stop",{offset:"0%",stopColor:"#4285EB"}),(0,C.jsx)("stop",{offset:"100%",stopColor:"#2EC7FF"})]}),(0,C.jsxs)("linearGradient",{id:"linearGradient-2",x1:"69.644%",x2:"54.043%",y1:"0%",y2:"108.457%",children:[(0,C.jsx)("stop",{offset:"0%",stopColor:"#29CDFF"}),(0,C.jsx)("stop",{offset:"37.86%",stopColor:"#148EFF"}),(0,C.jsx)("stop",{offset:"100%",stopColor:"#0A60FF"})]}),(0,C.jsxs)("linearGradient",{id:"linearGradient-3",x1:"69.691%",x2:"16.723%",y1:"-12.974%",y2:"117.391%",children:[(0,C.jsx)("stop",{offset:"0%",stopColor:"#FA816E"}),(0,C.jsx)("stop",{offset:"41.473%",stopColor:"#F74A5C"}),(0,C.jsx)("stop",{offset:"100%",stopColor:"#F51D2C"})]}),(0,C.jsxs)("linearGradient",{id:"linearGradient-4",x1:"68.128%",x2:"30.44%",y1:"-35.691%",y2:"114.943%",children:[(0,C.jsx)("stop",{offset:"0%",stopColor:"#FA8E7D"}),(0,C.jsx)("stop",{offset:"51.264%",stopColor:"#F74A5C"}),(0,C.jsx)("stop",{offset:"100%",stopColor:"#F51D2C"})]})]}),(0,C.jsx)("g",{fill:"none",fillRule:"evenodd",stroke:"none",strokeWidth:"1",children:(0,C.jsx)("g",{transform:"translate(-20 -20)",children:(0,C.jsx)("g",{transform:"translate(20 20)",children:(0,C.jsxs)("g",{children:[(0,C.jsxs)("g",{fillRule:"nonzero",children:[(0,C.jsxs)("g",{children:[(0,C.jsx)("path",{fill:"url(#linearGradient-1)",d:"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c1.17-1.169 2.944-1.169 4.114 0l27.783 27.76c4.209 4.205 11.032 4.205 15.24 0 4.209-4.205 4.209-11.022 0-15.227L108.581 4.056c-4.719-4.594-12.312-4.557-16.993.12z"}),(0,C.jsx)("path",{fill:"url(#linearGradient-2)",d:"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c2.912-2.51 7.664-7.596 14.642-8.786 5.186-.883 10.855 1.062 17.009 5.837L108.58 4.056c-4.719-4.594-12.312-4.557-16.993.12z"})]}),(0,C.jsx)("path",{fill:"url(#linearGradient-3)",d:"M153.686 135.855c4.208 4.205 11.031 4.205 15.24 0l27.034-27.012c4.7-4.696 4.7-12.28 0-16.974l-27.27-27.15c-4.218-4.2-11.043-4.195-15.254.013-4.209 4.205-4.209 11.022 0 15.227l18.418 18.403c1.17 1.169 1.17 2.943 0 4.111l-18.168 18.154c-4.209 4.205-4.209 11.023 0 15.228z"})]}),(0,C.jsx)("ellipse",{cx:"100.519",cy:"100.437",fill:"url(#linearGradient-4)",rx:"23.6",ry:"23.581"})]})})})})]})},Al=Tl,Nl=function(e){var r,n,a,i={};return e!=null&&e.hideFooter&&(i.footerRender=!1),(e==null?void 0:e.layout)==!1?(i.pure=!0,i):(e!=null&&(r=e.layout)!==null&&r!==void 0&&r.hideMenu&&(i.menuRender=!1),e!=null&&(n=e.layout)!==null&&n!==void 0&&n.hideFooter&&(i.footerRender=!1),e!=null&&(a=e.layout)!==null&&a!==void 0&&a.hideNav&&(i.headerRender=!1),i)},Dl=Nl,Il=function(e){var r,n=e.children,a=e.userConfig,i=a===void 0?{}:a,o=e.location,l=e.route,s=(0,ye.Z)(e,["children","userConfig","location","route"]),f=x.tT&&(0,x.tT)("@@initialState")||{initialState:void 0,loading:!1,setInitialState:null},c=f.initialState,d=f.loading,p=f.setInitialState,g=(0,u.useMemo)(function(){var m,y=ar((e==null||(m=e.route)===null||m===void 0?void 0:m.routes)||[],void 0,void 0,!0),h=y.menuData,S=lr(o.pathname,h).pop();return S||{}},[o==null?void 0:o.pathname,e==null||(r=e.route)===null||r===void 0?void 0:r.routes]),b=(0,w.Z)((0,w.Z)((0,w.Z)({itemRender:function(y){return(0,C.jsx)(Te.rU,{to:y.path,children:y.breadcrumbName})}},i),s),Dl(g||{})),P=x.md===null||x.md===void 0?void 0:(0,x.md)();return(0,C.jsx)(Cl,(0,w.Z)((0,w.Z)({route:l,location:o,title:(i==null?void 0:i.name)||(i==null?void 0:i.title),navTheme:"dark",siderWidth:256,onMenuHeaderClick:function(y){y.stopPropagation(),y.preventDefault(),x.m8.push("/")},menu:{locale:i.locale},menuDataRender:i.patchMenus?function(m){return i==null?void 0:i.patchMenus(m,f)}:void 0,formatMessage:i==null?void 0:i.formatMessage,logo:Al,menuItemRender:function(y,h){return y.isUrl?h:y.path&&o.pathname!==y.path?(0,C.jsx)(Te.rU,{to:y.path,target:y.target,children:h}):h},disableContentMargin:!0,fixSiderbar:!0,fixedHeader:!0,postMenuData:x.Bz?function(m){return x.Bz===null||x.Bz===void 0?void 0:(0,x.Bz)(m,P)}:void 0},b),{},{rightContentRender:(b==null?void 0:b.rightContentRender)!==!1&&function(m){var y=zt==null?void 0:zt(i,d,c,p);return b.rightContentRender?b.rightContentRender(m,y,{userConfig:i,loading:d,initialState:c,setInitialState:p}):y},children:(0,C.jsx)(Rl,{noFound:i==null?void 0:i.noFound,unAccessible:i==null?void 0:i.unAccessible,currentPathConfig:g,children:i.childrenRender?i.childrenRender(n,e):n})}))},Hl=Il,Ll=function(t){var e=(0,u.useState)(null),r=(0,T.Z)(e,2),n=r[0],a=r[1],i=x.tT&&(0,x.tT)("@@initialState")||{initialState:void 0,loading:!1,setInitialState:null},o=x.md===null||x.md===void 0?void 0:(0,x.md)();(0,u.useEffect)(function(){var c=x.BA.applyPlugins({key:"layout",type:V.Ac.modify,initialValue:(0,w.Z)((0,w.Z)({},i),{},{traverseModifyRoutes:function(p){return x.Bz===null||x.Bz===void 0?void 0:(0,x.Bz)(p,o)}})})||{};if(c instanceof Promise){c.then(function(d){a(d)});return}a(c)},[i==null?void 0:i.initialState,o]);var l=(0,w.Z)((0,w.Z)({},{name:"ant-design-pro",theme:"PRO",locale:!1,showBreadcrumb:!0,siderWidth:208,title:"LvaCMS2.0 \u540E\u53F0\u7BA1\u7406",logo:"/logo.png",layout:"mix",contentWidth:"Fluid",splitMenus:!0,navTheme:"light",primaryColor:"#1890ff",fixedHeader:!0,fixSiderbar:!0,pwa:!1,menu:{locale:!1}}),n||{}),s=(0,x.YB)(),f=s.formatMessage;return n?u.createElement(Hl,(0,w.Z)({userConfig:l,formatMessage:f},t)):null}},62991:function(ie){"use strict";var se=typeof BigInt64Array!="undefined";ie.exports=function v(w,T){if(w===T)return!0;if(w&&T&&typeof w=="object"&&typeof T=="object"){if(w.constructor!==T.constructor)return!1;var u,x,V;if(Array.isArray(w)){if(u=w.length,u!=T.length)return!1;for(x=u;x--!=0;)if(!v(w[x],T[x]))return!1;return!0}if(w instanceof Map&&T instanceof Map){if(w.size!==T.size)return!1;for(x of w.entries())if(!T.has(x[0]))return!1;for(x of w.entries())if(!v(x[1],T.get(x[0])))return!1;return!0}if(w instanceof Set&&T instanceof Set){if(w.size!==T.size)return!1;for(x of w.entries())if(!T.has(x[0]))return!1;return!0}if(ArrayBuffer.isView(w)&&ArrayBuffer.isView(T)){if(u=w.length,u!=T.length)return!1;for(x=u;x--!=0;)if(w[x]!==T[x])return!1;return!0}if(w.constructor===RegExp)return w.source===T.source&&w.flags===T.flags;if(w.valueOf!==Object.prototype.valueOf)return w.valueOf()===T.valueOf();if(w.toString!==Object.prototype.toString)return w.toString()===T.toString();if(V=Object.keys(w),u=V.length,u!==Object.keys(T).length)return!1;for(x=u;x--!=0;)if(!Object.prototype.hasOwnProperty.call(T,V[x]))return!1;for(x=u;x--!=0;){var ye=V[x];if(!v(w[ye],T[ye]))return!1}return!0}return w!==w&&T!==T}},81626:function(ie,se){"use strict";se.Z={items_per_page:"\u6761/\u9875",jump_to:"\u8DF3\u81F3",jump_to_confirm:"\u786E\u5B9A",page:"\u9875",prev_page:"\u4E0A\u4E00\u9875",next_page:"\u4E0B\u4E00\u9875",prev_5:"\u5411\u524D 5 \u9875",next_5:"\u5411\u540E 5 \u9875",prev_3:"\u5411\u524D 3 \u9875",next_3:"\u5411\u540E 3 \u9875",page_size:"\u9875\u7801"}}}]);
