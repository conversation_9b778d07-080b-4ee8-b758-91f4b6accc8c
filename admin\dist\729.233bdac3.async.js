(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[729],{25414:function(){},34952:function(Ne,ee,a){"use strict";var G=a(22122),u=a(67294),t=a(92389),v=function(I,de){var B={};for(var V in I)Object.prototype.hasOwnProperty.call(I,V)&&de.indexOf(V)<0&&(B[V]=I[V]);if(I!=null&&typeof Object.getOwnPropertySymbols=="function")for(var X=0,V=Object.getOwnPropertySymbols(I);X<V.length;X++)de.indexOf(V[X])<0&&Object.prototype.propertyIsEnumerable.call(I,V[X])&&(B[V[X]]=I[V[X]]);return B},ue={border:0,background:"transparent",padding:0,lineHeight:"inherit",display:"inline-block"},De=u.forwardRef(function(I,de){var B=function(ve){var j=ve.keyCode;j===t.Z.ENTER&&ve.preventDefault()},V=function(ve){var j=ve.keyCode,xe=I.onClick;j===t.Z.ENTER&&xe&&xe()},X=I.style,Re=I.noStyle,Ie=I.disabled,oe=v(I,["style","noStyle","disabled"]),te={};return Re||(te=(0,G.Z)({},ue)),Ie&&(te.pointerEvents="none"),te=(0,G.Z)((0,G.Z)({},te),X),u.createElement("div",(0,G.Z)({role:"button",tabIndex:0,ref:de},oe,{onKeyDown:B,onKeyUp:V,style:te}))});ee.Z=De},51752:function(Ne,ee,a){"use strict";a.d(ee,{Z:function(){return ra}});var G=a(22122),u=a(96156),t=a(67294),v=a(28481),ue=a(90484),De=a(81253),I=a(28991),de=a(94184),B=a.n(de),V=a(50344),X=a(31131),Re=a(21770),Ie=a(85061),oe=a(75164),te=a(48717);function Pe(e){var i=(0,t.useRef)(),r=(0,t.useRef)(!1);function o(){for(var n=arguments.length,c=new Array(n),l=0;l<n;l++)c[l]=arguments[l];r.current||(oe.Z.cancel(i.current),i.current=(0,oe.Z)(function(){e.apply(void 0,c)}))}return(0,t.useEffect)(function(){return function(){r.current=!0,oe.Z.cancel(i.current)}},[]),o}function ve(e){var i=(0,t.useRef)([]),r=(0,t.useState)({}),o=(0,v.Z)(r,2),n=o[1],c=(0,t.useRef)(typeof e=="function"?e():e),l=Pe(function(){var s=c.current;i.current.forEach(function(p){s=p(s)}),i.current=[],c.current=s,n({})});function d(s){i.current.push(s),l()}return[c.current,d]}var j=a(15105);function xe(e,i){var r,o=e.prefixCls,n=e.id,c=e.active,l=e.tab,d=l.key,s=l.tab,p=l.disabled,S=l.closeIcon,b=e.closable,T=e.renderWrapper,M=e.removeAriaLabel,R=e.editable,y=e.onClick,A=e.onRemove,_=e.onFocus,D=e.style,E="".concat(o,"-tab");t.useEffect(function(){return A},[]);var g=R&&b!==!1&&!p;function H(Z){p||y(Z)}function F(Z){Z.preventDefault(),Z.stopPropagation(),R.onEdit("remove",{key:d,event:Z})}var k=t.createElement("div",{key:d,ref:i,className:B()(E,(r={},(0,u.Z)(r,"".concat(E,"-with-remove"),g),(0,u.Z)(r,"".concat(E,"-active"),c),(0,u.Z)(r,"".concat(E,"-disabled"),p),r)),style:D,onClick:H},t.createElement("div",{role:"tab","aria-selected":c,id:n&&"".concat(n,"-tab-").concat(d),className:"".concat(E,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(d),"aria-disabled":p,tabIndex:p?null:0,onClick:function(f){f.stopPropagation(),H(f)},onKeyDown:function(f){[j.Z.SPACE,j.Z.ENTER].includes(f.which)&&(f.preventDefault(),H(f))},onFocus:_},s),g&&t.createElement("button",{type:"button","aria-label":M||"remove",tabIndex:0,className:"".concat(E,"-remove"),onClick:function(f){f.stopPropagation(),F(f)}},S||R.removeIcon||"\xD7"));return T?T(k):k}var wt=t.forwardRef(xe),Qe={width:0,height:0,left:0,top:0};function kt(e,i,r){return(0,t.useMemo)(function(){for(var o,n=new Map,c=i.get((o=e[0])===null||o===void 0?void 0:o.key)||Qe,l=c.left+c.width,d=0;d<e.length;d+=1){var s=e[d].key,p=i.get(s);if(!p){var S;p=i.get((S=e[d-1])===null||S===void 0?void 0:S.key)||Qe}var b=n.get(s)||(0,I.Z)({},p);b.right=l-b.left-b.width,n.set(s,b)}return n},[e.map(function(o){return o.key}).join("_"),i,r])}var Xe={width:0,height:0,left:0,top:0,right:0};function Ut(e,i,r,o,n){var c=n.tabs,l=n.tabPosition,d=n.rtl,s,p,S;["top","bottom"].includes(l)?(s="width",p=d?"right":"left",S=Math.abs(i.left)):(s="height",p="top",S=-i.top);var b=i[s],T=r[s],M=o[s],R=b;return T+M>b&&(R=b-M),(0,t.useMemo)(function(){if(!c.length)return[0,0];for(var y=c.length,A=y,_=0;_<y;_+=1){var D=e.get(c[_].key)||Xe;if(D[p]+D[s]>S+R){A=_-1;break}}for(var E=0,g=y-1;g>=0;g-=1){var H=e.get(c[g].key)||Xe;if(H[p]<S){E=g+1;break}}return[E,A]},[e,S,R,l,c.map(function(y){return y.key}).join("_"),d])}var Je=a(10985),Kt=a(70271);function Dt(e,i){var r=e.prefixCls,o=e.editable,n=e.locale,c=e.style;return!o||o.showAdd===!1?null:t.createElement("button",{ref:i,type:"button",className:"".concat(r,"-nav-add"),style:c,"aria-label":(n==null?void 0:n.addAriaLabel)||"Add tab",onClick:function(d){o.onEdit("add",{event:d})}},o.addIcon||"+")}var qe=t.forwardRef(Dt);function Wt(e,i){var r=e.prefixCls,o=e.id,n=e.tabs,c=e.locale,l=e.mobile,d=e.moreIcon,s=d===void 0?"More":d,p=e.moreTransitionName,S=e.style,b=e.className,T=e.editable,M=e.tabBarGutter,R=e.rtl,y=e.removeAriaLabel,A=e.onTabClick,_=(0,t.useState)(!1),D=(0,v.Z)(_,2),E=D[0],g=D[1],H=(0,t.useState)(null),F=(0,v.Z)(H,2),k=F[0],Z=F[1],f="".concat(o,"-more-popup"),P="".concat(r,"-dropdown"),O=k!==null?"".concat(f,"-").concat(k):null,m=c==null?void 0:c.dropdownAriaLabel;function x(h,w){h.preventDefault(),h.stopPropagation(),T.onEdit("remove",{key:w,event:h})}var L=t.createElement(Je.ZP,{onClick:function(w){var Y=w.key,$=w.domEvent;A(Y,$),g(!1)},id:f,tabIndex:-1,role:"listbox","aria-activedescendant":O,selectedKeys:[k],"aria-label":m!==void 0?m:"expanded dropdown"},n.map(function(h){var w=T&&h.closable!==!1&&!h.disabled;return t.createElement(Je.sN,{key:h.key,id:"".concat(f,"-").concat(h.key),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(h.key),disabled:h.disabled},t.createElement("span",null,h.tab),w&&t.createElement("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(P,"-menu-item-remove"),onClick:function($){$.stopPropagation(),x($,h.key)}},h.closeIcon||T.removeIcon||"\xD7"))}));function U(h){for(var w=n.filter(function(ne){return!ne.disabled}),Y=w.findIndex(function(ne){return ne.key===k})||0,$=w.length,ie=0;ie<$;ie+=1){Y=(Y+h+$)%$;var ce=w[Y];if(!ce.disabled){Z(ce.key);return}}}function ae(h){var w=h.which;if(!E){[j.Z.DOWN,j.Z.SPACE,j.Z.ENTER].includes(w)&&(g(!0),h.preventDefault());return}switch(w){case j.Z.UP:U(-1),h.preventDefault();break;case j.Z.DOWN:U(1),h.preventDefault();break;case j.Z.ESC:g(!1);break;case j.Z.SPACE:case j.Z.ENTER:k!==null&&A(k,h);break}}(0,t.useEffect)(function(){var h=document.getElementById(O);h&&h.scrollIntoView&&h.scrollIntoView(!1)},[k]),(0,t.useEffect)(function(){E||Z(null)},[E]);var q=(0,u.Z)({},R?"marginRight":"marginLeft",M);n.length||(q.visibility="hidden",q.order=1);var fe=B()((0,u.Z)({},"".concat(P,"-rtl"),R)),Q=l?null:t.createElement(Kt.Z,{prefixCls:P,overlay:L,trigger:["hover"],visible:E,transitionName:p,onVisibleChange:g,overlayClassName:fe,mouseEnterDelay:.1,mouseLeaveDelay:.1},t.createElement("button",{type:"button",className:"".concat(r,"-nav-more"),style:q,tabIndex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":f,id:"".concat(o,"-more"),"aria-expanded":E,onKeyDown:ae},s));return t.createElement("div",{className:B()("".concat(r,"-nav-operations"),b),style:S,ref:i},Q,t.createElement(qe,{prefixCls:r,locale:c,editable:T}))}var Bt=t.memo(t.forwardRef(Wt),function(e,i){return i.tabMoving}),We=(0,t.createContext)(null),_t=.1,et=.01,Ze=20,tt=Math.pow(.995,Ze);function Ht(e,i){var r=(0,t.useState)(),o=(0,v.Z)(r,2),n=o[0],c=o[1],l=(0,t.useState)(0),d=(0,v.Z)(l,2),s=d[0],p=d[1],S=(0,t.useState)(0),b=(0,v.Z)(S,2),T=b[0],M=b[1],R=(0,t.useState)(),y=(0,v.Z)(R,2),A=y[0],_=y[1],D=(0,t.useRef)();function E(f){var P=f.touches[0],O=P.screenX,m=P.screenY;c({x:O,y:m}),window.clearInterval(D.current)}function g(f){if(!!n){f.preventDefault();var P=f.touches[0],O=P.screenX,m=P.screenY;c({x:O,y:m});var x=O-n.x,L=m-n.y;i(x,L);var U=Date.now();p(U),M(U-s),_({x,y:L})}}function H(){if(!!n&&(c(null),_(null),A)){var f=A.x/T,P=A.y/T,O=Math.abs(f),m=Math.abs(P);if(Math.max(O,m)<_t)return;var x=f,L=P;D.current=window.setInterval(function(){if(Math.abs(x)<et&&Math.abs(L)<et){window.clearInterval(D.current);return}x*=tt,L*=tt,i(x*Ze,L*Ze)},Ze)}}var F=(0,t.useRef)();function k(f){var P=f.deltaX,O=f.deltaY,m=0,x=Math.abs(P),L=Math.abs(O);x===L?m=F.current==="x"?P:O:x>L?(m=P,F.current="x"):(m=O,F.current="y"),i(-m,-m)&&f.preventDefault()}var Z=(0,t.useRef)(null);Z.current={onTouchStart:E,onTouchMove:g,onTouchEnd:H,onWheel:k},t.useEffect(function(){function f(x){Z.current.onTouchStart(x)}function P(x){Z.current.onTouchMove(x)}function O(x){Z.current.onTouchEnd(x)}function m(x){Z.current.onWheel(x)}return document.addEventListener("touchmove",P,{passive:!1}),document.addEventListener("touchend",O,{passive:!1}),e.current.addEventListener("touchstart",f,{passive:!1}),e.current.addEventListener("wheel",m),function(){document.removeEventListener("touchmove",P),document.removeEventListener("touchend",O)}},[])}function Ft(){var e=(0,t.useRef)(new Map);function i(o){return e.current.has(o)||e.current.set(o,t.createRef()),e.current.get(o)}function r(o){e.current.delete(o)}return[i,r]}function at(e,i){var r=t.useRef(e),o=t.useState({}),n=(0,v.Z)(o,2),c=n[1];function l(d){var s=typeof d=="function"?d(r.current):d;s!==r.current&&i(s,r.current),r.current=s,c({})}return[r.current,l]}var nt=function(i){var r=i.position,o=i.prefixCls,n=i.extra;if(!n)return null;var c,l={};return n&&(0,ue.Z)(n)==="object"&&!t.isValidElement(n)?l=n:l.right=n,r==="right"&&(c=l.right),r==="left"&&(c=l.left),c?t.createElement("div",{className:"".concat(o,"-extra-content")},c):null};function $t(e,i){var r,o=t.useContext(We),n=o.prefixCls,c=o.tabs,l=e.className,d=e.style,s=e.id,p=e.animated,S=e.activeKey,b=e.rtl,T=e.extra,M=e.editable,R=e.locale,y=e.tabPosition,A=e.tabBarGutter,_=e.children,D=e.onTabClick,E=e.onTabScroll,g=(0,t.useRef)(),H=(0,t.useRef)(),F=(0,t.useRef)(),k=(0,t.useRef)(),Z=Ft(),f=(0,v.Z)(Z,2),P=f[0],O=f[1],m=y==="top"||y==="bottom",x=at(0,function(N,C){m&&E&&E({direction:N>C?"left":"right"})}),L=(0,v.Z)(x,2),U=L[0],ae=L[1],q=at(0,function(N,C){!m&&E&&E({direction:N>C?"top":"bottom"})}),fe=(0,v.Z)(q,2),Q=fe[0],h=fe[1],w=(0,t.useState)(0),Y=(0,v.Z)(w,2),$=Y[0],ie=Y[1],ce=(0,t.useState)(0),ne=(0,v.Z)(ce,2),Ee=ne[0],Me=ne[1],Be=(0,t.useState)(0),Ae=(0,v.Z)(Be,2),he=Ae[0],_e=Ae[1],Oe=(0,t.useState)(0),me=(0,v.Z)(Oe,2),Le=me[0],W=me[1],se=(0,t.useState)(null),we=(0,v.Z)(se,2),J=we[0],oa=we[1],ia=(0,t.useState)(null),lt=(0,v.Z)(ia,2),be=lt[0],ca=lt[1],sa=(0,t.useState)(0),ut=(0,v.Z)(sa,2),la=ut[0],ua=ut[1],da=(0,t.useState)(0),dt=(0,v.Z)(da,2),va=dt[0],fa=dt[1],ma=ve(new Map),vt=(0,v.Z)(ma,2),ba=vt[0],Ea=vt[1],ke=kt(c,ba,$),ft="".concat(n,"-nav-operations-hidden"),pe=0,Se=0;m?b?(pe=0,Se=Math.max(0,$-J)):(pe=Math.min(0,J-$),Se=0):(pe=Math.min(0,be-Ee),Se=0);function He(N){return N<pe?pe:N>Se?Se:N}var mt=(0,t.useRef)(),ha=(0,t.useState)(),bt=(0,v.Z)(ha,2),Ue=bt[0],Et=bt[1];function Fe(){Et(Date.now())}function $e(){window.clearTimeout(mt.current)}Ht(g,function(N,C){function K(z,le){z(function(Te){var ge=He(Te+le);return ge})}if(m){if(J>=$)return!1;K(ae,N)}else{if(be>=Ee)return!1;K(h,C)}return $e(),Fe(),!0}),(0,t.useEffect)(function(){return $e(),Ue&&(mt.current=window.setTimeout(function(){Et(0)},100)),$e},[Ue]);function ht(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:S,C=ke.get(N)||{width:0,height:0,left:0,right:0,top:0};if(m){var K=U;b?C.right<U?K=C.right:C.right+C.width>U+J&&(K=C.right+C.width-J):C.left<-U?K=-C.left:C.left+C.width>-U+J&&(K=-(C.left+C.width-J)),h(0),ae(He(K))}else{var z=Q;C.top<-Q?z=-C.top:C.top+C.height>-Q+be&&(z=-(C.top+C.height-be)),ae(0),h(He(z))}}var pa=Ut(ke,{width:J,height:be,left:U,top:Q},{width:he,height:Le},{width:la,height:va},(0,I.Z)((0,I.Z)({},e),{},{tabs:c})),pt=(0,v.Z)(pa,2),Sa=pt[0],ya=pt[1],Ke={};y==="top"||y==="bottom"?Ke[b?"marginRight":"marginLeft"]=A:Ke.marginTop=A;var St=c.map(function(N,C){var K=N.key;return t.createElement(wt,{id:s,prefixCls:n,key:K,tab:N,style:C===0?void 0:Ke,closable:N.closable,editable:M,active:K===S,renderWrapper:_,removeAriaLabel:R==null?void 0:R.removeAriaLabel,ref:P(K),onClick:function(le){D(K,le)},onRemove:function(){O(K)},onFocus:function(){ht(K),Fe(),!!g.current&&(b||(g.current.scrollLeft=0),g.current.scrollTop=0)}})}),Ge=Pe(function(){var N,C,K,z,le,Te,ge,Ye,ze,Ia=((N=g.current)===null||N===void 0?void 0:N.offsetWidth)||0,Pa=((C=g.current)===null||C===void 0?void 0:C.offsetHeight)||0,Pt=((K=k.current)===null||K===void 0?void 0:K.offsetWidth)||0,xt=((z=k.current)===null||z===void 0?void 0:z.offsetHeight)||0,xa=((le=F.current)===null||le===void 0?void 0:le.offsetWidth)||0,Za=((Te=F.current)===null||Te===void 0?void 0:Te.offsetHeight)||0;oa(Ia),ca(Pa),ua(Pt),fa(xt);var Zt=(((ge=H.current)===null||ge===void 0?void 0:ge.offsetWidth)||0)-Pt,Mt=(((Ye=H.current)===null||Ye===void 0?void 0:Ye.offsetHeight)||0)-xt;ie(Zt),Me(Mt);var At=(ze=F.current)===null||ze===void 0?void 0:ze.className.includes(ft);_e(Zt-(At?0:xa)),W(Mt-(At?0:Za)),Ea(function(){var Ot=new Map;return c.forEach(function(Ma){var Lt=Ma.key,Ce=P(Lt).current;Ce&&Ot.set(Lt,{width:Ce.offsetWidth,height:Ce.offsetHeight,left:Ce.offsetLeft,top:Ce.offsetTop})}),Ot})}),Ta=c.slice(0,Sa),ga=c.slice(ya+1),yt=[].concat((0,Ie.Z)(Ta),(0,Ie.Z)(ga)),Ca=(0,t.useState)(),Tt=(0,v.Z)(Ca,2),Na=Tt[0],Ra=Tt[1],re=ke.get(S),gt=(0,t.useRef)();function Ct(){oe.Z.cancel(gt.current)}(0,t.useEffect)(function(){var N={};return re&&(m?(b?N.right=re.right:N.left=re.left,N.width=re.width):(N.top=re.top,N.height=re.height)),Ct(),gt.current=(0,oe.Z)(function(){Ra(N)}),Ct},[re,m,b]),(0,t.useEffect)(function(){ht()},[S,re,ke,m]),(0,t.useEffect)(function(){Ge()},[b,A,S,c.map(function(N){return N.key}).join("_")]);var Nt=!!yt.length,ye="".concat(n,"-nav-wrap"),Ve,je,Rt,It;return m?b?(je=U>0,Ve=U+J<$):(Ve=U<0,je=-U+J<$):(Rt=Q<0,It=-Q+be<Ee),t.createElement("div",{ref:i,role:"tablist",className:B()("".concat(n,"-nav"),l),style:d,onKeyDown:function(){Fe()}},t.createElement(nt,{position:"left",extra:T,prefixCls:n}),t.createElement(te.Z,{onResize:Ge},t.createElement("div",{className:B()(ye,(r={},(0,u.Z)(r,"".concat(ye,"-ping-left"),Ve),(0,u.Z)(r,"".concat(ye,"-ping-right"),je),(0,u.Z)(r,"".concat(ye,"-ping-top"),Rt),(0,u.Z)(r,"".concat(ye,"-ping-bottom"),It),r)),ref:g},t.createElement(te.Z,{onResize:Ge},t.createElement("div",{ref:H,className:"".concat(n,"-nav-list"),style:{transform:"translate(".concat(U,"px, ").concat(Q,"px)"),transition:Ue?"none":void 0}},St,t.createElement(qe,{ref:k,prefixCls:n,locale:R,editable:M,style:(0,I.Z)((0,I.Z)({},St.length===0?void 0:Ke),{},{visibility:Nt?"hidden":null})}),t.createElement("div",{className:B()("".concat(n,"-ink-bar"),(0,u.Z)({},"".concat(n,"-ink-bar-animated"),p.inkBar)),style:Na}))))),t.createElement(Bt,(0,G.Z)({},e,{removeAriaLabel:R==null?void 0:R.removeAriaLabel,ref:F,prefixCls:n,tabs:yt,className:!Nt&&ft,tabMoving:!!Ue})),t.createElement(nt,{position:"right",extra:T,prefixCls:n}))}var rt=t.forwardRef($t);function Gt(e){var i=e.id,r=e.activeKey,o=e.animated,n=e.tabPosition,c=e.rtl,l=e.destroyInactiveTabPane,d=t.useContext(We),s=d.prefixCls,p=d.tabs,S=o.tabPane,b=p.findIndex(function(T){return T.key===r});return t.createElement("div",{className:B()("".concat(s,"-content-holder"))},t.createElement("div",{className:B()("".concat(s,"-content"),"".concat(s,"-content-").concat(n),(0,u.Z)({},"".concat(s,"-content-animated"),S)),style:b&&S?(0,u.Z)({},c?"marginRight":"marginLeft","-".concat(b,"00%")):null},p.map(function(T){return t.cloneElement(T.node,{key:T.key,prefixCls:s,tabKey:T.key,id:i,animated:S,active:T.key===r,destroyInactiveTabPane:l})})))}function ot(e){var i=e.prefixCls,r=e.forceRender,o=e.className,n=e.style,c=e.id,l=e.active,d=e.animated,s=e.destroyInactiveTabPane,p=e.tabKey,S=e.children,b=t.useState(r),T=(0,v.Z)(b,2),M=T[0],R=T[1];t.useEffect(function(){l?R(!0):s&&R(!1)},[l,s]);var y={};return l||(d?(y.visibility="hidden",y.height=0,y.overflowY="hidden"):y.display="none"),t.createElement("div",{id:c&&"".concat(c,"-panel-").concat(p),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":c&&"".concat(c,"-tab-").concat(p),"aria-hidden":!l,style:(0,I.Z)((0,I.Z)({},y),n),className:B()("".concat(i,"-tabpane"),l&&"".concat(i,"-tabpane-active"),o)},(l||M||r)&&S)}var Vt=["id","prefixCls","className","children","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","moreIcon","moreTransitionName","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll"],it=0;function jt(e){return(0,V.Z)(e).map(function(i){if(t.isValidElement(i)){var r=i.key!==void 0?String(i.key):void 0;return(0,I.Z)((0,I.Z)({key:r},i.props),{},{node:i})}return null}).filter(function(i){return i})}function Yt(e,i){var r,o=e.id,n=e.prefixCls,c=n===void 0?"rc-tabs":n,l=e.className,d=e.children,s=e.direction,p=e.activeKey,S=e.defaultActiveKey,b=e.editable,T=e.animated,M=T===void 0?{inkBar:!0,tabPane:!1}:T,R=e.tabPosition,y=R===void 0?"top":R,A=e.tabBarGutter,_=e.tabBarStyle,D=e.tabBarExtraContent,E=e.locale,g=e.moreIcon,H=e.moreTransitionName,F=e.destroyInactiveTabPane,k=e.renderTabBar,Z=e.onChange,f=e.onTabClick,P=e.onTabScroll,O=(0,De.Z)(e,Vt),m=jt(d),x=s==="rtl",L;M===!1?L={inkBar:!1,tabPane:!1}:M===!0?L={inkBar:!0,tabPane:!0}:L=(0,I.Z)({inkBar:!0,tabPane:!1},(0,ue.Z)(M)==="object"?M:{});var U=(0,t.useState)(!1),ae=(0,v.Z)(U,2),q=ae[0],fe=ae[1];(0,t.useEffect)(function(){fe((0,X.Z)())},[]);var Q=(0,Re.Z)(function(){var W;return(W=m[0])===null||W===void 0?void 0:W.key},{value:p,defaultValue:S}),h=(0,v.Z)(Q,2),w=h[0],Y=h[1],$=(0,t.useState)(function(){return m.findIndex(function(W){return W.key===w})}),ie=(0,v.Z)($,2),ce=ie[0],ne=ie[1];(0,t.useEffect)(function(){var W=m.findIndex(function(we){return we.key===w});if(W===-1){var se;W=Math.max(0,Math.min(ce,m.length-1)),Y((se=m[W])===null||se===void 0?void 0:se.key)}ne(W)},[m.map(function(W){return W.key}).join("_"),w,ce]);var Ee=(0,Re.Z)(null,{value:o}),Me=(0,v.Z)(Ee,2),Be=Me[0],Ae=Me[1],he=y;q&&!["left","right"].includes(y)&&(he="top"),(0,t.useEffect)(function(){o||(Ae("rc-tabs-".concat(it)),it+=1)},[]);function _e(W,se){f==null||f(W,se),Y(W),Z==null||Z(W)}var Oe={id:Be,activeKey:w,animated:L,tabPosition:he,rtl:x,mobile:q},me,Le=(0,I.Z)((0,I.Z)({},Oe),{},{editable:b,locale:E,moreIcon:g,moreTransitionName:H,tabBarGutter:A,onTabClick:_e,onTabScroll:P,extra:D,style:_,panes:d});return k?me=k(Le,rt):me=t.createElement(rt,Le),t.createElement(We.Provider,{value:{tabs:m,prefixCls:c}},t.createElement("div",(0,G.Z)({ref:i,id:o,className:B()(c,"".concat(c,"-").concat(he),(r={},(0,u.Z)(r,"".concat(c,"-mobile"),q),(0,u.Z)(r,"".concat(c,"-editable"),b),(0,u.Z)(r,"".concat(c,"-rtl"),x),r),l)},O),me,t.createElement(Gt,(0,G.Z)({destroyInactiveTabPane:F},Oe,{animated:L}))))}var ct=t.forwardRef(Yt);ct.TabPane=ot;var zt=ct,Qt=zt,Xt=a(44545),Jt=a(49101),qt=a(54549),ea=a(21687),ta=a(65632),aa=a(97647),na=function(e,i){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&i.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)i.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};function st(e){var i=e.type,r=e.className,o=e.size,n=e.onEdit,c=e.hideAdd,l=e.centered,d=e.addIcon,s=na(e,["type","className","size","onEdit","hideAdd","centered","addIcon"]),p=s.prefixCls,S=s.moreIcon,b=S===void 0?t.createElement(Xt.Z,null):S,T=t.useContext(ta.E_),M=T.getPrefixCls,R=T.direction,y=M("tabs",p),A;i==="editable-card"&&(A={onEdit:function(E,g){var H=g.key,F=g.event;n==null||n(E==="add"?F:H,E)},removeIcon:t.createElement(qt.Z,null),addIcon:d||t.createElement(Jt.Z,null),showAdd:c!==!0});var _=M();return(0,ea.Z)(!("onPrevClick"in s)&&!("onNextClick"in s),"Tabs","`onPrevClick` and `onNextClick` has been removed. Please use `onTabScroll` instead."),t.createElement(aa.Z.Consumer,null,function(D){var E,g=o!==void 0?o:D;return t.createElement(Qt,(0,G.Z)({direction:R,moreTransitionName:"".concat(_,"-slide-up")},s,{className:B()((E={},(0,u.Z)(E,"".concat(y,"-").concat(g),g),(0,u.Z)(E,"".concat(y,"-card"),["card","editable-card"].includes(i)),(0,u.Z)(E,"".concat(y,"-editable-card"),i==="editable-card"),(0,u.Z)(E,"".concat(y,"-centered"),l),E),r),editable:A,moreIcon:b,prefixCls:y}))})}st.TabPane=ot;var ra=st},18106:function(Ne,ee,a){"use strict";var G=a(65056),u=a.n(G),t=a(25414),v=a.n(t)},92389:function(Ne,ee){"use strict";var a={MAC_ENTER:3,BACKSPACE:8,TAB:9,NUM_CENTER:12,ENTER:13,SHIFT:16,CTRL:17,ALT:18,PAUSE:19,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,PRINT_SCREEN:44,INSERT:45,DELETE:46,ZERO:48,ONE:49,TWO:50,THREE:51,FOUR:52,FIVE:53,SIX:54,SEVEN:55,EIGHT:56,NINE:57,QUESTION_MARK:63,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,NUM_ZERO:96,NUM_ONE:97,NUM_TWO:98,NUM_THREE:99,NUM_FOUR:100,NUM_FIVE:101,NUM_SIX:102,NUM_SEVEN:103,NUM_EIGHT:104,NUM_NINE:105,NUM_MULTIPLY:106,NUM_PLUS:107,NUM_MINUS:109,NUM_PERIOD:110,NUM_DIVISION:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,NUMLOCK:144,SEMICOLON:186,DASH:189,EQUALS:187,COMMA:188,PERIOD:190,SLASH:191,APOSTROPHE:192,SINGLE_QUOTE:222,OPEN_SQUARE_BRACKET:219,BACKSLASH:220,CLOSE_SQUARE_BRACKET:221,WIN_KEY:224,MAC_FF_META:224,WIN_IME:229,isTextModifyingKeyEvent:function(u){var t=u.keyCode;if(u.altKey&&!u.ctrlKey||u.metaKey||t>=a.F1&&t<=a.F12)return!1;switch(t){case a.ALT:case a.CAPS_LOCK:case a.CONTEXT_MENU:case a.CTRL:case a.DOWN:case a.END:case a.ESC:case a.HOME:case a.INSERT:case a.LEFT:case a.MAC_FF_META:case a.META:case a.NUMLOCK:case a.NUM_CENTER:case a.PAGE_DOWN:case a.PAGE_UP:case a.PAUSE:case a.PRINT_SCREEN:case a.RIGHT:case a.SHIFT:case a.UP:case a.WIN_KEY:case a.WIN_KEY_RIGHT:return!1;default:return!0}},isCharacterKey:function(u){if(u>=a.ZERO&&u<=a.NINE||u>=a.NUM_ZERO&&u<=a.NUM_MULTIPLY||u>=a.A&&u<=a.Z||window.navigator.userAgent.indexOf("WebKit")!==-1&&u===0)return!0;switch(u){case a.SPACE:case a.QUESTION_MARK:case a.NUM_PLUS:case a.NUM_MINUS:case a.NUM_PERIOD:case a.NUM_DIVISION:case a.SEMICOLON:case a.DASH:case a.EQUALS:case a.COMMA:case a.PERIOD:case a.SLASH:case a.APOSTROPHE:case a.SINGLE_QUOTE:case a.OPEN_SQUARE_BRACKET:case a.BACKSLASH:case a.CLOSE_SQUARE_BRACKET:return!0;default:return!1}}};ee.Z=a},97435:function(Ne,ee){"use strict";function a(G,u){for(var t=Object.assign({},G),v=0;v<u.length;v+=1){var ue=u[v];delete t[ue]}return t}ee.Z=a}}]);
