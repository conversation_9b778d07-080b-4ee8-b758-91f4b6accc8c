(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_umijs_preset-dumi_lib_theme_layout_js"],{

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/context.js":
/*!**************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/context.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = _interopRequireDefault(__webpack_require__(/*! react */ "./node_modules/react/index.js"));

  _react = function _react() {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var _default = _react().default.createContext({
  config: {
    mode: 'doc',
    title: '',
    navs: {},
    menus: {},
    locales: [],
    repository: {
      branch: 'master'
    },
    theme: {}
  },
  meta: {
    title: ''
  },
  menu: [],
  nav: [],
  base: '',
  routes: [],
  apis: {},
  demos: {}
});

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/layout.js":
/*!*************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/layout.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "./node_modules/react/index.js"));

var _context = _interopRequireDefault(__webpack_require__(/*! ./context */ "./node_modules/@umijs/preset-dumi/lib/theme/context.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

/**
 * hooks for get meta data of current route
 * @param routes    project route configurations
 * @param pathname  pathname of location
 */
var useCurrentRouteMeta = function useCurrentRouteMeta(routes, pathname) {
  var handler = function handler() {
    var _args$0$find;

    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }

    var pathWithoutSuffix = args[1].replace(/([^^])\/$/, '$1');
    return _objectSpread(_objectSpread({}, ((_args$0$find = args[0].find(function (_ref) {
      var path = _ref.path;
      return path === pathWithoutSuffix;
    })) === null || _args$0$find === void 0 ? void 0 : _args$0$find.meta) || {}), {}, {
      __pathname: pathname
    });
  };

  var _useState = (0, _react.useState)(handler(routes, pathname)),
      _useState2 = _slicedToArray(_useState, 2),
      meta = _useState2[0],
      setMeta = _useState2[1];

  (0, _react.useLayoutEffect)(function () {
    setMeta(handler(routes, pathname));
  }, [pathname]);
  return meta;
};
/**
 * hooks for get locale from current route
 * @param locales   project locale configurations
 * @param pathname  pathname of location
 */


var useCurrentLocale = function useCurrentLocale(locales, pathname) {
  var handler = function handler() {
    var _args$0$find2;

    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }

    // get locale by route prefix
    return ((_args$0$find2 = args[0].find(function (locale) {
      return new RegExp("/".concat(locale.name, "(/|$)")).test(args[1]);
    })) === null || _args$0$find2 === void 0 ? void 0 : _args$0$find2.name) || locales[0].name;
  };

  var _useState3 = (0, _react.useState)(handler(locales, pathname)),
      _useState4 = _slicedToArray(_useState3, 2),
      locale = _useState4[0],
      setLocale = _useState4[1];

  (0, _react.useLayoutEffect)(function () {
    setLocale(handler(locales, pathname));
  }, [pathname]);
  return locale;
};
/**
 * hooks for get menu data of current route
 * @param ctxConfig context config
 * @param locale    locale from current route
 * @param pathname  pathname of location
 */


var useCurrentMenu = function useCurrentMenu(ctxConfig, locale, pathname) {
  var handler = function handler() {
    var _args$0$menus$args$;

    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
      args[_key3] = arguments[_key3];
    }

    var navs = args[0].navs[args[1]] || [];
    var navPath = '*'; // find nav in reverse way to fallback to the first nav

    for (var i = navs.length - 1; i >= 0; i -= 1) {
      var nav = navs[i];
      var items = [nav].concat(nav.children).filter(Boolean);
      var matched = items.find(function (item) {
        return item.path && new RegExp("^".concat(item.path.replace(/\.html$/, ''), "(/|.|$)")).test(args[2]);
      });

      if (matched) {
        navPath = matched.path;
        break;
      }
    }

    return ((_args$0$menus$args$ = args[0].menus[args[1]]) === null || _args$0$menus$args$ === void 0 ? void 0 : _args$0$menus$args$[navPath]) || [];
  };

  var _useState5 = (0, _react.useState)(handler(ctxConfig, locale, pathname)),
      _useState6 = _slicedToArray(_useState5, 2),
      menu = _useState6[0],
      setMenu = _useState6[1];

  (0, _react.useLayoutEffect)(function () {
    setMenu(handler(ctxConfig, locale, pathname));
  }, [ctxConfig.navs, ctxConfig.menus, locale, pathname]);
  return menu;
};
/**
 * hooks for doc base route path
 * @param locale    current locale
 * @param locales   project locale configurations
 * @param route     layout route configurations
 * @note  handle these points:
 *          1. locale prefix, such as empty or /zh-CN
 *          2. integrate mode route prefix, such as /~docs or /~docs/zh-CN
 */


var useCurrentBase = function useCurrentBase(locale, locales, route) {
  var handler = function handler() {
    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
      args[_key4] = arguments[_key4];
    }

    if (args[0] === args[1][0].name) {
      // use layout route path as base in default locale
      return args[2].path;
    } // join layout route path & locale prefix in other locale


    return "".concat(route.path, "/").concat(locale).replace(/\/\//, '/');
  };

  var _useState7 = (0, _react.useState)(handler(locale, locales, route)),
      _useState8 = _slicedToArray(_useState7, 2),
      base = _useState8[0],
      setBase = _useState8[1];

  (0, _react.useLayoutEffect)(function () {
    setBase(handler(locale, locales, route));
  }, [locale]);
  return base;
};

var findDumiRoot = function findDumiRoot(routes) {
  var _routes$find;

  return (_routes$find = routes.find(function (item) {
    if (item.__dumiRoot) {
      return true;
    }

    if (item.routes) {
      return findDumiRoot(item.routes);
    }

    return false;
  })) === null || _routes$find === void 0 ? void 0 : _routes$find.routes;
};
/**
 * outer theme layout
 */


var OuterLayout = function OuterLayout(props) {
  var location = props.location,
      route = props.route,
      children = props.children,
      config = props.config,
      apis = props.apis,
      demos = props.demos;
  var pathWithoutPrefix = location.pathname.replace( // to avoid stripped the first /
  route.path.replace(/^\/$/, '//'), '');
  var routes = findDumiRoot(props.routes) || [];
  var meta = useCurrentRouteMeta(routes, location.pathname); // use non-prefix for detect current locale, such as /~docs/en-US -> /en-US

  var locale = useCurrentLocale(config.locales, pathWithoutPrefix);
  var menu = useCurrentMenu(config, locale, location.pathname);
  var base = useCurrentBase(locale, config.locales, route);
  return /*#__PURE__*/_react.default.createElement(_context.default.Provider, {
    value: {
      config: config,
      meta: meta.__pathname === location.pathname ? meta : {},
      locale: locale,
      nav: config.navs[locale] || [],
      menu: menu,
      base: base,
      routes: routes,
      apis: apis,
      demos: demos
    }
  }, children);
};

var _default = OuterLayout;
exports.default = _default;

/***/ })

}]);