(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4059],{84059:function(se,J,U){"use strict";U.d(J,{ZP:function(){return W}});var w=U(67294),q=Object.defineProperty,O=Object.getOwnPropertySymbols,Q=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable,H=(u,i,a)=>i in u?q(u,i,{enumerable:!0,configurable:!0,writable:!0,value:a}):u[i]=a,v=(u,i)=>{for(var a in i||(i={}))Q.call(i,a)&&H(u,a,i[a]);if(O)for(var a of O(i))k.call(i,a)&&H(u,a,i[a]);return u},z=(u,i)=>{var a={};for(var l in u)Q.call(u,l)&&i.indexOf(l)<0&&(a[l]=u[l]);if(u!=null&&O)for(var l of O(u))i.indexOf(l)<0&&k.call(u,l)&&(a[l]=u[l]);return a};/**
 * @license QR Code generator library (TypeScript)
 * Copyright (c) Project Nayuki.
 * SPDX-License-Identifier: MIT
 */var A;(u=>{const i=class{constructor(e,n,t,r){if(this.version=e,this.errorCorrectionLevel=n,this.modules=[],this.isFunction=[],e<i.MIN_VERSION||e>i.MAX_VERSION)throw new RangeError("Version value out of range");if(r<-1||r>7)throw new RangeError("Mask value out of range");this.size=e*4+17;let o=[];for(let s=0;s<this.size;s++)o.push(!1);for(let s=0;s<this.size;s++)this.modules.push(o.slice()),this.isFunction.push(o.slice());this.drawFunctionPatterns();const c=this.addEccAndInterleave(t);if(this.drawCodewords(c),r==-1){let s=1e9;for(let m=0;m<8;m++){this.applyMask(m),this.drawFormatBits(m);const d=this.getPenaltyScore();d<s&&(r=m,s=d),this.applyMask(m)}}h(0<=r&&r<=7),this.mask=r,this.applyMask(r),this.drawFormatBits(r),this.isFunction=[]}static encodeText(e,n){const t=u.QrSegment.makeSegments(e);return i.encodeSegments(t,n)}static encodeBinary(e,n){const t=u.QrSegment.makeBytes(e);return i.encodeSegments([t],n)}static encodeSegments(e,n,t=1,r=40,o=-1,c=!0){if(!(i.MIN_VERSION<=t&&t<=r&&r<=i.MAX_VERSION)||o<-1||o>7)throw new RangeError("Invalid value");let s,m;for(s=t;;s++){const E=i.getNumDataCodewords(s,n)*8,M=C.getTotalBits(e,s);if(M<=E){m=M;break}if(s>=r)throw new RangeError("Data too long")}for(const E of[i.Ecc.MEDIUM,i.Ecc.QUARTILE,i.Ecc.HIGH])c&&m<=i.getNumDataCodewords(s,E)*8&&(n=E);let d=[];for(const E of e){l(E.mode.modeBits,4,d),l(E.numChars,E.mode.numCharCountBits(s),d);for(const M of E.getData())d.push(M)}h(d.length==m);const _=i.getNumDataCodewords(s,n)*8;h(d.length<=_),l(0,Math.min(4,_-d.length),d),l(0,(8-d.length%8)%8,d),h(d.length%8==0);for(let E=236;d.length<_;E^=236^17)l(E,8,d);let R=[];for(;R.length*8<d.length;)R.push(0);return d.forEach((E,M)=>R[M>>>3]|=E<<7-(M&7)),new i(s,n,R,o)}getModule(e,n){return 0<=e&&e<this.size&&0<=n&&n<this.size&&this.modules[n][e]}getModules(){return this.modules}drawFunctionPatterns(){for(let t=0;t<this.size;t++)this.setFunctionModule(6,t,t%2==0),this.setFunctionModule(t,6,t%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);const e=this.getAlignmentPatternPositions(),n=e.length;for(let t=0;t<n;t++)for(let r=0;r<n;r++)t==0&&r==0||t==0&&r==n-1||t==n-1&&r==0||this.drawAlignmentPattern(e[t],e[r]);this.drawFormatBits(0),this.drawVersion()}drawFormatBits(e){const n=this.errorCorrectionLevel.formatBits<<3|e;let t=n;for(let o=0;o<10;o++)t=t<<1^(t>>>9)*1335;const r=(n<<10|t)^21522;h(r>>>15==0);for(let o=0;o<=5;o++)this.setFunctionModule(8,o,g(r,o));this.setFunctionModule(8,7,g(r,6)),this.setFunctionModule(8,8,g(r,7)),this.setFunctionModule(7,8,g(r,8));for(let o=9;o<15;o++)this.setFunctionModule(14-o,8,g(r,o));for(let o=0;o<8;o++)this.setFunctionModule(this.size-1-o,8,g(r,o));for(let o=8;o<15;o++)this.setFunctionModule(8,this.size-15+o,g(r,o));this.setFunctionModule(8,this.size-8,!0)}drawVersion(){if(this.version<7)return;let e=this.version;for(let t=0;t<12;t++)e=e<<1^(e>>>11)*7973;const n=this.version<<12|e;h(n>>>18==0);for(let t=0;t<18;t++){const r=g(n,t),o=this.size-11+t%3,c=Math.floor(t/3);this.setFunctionModule(o,c,r),this.setFunctionModule(c,o,r)}}drawFinderPattern(e,n){for(let t=-4;t<=4;t++)for(let r=-4;r<=4;r++){const o=Math.max(Math.abs(r),Math.abs(t)),c=e+r,s=n+t;0<=c&&c<this.size&&0<=s&&s<this.size&&this.setFunctionModule(c,s,o!=2&&o!=4)}}drawAlignmentPattern(e,n){for(let t=-2;t<=2;t++)for(let r=-2;r<=2;r++)this.setFunctionModule(e+r,n+t,Math.max(Math.abs(r),Math.abs(t))!=1)}setFunctionModule(e,n,t){this.modules[n][e]=t,this.isFunction[n][e]=!0}addEccAndInterleave(e){const n=this.version,t=this.errorCorrectionLevel;if(e.length!=i.getNumDataCodewords(n,t))throw new RangeError("Invalid argument");const r=i.NUM_ERROR_CORRECTION_BLOCKS[t.ordinal][n],o=i.ECC_CODEWORDS_PER_BLOCK[t.ordinal][n],c=Math.floor(i.getNumRawDataModules(n)/8),s=r-c%r,m=Math.floor(c/r);let d=[];const _=i.reedSolomonComputeDivisor(o);for(let E=0,M=0;E<r;E++){let P=e.slice(M,M+m-o+(E<s?0:1));M+=P.length;const F=i.reedSolomonComputeRemainder(P,_);E<s&&P.push(0),d.push(P.concat(F))}let R=[];for(let E=0;E<d[0].length;E++)d.forEach((M,P)=>{(E!=m-o||P>=s)&&R.push(M[E])});return h(R.length==c),R}drawCodewords(e){if(e.length!=Math.floor(i.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");let n=0;for(let t=this.size-1;t>=1;t-=2){t==6&&(t=5);for(let r=0;r<this.size;r++)for(let o=0;o<2;o++){const c=t-o,m=(t+1&2)==0?this.size-1-r:r;!this.isFunction[m][c]&&n<e.length*8&&(this.modules[m][c]=g(e[n>>>3],7-(n&7)),n++)}}h(n==e.length*8)}applyMask(e){if(e<0||e>7)throw new RangeError("Mask value out of range");for(let n=0;n<this.size;n++)for(let t=0;t<this.size;t++){let r;switch(e){case 0:r=(t+n)%2==0;break;case 1:r=n%2==0;break;case 2:r=t%3==0;break;case 3:r=(t+n)%3==0;break;case 4:r=(Math.floor(t/3)+Math.floor(n/2))%2==0;break;case 5:r=t*n%2+t*n%3==0;break;case 6:r=(t*n%2+t*n%3)%2==0;break;case 7:r=((t+n)%2+t*n%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[n][t]&&r&&(this.modules[n][t]=!this.modules[n][t])}}getPenaltyScore(){let e=0;for(let o=0;o<this.size;o++){let c=!1,s=0,m=[0,0,0,0,0,0,0];for(let d=0;d<this.size;d++)this.modules[o][d]==c?(s++,s==5?e+=i.PENALTY_N1:s>5&&e++):(this.finderPenaltyAddHistory(s,m),c||(e+=this.finderPenaltyCountPatterns(m)*i.PENALTY_N3),c=this.modules[o][d],s=1);e+=this.finderPenaltyTerminateAndCount(c,s,m)*i.PENALTY_N3}for(let o=0;o<this.size;o++){let c=!1,s=0,m=[0,0,0,0,0,0,0];for(let d=0;d<this.size;d++)this.modules[d][o]==c?(s++,s==5?e+=i.PENALTY_N1:s>5&&e++):(this.finderPenaltyAddHistory(s,m),c||(e+=this.finderPenaltyCountPatterns(m)*i.PENALTY_N3),c=this.modules[d][o],s=1);e+=this.finderPenaltyTerminateAndCount(c,s,m)*i.PENALTY_N3}for(let o=0;o<this.size-1;o++)for(let c=0;c<this.size-1;c++){const s=this.modules[o][c];s==this.modules[o][c+1]&&s==this.modules[o+1][c]&&s==this.modules[o+1][c+1]&&(e+=i.PENALTY_N2)}let n=0;for(const o of this.modules)n=o.reduce((c,s)=>c+(s?1:0),n);const t=this.size*this.size,r=Math.ceil(Math.abs(n*20-t*10)/t)-1;return h(0<=r&&r<=9),e+=r*i.PENALTY_N4,h(0<=e&&e<=2568888),e}getAlignmentPatternPositions(){if(this.version==1)return[];{const e=Math.floor(this.version/7)+2,n=this.version==32?26:Math.ceil((this.version*4+4)/(e*2-2))*2;let t=[6];for(let r=this.size-7;t.length<e;r-=n)t.splice(1,0,r);return t}}static getNumRawDataModules(e){if(e<i.MIN_VERSION||e>i.MAX_VERSION)throw new RangeError("Version number out of range");let n=(16*e+128)*e+64;if(e>=2){const t=Math.floor(e/7)+2;n-=(25*t-10)*t-55,e>=7&&(n-=36)}return h(208<=n&&n<=29648),n}static getNumDataCodewords(e,n){return Math.floor(i.getNumRawDataModules(e)/8)-i.ECC_CODEWORDS_PER_BLOCK[n.ordinal][e]*i.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][e]}static reedSolomonComputeDivisor(e){if(e<1||e>255)throw new RangeError("Degree out of range");let n=[];for(let r=0;r<e-1;r++)n.push(0);n.push(1);let t=1;for(let r=0;r<e;r++){for(let o=0;o<n.length;o++)n[o]=i.reedSolomonMultiply(n[o],t),o+1<n.length&&(n[o]^=n[o+1]);t=i.reedSolomonMultiply(t,2)}return n}static reedSolomonComputeRemainder(e,n){let t=n.map(r=>0);for(const r of e){const o=r^t.shift();t.push(0),n.forEach((c,s)=>t[s]^=i.reedSolomonMultiply(c,o))}return t}static reedSolomonMultiply(e,n){if(e>>>8!=0||n>>>8!=0)throw new RangeError("Byte out of range");let t=0;for(let r=7;r>=0;r--)t=t<<1^(t>>>7)*285,t^=(n>>>r&1)*e;return h(t>>>8==0),t}finderPenaltyCountPatterns(e){const n=e[1];h(n<=this.size*3);const t=n>0&&e[2]==n&&e[3]==n*3&&e[4]==n&&e[5]==n;return(t&&e[0]>=n*4&&e[6]>=n?1:0)+(t&&e[6]>=n*4&&e[0]>=n?1:0)}finderPenaltyTerminateAndCount(e,n,t){return e&&(this.finderPenaltyAddHistory(n,t),n=0),n+=this.size,this.finderPenaltyAddHistory(n,t),this.finderPenaltyCountPatterns(t)}finderPenaltyAddHistory(e,n){n[0]==0&&(e+=this.size),n.pop(),n.unshift(e)}};let a=i;a.MIN_VERSION=1,a.MAX_VERSION=40,a.PENALTY_N1=3,a.PENALTY_N2=3,a.PENALTY_N3=40,a.PENALTY_N4=10,a.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],a.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],u.QrCode=a;function l(e,n,t){if(n<0||n>31||e>>>n!=0)throw new RangeError("Value out of range");for(let r=n-1;r>=0;r--)t.push(e>>>r&1)}function g(e,n){return(e>>>n&1)!=0}function h(e){if(!e)throw new Error("Assertion error")}const f=class{constructor(e,n,t){if(this.mode=e,this.numChars=n,this.bitData=t,n<0)throw new RangeError("Invalid argument");this.bitData=t.slice()}static makeBytes(e){let n=[];for(const t of e)l(t,8,n);return new f(f.Mode.BYTE,e.length,n)}static makeNumeric(e){if(!f.isNumeric(e))throw new RangeError("String contains non-numeric characters");let n=[];for(let t=0;t<e.length;){const r=Math.min(e.length-t,3);l(parseInt(e.substr(t,r),10),r*3+1,n),t+=r}return new f(f.Mode.NUMERIC,e.length,n)}static makeAlphanumeric(e){if(!f.isAlphanumeric(e))throw new RangeError("String contains unencodable characters in alphanumeric mode");let n=[],t;for(t=0;t+2<=e.length;t+=2){let r=f.ALPHANUMERIC_CHARSET.indexOf(e.charAt(t))*45;r+=f.ALPHANUMERIC_CHARSET.indexOf(e.charAt(t+1)),l(r,11,n)}return t<e.length&&l(f.ALPHANUMERIC_CHARSET.indexOf(e.charAt(t)),6,n),new f(f.Mode.ALPHANUMERIC,e.length,n)}static makeSegments(e){return e==""?[]:f.isNumeric(e)?[f.makeNumeric(e)]:f.isAlphanumeric(e)?[f.makeAlphanumeric(e)]:[f.makeBytes(f.toUtf8ByteArray(e))]}static makeEci(e){let n=[];if(e<0)throw new RangeError("ECI assignment value out of range");if(e<1<<7)l(e,8,n);else if(e<1<<14)l(2,2,n),l(e,14,n);else if(e<1e6)l(6,3,n),l(e,21,n);else throw new RangeError("ECI assignment value out of range");return new f(f.Mode.ECI,0,n)}static isNumeric(e){return f.NUMERIC_REGEX.test(e)}static isAlphanumeric(e){return f.ALPHANUMERIC_REGEX.test(e)}getData(){return this.bitData.slice()}static getTotalBits(e,n){let t=0;for(const r of e){const o=r.mode.numCharCountBits(n);if(r.numChars>=1<<o)return Infinity;t+=4+o+r.bitData.length}return t}static toUtf8ByteArray(e){e=encodeURI(e);let n=[];for(let t=0;t<e.length;t++)e.charAt(t)!="%"?n.push(e.charCodeAt(t)):(n.push(parseInt(e.substr(t+1,2),16)),t+=2);return n}};let C=f;C.NUMERIC_REGEX=/^[0-9]*$/,C.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,C.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",u.QrSegment=C})(A||(A={})),(u=>{let i;(a=>{const l=class{constructor(h,f){this.ordinal=h,this.formatBits=f}};let g=l;g.LOW=new l(0,1),g.MEDIUM=new l(1,0),g.QUARTILE=new l(2,3),g.HIGH=new l(3,2),a.Ecc=g})(i=u.QrCode||(u.QrCode={}))})(A||(A={})),(u=>{let i;(a=>{const l=class{constructor(h,f){this.modeBits=h,this.numBitsCharCount=f}numCharCountBits(h){return this.numBitsCharCount[Math.floor((h+7)/17)]}};let g=l;g.NUMERIC=new l(1,[10,12,14]),g.ALPHANUMERIC=new l(2,[9,11,13]),g.BYTE=new l(4,[8,16,16]),g.KANJI=new l(8,[8,10,12]),g.ECI=new l(7,[0,0,0]),a.Mode=g})(i=u.QrSegment||(u.QrSegment={}))})(A||(A={}));var S=A;/**
 * @license qrcode.react
 * Copyright (c) Paul O'Shannessy
 * SPDX-License-Identifier: ISC
 */var $={L:S.QrCode.Ecc.LOW,M:S.QrCode.Ecc.MEDIUM,Q:S.QrCode.Ecc.QUARTILE,H:S.QrCode.Ecc.HIGH},B={size:128,level:"L",bgColor:"#FFFFFF",fgColor:"#000000",includeMargin:!1},L=4,ee=.1;function Y(u,i=0){const a=[];return u.forEach(function(l,g){let h=null;l.forEach(function(f,C){if(!f&&h!==null){a.push(`M${h+i} ${g+i}h${C-h}v1H${h+i}z`),h=null;return}if(C===l.length-1){if(!f)return;h===null?a.push(`M${C+i},${g+i} h1v1H${C+i}z`):a.push(`M${h+i},${g+i} h${C+1-h}v1H${h+i}z`);return}f&&h===null&&(h=C)})}),a.join("")}function x(u,i){return u.slice().map((a,l)=>l<i.y||l>=i.y+i.h?a:a.map((g,h)=>h<i.x||h>=i.x+i.w?g:!1))}function G(u,i){const{imageSettings:a,size:l,includeMargin:g}=u;if(a==null)return null;const h=g?L:0,f=i.length+h*2,C=Math.floor(l*ee),e=f/l,n=(a.width||C)*e,t=(a.height||C)*e,r=a.x==null?i.length/2-n/2:a.x*e,o=a.y==null?i.length/2-t/2:a.y*e;let c=null;if(a.excavate){let s=Math.floor(r),m=Math.floor(o),d=Math.ceil(n+r-s),_=Math.ceil(t+o-m);c={x:s,y:m,w:d,h:_}}return{x:r,y:o,h:t,w:n,excavation:c}}var te=function(){try{new Path2D().addPath(new Path2D)}catch(u){return!1}return!0}();function X(u){const i=(0,w.useRef)(null),a=(0,w.useRef)(null);function l(){const{value:_,size:R,level:E,bgColor:M,fgColor:P,includeMargin:F}=u;if(i.current!=null){const T=i.current,p=T.getContext("2d");if(!p)return;let I=S.QrCode.encodeText(_,$[E]).getModules();const y=F?L:0,D=I.length+y*2,N=G(u,I),b=a.current,j=N!=null&&b!==null&&b.complete&&b.naturalHeight!==0&&b.naturalWidth!==0;j&&N.excavation!=null&&(I=x(I,N.excavation));const V=window.devicePixelRatio||1;T.height=T.width=R*V;const Z=R/D*V;p.scale(Z,Z),p.fillStyle=M,p.fillRect(0,0,D,D),p.fillStyle=P,te?p.fill(new Path2D(Y(I,y))):I.forEach(function(ne,re){ne.forEach(function(oe,ie){oe&&p.fillRect(ie+y,re+y,1,1)})}),j&&p.drawImage(b,N.x+y,N.y+y,N.w,N.h)}}(0,w.useEffect)(()=>{l()});const g=u,{value:h,size:f,level:C,bgColor:e,fgColor:n,style:t,includeMargin:r,imageSettings:o}=g,c=z(g,["value","size","level","bgColor","fgColor","style","includeMargin","imageSettings"]),s=v({height:f,width:f},t);let m=null,d=o==null?void 0:o.src;return d!=null&&(m=w.createElement("img",{src:d,key:d,style:{display:"none"},onLoad:()=>{l()},ref:a})),w.createElement(w.Fragment,null,w.createElement("canvas",v({style:s,height:f,width:f,ref:i},c)),m)}X.defaultProps=B;function K(u){const i=u,{value:a,size:l,level:g,bgColor:h,fgColor:f,includeMargin:C,imageSettings:e}=i,n=z(i,["value","size","level","bgColor","fgColor","includeMargin","imageSettings"]);let t=S.QrCode.encodeText(a,$[g]).getModules();const r=C?L:0,o=t.length+r*2,c=G(u,t);let s=null;e!=null&&c!=null&&(c.excavation!=null&&(t=x(t,c.excavation)),s=w.createElement("image",{xlinkHref:e.src,height:c.h,width:c.w,x:c.x+r,y:c.y+r,preserveAspectRatio:"none"}));const m=Y(t,r);return w.createElement("svg",v({shapeRendering:"crispEdges",height:l,width:l,viewBox:`0 0 ${o} ${o}`},n),w.createElement("path",{fill:h,d:`M0,0 h${o}v${o}H0z`}),w.createElement("path",{fill:f,d:m}),s)}K.defaultProps=B;var W=u=>{const i=u,{renderAs:a}=i,l=z(i,["renderAs"]);return a==="svg"?w.createElement(K,v({},l)):w.createElement(X,v({},l))};W.defaultProps=v({renderAs:"canvas"},B)}}]);
