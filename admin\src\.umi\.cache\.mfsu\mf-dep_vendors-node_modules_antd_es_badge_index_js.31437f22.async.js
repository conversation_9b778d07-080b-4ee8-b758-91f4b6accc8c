(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_antd_es_badge_index_js"],{

/***/ "./node_modules/antd/es/badge/Ribbon.js":
/*!**********************************************!*\
  !*** ./node_modules/antd/es/badge/Ribbon.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils */ "./node_modules/antd/es/badge/utils.js");







var Ribbon = function Ribbon(_ref) {
  var _classNames;

  var className = _ref.className,
      customizePrefixCls = _ref.prefixCls,
      style = _ref.style,
      color = _ref.color,
      children = _ref.children,
      text = _ref.text,
      _ref$placement = _ref.placement,
      placement = _ref$placement === void 0 ? 'end' : _ref$placement;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_4__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var prefixCls = getPrefixCls('ribbon', customizePrefixCls);
  var colorInPreset = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.isPresetColor)(color);
  var ribbonCls = classnames__WEBPACK_IMPORTED_MODULE_3___default()(prefixCls, "".concat(prefixCls, "-placement-").concat(placement), (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-rtl"), direction === 'rtl'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-color-").concat(color), colorInPreset), _classNames), className);
  var colorStyle = {};
  var cornerColorStyle = {};

  if (color && !colorInPreset) {
    colorStyle.background = color;
    cornerColorStyle.color = color;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(prefixCls, "-wrapper")
  }, children, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: ribbonCls,
    style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, colorStyle), style)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
    className: "".concat(prefixCls, "-text")
  }, text), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(prefixCls, "-corner"),
    style: cornerColorStyle
  })));
};

/* harmony default export */ __webpack_exports__["default"] = (Ribbon);

/***/ }),

/***/ "./node_modules/antd/es/badge/ScrollNumber.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/badge/ScrollNumber.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _util_reactNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../_util/reactNode */ "./node_modules/antd/es/_util/reactNode.js");
/* harmony import */ var _SingleNumber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SingleNumber */ "./node_modules/antd/es/badge/SingleNumber.js");


var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};







var ScrollNumber = function ScrollNumber(_a) {
  var customizePrefixCls = _a.prefixCls,
      count = _a.count,
      className = _a.className,
      motionClassName = _a.motionClassName,
      style = _a.style,
      title = _a.title,
      show = _a.show,
      _a$component = _a.component,
      component = _a$component === void 0 ? 'sup' : _a$component,
      children = _a.children,
      restProps = __rest(_a, ["prefixCls", "count", "className", "motionClassName", "style", "title", "show", "component", "children"]);

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_3__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls;

  var prefixCls = getPrefixCls('scroll-number', customizePrefixCls); // ============================ Render ============================

  var newProps = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, restProps), {
    'data-show': show,
    style: style,
    className: classnames__WEBPACK_IMPORTED_MODULE_2___default()(prefixCls, className, motionClassName),
    title: title
  }); // Only integer need motion


  var numberNodes = count;

  if (count && Number(count) % 1 === 0) {
    var numberList = String(count).split('');
    numberNodes = numberList.map(function (num, i) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_SingleNumber__WEBPACK_IMPORTED_MODULE_4__.default, {
        prefixCls: prefixCls,
        count: Number(count),
        value: num // eslint-disable-next-line react/no-array-index-key
        ,
        key: numberList.length - i
      });
    });
  } // allow specify the border
  // mock border-color by box-shadow for compatible with old usage:
  // <Badge count={4} style={{ backgroundColor: '#fff', color: '#999', borderColor: '#d9d9d9' }} />


  if (style && style.borderColor) {
    newProps.style = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, style), {
      boxShadow: "0 0 0 1px ".concat(style.borderColor, " inset")
    });
  }

  if (children) {
    return (0,_util_reactNode__WEBPACK_IMPORTED_MODULE_5__.cloneElement)(children, function (oriProps) {
      return {
        className: classnames__WEBPACK_IMPORTED_MODULE_2___default()("".concat(prefixCls, "-custom-component"), oriProps === null || oriProps === void 0 ? void 0 : oriProps.className, motionClassName)
      };
    });
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(component, newProps, numberNodes);
};

/* harmony default export */ __webpack_exports__["default"] = (ScrollNumber);

/***/ }),

/***/ "./node_modules/antd/es/badge/SingleNumber.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/badge/SingleNumber.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ SingleNumber; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);





function UnitNumber(_ref) {
  var prefixCls = _ref.prefixCls,
      value = _ref.value,
      current = _ref.current,
      _ref$offset = _ref.offset,
      offset = _ref$offset === void 0 ? 0 : _ref$offset;
  var style;

  if (offset) {
    style = {
      position: 'absolute',
      top: "".concat(offset, "00%"),
      left: 0
    };
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
    style: style,
    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()("".concat(prefixCls, "-only-unit"), {
      current: current
    })
  }, value);
}

function getOffset(start, end, unit) {
  var index = start;
  var offset = 0;

  while ((index + 10) % 10 !== end) {
    index += unit;
    offset += unit;
  }

  return offset;
}

function SingleNumber(props) {
  var prefixCls = props.prefixCls,
      originCount = props.count,
      originValue = props.value;
  var value = Number(originValue);
  var count = Math.abs(originCount);

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_2__.useState(value),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__.default)(_React$useState, 2),
      prevValue = _React$useState2[0],
      setPrevValue = _React$useState2[1];

  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_2__.useState(count),
      _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__.default)(_React$useState3, 2),
      prevCount = _React$useState4[0],
      setPrevCount = _React$useState4[1]; // ============================= Events =============================


  var onTransitionEnd = function onTransitionEnd() {
    setPrevValue(value);
    setPrevCount(count);
  }; // Fallback if transition event not support


  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {
    var timeout = setTimeout(function () {
      onTransitionEnd();
    }, 1000);
    return function () {
      clearTimeout(timeout);
    };
  }, [value]); // ============================= Render =============================
  // Render unit list

  var unitNodes;
  var offsetStyle;

  if (prevValue === value || Number.isNaN(value) || Number.isNaN(prevValue)) {
    // Nothing to change
    unitNodes = [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(UnitNumber, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
      key: value,
      current: true
    }))];
    offsetStyle = {
      transition: 'none'
    };
  } else {
    unitNodes = []; // Fill basic number units

    var end = value + 10;
    var unitNumberList = [];

    for (var index = value; index <= end; index += 1) {
      unitNumberList.push(index);
    } // Fill with number unit nodes


    var prevIndex = unitNumberList.findIndex(function (n) {
      return n % 10 === prevValue;
    });
    unitNodes = unitNumberList.map(function (n, index) {
      var singleUnit = n % 10;
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(UnitNumber, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
        key: n,
        value: singleUnit,
        offset: index - prevIndex,
        current: index === prevIndex
      }));
    }); // Calculate container offset value

    var unit = prevCount < count ? 1 : -1;
    offsetStyle = {
      transform: "translateY(".concat(-getOffset(prevValue, value, unit), "00%)")
    };
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
    className: "".concat(prefixCls, "-only"),
    style: offsetStyle,
    onTransitionEnd: onTransitionEnd
  }, unitNodes);
}

/***/ }),

/***/ "./node_modules/antd/es/badge/index.js":
/*!*********************************************!*\
  !*** ./node_modules/antd/es/badge/index.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-motion */ "./node_modules/rc-motion/es/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _ScrollNumber__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ScrollNumber */ "./node_modules/antd/es/badge/ScrollNumber.js");
/* harmony import */ var _Ribbon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Ribbon */ "./node_modules/antd/es/badge/Ribbon.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _util_reactNode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../_util/reactNode */ "./node_modules/antd/es/_util/reactNode.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils */ "./node_modules/antd/es/badge/utils.js");




var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};











var Badge = function Badge(_a) {
  var _classNames, _classNames2;

  var customizePrefixCls = _a.prefixCls,
      customizeScrollNumberPrefixCls = _a.scrollNumberPrefixCls,
      children = _a.children,
      status = _a.status,
      text = _a.text,
      color = _a.color,
      _a$count = _a.count,
      count = _a$count === void 0 ? null : _a$count,
      _a$overflowCount = _a.overflowCount,
      overflowCount = _a$overflowCount === void 0 ? 99 : _a$overflowCount,
      _a$dot = _a.dot,
      dot = _a$dot === void 0 ? false : _a$dot,
      _a$size = _a.size,
      size = _a$size === void 0 ? 'default' : _a$size,
      title = _a.title,
      offset = _a.offset,
      style = _a.style,
      className = _a.className,
      _a$showZero = _a.showZero,
      showZero = _a$showZero === void 0 ? false : _a$showZero,
      restProps = __rest(_a, ["prefixCls", "scrollNumberPrefixCls", "children", "status", "text", "color", "count", "overflowCount", "dot", "size", "title", "offset", "style", "className", "showZero"]);

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_3__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_6__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var prefixCls = getPrefixCls('badge', customizePrefixCls); // ================================ Misc ================================

  var numberedDisplayCount = count > overflowCount ? "".concat(overflowCount, "+") : count;
  var hasStatus = status !== null && status !== undefined || color !== null && color !== undefined;
  var isZero = numberedDisplayCount === '0' || numberedDisplayCount === 0;
  var showAsDot = dot && !isZero;
  var mergedCount = showAsDot ? '' : numberedDisplayCount;
  var isHidden = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var isEmpty = mergedCount === null || mergedCount === undefined || mergedCount === '';
    return (isEmpty || isZero && !showZero) && !showAsDot;
  }, [mergedCount, isZero, showZero, showAsDot]); // Count should be cache in case hidden change it

  var countRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(count);

  if (!isHidden) {
    countRef.current = count;
  }

  var livingCount = countRef.current; // We need cache count since remove motion should not change count display

  var displayCountRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(mergedCount);

  if (!isHidden) {
    displayCountRef.current = mergedCount;
  }

  var displayCount = displayCountRef.current; // We will cache the dot status to avoid shaking on leaved motion

  var isDotRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(showAsDot);

  if (!isHidden) {
    isDotRef.current = showAsDot;
  } // =============================== Styles ===============================


  var mergedStyle = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    if (!offset) {
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__.default)({}, style);
    }

    var offsetStyle = {
      marginTop: offset[1]
    };

    if (direction === 'rtl') {
      offsetStyle.left = parseInt(offset[0], 10);
    } else {
      offsetStyle.right = -parseInt(offset[0], 10);
    }

    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__.default)({}, offsetStyle), style);
  }, [direction, offset, style]); // =============================== Render ===============================
  // >>> Title

  var titleNode = title !== null && title !== void 0 ? title : typeof livingCount === 'string' || typeof livingCount === 'number' ? livingCount : undefined; // >>> Status Text

  var statusTextNode = isHidden || !text ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
    className: "".concat(prefixCls, "-status-text")
  }, text); // >>> Display Component

  var displayNode = !livingCount || (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__.default)(livingCount) !== 'object' ? undefined : (0,_util_reactNode__WEBPACK_IMPORTED_MODULE_7__.cloneElement)(livingCount, function (oriProps) {
    return {
      style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__.default)({}, mergedStyle), oriProps.style)
    };
  }); // Shared styles

  var statusCls = classnames__WEBPACK_IMPORTED_MODULE_5___default()((_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-status-dot"), hasStatus), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-status-").concat(status), !!status), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-status-").concat(color), (0,_utils__WEBPACK_IMPORTED_MODULE_8__.isPresetColor)(color)), _classNames));
  var statusStyle = {};

  if (color && !(0,_utils__WEBPACK_IMPORTED_MODULE_8__.isPresetColor)(color)) {
    statusStyle.background = color;
  }

  var badgeClassName = classnames__WEBPACK_IMPORTED_MODULE_5___default()(prefixCls, (_classNames2 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames2, "".concat(prefixCls, "-status"), hasStatus), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames2, "".concat(prefixCls, "-not-a-wrapper"), !children), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames2, "".concat(prefixCls, "-rtl"), direction === 'rtl'), _classNames2), className); // <Badge status="success" />

  if (!children && hasStatus) {
    var statusTextColor = mergedStyle.color;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__.default)({}, restProps, {
      className: badgeClassName,
      style: mergedStyle
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
      className: statusCls,
      style: statusStyle
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
      style: {
        color: statusTextColor
      },
      className: "".concat(prefixCls, "-status-text")
    }, text));
  } // <Badge status="success" count={<Icon type="xxx" />}></Badge>


  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__.default)({}, restProps, {
    className: badgeClassName
  }), children, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(rc_motion__WEBPACK_IMPORTED_MODULE_4__.default, {
    visible: !isHidden,
    motionName: "".concat(prefixCls, "-zoom"),
    motionAppear: false
  }, function (_ref) {
    var _classNames3;

    var motionClassName = _ref.className;
    var scrollNumberPrefixCls = getPrefixCls('scroll-number', customizeScrollNumberPrefixCls);
    var isDot = isDotRef.current;
    var scrollNumberCls = classnames__WEBPACK_IMPORTED_MODULE_5___default()((_classNames3 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames3, "".concat(prefixCls, "-dot"), isDot), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames3, "".concat(prefixCls, "-count"), !isDot), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames3, "".concat(prefixCls, "-count-sm"), size === 'small'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames3, "".concat(prefixCls, "-multiple-words"), !isDot && displayCount && displayCount.toString().length > 1), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames3, "".concat(prefixCls, "-status-").concat(status), !!status), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames3, "".concat(prefixCls, "-status-").concat(color), (0,_utils__WEBPACK_IMPORTED_MODULE_8__.isPresetColor)(color)), _classNames3));

    var scrollNumberStyle = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__.default)({}, mergedStyle);

    if (color && !(0,_utils__WEBPACK_IMPORTED_MODULE_8__.isPresetColor)(color)) {
      scrollNumberStyle = scrollNumberStyle || {};
      scrollNumberStyle.background = color;
    }

    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ScrollNumber__WEBPACK_IMPORTED_MODULE_9__.default, {
      prefixCls: scrollNumberPrefixCls,
      show: !isHidden,
      motionClassName: motionClassName,
      className: scrollNumberCls,
      count: displayCount,
      title: titleNode,
      style: scrollNumberStyle,
      key: "scrollNumber"
    }, displayNode);
  }), statusTextNode);
};

Badge.Ribbon = _Ribbon__WEBPACK_IMPORTED_MODULE_10__.default;
/* harmony default export */ __webpack_exports__["default"] = (Badge);

/***/ }),

/***/ "./node_modules/antd/es/badge/utils.js":
/*!*********************************************!*\
  !*** ./node_modules/antd/es/badge/utils.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "isPresetColor": function() { return /* binding */ isPresetColor; }
/* harmony export */ });
/* harmony import */ var _util_colors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_util/colors */ "./node_modules/antd/es/_util/colors.js");
 // eslint-disable-next-line import/prefer-default-export

function isPresetColor(color) {
  return _util_colors__WEBPACK_IMPORTED_MODULE_0__.PresetColorTypes.indexOf(color) !== -1;
}

/***/ })

}]);