(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_antd_es_tree-select_index_js"],{

/***/ "./node_modules/antd/es/tree-select/index.js":
/*!***************************************************!*\
  !*** ./node_modules/antd/es/tree-select/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "TreeNode": function() { return /* reexport safe */ rc_tree_select__WEBPACK_IMPORTED_MODULE_3__.TreeNode; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_tree_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-tree-select */ "./node_modules/antd/node_modules/rc-tree-select/es/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var rc_util_es_omit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/omit */ "./node_modules/antd/node_modules/rc-util/es/omit.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _util_devWarning__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../_util/devWarning */ "./node_modules/antd/es/_util/devWarning.js");
/* harmony import */ var _select_utils_iconUtil__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../select/utils/iconUtil */ "./node_modules/antd/es/select/utils/iconUtil.js");
/* harmony import */ var _tree_utils_iconUtil__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../tree/utils/iconUtil */ "./node_modules/antd/es/tree/utils/iconUtil.js");
/* harmony import */ var _config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../config-provider/SizeContext */ "./node_modules/antd/es/config-provider/SizeContext.js");
/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../_util/motion */ "./node_modules/antd/es/_util/motion.js");



var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};












var InternalTreeSelect = function InternalTreeSelect(_a, ref) {
  var _classNames2;

  var customizePrefixCls = _a.prefixCls,
      customizeSize = _a.size,
      _a$bordered = _a.bordered,
      bordered = _a$bordered === void 0 ? true : _a$bordered,
      className = _a.className,
      treeCheckable = _a.treeCheckable,
      multiple = _a.multiple,
      _a$listHeight = _a.listHeight,
      listHeight = _a$listHeight === void 0 ? 256 : _a$listHeight,
      _a$listItemHeight = _a.listItemHeight,
      listItemHeight = _a$listItemHeight === void 0 ? 26 : _a$listItemHeight,
      notFoundContent = _a.notFoundContent,
      _switcherIcon = _a.switcherIcon,
      treeLine = _a.treeLine,
      getPopupContainer = _a.getPopupContainer,
      dropdownClassName = _a.dropdownClassName,
      _a$treeIcon = _a.treeIcon,
      treeIcon = _a$treeIcon === void 0 ? false : _a$treeIcon,
      transitionName = _a.transitionName,
      _a$choiceTransitionNa = _a.choiceTransitionName,
      choiceTransitionName = _a$choiceTransitionNa === void 0 ? '' : _a$choiceTransitionNa,
      props = __rest(_a, ["prefixCls", "size", "bordered", "className", "treeCheckable", "multiple", "listHeight", "listItemHeight", "notFoundContent", "switcherIcon", "treeLine", "getPopupContainer", "dropdownClassName", "treeIcon", "transitionName", "choiceTransitionName"]);

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_6__.ConfigContext),
      getContextPopupContainer = _React$useContext.getPopupContainer,
      getPrefixCls = _React$useContext.getPrefixCls,
      renderEmpty = _React$useContext.renderEmpty,
      direction = _React$useContext.direction,
      virtual = _React$useContext.virtual,
      dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth;

  var size = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_7__.default);
  (0,_util_devWarning__WEBPACK_IMPORTED_MODULE_8__.default)(multiple !== false || !treeCheckable, 'TreeSelect', '`multiple` will always be `true` when `treeCheckable` is true');
  var prefixCls = getPrefixCls('select', customizePrefixCls);
  var treePrefixCls = getPrefixCls('select-tree', customizePrefixCls);
  var treeSelectPrefixCls = getPrefixCls('tree-select', customizePrefixCls);
  var mergedDropdownClassName = classnames__WEBPACK_IMPORTED_MODULE_4___default()(dropdownClassName, "".concat(treeSelectPrefixCls, "-dropdown"), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(treeSelectPrefixCls, "-dropdown-rtl"), direction === 'rtl'));
  var isMultiple = !!(treeCheckable || multiple); // ===================== Icons =====================

  var _getIcons = (0,_select_utils_iconUtil__WEBPACK_IMPORTED_MODULE_9__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props), {
    multiple: isMultiple,
    prefixCls: prefixCls
  })),
      suffixIcon = _getIcons.suffixIcon,
      removeIcon = _getIcons.removeIcon,
      clearIcon = _getIcons.clearIcon; // ===================== Empty =====================


  var mergedNotFound;

  if (notFoundContent !== undefined) {
    mergedNotFound = notFoundContent;
  } else {
    mergedNotFound = renderEmpty('Select');
  } // ==================== Render =====================


  var selectProps = (0,rc_util_es_omit__WEBPACK_IMPORTED_MODULE_5__.default)(props, ['suffixIcon', 'itemIcon', 'removeIcon', 'clearIcon', 'switcherIcon']);
  var mergedSize = customizeSize || size;
  var mergedClassName = classnames__WEBPACK_IMPORTED_MODULE_4___default()(!customizePrefixCls && treeSelectPrefixCls, (_classNames2 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames2, "".concat(prefixCls, "-lg"), mergedSize === 'large'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames2, "".concat(prefixCls, "-sm"), mergedSize === 'small'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames2, "".concat(prefixCls, "-rtl"), direction === 'rtl'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames2, "".concat(prefixCls, "-borderless"), !bordered), _classNames2), className);
  var rootPrefixCls = getPrefixCls();
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(rc_tree_select__WEBPACK_IMPORTED_MODULE_3__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    virtual: virtual,
    dropdownMatchSelectWidth: dropdownMatchSelectWidth
  }, selectProps, {
    ref: ref,
    prefixCls: prefixCls,
    className: mergedClassName,
    listHeight: listHeight,
    listItemHeight: listItemHeight,
    treeCheckable: treeCheckable ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
      className: "".concat(prefixCls, "-tree-checkbox-inner")
    }) : treeCheckable,
    inputIcon: suffixIcon,
    multiple: multiple,
    removeIcon: removeIcon,
    clearIcon: clearIcon,
    switcherIcon: function switcherIcon(nodeProps) {
      return (0,_tree_utils_iconUtil__WEBPACK_IMPORTED_MODULE_10__.default)(treePrefixCls, _switcherIcon, treeLine, nodeProps);
    },
    showTreeIcon: treeIcon,
    notFoundContent: mergedNotFound,
    getPopupContainer: getPopupContainer || getContextPopupContainer,
    treeMotion: null,
    dropdownClassName: mergedDropdownClassName,
    choiceTransitionName: (0,_util_motion__WEBPACK_IMPORTED_MODULE_11__.getTransitionName)(rootPrefixCls, '', choiceTransitionName),
    transitionName: (0,_util_motion__WEBPACK_IMPORTED_MODULE_11__.getTransitionName)(rootPrefixCls, 'slide-up', transitionName)
  }));
};

var TreeSelectRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(InternalTreeSelect);
var TreeSelect = TreeSelectRef;
TreeSelect.TreeNode = rc_tree_select__WEBPACK_IMPORTED_MODULE_3__.TreeNode;
TreeSelect.SHOW_ALL = rc_tree_select__WEBPACK_IMPORTED_MODULE_3__.SHOW_ALL;
TreeSelect.SHOW_PARENT = rc_tree_select__WEBPACK_IMPORTED_MODULE_3__.SHOW_PARENT;
TreeSelect.SHOW_CHILD = rc_tree_select__WEBPACK_IMPORTED_MODULE_3__.SHOW_CHILD;

/* harmony default export */ __webpack_exports__["default"] = (TreeSelect);

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/Context.js":
/*!*********************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/Context.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "SelectContext": function() { return /* binding */ SelectContext; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

var SelectContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/OptionList.js":
/*!************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/OptionList.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/KeyCode */ "./node_modules/antd/node_modules/rc-util/es/KeyCode.js");
/* harmony import */ var rc_util_es_hooks_useMemo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/hooks/useMemo */ "./node_modules/antd/node_modules/rc-util/es/hooks/useMemo.js");
/* harmony import */ var rc_tree__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-tree */ "./node_modules/antd/node_modules/rc-tree/es/index.js");
/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Context */ "./node_modules/antd/node_modules/rc-tree-select/es/Context.js");
/* harmony import */ var _hooks_useKeyValueMapping__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./hooks/useKeyValueMapping */ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useKeyValueMapping.js");
/* harmony import */ var _hooks_useKeyValueMap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useKeyValueMap */ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useKeyValueMap.js");










var HIDDEN_STYLE = {
  width: 0,
  height: 0,
  display: 'flex',
  overflow: 'hidden',
  opacity: 0,
  border: 0,
  padding: 0,
  margin: 0
};

var OptionList = function OptionList(props, ref) {
  var prefixCls = props.prefixCls,
      height = props.height,
      itemHeight = props.itemHeight,
      virtual = props.virtual,
      options = props.options,
      flattenOptions = props.flattenOptions,
      multiple = props.multiple,
      searchValue = props.searchValue,
      onSelect = props.onSelect,
      onToggleOpen = props.onToggleOpen,
      open = props.open,
      notFoundContent = props.notFoundContent,
      onMouseEnter = props.onMouseEnter;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_3__.useContext(_Context__WEBPACK_IMPORTED_MODULE_7__.SelectContext),
      checkable = _React$useContext.checkable,
      checkedKeys = _React$useContext.checkedKeys,
      halfCheckedKeys = _React$useContext.halfCheckedKeys,
      treeExpandedKeys = _React$useContext.treeExpandedKeys,
      treeDefaultExpandAll = _React$useContext.treeDefaultExpandAll,
      treeDefaultExpandedKeys = _React$useContext.treeDefaultExpandedKeys,
      onTreeExpand = _React$useContext.onTreeExpand,
      treeIcon = _React$useContext.treeIcon,
      showTreeIcon = _React$useContext.showTreeIcon,
      switcherIcon = _React$useContext.switcherIcon,
      treeLine = _React$useContext.treeLine,
      treeNodeFilterProp = _React$useContext.treeNodeFilterProp,
      loadData = _React$useContext.loadData,
      treeLoadedKeys = _React$useContext.treeLoadedKeys,
      treeMotion = _React$useContext.treeMotion,
      onTreeLoad = _React$useContext.onTreeLoad;

  var treeRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef();
  var memoOptions = (0,rc_util_es_hooks_useMemo__WEBPACK_IMPORTED_MODULE_5__.default)(function () {
    return options;
  }, [open, options], function (prev, next) {
    return next[0] && prev[1] !== next[1];
  });

  var _useKeyValueMap = (0,_hooks_useKeyValueMap__WEBPACK_IMPORTED_MODULE_9__.default)(flattenOptions),
      _useKeyValueMap2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__.default)(_useKeyValueMap, 2),
      cacheKeyMap = _useKeyValueMap2[0],
      cacheValueMap = _useKeyValueMap2[1];

  var _useKeyValueMapping = (0,_hooks_useKeyValueMapping__WEBPACK_IMPORTED_MODULE_8__.default)(cacheKeyMap, cacheValueMap),
      _useKeyValueMapping2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__.default)(_useKeyValueMapping, 2),
      getEntityByKey = _useKeyValueMapping2[0],
      getEntityByValue = _useKeyValueMapping2[1]; // ========================== Values ==========================


  var valueKeys = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function () {
    return checkedKeys.map(function (val) {
      var entity = getEntityByValue(val);
      return entity ? entity.key : null;
    });
  }, [checkedKeys, getEntityByValue]);
  var mergedCheckedKeys = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function () {
    if (!checkable) {
      return null;
    }

    return {
      checked: valueKeys,
      halfChecked: halfCheckedKeys
    };
  }, [valueKeys, halfCheckedKeys, checkable]); // ========================== Scroll ==========================

  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {
    // Single mode should scroll to current key
    if (open && !multiple && valueKeys.length) {
      var _treeRef$current;

      (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 ? void 0 : _treeRef$current.scrollTo({
        key: valueKeys[0]
      });
    }
  }, [open]); // ========================== Search ==========================

  var lowerSearchValue = String(searchValue).toLowerCase();

  var filterTreeNode = function filterTreeNode(treeNode) {
    if (!lowerSearchValue) {
      return false;
    }

    return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);
  }; // =========================== Keys ===========================


  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState(treeDefaultExpandedKeys),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__.default)(_React$useState, 2),
      expandedKeys = _React$useState2[0],
      setExpandedKeys = _React$useState2[1];

  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState(null),
      _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__.default)(_React$useState3, 2),
      searchExpandedKeys = _React$useState4[0],
      setSearchExpandedKeys = _React$useState4[1];

  var mergedExpandedKeys = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function () {
    if (treeExpandedKeys) {
      return (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__.default)(treeExpandedKeys);
    }

    return searchValue ? searchExpandedKeys : expandedKeys;
  }, [expandedKeys, searchExpandedKeys, lowerSearchValue, treeExpandedKeys]);
  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {
    if (searchValue) {
      setSearchExpandedKeys(flattenOptions.map(function (o) {
        return o.key;
      }));
    }
  }, [searchValue]);

  var onInternalExpand = function onInternalExpand(keys) {
    setExpandedKeys(keys);
    setSearchExpandedKeys(keys);

    if (onTreeExpand) {
      onTreeExpand(keys);
    }
  }; // ========================== Events ==========================


  var onListMouseDown = function onListMouseDown(event) {
    event.preventDefault();
  };

  var onInternalSelect = function onInternalSelect(_, _ref) {
    var key = _ref.node.key;
    var entity = getEntityByKey(key, checkable ? 'checkbox' : 'select');

    if (entity !== null) {
      onSelect(entity.data.value, {
        selected: !checkedKeys.includes(entity.data.value)
      });
    }

    if (!multiple) {
      onToggleOpen(false);
    }
  }; // ========================= Keyboard =========================


  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_3__.useState(null),
      _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__.default)(_React$useState5, 2),
      activeKey = _React$useState6[0],
      setActiveKey = _React$useState6[1];

  var activeEntity = getEntityByKey(activeKey);
  react__WEBPACK_IMPORTED_MODULE_3__.useImperativeHandle(ref, function () {
    var _treeRef$current2;

    return {
      scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,
      onKeyDown: function onKeyDown(event) {
        var _treeRef$current3;

        var which = event.which;

        switch (which) {
          // >>> Arrow keys
          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_4__.default.UP:
          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_4__.default.DOWN:
          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_4__.default.LEFT:
          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_4__.default.RIGHT:
            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 ? void 0 : _treeRef$current3.onKeyDown(event);
            break;
          // >>> Select item

          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_4__.default.ENTER:
            {
              var _ref2 = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.data) || {},
                  selectable = _ref2.selectable,
                  value = _ref2.value;

              if (selectable !== false) {
                onInternalSelect(null, {
                  node: {
                    key: activeKey
                  },
                  selected: !checkedKeys.includes(value)
                });
              }

              break;
            }
          // >>> Close

          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_4__.default.ESC:
            {
              onToggleOpen(false);
            }
        }
      },
      onKeyUp: function onKeyUp() {}
    };
  }); // ========================== Render ==========================

  if (memoOptions.length === 0) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      role: "listbox",
      className: "".concat(prefixCls, "-empty"),
      onMouseDown: onListMouseDown
    }, notFoundContent);
  }

  var treeProps = {};

  if (treeLoadedKeys) {
    treeProps.loadedKeys = treeLoadedKeys;
  }

  if (mergedExpandedKeys) {
    treeProps.expandedKeys = mergedExpandedKeys;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    onMouseDown: onListMouseDown,
    onMouseEnter: onMouseEnter
  }, activeEntity && open && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
    style: HIDDEN_STYLE,
    "aria-live": "assertive"
  }, activeEntity.data.value), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(rc_tree__WEBPACK_IMPORTED_MODULE_6__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    ref: treeRef,
    focusable: false,
    prefixCls: "".concat(prefixCls, "-tree"),
    treeData: memoOptions,
    height: height,
    itemHeight: itemHeight,
    virtual: virtual,
    multiple: multiple,
    icon: treeIcon,
    showIcon: showTreeIcon,
    switcherIcon: switcherIcon,
    showLine: treeLine,
    loadData: searchValue ? null : loadData,
    motion: treeMotion // We handle keys by out instead tree self
    ,
    checkable: checkable,
    checkStrictly: true,
    checkedKeys: mergedCheckedKeys,
    selectedKeys: !checkable ? valueKeys : [],
    defaultExpandAll: treeDefaultExpandAll
  }, treeProps, {
    // Proxy event out
    onActiveChange: setActiveKey,
    onSelect: onInternalSelect,
    onCheck: onInternalSelect,
    onExpand: onInternalExpand,
    onLoad: onTreeLoad,
    filterTreeNode: filterTreeNode
  })));
};

var RefOptionList = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(OptionList);
RefOptionList.displayName = 'OptionList';
/* harmony default export */ __webpack_exports__["default"] = (RefOptionList);

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/TreeNode.js":
/*!**********************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/TreeNode.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/** This is a placeholder, not real render in dom */
var TreeNode = function TreeNode() {
  return null;
};

/* harmony default export */ __webpack_exports__["default"] = (TreeNode);

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/TreeSelect.js":
/*!************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/TreeSelect.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ "./node_modules/@babel/runtime/helpers/esm/classCallCheck.js");
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ "./node_modules/@babel/runtime/helpers/esm/createClass.js");
/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ "./node_modules/@babel/runtime/helpers/esm/inherits.js");
/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ "./node_modules/@babel/runtime/helpers/esm/createSuper.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_select_es_generate__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-select/es/generate */ "./node_modules/rc-select/es/generate.js");
/* harmony import */ var rc_select_es_utils_valueUtil__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rc-select/es/utils/valueUtil */ "./node_modules/rc-select/es/utils/valueUtil.js");
/* harmony import */ var rc_tree_es_utils_treeUtil__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rc-tree/es/utils/treeUtil */ "./node_modules/antd/node_modules/rc-tree/es/utils/treeUtil.js");
/* harmony import */ var rc_tree_es_utils_conductUtil__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rc-tree/es/utils/conductUtil */ "./node_modules/antd/node_modules/rc-tree/es/utils/conductUtil.js");
/* harmony import */ var rc_select_es_interface_generator__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! rc-select/es/interface/generator */ "./node_modules/rc-select/es/interface/generator.js");
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ "./node_modules/antd/node_modules/rc-util/es/hooks/useMergedState.js");
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/antd/node_modules/rc-util/es/warning.js");
/* harmony import */ var _OptionList__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./OptionList */ "./node_modules/antd/node_modules/rc-tree-select/es/OptionList.js");
/* harmony import */ var _TreeNode__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./TreeNode */ "./node_modules/antd/node_modules/rc-tree-select/es/TreeNode.js");
/* harmony import */ var _utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./utils/valueUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/valueUtil.js");
/* harmony import */ var _utils_warningPropsUtil__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./utils/warningPropsUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/warningPropsUtil.js");
/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./Context */ "./node_modules/antd/node_modules/rc-tree-select/es/Context.js");
/* harmony import */ var _hooks_useTreeData__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./hooks/useTreeData */ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useTreeData.js");
/* harmony import */ var _hooks_useKeyValueMap__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./hooks/useKeyValueMap */ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useKeyValueMap.js");
/* harmony import */ var _hooks_useKeyValueMapping__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./hooks/useKeyValueMapping */ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useKeyValueMapping.js");
/* harmony import */ var _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./utils/strategyUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/strategyUtil.js");
/* harmony import */ var _utils_legacyUtil__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./utils/legacyUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/legacyUtil.js");
/* harmony import */ var _hooks_useSelectValues__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./hooks/useSelectValues */ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useSelectValues.js");





























var OMIT_PROPS = ['expandedKeys', 'treeData', 'treeCheckable', 'showCheckedStrategy', 'searchPlaceholder', 'treeLine', 'treeIcon', 'showTreeIcon', 'switcherIcon', 'treeNodeFilterProp', 'filterTreeNode', 'dropdownPopupAlign', 'treeDefaultExpandAll', 'treeCheckStrictly', 'treeExpandedKeys', 'treeLoadedKeys', 'treeMotion', 'onTreeExpand', 'onTreeLoad', 'loadData', 'treeDataSimpleMode', 'treeNodeLabelProp', 'treeDefaultExpandedKeys'];
var RefSelect = (0,rc_select_es_generate__WEBPACK_IMPORTED_MODULE_10__.default)({
  prefixCls: 'rc-tree-select',
  components: {
    optionList: _OptionList__WEBPACK_IMPORTED_MODULE_17__.default
  },
  // Not use generate since we will handle ourself
  convertChildrenToData: function convertChildrenToData() {
    return null;
  },
  flattenOptions: _utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.flattenOptions,
  // Handle `optionLabelProp` in TreeSelect component
  getLabeledValue: rc_select_es_utils_valueUtil__WEBPACK_IMPORTED_MODULE_11__.getLabeledValue,
  filterOptions: _utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.filterOptions,
  isValueDisabled: _utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.isValueDisabled,
  findValueOption: _utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.findValueOption,
  omitDOMProps: function omitDOMProps(props) {
    var cloneProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_8__.default)({}, props);

    OMIT_PROPS.forEach(function (prop) {
      delete cloneProps[prop];
    });
    return cloneProps;
  }
});
RefSelect.displayName = 'Select';
var RefTreeSelect = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.forwardRef(function (props, ref) {
  var multiple = props.multiple,
      treeCheckable = props.treeCheckable,
      treeCheckStrictly = props.treeCheckStrictly,
      _props$showCheckedStr = props.showCheckedStrategy,
      showCheckedStrategy = _props$showCheckedStr === void 0 ? 'SHOW_CHILD' : _props$showCheckedStr,
      labelInValue = props.labelInValue,
      loadData = props.loadData,
      treeLoadedKeys = props.treeLoadedKeys,
      _props$treeNodeFilter = props.treeNodeFilterProp,
      treeNodeFilterProp = _props$treeNodeFilter === void 0 ? 'value' : _props$treeNodeFilter,
      treeNodeLabelProp = props.treeNodeLabelProp,
      treeDataSimpleMode = props.treeDataSimpleMode,
      treeData = props.treeData,
      treeExpandedKeys = props.treeExpandedKeys,
      treeDefaultExpandedKeys = props.treeDefaultExpandedKeys,
      treeDefaultExpandAll = props.treeDefaultExpandAll,
      children = props.children,
      treeIcon = props.treeIcon,
      showTreeIcon = props.showTreeIcon,
      switcherIcon = props.switcherIcon,
      treeLine = props.treeLine,
      treeMotion = props.treeMotion,
      filterTreeNode = props.filterTreeNode,
      dropdownPopupAlign = props.dropdownPopupAlign,
      onChange = props.onChange,
      onTreeExpand = props.onTreeExpand,
      onTreeLoad = props.onTreeLoad,
      onDropdownVisibleChange = props.onDropdownVisibleChange,
      onSelect = props.onSelect,
      onDeselect = props.onDeselect;
  var mergedCheckable = treeCheckable || treeCheckStrictly;
  var mergedMultiple = multiple || mergedCheckable;
  var treeConduction = treeCheckable && !treeCheckStrictly;
  var mergedLabelInValue = treeCheckStrictly || labelInValue; // ========================== Ref ==========================

  var selectRef = react__WEBPACK_IMPORTED_MODULE_9__.useRef(null);
  react__WEBPACK_IMPORTED_MODULE_9__.useImperativeHandle(ref, function () {
    return {
      scrollTo: selectRef.current.scrollTo,
      focus: selectRef.current.focus,
      blur: selectRef.current.blur
    };
  }); // ======================= Tree Data =======================
  // Legacy both support `label` or `title` if not set.
  // We have to fallback to function to handle this

  var getTreeNodeTitle = function getTreeNodeTitle(node) {
    if (!treeData) {
      return node.title;
    }

    return node.label || node.title;
  };

  var getTreeNodeLabelProp = function getTreeNodeLabelProp(node) {
    if (treeNodeLabelProp) {
      return node[treeNodeLabelProp];
    }

    return getTreeNodeTitle(node);
  };

  var mergedTreeData = (0,_hooks_useTreeData__WEBPACK_IMPORTED_MODULE_22__.default)(treeData, children, {
    getLabelProp: getTreeNodeTitle,
    simpleMode: treeDataSimpleMode
  });
  var flattedOptions = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(function () {
    return (0,_utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.flattenOptions)(mergedTreeData);
  }, [mergedTreeData]);

  var _useKeyValueMap = (0,_hooks_useKeyValueMap__WEBPACK_IMPORTED_MODULE_23__.default)(flattedOptions),
      _useKeyValueMap2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useKeyValueMap, 2),
      cacheKeyMap = _useKeyValueMap2[0],
      cacheValueMap = _useKeyValueMap2[1];

  var _useKeyValueMapping = (0,_hooks_useKeyValueMapping__WEBPACK_IMPORTED_MODULE_24__.default)(cacheKeyMap, cacheValueMap),
      _useKeyValueMapping2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useKeyValueMapping, 2),
      getEntityByKey = _useKeyValueMapping2[0],
      getEntityByValue = _useKeyValueMapping2[1]; // Only generate keyEntities for check conduction when is `treeCheckable`


  var _useMemo = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(function () {
    if (treeConduction) {
      return (0,rc_tree_es_utils_treeUtil__WEBPACK_IMPORTED_MODULE_12__.convertDataToEntities)(mergedTreeData);
    }

    return {
      keyEntities: null
    };
  }, [mergedTreeData, treeCheckable, treeCheckStrictly]),
      conductKeyEntities = _useMemo.keyEntities; // ========================= Value =========================


  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_15__.default)(props.defaultValue, {
    value: props.value
  }),
      _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useMergedState, 2),
      value = _useMergedState2[0],
      setValue = _useMergedState2[1];
  /** Get `missingRawValues` which not exist in the tree yet */


  var splitRawValues = function splitRawValues(newRawValues) {
    var missingRawValues = [];
    var existRawValues = []; // Keep missing value in the cache

    newRawValues.forEach(function (val) {
      if (getEntityByValue(val)) {
        existRawValues.push(val);
      } else {
        missingRawValues.push(val);
      }
    });
    return {
      missingRawValues: missingRawValues,
      existRawValues: existRawValues
    };
  };

  var _useMemo2 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(function () {
    var valueHalfCheckedKeys = [];
    var newRawValues = [];
    (0,_utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.toArray)(value).forEach(function (item) {
      if (item && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_6__.default)(item) === 'object' && 'value' in item) {
        if (item.halfChecked && treeCheckStrictly) {
          var entity = getEntityByValue(item.value);
          valueHalfCheckedKeys.push(entity ? entity.key : item.value);
        } else {
          newRawValues.push(item.value);
        }
      } else {
        newRawValues.push(item);
      }
    }); // We need do conduction of values

    if (treeConduction) {
      var _splitRawValues = splitRawValues(newRawValues),
          missingRawValues = _splitRawValues.missingRawValues,
          existRawValues = _splitRawValues.existRawValues;

      var keyList = existRawValues.map(function (val) {
        return getEntityByValue(val).key;
      });

      var _conductCheck = (0,rc_tree_es_utils_conductUtil__WEBPACK_IMPORTED_MODULE_13__.conductCheck)(keyList, true, conductKeyEntities),
          checkedKeys = _conductCheck.checkedKeys,
          halfCheckedKeys = _conductCheck.halfCheckedKeys;

      return [[].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__.default)(missingRawValues), (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__.default)(checkedKeys.map(function (key) {
        return getEntityByKey(key).data.value;
      }))), halfCheckedKeys];
    }

    return [newRawValues, valueHalfCheckedKeys];
  }, [value, mergedMultiple, mergedLabelInValue, treeCheckable, treeCheckStrictly]),
      _useMemo3 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useMemo2, 2),
      rawValues = _useMemo3[0],
      rawHalfCheckedKeys = _useMemo3[1];

  var selectValues = (0,_hooks_useSelectValues__WEBPACK_IMPORTED_MODULE_27__.default)(rawValues, {
    treeConduction: treeConduction,
    value: value,
    showCheckedStrategy: showCheckedStrategy,
    conductKeyEntities: conductKeyEntities,
    getEntityByValue: getEntityByValue,
    getEntityByKey: getEntityByKey,
    getLabelProp: getTreeNodeLabelProp
  });

  var triggerChange = function triggerChange(newRawValues, extra, source) {
    setValue(mergedMultiple ? newRawValues : newRawValues[0]);

    if (onChange) {
      var eventValues = newRawValues;

      if (treeConduction && showCheckedStrategy !== 'SHOW_ALL') {
        var keyList = newRawValues.map(function (val) {
          var entity = getEntityByValue(val);
          return entity ? entity.key : val;
        });
        var formattedKeyList = (0,_utils_strategyUtil__WEBPACK_IMPORTED_MODULE_25__.formatStrategyKeys)(keyList, showCheckedStrategy, conductKeyEntities);
        eventValues = formattedKeyList.map(function (key) {
          var entity = getEntityByKey(key);
          return entity ? entity.data.value : key;
        });
      }

      var _ref = extra || {
        triggerValue: undefined,
        selected: undefined
      },
          triggerValue = _ref.triggerValue,
          selected = _ref.selected;

      var returnValues = mergedLabelInValue ? (0,_utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.getRawValueLabeled)(eventValues, value, getEntityByValue, getTreeNodeLabelProp) : eventValues; // We need fill half check back

      if (treeCheckStrictly) {
        var halfValues = rawHalfCheckedKeys.map(function (key) {
          var entity = getEntityByKey(key);
          return entity ? entity.data.value : key;
        }).filter(function (val) {
          return !eventValues.includes(val);
        });
        returnValues = [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__.default)(returnValues), (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__.default)((0,_utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.getRawValueLabeled)(halfValues, value, getEntityByValue, getTreeNodeLabelProp)));
      }

      var additionalInfo = {
        // [Legacy] Always return as array contains label & value
        preValue: selectValues,
        triggerValue: triggerValue
      }; // [Legacy] Fill legacy data if user query.
      // This is expansive that we only fill when user query
      // https://github.com/react-component/tree-select/blob/fe33eb7c27830c9ac70cd1fdb1ebbe7bc679c16a/src/Select.jsx

      var showPosition = true;

      if (treeCheckStrictly || source === 'selection' && !selected) {
        showPosition = false;
      }

      (0,_utils_legacyUtil__WEBPACK_IMPORTED_MODULE_26__.fillAdditionalInfo)(additionalInfo, triggerValue, newRawValues, mergedTreeData, showPosition);

      if (mergedCheckable) {
        additionalInfo.checked = selected;
      } else {
        additionalInfo.selected = selected;
      }

      onChange(mergedMultiple ? returnValues : returnValues[0], mergedLabelInValue ? null : eventValues.map(function (val) {
        var entity = getEntityByValue(val);
        return entity ? getTreeNodeLabelProp(entity.data) : null;
      }), additionalInfo);
    }
  };

  var onInternalSelect = function onInternalSelect(selectValue, option, source) {
    var eventValue = mergedLabelInValue ? selectValue : selectValue;

    if (!mergedMultiple) {
      // Single mode always set value
      triggerChange([selectValue], {
        selected: true,
        triggerValue: selectValue
      }, source);
    } else {
      var newRawValues = (0,_utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.addValue)(rawValues, selectValue); // Add keys if tree conduction

      if (treeConduction) {
        // Should keep missing values
        var _splitRawValues2 = splitRawValues(newRawValues),
            missingRawValues = _splitRawValues2.missingRawValues,
            existRawValues = _splitRawValues2.existRawValues;

        var keyList = existRawValues.map(function (val) {
          return getEntityByValue(val).key;
        });

        var _conductCheck2 = (0,rc_tree_es_utils_conductUtil__WEBPACK_IMPORTED_MODULE_13__.conductCheck)(keyList, true, conductKeyEntities),
            checkedKeys = _conductCheck2.checkedKeys;

        newRawValues = [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__.default)(missingRawValues), (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__.default)(checkedKeys.map(function (key) {
          return getEntityByKey(key).data.value;
        })));
      }

      triggerChange(newRawValues, {
        selected: true,
        triggerValue: selectValue
      }, source);
    }

    if (onSelect) {
      onSelect(eventValue, option);
    }
  };

  var onInternalDeselect = function onInternalDeselect(selectValue, option, source) {
    var eventValue = mergedLabelInValue ? selectValue : selectValue;
    var newRawValues = (0,_utils_valueUtil__WEBPACK_IMPORTED_MODULE_19__.removeValue)(rawValues, selectValue); // Remove keys if tree conduction

    if (treeConduction) {
      var _splitRawValues3 = splitRawValues(newRawValues),
          missingRawValues = _splitRawValues3.missingRawValues,
          existRawValues = _splitRawValues3.existRawValues;

      var keyList = existRawValues.map(function (val) {
        return getEntityByValue(val).key;
      });

      var _conductCheck3 = (0,rc_tree_es_utils_conductUtil__WEBPACK_IMPORTED_MODULE_13__.conductCheck)(keyList, {
        checked: false,
        halfCheckedKeys: rawHalfCheckedKeys
      }, conductKeyEntities),
          checkedKeys = _conductCheck3.checkedKeys;

      newRawValues = [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__.default)(missingRawValues), (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_5__.default)(checkedKeys.map(function (key) {
        return getEntityByKey(key).data.value;
      })));
    }

    triggerChange(newRawValues, {
      selected: false,
      triggerValue: selectValue
    }, source);

    if (onDeselect) {
      onDeselect(eventValue, option);
    }
  };

  var onInternalClear = function onInternalClear() {
    triggerChange([], null, 'clear');
  }; // ========================= Open ==========================


  var onInternalDropdownVisibleChange = react__WEBPACK_IMPORTED_MODULE_9__.useCallback(function (open) {
    if (onDropdownVisibleChange) {
      var legacyParam = {};
      Object.defineProperty(legacyParam, 'documentClickClose', {
        get: function get() {
          (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_16__.default)(false, 'Second param of `onDropdownVisibleChange` has been removed.');
          return false;
        }
      });
      onDropdownVisibleChange(open, legacyParam);
    }
  }, [onDropdownVisibleChange]); // ======================== Warning ========================

  if (true) {
    (0,_utils_warningPropsUtil__WEBPACK_IMPORTED_MODULE_20__.default)(props);
  } // ======================== Render =========================
  // We pass some props into select props style


  var selectProps = {
    optionLabelProp: null,
    optionFilterProp: treeNodeFilterProp,
    dropdownAlign: dropdownPopupAlign,
    internalProps: {
      mark: rc_select_es_interface_generator__WEBPACK_IMPORTED_MODULE_14__.INTERNAL_PROPS_MARK,
      onClear: onInternalClear,
      skipTriggerChange: true,
      skipTriggerSelect: true,
      onRawSelect: onInternalSelect,
      onRawDeselect: onInternalDeselect
    }
  };

  if ('filterTreeNode' in props) {
    selectProps.filterOption = filterTreeNode;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_Context__WEBPACK_IMPORTED_MODULE_21__.SelectContext.Provider, {
    value: {
      checkable: mergedCheckable,
      loadData: loadData,
      treeLoadedKeys: treeLoadedKeys,
      onTreeLoad: onTreeLoad,
      checkedKeys: rawValues,
      halfCheckedKeys: rawHalfCheckedKeys,
      treeDefaultExpandAll: treeDefaultExpandAll,
      treeExpandedKeys: treeExpandedKeys,
      treeDefaultExpandedKeys: treeDefaultExpandedKeys,
      onTreeExpand: onTreeExpand,
      treeIcon: treeIcon,
      treeMotion: treeMotion,
      showTreeIcon: showTreeIcon,
      switcherIcon: switcherIcon,
      treeLine: treeLine,
      treeNodeFilterProp: treeNodeFilterProp
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(RefSelect, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({
    ref: selectRef,
    mode: mergedMultiple ? 'multiple' : null
  }, props, selectProps, {
    value: selectValues // We will handle this ourself since we need calculate conduction
    ,
    labelInValue: true,
    options: mergedTreeData,
    onChange: null,
    onSelect: null,
    onDeselect: null,
    onDropdownVisibleChange: onInternalDropdownVisibleChange
  })));
}); // Use class component since typescript not support generic
// by `forwardRef` with function component yet.

var TreeSelect = /*#__PURE__*/function (_React$Component) {
  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__.default)(TreeSelect, _React$Component);

  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__.default)(TreeSelect);

  function TreeSelect() {
    var _this;

    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__.default)(this, TreeSelect);

    _this = _super.apply(this, arguments);
    _this.selectRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createRef();

    _this.focus = function () {
      _this.selectRef.current.focus();
    };

    _this.blur = function () {
      _this.selectRef.current.blur();
    };

    return _this;
  }

  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__.default)(TreeSelect, [{
    key: "render",
    value: function render() {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(RefTreeSelect, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({
        ref: this.selectRef
      }, this.props));
    }
  }]);

  return TreeSelect;
}(react__WEBPACK_IMPORTED_MODULE_9__.Component);

TreeSelect.TreeNode = _TreeNode__WEBPACK_IMPORTED_MODULE_18__.default;
TreeSelect.SHOW_ALL = _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_25__.SHOW_ALL;
TreeSelect.SHOW_PARENT = _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_25__.SHOW_PARENT;
TreeSelect.SHOW_CHILD = _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_25__.SHOW_CHILD;
/* harmony default export */ __webpack_exports__["default"] = (TreeSelect);

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useKeyValueMap.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/hooks/useKeyValueMap.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useKeyValueMap; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

/**
 * Return cached Key Value map with DataNode.
 * Only re-calculate when `flattenOptions` changed.
 */

function useKeyValueMap(flattenOptions) {
  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {
    var cacheKeyMap = new Map();
    var cacheValueMap = new Map(); // Cache options by key

    flattenOptions.forEach(function (dataNode) {
      cacheKeyMap.set(dataNode.key, dataNode);
      cacheValueMap.set(dataNode.data.value, dataNode);
    });
    return [cacheKeyMap, cacheValueMap];
  }, [flattenOptions]);
}

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useKeyValueMapping.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/hooks/useKeyValueMapping.js ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "isDisabled": function() { return /* binding */ isDisabled; },
/* harmony export */   "default": function() { return /* binding */ useKeyValueMapping; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

function isDisabled(dataNode, skipType) {
  if (!dataNode) {
    return true;
  }

  var _dataNode$data = dataNode.data,
      disabled = _dataNode$data.disabled,
      disableCheckbox = _dataNode$data.disableCheckbox;

  switch (skipType) {
    case 'select':
      return disabled;

    case 'checkbox':
      return disabled || disableCheckbox;
  }

  return false;
}
function useKeyValueMapping(cacheKeyMap, cacheValueMap) {
  var getEntityByKey = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (key) {
    var skipType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'select';
    var ignoreDisabledCheck = arguments.length > 2 ? arguments[2] : undefined;
    var dataNode = cacheKeyMap.get(key);

    if (!ignoreDisabledCheck && isDisabled(dataNode, skipType)) {
      return null;
    }

    return dataNode;
  }, [cacheKeyMap]);
  var getEntityByValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (value) {
    var skipType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'select';
    var ignoreDisabledCheck = arguments.length > 2 ? arguments[2] : undefined;
    var dataNode = cacheValueMap.get(value);

    if (!ignoreDisabledCheck && isDisabled(dataNode, skipType)) {
      return null;
    }

    return dataNode;
  }, [cacheValueMap]);
  return [getEntityByKey, getEntityByValue];
}

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useSelectValues.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/hooks/useSelectValues.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useSelectValues; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_valueUtil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/valueUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/valueUtil.js");
/* harmony import */ var _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/strategyUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/strategyUtil.js");



/** Return  */

function useSelectValues(rawValues, _ref) {
  var value = _ref.value,
      getEntityByValue = _ref.getEntityByValue,
      getEntityByKey = _ref.getEntityByKey,
      treeConduction = _ref.treeConduction,
      showCheckedStrategy = _ref.showCheckedStrategy,
      conductKeyEntities = _ref.conductKeyEntities,
      getLabelProp = _ref.getLabelProp;
  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {
    var mergedRawValues = rawValues;

    if (treeConduction) {
      var rawKeys = (0,_utils_strategyUtil__WEBPACK_IMPORTED_MODULE_2__.formatStrategyKeys)(rawValues.map(function (val) {
        var entity = getEntityByValue(val);
        return entity ? entity.key : val;
      }), showCheckedStrategy, conductKeyEntities);
      mergedRawValues = rawKeys.map(function (key) {
        var entity = getEntityByKey(key);
        return entity ? entity.data.value : key;
      });
    }

    return (0,_utils_valueUtil__WEBPACK_IMPORTED_MODULE_1__.getRawValueLabeled)(mergedRawValues, value, getEntityByValue, getLabelProp);
  }, [rawValues, value, treeConduction, showCheckedStrategy, getEntityByValue]);
}

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/hooks/useTreeData.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/hooks/useTreeData.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useTreeData; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ "./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/antd/node_modules/rc-util/es/warning.js");
/* harmony import */ var _utils_legacyUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/legacyUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/legacyUtil.js");





var MAX_WARNING_TIMES = 10;

function parseSimpleTreeData(treeData, _ref) {
  var id = _ref.id,
      pId = _ref.pId,
      rootPId = _ref.rootPId;
  var keyNodes = {};
  var rootNodeList = []; // Fill in the map

  var nodeList = treeData.map(function (node) {
    var clone = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)({}, node);

    var key = clone[id];
    keyNodes[key] = clone;
    clone.key = clone.key || key;
    return clone;
  }); // Connect tree

  nodeList.forEach(function (node) {
    var parentKey = node[pId];
    var parent = keyNodes[parentKey]; // Fill parent

    if (parent) {
      parent.children = parent.children || [];
      parent.children.push(node);
    } // Fill root tree node


    if (parentKey === rootPId || !parent && rootPId === null) {
      rootNodeList.push(node);
    }
  });
  return rootNodeList;
}
/**
 * Format `treeData` with `value` & `key` which is used for calculation
 */


function formatTreeData(treeData, getLabelProp) {
  var warningTimes = 0;
  var valueSet = new Set();

  function dig(dataNodes) {
    return (dataNodes || []).map(function (node) {
      var key = node.key,
          value = node.value,
          children = node.children,
          rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__.default)(node, ["key", "value", "children"]);

      var mergedValue = 'value' in node ? value : key;

      var dataNode = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)({}, rest), {}, {
        key: key !== null && key !== undefined ? key : mergedValue,
        value: mergedValue,
        title: getLabelProp(node)
      }); // Check `key` & `value` and warning user


      if (true) {
        if (key !== null && key !== undefined && value !== undefined && String(key) !== String(value) && warningTimes < MAX_WARNING_TIMES) {
          warningTimes += 1;
          (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_3__.default)(false, "`key` or `value` with TreeNode must be the same or you can remove one of them. key: ".concat(key, ", value: ").concat(value, "."));
        }

        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_3__.default)(!valueSet.has(value), "Same `value` exist in the tree: ".concat(value));
        valueSet.add(value);
      }

      if ('children' in node) {
        dataNode.children = dig(children);
      }

      return dataNode;
    });
  }

  return dig(treeData);
}
/**
 * Convert `treeData` or `children` into formatted `treeData`.
 * Will not re-calculate if `treeData` or `children` not change.
 */


function useTreeData(treeData, children, _ref2) {
  var getLabelProp = _ref2.getLabelProp,
      simpleMode = _ref2.simpleMode;
  var cacheRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef({});

  if (treeData) {
    cacheRef.current.formatTreeData = cacheRef.current.treeData === treeData ? cacheRef.current.formatTreeData : formatTreeData(simpleMode ? parseSimpleTreeData(treeData, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)({
      id: 'id',
      pId: 'pId',
      rootPId: null
    }, simpleMode !== true ? simpleMode : {})) : treeData, getLabelProp);
    cacheRef.current.treeData = treeData;
  } else {
    cacheRef.current.formatTreeData = cacheRef.current.children === children ? cacheRef.current.formatTreeData : formatTreeData((0,_utils_legacyUtil__WEBPACK_IMPORTED_MODULE_4__.convertChildrenToData)(children), getLabelProp);
  }

  return cacheRef.current.formatTreeData;
}

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/index.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "TreeNode": function() { return /* reexport safe */ _TreeNode__WEBPACK_IMPORTED_MODULE_1__.default; },
/* harmony export */   "SHOW_ALL": function() { return /* reexport safe */ _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_2__.SHOW_ALL; },
/* harmony export */   "SHOW_CHILD": function() { return /* reexport safe */ _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_2__.SHOW_CHILD; },
/* harmony export */   "SHOW_PARENT": function() { return /* reexport safe */ _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_2__.SHOW_PARENT; }
/* harmony export */ });
/* harmony import */ var _TreeSelect__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TreeSelect */ "./node_modules/antd/node_modules/rc-tree-select/es/TreeSelect.js");
/* harmony import */ var _TreeNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TreeNode */ "./node_modules/antd/node_modules/rc-tree-select/es/TreeNode.js");
/* harmony import */ var _utils_strategyUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/strategyUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/strategyUtil.js");




/* harmony default export */ __webpack_exports__["default"] = (_TreeSelect__WEBPACK_IMPORTED_MODULE_0__.default);

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/utils/legacyUtil.js":
/*!******************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/utils/legacyUtil.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "convertChildrenToData": function() { return /* binding */ convertChildrenToData; },
/* harmony export */   "fillLegacyProps": function() { return /* binding */ fillLegacyProps; },
/* harmony export */   "fillAdditionalInfo": function() { return /* binding */ fillAdditionalInfo; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ "./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/Children/toArray */ "./node_modules/antd/node_modules/rc-util/es/Children/toArray.js");
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/antd/node_modules/rc-util/es/warning.js");
/* harmony import */ var _TreeNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../TreeNode */ "./node_modules/antd/node_modules/rc-tree-select/es/TreeNode.js");






function convertChildrenToData(nodes) {
  return (0,rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_3__.default)(nodes).map(function (node) {
    if (! /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.isValidElement(node) || !node.type) {
      return null;
    }

    var key = node.key,
        _node$props = node.props,
        children = _node$props.children,
        value = _node$props.value,
        restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__.default)(_node$props, ["children", "value"]);

    var data = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)({
      key: key,
      value: value
    }, restProps);

    var childData = convertChildrenToData(children);

    if (childData.length) {
      data.children = childData;
    }

    return data;
  }).filter(function (data) {
    return data;
  });
}
function fillLegacyProps(dataNode) {
  // Skip if not dataNode exist
  if (!dataNode) {
    return dataNode;
  }

  var cloneNode = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)({}, dataNode);

  if (!('props' in cloneNode)) {
    Object.defineProperty(cloneNode, 'props', {
      get: function get() {
        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_4__.default)(false, 'New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access.');
        return cloneNode;
      }
    });
  }

  return cloneNode;
}
function fillAdditionalInfo(extra, triggerValue, checkedValues, treeData, showPosition) {
  var triggerNode = null;
  var nodeList = null;

  function generateMap() {
    function dig(list) {
      var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '0';
      var parentIncluded = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      return list.map(function (dataNode, index) {
        var pos = "".concat(level, "-").concat(index);
        var included = checkedValues.includes(dataNode.value);
        var children = dig(dataNode.children || [], pos, included);
        var node = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TreeNode__WEBPACK_IMPORTED_MODULE_5__.default, dataNode, children.map(function (child) {
          return child.node;
        })); // Link with trigger node

        if (triggerValue === dataNode.value) {
          triggerNode = node;
        }

        if (included) {
          var checkedNode = {
            pos: pos,
            node: node,
            children: children
          };

          if (!parentIncluded) {
            nodeList.push(checkedNode);
          }

          return checkedNode;
        }

        return null;
      }).filter(function (node) {
        return node;
      });
    }

    if (!nodeList) {
      nodeList = [];
      dig(treeData); // Sort to keep the checked node length

      nodeList.sort(function (_ref, _ref2) {
        var val1 = _ref.node.props.value;
        var val2 = _ref2.node.props.value;
        var index1 = checkedValues.indexOf(val1);
        var index2 = checkedValues.indexOf(val2);
        return index1 - index2;
      });
    }
  }

  Object.defineProperty(extra, 'triggerNode', {
    get: function get() {
      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_4__.default)(false, '`triggerNode` is deprecated. Please consider decoupling data with node.');
      generateMap();
      return triggerNode;
    }
  });
  Object.defineProperty(extra, 'allCheckedNodes', {
    get: function get() {
      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_4__.default)(false, '`allCheckedNodes` is deprecated. Please consider decoupling data with node.');
      generateMap();

      if (showPosition) {
        return nodeList;
      }

      return nodeList.map(function (_ref3) {
        var node = _ref3.node;
        return node;
      });
    }
  });
}

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/utils/strategyUtil.js":
/*!********************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/utils/strategyUtil.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "SHOW_ALL": function() { return /* binding */ SHOW_ALL; },
/* harmony export */   "SHOW_PARENT": function() { return /* binding */ SHOW_PARENT; },
/* harmony export */   "SHOW_CHILD": function() { return /* binding */ SHOW_CHILD; },
/* harmony export */   "formatStrategyKeys": function() { return /* binding */ formatStrategyKeys; }
/* harmony export */ });
/* harmony import */ var _valueUtil__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./valueUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/valueUtil.js");

var SHOW_ALL = 'SHOW_ALL';
var SHOW_PARENT = 'SHOW_PARENT';
var SHOW_CHILD = 'SHOW_CHILD';
function formatStrategyKeys(keys, strategy, keyEntities) {
  var keySet = new Set(keys);

  if (strategy === SHOW_CHILD) {
    return keys.filter(function (key) {
      var entity = keyEntities[key];

      if (entity && entity.children && entity.children.every(function (_ref) {
        var node = _ref.node;
        return (0,_valueUtil__WEBPACK_IMPORTED_MODULE_0__.isCheckDisabled)(node) || keySet.has(node.key);
      })) {
        return false;
      }

      return true;
    });
  }

  if (strategy === SHOW_PARENT) {
    return keys.filter(function (key) {
      var entity = keyEntities[key];
      var parent = entity ? entity.parent : null;

      if (parent && !(0,_valueUtil__WEBPACK_IMPORTED_MODULE_0__.isCheckDisabled)(parent.node) && keySet.has(parent.node.key)) {
        return false;
      }

      return true;
    });
  }

  return keys;
}

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/utils/valueUtil.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/utils/valueUtil.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "toArray": function() { return /* binding */ toArray; },
/* harmony export */   "findValueOption": function() { return /* binding */ findValueOption; },
/* harmony export */   "isValueDisabled": function() { return /* binding */ isValueDisabled; },
/* harmony export */   "isCheckDisabled": function() { return /* binding */ isCheckDisabled; },
/* harmony export */   "flattenOptions": function() { return /* binding */ flattenOptions; },
/* harmony export */   "filterOptions": function() { return /* binding */ filterOptions; },
/* harmony export */   "getRawValueLabeled": function() { return /* binding */ getRawValueLabeled; },
/* harmony export */   "addValue": function() { return /* binding */ addValue; },
/* harmony export */   "removeValue": function() { return /* binding */ removeValue; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var rc_tree_es_utils_treeUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-tree/es/utils/treeUtil */ "./node_modules/antd/node_modules/rc-tree/es/utils/treeUtil.js");
/* harmony import */ var _legacyUtil__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./legacyUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/legacyUtil.js");




function toArray(value) {
  if (Array.isArray(value)) {
    return value;
  }

  return value !== undefined ? [value] : [];
}
function findValueOption(values, options) {
  var optionMap = new Map();
  options.forEach(function (flattenItem) {
    var data = flattenItem.data;
    optionMap.set(data.value, data);
  });
  return values.map(function (val) {
    return (0,_legacyUtil__WEBPACK_IMPORTED_MODULE_3__.fillLegacyProps)(optionMap.get(val));
  });
}
function isValueDisabled(value, options) {
  var option = findValueOption([value], options)[0];

  if (option) {
    return option.disabled;
  }

  return false;
}
function isCheckDisabled(node) {
  return node.disabled || node.disableCheckbox || node.checkable === false;
}

function getLevel(_ref) {
  var parent = _ref.parent;
  var level = 0;
  var current = parent;

  while (current) {
    current = current.parent;
    level += 1;
  }

  return level;
}
/**
 * Before reuse `rc-tree` logic, we need to add key since TreeSelect use `value` instead of `key`.
 */


function flattenOptions(options) {
  // Add missing key
  function fillKey(list) {
    return (list || []).map(function (node) {
      var value = node.value,
          key = node.key,
          children = node.children;

      var clone = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)({}, node), {}, {
        key: 'key' in node ? key : value
      });

      if (children) {
        clone.children = fillKey(children);
      }

      return clone;
    });
  }

  var flattenList = (0,rc_tree_es_utils_treeUtil__WEBPACK_IMPORTED_MODULE_2__.flattenTreeData)(fillKey(options), true);
  return flattenList.map(function (node) {
    return {
      key: node.data.key,
      data: node.data,
      level: getLevel(node)
    };
  });
}

function getDefaultFilterOption(optionFilterProp) {
  return function (searchValue, dataNode) {
    var value = dataNode[optionFilterProp];
    return String(value).toLowerCase().includes(String(searchValue).toLowerCase());
  };
}
/** Filter options and return a new options by the search text */


function filterOptions(searchValue, options, _ref2) {
  var optionFilterProp = _ref2.optionFilterProp,
      filterOption = _ref2.filterOption;

  if (filterOption === false) {
    return options;
  }

  var filterOptionFunc;

  if (typeof filterOption === 'function') {
    filterOptionFunc = filterOption;
  } else {
    filterOptionFunc = getDefaultFilterOption(optionFilterProp);
  }

  function dig(list) {
    var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    return list.map(function (dataNode) {
      var children = dataNode.children;
      var match = keepAll || filterOptionFunc(searchValue, (0,_legacyUtil__WEBPACK_IMPORTED_MODULE_3__.fillLegacyProps)(dataNode));
      var childList = dig(children || [], match);

      if (match || childList.length) {
        return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)({}, dataNode), {}, {
          children: childList
        });
      }

      return null;
    }).filter(function (node) {
      return node;
    });
  }

  return dig(options);
}
function getRawValueLabeled(values, prevValue, getEntityByValue, getLabelProp) {
  var valueMap = new Map();
  toArray(prevValue).forEach(function (item) {
    if (item && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__.default)(item) === 'object' && 'value' in item) {
      valueMap.set(item.value, item);
    }
  });
  return values.map(function (val) {
    var item = {
      value: val
    };
    var entity = getEntityByValue(val, 'select', true);
    var label = entity ? getLabelProp(entity.data) : val;

    if (valueMap.has(val)) {
      var labeledValue = valueMap.get(val);
      item.label = 'label' in labeledValue ? labeledValue.label : label;

      if ('halfChecked' in labeledValue) {
        item.halfChecked = labeledValue.halfChecked;
      }
    } else {
      item.label = label;
    }

    return item;
  });
}
function addValue(rawValues, value) {
  var values = new Set(rawValues);
  values.add(value);
  return Array.from(values);
}
function removeValue(rawValues, value) {
  var values = new Set(rawValues);
  values.delete(value);
  return Array.from(values);
}

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-tree-select/es/utils/warningPropsUtil.js":
/*!************************************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-tree-select/es/utils/warningPropsUtil.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/antd/node_modules/rc-util/es/warning.js");
/* harmony import */ var _valueUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./valueUtil */ "./node_modules/antd/node_modules/rc-tree-select/es/utils/valueUtil.js");




function warningProps(props) {
  var searchPlaceholder = props.searchPlaceholder,
      treeCheckStrictly = props.treeCheckStrictly,
      treeCheckable = props.treeCheckable,
      labelInValue = props.labelInValue,
      value = props.value,
      multiple = props.multiple;
  (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__.default)(!searchPlaceholder, '`searchPlaceholder` has been removed.');

  if (treeCheckStrictly && labelInValue === false) {
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__.default)(false, '`treeCheckStrictly` will force set `labelInValue` to `true`.');
  }

  if (labelInValue || treeCheckStrictly) {
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__.default)((0,_valueUtil__WEBPACK_IMPORTED_MODULE_2__.toArray)(value).every(function (val) {
      return val && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__.default)(val) === 'object' && 'value' in val;
    }), 'Invalid prop `value` supplied to `TreeSelect`. You should use { label: string, value: string | number } or [{ label: string, value: string | number }] instead.');
  }

  if (treeCheckStrictly || multiple || treeCheckable) {
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__.default)(!value || Array.isArray(value), '`value` should be an array when `TreeSelect` is checkable or multiple.');
  } else {
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__.default)(!Array.isArray(value), '`value` should not be array when `TreeSelect` is single mode.');
  }
}

/* harmony default export */ __webpack_exports__["default"] = (warningProps);

/***/ })

}]);