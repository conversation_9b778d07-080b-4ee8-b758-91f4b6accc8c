(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_ant-design_pro-provider_es_index_js-node_modules_ant-design_pro-utils_es-d4a88c"],{

/***/ "./node_modules/@ant-design/pro-provider/es/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/index.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "arEGIntl": function() { return /* binding */ arEGIntl; },
/* harmony export */   "enUSIntl": function() { return /* binding */ enUSIntl; },
/* harmony export */   "enGBIntl": function() { return /* binding */ enGBIntl; },
/* harmony export */   "zhCNIntl": function() { return /* binding */ zhCNIntl; },
/* harmony export */   "viVNIntl": function() { return /* binding */ viVNIntl; },
/* harmony export */   "itITIntl": function() { return /* binding */ itITIntl; },
/* harmony export */   "jaJPIntl": function() { return /* binding */ jaJPIntl; },
/* harmony export */   "esESIntl": function() { return /* binding */ esESIntl; },
/* harmony export */   "ruRUIntl": function() { return /* binding */ ruRUIntl; },
/* harmony export */   "srRSIntl": function() { return /* binding */ srRSIntl; },
/* harmony export */   "msMYIntl": function() { return /* binding */ msMYIntl; },
/* harmony export */   "zhTWIntl": function() { return /* binding */ zhTWIntl; },
/* harmony export */   "frFRIntl": function() { return /* binding */ frFRIntl; },
/* harmony export */   "ptBRIntl": function() { return /* binding */ ptBRIntl; },
/* harmony export */   "koKRIntl": function() { return /* binding */ koKRIntl; },
/* harmony export */   "idIDIntl": function() { return /* binding */ idIDIntl; },
/* harmony export */   "deDEIntl": function() { return /* binding */ deDEIntl; },
/* harmony export */   "faIRIntl": function() { return /* binding */ faIRIntl; },
/* harmony export */   "trTRIntl": function() { return /* binding */ trTRIntl; },
/* harmony export */   "plPLIntl": function() { return /* binding */ plPLIntl; },
/* harmony export */   "intlMap": function() { return /* binding */ intlMap; },
/* harmony export */   "intlMapKeys": function() { return /* binding */ intlMapKeys; },
/* harmony export */   "ConfigConsumer": function() { return /* binding */ ConfigConsumer; },
/* harmony export */   "ConfigProvider": function() { return /* binding */ ConfigProvider; },
/* harmony export */   "ConfigProviderWrap": function() { return /* binding */ ConfigProviderWrap; },
/* harmony export */   "createIntl": function() { return /* binding */ createIntl; },
/* harmony export */   "useIntl": function() { return /* binding */ useIntl; }
/* harmony export */ });
/* harmony import */ var antd_es_config_provider_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/config-provider/style */ "./node_modules/antd/es/config-provider/style/index.js");
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! antd/es/config-provider */ "./node_modules/antd/es/config-provider/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var antd_es_locale_zh_CN__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! antd/es/locale/zh_CN */ "./node_modules/antd/es/locale/zh_CN.js");
/* harmony import */ var _locale_ar_EG__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./locale/ar_EG */ "./node_modules/@ant-design/pro-provider/es/locale/ar_EG.js");
/* harmony import */ var _locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./locale/zh_CN */ "./node_modules/@ant-design/pro-provider/es/locale/zh_CN.js");
/* harmony import */ var _locale_en_US__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./locale/en_US */ "./node_modules/@ant-design/pro-provider/es/locale/en_US.js");
/* harmony import */ var _locale_en_GB__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./locale/en_GB */ "./node_modules/@ant-design/pro-provider/es/locale/en_GB.js");
/* harmony import */ var _locale_vi_VN__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./locale/vi_VN */ "./node_modules/@ant-design/pro-provider/es/locale/vi_VN.js");
/* harmony import */ var _locale_it_IT__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./locale/it_IT */ "./node_modules/@ant-design/pro-provider/es/locale/it_IT.js");
/* harmony import */ var _locale_es_ES__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./locale/es_ES */ "./node_modules/@ant-design/pro-provider/es/locale/es_ES.js");
/* harmony import */ var _locale_ja_JP__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./locale/ja_JP */ "./node_modules/@ant-design/pro-provider/es/locale/ja_JP.js");
/* harmony import */ var _locale_ru_RU__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./locale/ru_RU */ "./node_modules/@ant-design/pro-provider/es/locale/ru_RU.js");
/* harmony import */ var _locale_sr_RS__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./locale/sr_RS */ "./node_modules/@ant-design/pro-provider/es/locale/sr_RS.js");
/* harmony import */ var _locale_ms_MY__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./locale/ms_MY */ "./node_modules/@ant-design/pro-provider/es/locale/ms_MY.js");
/* harmony import */ var _locale_zh_TW__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./locale/zh_TW */ "./node_modules/@ant-design/pro-provider/es/locale/zh_TW.js");
/* harmony import */ var _locale_fr_FR__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./locale/fr_FR */ "./node_modules/@ant-design/pro-provider/es/locale/fr_FR.js");
/* harmony import */ var _locale_pt_BR__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./locale/pt_BR */ "./node_modules/@ant-design/pro-provider/es/locale/pt_BR.js");
/* harmony import */ var _locale_ko_KR__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./locale/ko_KR */ "./node_modules/@ant-design/pro-provider/es/locale/ko_KR.js");
/* harmony import */ var _locale_id_ID__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./locale/id_ID */ "./node_modules/@ant-design/pro-provider/es/locale/id_ID.js");
/* harmony import */ var _locale_de_DE__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./locale/de_DE */ "./node_modules/@ant-design/pro-provider/es/locale/de_DE.js");
/* harmony import */ var _locale_fa_IR__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./locale/fa_IR */ "./node_modules/@ant-design/pro-provider/es/locale/fa_IR.js");
/* harmony import */ var _locale_tr_TR__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./locale/tr_TR */ "./node_modules/@ant-design/pro-provider/es/locale/tr_TR.js");
/* harmony import */ var _locale_pl_PL__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./locale/pl_PL */ "./node_modules/@ant-design/pro-provider/es/locale/pl_PL.js");



function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }
























function get(source, path, defaultValue) {
  // a[3].b -> a.3.b
  var paths = path.replace(/\[(\d+)\]/g, '.$1').split('.');
  var result = source;
  var message = defaultValue; // eslint-disable-next-line no-restricted-syntax

  var _iterator = _createForOfIteratorHelper(paths),
      _step;

  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var p = _step.value;
      message = Object(result)[p];
      result = Object(result)[p];

      if (message === undefined) {
        return defaultValue;
      }
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }

  return message;
}
/**
 * 创建一个操作函数
 *
 * @param locale
 * @param localeMap
 */


var createIntl = function createIntl(locale, localeMap) {
  return {
    getMessage: function getMessage(id, defaultMessage) {
      return get(localeMap, id, defaultMessage) || defaultMessage;
    },
    locale: locale
  };
};

var arEGIntl = createIntl('ar_EG', _locale_ar_EG__WEBPACK_IMPORTED_MODULE_2__.default);
var zhCNIntl = createIntl('zh_CN', _locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__.default);
var enUSIntl = createIntl('en_US', _locale_en_US__WEBPACK_IMPORTED_MODULE_4__.default);
var enGBIntl = createIntl('en_GB', _locale_en_GB__WEBPACK_IMPORTED_MODULE_5__.default);
var viVNIntl = createIntl('vi_VN', _locale_vi_VN__WEBPACK_IMPORTED_MODULE_6__.default);
var itITIntl = createIntl('it_IT', _locale_it_IT__WEBPACK_IMPORTED_MODULE_7__.default);
var jaJPIntl = createIntl('ja_JP', _locale_ja_JP__WEBPACK_IMPORTED_MODULE_9__.default);
var esESIntl = createIntl('es_ES', _locale_es_ES__WEBPACK_IMPORTED_MODULE_8__.default);
var ruRUIntl = createIntl('ru_RU', _locale_ru_RU__WEBPACK_IMPORTED_MODULE_10__.default);
var srRSIntl = createIntl('sr_RS', _locale_sr_RS__WEBPACK_IMPORTED_MODULE_11__.default);
var msMYIntl = createIntl('ms_MY', _locale_ms_MY__WEBPACK_IMPORTED_MODULE_12__.default);
var zhTWIntl = createIntl('zh_TW', _locale_zh_TW__WEBPACK_IMPORTED_MODULE_13__.default);
var frFRIntl = createIntl('fr_FR', _locale_fr_FR__WEBPACK_IMPORTED_MODULE_14__.default);
var ptBRIntl = createIntl('pt_BR', _locale_pt_BR__WEBPACK_IMPORTED_MODULE_15__.default);
var koKRIntl = createIntl('ko_KR', _locale_ko_KR__WEBPACK_IMPORTED_MODULE_16__.default);
var idIDIntl = createIntl('id_ID', _locale_id_ID__WEBPACK_IMPORTED_MODULE_17__.default);
var deDEIntl = createIntl('de_DE', _locale_de_DE__WEBPACK_IMPORTED_MODULE_18__.default);
var faIRIntl = createIntl('fa_IR', _locale_fa_IR__WEBPACK_IMPORTED_MODULE_19__.default);
var trTRIntl = createIntl('tr_TR', _locale_tr_TR__WEBPACK_IMPORTED_MODULE_20__.default);
var plPLIntl = createIntl('pl_PL', _locale_pl_PL__WEBPACK_IMPORTED_MODULE_21__.default);
var intlMap = {
  'ar-EG': arEGIntl,
  'zh-CN': zhCNIntl,
  'en-US': enUSIntl,
  'en-GB': enGBIntl,
  'vi-VN': viVNIntl,
  'it-IT': itITIntl,
  'ja-JP': jaJPIntl,
  'es-ES': esESIntl,
  'ru-RU': ruRUIntl,
  'sr-RS': srRSIntl,
  'ms-MY': msMYIntl,
  'zh-TW': zhTWIntl,
  'fr-FR': frFRIntl,
  'pt-BR': ptBRIntl,
  'ko-KR': koKRIntl,
  'id-ID': idIDIntl,
  'de-DE': deDEIntl,
  'fa-IR': faIRIntl,
  'tr-TR': trTRIntl,
  'pl-PL': plPLIntl
};
var intlMapKeys = Object.keys(intlMap);

var ConfigContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createContext({
  intl: _objectSpread(_objectSpread({}, zhCNIntl), {}, {
    locale: 'default'
  }),
  valueTypeMap: {}
});
var ConfigConsumer = ConfigContext.Consumer,
    ConfigProvider = ConfigContext.Provider;
/**
 * 根据 antd 的 key 来找到的 locale 插件的 key
 *
 * @param localeKey
 */

var findIntlKeyByAntdLocaleKey = function findIntlKeyByAntdLocaleKey(localeKey) {
  if (!localeKey) {
    return 'zh-CN';
  }

  var localeName = localeKey.toLocaleLowerCase();
  return intlMapKeys.find(function (intlKey) {
    var LowerCaseKey = intlKey.toLocaleLowerCase();
    return LowerCaseKey.includes(localeName);
  });
};
/**
 * 如果没有配置 locale，这里组件会根据 antd 的 key 来自动选择
 *
 * @param param0
 */


var ConfigProviderWrap = function ConfigProviderWrap(_ref) {
  var children = _ref.children;

  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(antd_es_config_provider__WEBPACK_IMPORTED_MODULE_22__.default.ConfigContext),
      locale = _useContext.locale; // 如果 locale 不存在自动注入的 AntdConfigProvider


  var Provider = locale === undefined ? antd_es_config_provider__WEBPACK_IMPORTED_MODULE_22__.default : react__WEBPACK_IMPORTED_MODULE_1__.Fragment;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ConfigConsumer, null, function (value) {
    var _value$intl;

    var localeName = locale === null || locale === void 0 ? void 0 : locale.locale;
    var key = findIntlKeyByAntdLocaleKey(localeName); // antd 的 key 存在的时候以 antd 的为主

    var intl = localeName && ((_value$intl = value.intl) === null || _value$intl === void 0 ? void 0 : _value$intl.locale) === 'default' ? intlMap[key] : value.intl || intlMap[key]; // 自动注入 antd 的配置

    var configProvider = locale === undefined ? {
      locale: antd_es_locale_zh_CN__WEBPACK_IMPORTED_MODULE_23__.default
    } : {};
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Provider, configProvider, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ConfigProvider, {
      value: _objectSpread(_objectSpread({}, value), {}, {
        intl: intl || zhCNIntl
      })
    }, children));
  });
};


function useIntl() {
  var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ConfigContext);
  return context.intl || zhCNIntl;
}
/* harmony default export */ __webpack_exports__["default"] = (ConfigContext);

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/ar_EG.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/ar_EG.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '$',
  form: {
    lightFilter: {
      more: 'المزيد',
      clear: 'نظف',
      confirm: 'تأكيد',
      itemUnit: 'عناصر'
    }
  },
  tableForm: {
    search: 'ابحث',
    reset: 'إعادة تعيين',
    submit: 'ارسال',
    collapsed: 'مُقلص',
    expand: 'مُوسع',
    inputPlaceholder: 'الرجاء الإدخال',
    selectPlaceholder: 'الرجاء الإختيار'
  },
  alert: {
    clear: 'نظف',
    selected: 'محدد',
    item: 'عنصر'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'من',
      item: 'عناصر'
    }
  },
  tableToolBar: {
    leftPin: 'ثبت على اليسار',
    rightPin: 'ثبت على اليمين',
    noPin: 'الغاء التثبيت',
    leftFixedTitle: 'لصق على اليسار',
    rightFixedTitle: 'لصق على اليمين',
    noFixedTitle: 'إلغاء الإلصاق',
    reset: 'إعادة تعيين',
    columnDisplay: 'الأعمدة المعروضة',
    columnSetting: 'الإعدادات',
    fullScreen: 'وضع كامل الشاشة',
    exitFullScreen: 'الخروج من وضع كامل الشاشة',
    reload: 'تحديث',
    density: 'الكثافة',
    densityDefault: 'افتراضي',
    densityLarger: 'أكبر',
    densityMiddle: 'وسط',
    densitySmall: 'مدمج'
  },
  stepsForm: {
    next: 'التالي',
    prev: 'السابق'
  },
  loginForm: {
    submitText: 'تسجيل الدخول'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/de_DE.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/de_DE.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '€',
  form: {
    lightFilter: {
      more: 'Mehr',
      clear: 'Zurücksetzen',
      confirm: 'Bestätigen',
      itemUnit: 'Einträge'
    }
  },
  tableForm: {
    search: 'Suchen',
    reset: 'Zurücksetzen',
    submit: 'Absenden',
    collapsed: 'Zeige mehr',
    expand: 'Zeige weniger',
    inputPlaceholder: 'Bitte eingeben',
    selectPlaceholder: 'Bitte auswählen'
  },
  alert: {
    clear: 'Zurücksetzen',
    selected: 'Ausgewählt',
    item: 'Eintrag'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'von',
      item: 'Einträgen'
    }
  },
  tableToolBar: {
    leftPin: 'Links anheften',
    rightPin: 'Rechts anheften',
    noPin: 'Nicht angeheftet',
    leftFixedTitle: 'Links fixiert',
    rightFixedTitle: 'Rechts fixiert',
    noFixedTitle: 'Nicht fixiert',
    reset: 'Zurücksetzen',
    columnDisplay: 'Angezeigte Reihen',
    columnSetting: 'Einstellungen',
    fullScreen: 'Vollbild',
    exitFullScreen: 'Vollbild verlassen',
    reload: 'Aktualisieren',
    density: 'Abstand',
    densityDefault: 'Standard',
    densityLarger: 'Größer',
    densityMiddle: 'Mittel',
    densitySmall: 'Kompakt'
  },
  stepsForm: {
    next: 'Weiter',
    prev: 'Zurück',
    submit: 'Abschließen'
  },
  loginForm: {
    submitText: 'Anmelden'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/en_GB.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/en_GB.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '£',
  form: {
    lightFilter: {
      more: 'More',
      clear: 'Clear',
      confirm: 'Confirm',
      itemUnit: 'Items'
    }
  },
  tableForm: {
    search: 'Query',
    reset: 'Reset',
    submit: 'Submit',
    collapsed: 'Expand',
    expand: 'Collapse',
    inputPlaceholder: 'Please enter',
    selectPlaceholder: 'Please select'
  },
  alert: {
    clear: 'Clear',
    selected: 'Selected',
    item: 'Item'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'of',
      item: 'items'
    }
  },
  tableToolBar: {
    leftPin: 'Pin to left',
    rightPin: 'Pin to right',
    noPin: 'Unpinned',
    leftFixedTitle: 'Fixed the left',
    rightFixedTitle: 'Fixed the right',
    noFixedTitle: 'Not Fixed',
    reset: 'Reset',
    columnDisplay: 'Column Display',
    columnSetting: 'Settings',
    fullScreen: 'Full Screen',
    exitFullScreen: 'Exit Full Screen',
    reload: 'Refresh',
    density: 'Density',
    densityDefault: 'Default',
    densityLarger: 'Larger',
    densityMiddle: 'Middle',
    densitySmall: 'Compact'
  },
  stepsForm: {
    next: 'Next',
    prev: 'Previous',
    submit: 'Finish'
  },
  loginForm: {
    submitText: 'Login'
  },
  editableTable: {
    action: {
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete'
    }
  },
  switch: {
    open: 'open',
    close: 'close'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/en_US.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/en_US.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '$',
  form: {
    lightFilter: {
      more: 'More',
      clear: 'Clear',
      confirm: 'Confirm',
      itemUnit: 'Items'
    }
  },
  tableForm: {
    search: 'Query',
    reset: 'Reset',
    submit: 'Submit',
    collapsed: 'Expand',
    expand: 'Collapse',
    inputPlaceholder: 'Please enter',
    selectPlaceholder: 'Please select'
  },
  alert: {
    clear: 'Clear',
    selected: 'Selected',
    item: 'Item'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'of',
      item: 'items'
    }
  },
  tableToolBar: {
    leftPin: 'Pin to left',
    rightPin: 'Pin to right',
    noPin: 'Unpinned',
    leftFixedTitle: 'Fixed the left',
    rightFixedTitle: 'Fixed the right',
    noFixedTitle: 'Not Fixed',
    reset: 'Reset',
    columnDisplay: 'Column Display',
    columnSetting: 'Settings',
    fullScreen: 'Full Screen',
    exitFullScreen: 'Exit Full Screen',
    reload: 'Refresh',
    density: 'Density',
    densityDefault: 'Default',
    densityLarger: 'Larger',
    densityMiddle: 'Middle',
    densitySmall: 'Compact'
  },
  stepsForm: {
    next: 'Next',
    prev: 'Previous',
    submit: 'Finish'
  },
  loginForm: {
    submitText: 'Login'
  },
  editableTable: {
    action: {
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete'
    }
  },
  switch: {
    open: 'open',
    close: 'close'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/es_ES.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/es_ES.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '€',
  tableForm: {
    search: 'Buscar',
    reset: 'Limpiar',
    submit: 'Submit',
    collapsed: 'Expandir',
    expand: 'Colapsar',
    inputPlaceholder: 'Ingrese valor',
    selectPlaceholder: 'Seleccione valor'
  },
  alert: {
    clear: 'Limpiar',
    selected: 'Seleccionado',
    item: 'Articulo'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'de',
      item: 'artículos'
    }
  },
  tableToolBar: {
    leftPin: 'Pin a la izquierda',
    rightPin: 'Pin a la derecha',
    noPin: 'Sin Pin',
    leftFixedTitle: 'Fijado a la izquierda',
    rightFixedTitle: 'Fijado a la derecha',
    noFixedTitle: 'Sin Fijar',
    reset: 'Reiniciar',
    columnDisplay: 'Mostrar Columna',
    columnSetting: 'Configuración',
    fullScreen: 'Pantalla Completa',
    exitFullScreen: 'Salir Pantalla Completa',
    reload: 'Refrescar',
    density: 'Densidad',
    densityDefault: 'Por Defecto',
    densityLarger: 'Largo',
    densityMiddle: 'Medio',
    densitySmall: 'Compacto'
  },
  stepsForm: {
    next: 'Siguiente',
    prev: 'Anterior',
    submit: 'Finalizar'
  },
  loginForm: {
    submitText: 'Entrar'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/fa_IR.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/fa_IR.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: 'تومان',
  form: {
    lightFilter: {
      more: 'بیشتر',
      clear: 'پاک کردن',
      confirm: 'تایید',
      itemUnit: 'مورد'
    }
  },
  tableForm: {
    search: 'جستجو',
    reset: 'بازنشانی',
    submit: 'تایید',
    collapsed: 'نمایش بیشتر',
    expand: 'نمایش کمتر',
    inputPlaceholder: 'پیدا کنید',
    selectPlaceholder: 'انتخاب کنید'
  },
  alert: {
    clear: 'پاک سازی',
    selected: 'انتخاب',
    item: 'مورد'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'از',
      item: 'مورد'
    }
  },
  tableToolBar: {
    leftPin: 'سنجاق به چپ',
    rightPin: 'سنجاق به راست',
    noPin: 'سنجاق نشده',
    leftFixedTitle: 'ثابت شده در چپ',
    rightFixedTitle: 'ثابت شده در راست',
    noFixedTitle: 'شناور',
    reset: 'بازنشانی',
    columnDisplay: 'نمایش همه',
    columnSetting: 'تنظیمات',
    fullScreen: 'تمام صفحه',
    exitFullScreen: 'خروج از حالت تمام صفحه',
    reload: 'تازه سازی',
    density: 'تراکم',
    densityDefault: 'پیش فرض',
    densityLarger: 'بزرگ',
    densityMiddle: 'متوسط',
    densitySmall: 'کوچک'
  },
  stepsForm: {
    next: 'بعدی',
    prev: 'قبلی',
    submit: 'اتمام'
  },
  loginForm: {
    submitText: 'ورود'
  },
  editableTable: {
    action: {
      save: 'ذخیره',
      cancel: 'لغو',
      delete: 'حذف'
    }
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/fr_FR.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/fr_FR.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '€',
  form: {
    lightFilter: {
      more: 'Plus',
      clear: 'Effacer',
      confirm: 'Confirmer',
      itemUnit: 'Items'
    }
  },
  tableForm: {
    search: 'Rechercher',
    reset: 'Réinitialiser',
    submit: 'Envoyer',
    collapsed: 'Agrandir',
    expand: 'Réduire',
    inputPlaceholder: 'Entrer une valeur',
    selectPlaceholder: 'Sélectionner une valeur'
  },
  alert: {
    clear: 'Réinitialiser',
    selected: 'Sélectionné',
    item: 'Item'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'sur',
      item: 'éléments'
    }
  },
  tableToolBar: {
    leftPin: 'Épingler à gauche',
    rightPin: 'Épingler à gauche',
    noPin: 'Sans épingle',
    leftFixedTitle: 'Fixer à gauche',
    rightFixedTitle: 'Fixer à droite',
    noFixedTitle: 'Non fixé',
    reset: 'Réinitialiser',
    columnDisplay: 'Affichage colonne',
    columnSetting: 'Réglages',
    fullScreen: 'Plein écran',
    exitFullScreen: 'Quitter Plein écran',
    reload: 'Rafraichir',
    density: 'Densité',
    densityDefault: 'Par défaut',
    densityLarger: 'Larger',
    densityMiddle: 'Moyenne',
    densitySmall: 'Compacte'
  },
  stepsForm: {
    next: 'Suivante',
    prev: 'Précédente',
    submit: 'Finaliser'
  },
  loginForm: {
    submitText: 'Se connecter'
  },
  editableTable: {
    action: {
      save: 'Sauvegarder',
      cancel: 'Annuler',
      delete: 'Supprimer'
    }
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/id_ID.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/id_ID.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: 'RP',
  form: {
    lightFilter: {
      more: 'Lebih',
      clear: 'Hapus',
      confirm: 'Konfirmasi',
      itemUnit: 'Unit'
    }
  },
  tableForm: {
    search: 'Cari',
    reset: 'Atur ulang',
    submit: 'Kirim',
    collapsed: 'Lebih sedikit',
    expand: 'Lebih banyak',
    inputPlaceholder: 'Masukkan pencarian',
    selectPlaceholder: 'Pilih'
  },
  alert: {
    clear: 'Hapus',
    selected: 'Dipilih',
    item: 'Butir'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'Dari',
      item: 'Butir'
    }
  },
  tableToolBar: {
    leftPin: 'Pin kiri',
    rightPin: 'Pin kanan',
    noPin: 'Tidak ada pin',
    leftFixedTitle: 'Rata kiri',
    rightFixedTitle: 'Rata kanan',
    noFixedTitle: 'Tidak tetap',
    reset: 'Atur ulang',
    columnDisplay: 'Tampilan kolom',
    columnSetting: 'Pengaturan',
    fullScreen: 'Layar penuh',
    exitFullScreen: 'Keluar layar penuh',
    reload: 'Atur ulang',
    density: 'Kerapatan',
    densityDefault: 'Standar',
    densityLarger: 'Lebih besar',
    densityMiddle: 'Sedang',
    densitySmall: 'Rapat'
  },
  stepsForm: {
    next: 'Selanjutnya',
    prev: 'Sebelumnya',
    submit: 'Selesai'
  },
  loginForm: {
    submitText: 'Login'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/it_IT.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/it_IT.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '€',
  tableForm: {
    search: 'Filtra',
    reset: 'Pulisci',
    submit: 'Invia',
    collapsed: 'Espandi',
    expand: 'Contrai',
    inputPlaceholder: 'Digita',
    selectPlaceholder: 'Seleziona'
  },
  alert: {
    clear: 'Rimuovi',
    selected: 'Selezionati',
    item: 'elementi'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'di',
      item: 'elementi'
    }
  },
  tableToolBar: {
    leftPin: 'Fissa a sinistra',
    rightPin: 'Fissa a destra',
    noPin: 'Ripristina posizione',
    leftFixedTitle: 'Fissato a sinistra',
    rightFixedTitle: 'Fissato a destra',
    noFixedTitle: 'Non fissato',
    reset: 'Ripristina',
    columnDisplay: 'Disposizione colonne',
    columnSetting: 'Impostazioni',
    fullScreen: 'Modalità schermo intero',
    exitFullScreen: 'Esci da modalità schermo intero',
    reload: 'Ricarica',
    density: 'Grandezza tabella',
    densityLarger: 'Grande',
    densityMiddle: 'Media',
    densitySmall: 'Compatta'
  },
  loginForm: {
    submitText: 'Accedi'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/ja_JP.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/ja_JP.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '¥',
  tableForm: {
    search: '検索',
    reset: 'リセット',
    submit: '提交',
    collapsed: '展開',
    expand: '収納',
    inputPlaceholder: '入力してください',
    selectPlaceholder: '選択してください'
  },
  alert: {
    clear: 'クリア',
    selected: '選択した',
    item: '項目'
  },
  pagination: {
    total: {
      range: '記事',
      total: '/合計',
      item: ' '
    }
  },
  tableToolBar: {
    leftPin: '左に固定',
    rightPin: '右に固定',
    noPin: 'キャンセル',
    leftFixedTitle: '左に固定された項目',
    rightFixedTitle: '右に固定された項目',
    noFixedTitle: '固定されてない項目',
    reset: 'リセット',
    columnDisplay: '表示列',
    columnSetting: '列表示設定',
    fullScreen: 'フルスクリーン',
    exitFullScreen: '終了',
    reload: '更新',
    density: '行高',
    densityLarger: '默认',
    densityMiddle: '中',
    densitySmall: '小'
  },
  stepsForm: {
    next: '次のステップ',
    pre: '前へ',
    submit: '送信'
  },
  loginForm: {
    submitText: 'ログイン'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/ko_KR.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/ko_KR.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '₩',
  form: {
    lightFilter: {
      more: '더보기',
      clear: '취소',
      confirm: '확인',
      itemUnit: '건수'
    }
  },
  tableForm: {
    search: '조회',
    reset: '초기화',
    submit: '제출',
    collapsed: '확장',
    expand: '닫기',
    inputPlaceholder: '입력해 주세요',
    selectPlaceholder: '선택해 주세요'
  },
  alert: {
    clear: '취소',
    selected: '선택',
    item: '건'
  },
  pagination: {
    total: {
      range: ' ',
      total: '/ 총',
      item: '건'
    }
  },
  tableToolBar: {
    leftPin: '왼쪽으로 핀',
    rightPin: '오른쪽으로 핀',
    noPin: '핀 제거',
    leftFixedTitle: '왼쪽으로 고정',
    rightFixedTitle: '오른쪽으로 고정',
    noFixedTitle: '비고정',
    reset: '초기화',
    columnDisplay: '컬럼 표시',
    columnSetting: '설정',
    fullScreen: '전체 화면',
    exitFullScreen: '전체 화면 취소',
    reload: '다시 읽기',
    density: '여백',
    densityDefault: '기본',
    densityLarger: '많은 여백',
    densityMiddle: '중간 여백',
    densitySmall: '좁은 여백'
  },
  stepsForm: {
    next: '다음',
    prev: '이전',
    submit: '종료'
  },
  loginForm: {
    submitText: '로그인'
  },
  editableTable: {
    action: {
      save: '저장',
      cancel: '취소',
      delete: '삭제'
    }
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/ms_MY.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/ms_MY.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: 'RM',
  tableForm: {
    search: 'Cari',
    reset: 'Menetapkan semula',
    submit: 'Hantar',
    collapsed: 'Kembang',
    expand: 'Kuncup',
    inputPlaceholder: 'Sila masuk',
    selectPlaceholder: 'Sila pilih'
  },
  alert: {
    clear: 'Padam',
    selected: 'Dipilih',
    item: 'Item'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'daripada',
      item: 'item'
    }
  },
  tableToolBar: {
    leftPin: 'Pin ke kiri',
    rightPin: 'Pin ke kanan',
    noPin: 'Tidak pin',
    leftFixedTitle: 'Tetap ke kiri',
    rightFixedTitle: 'Tetap ke kanan',
    noFixedTitle: 'Tidak Tetap',
    reset: 'Menetapkan semula',
    columnDisplay: 'Lajur',
    columnSetting: 'Settings',
    fullScreen: 'Full Screen',
    exitFullScreen: 'Keluar Full Screen',
    reload: 'Muat Semula',
    density: 'Densiti',
    densityDefault: 'Biasa',
    densityLarger: 'Besar',
    densityMiddle: 'Tengah',
    densitySmall: 'Kecil'
  },
  loginForm: {
    submitText: 'Log Masuk'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/pl_PL.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/pl_PL.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: 'zł',
  form: {
    lightFilter: {
      more: 'Więcej',
      clear: 'Wyczyść',
      confirm: 'Potwierdź',
      itemUnit: 'Ilość'
    }
  },
  tableForm: {
    search: 'Szukaj',
    reset: 'Reset',
    submit: 'Zatwierdź',
    collapsed: 'Pokaż wiecej',
    expand: 'Pokaż mniej',
    inputPlaceholder: 'Proszę podać',
    selectPlaceholder: 'Proszę wybrać'
  },
  alert: {
    clear: 'Wyczyść',
    selected: 'Wybrane',
    item: 'Wpis'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'z',
      item: 'Wpisów'
    }
  },
  tableToolBar: {
    leftPin: 'Przypnij do lewej',
    rightPin: 'Przypnij do prawej',
    noPin: 'Odepnij',
    leftFixedTitle: 'Przypięte do lewej',
    rightFixedTitle: 'Przypięte do prawej',
    noFixedTitle: 'Nieprzypięte',
    reset: 'Reset',
    columnDisplay: 'Wyświetlane wiersze',
    columnSetting: 'Ustawienia',
    fullScreen: 'Pełen ekran',
    exitFullScreen: 'Zamknij pełen ekran',
    reload: 'Odśwież',
    density: 'Odstęp',
    densityDefault: 'Standard',
    densityLarger: 'Wiekszy',
    densityMiddle: 'Sredni',
    densitySmall: 'Kompaktowy'
  },
  stepsForm: {
    next: 'Weiter',
    prev: 'Zurück',
    submit: 'Abschließen'
  },
  loginForm: {
    submitText: 'Zaloguj się'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/pt_BR.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/pt_BR.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: 'R$',
  form: {
    lightFilter: {
      more: 'Mais',
      clear: 'Limpar',
      confirm: 'Confirmar',
      itemUnit: 'Itens'
    }
  },
  tableForm: {
    search: 'Filtrar',
    reset: 'Limpar',
    submit: 'Confirmar',
    collapsed: 'Expandir',
    expand: 'Colapsar',
    inputPlaceholder: 'Por favor insira',
    selectPlaceholder: 'Por favor selecione'
  },
  alert: {
    clear: 'Limpar',
    selected: 'Selecionado(s)',
    item: 'Item(s)'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'de',
      item: 'items'
    }
  },
  tableToolBar: {
    leftPin: 'Fixar à esquerda',
    rightPin: 'Fixar à direita',
    noPin: 'Desfixado',
    leftFixedTitle: 'Fixado à esquerda',
    rightFixedTitle: 'Fixado à direita',
    noFixedTitle: 'Não fixado',
    reset: 'Limpar',
    columnDisplay: 'Mostrar Coluna',
    columnSetting: 'Configurações',
    fullScreen: 'Tela Cheia',
    exitFullScreen: 'Sair da Tela Cheia',
    reload: 'Atualizar',
    density: 'Densidade',
    densityDefault: 'Padrão',
    densityLarger: 'Largo',
    densityMiddle: 'Médio',
    densitySmall: 'Compacto'
  },
  stepsForm: {
    next: 'Próximo',
    prev: 'Anterior',
    submit: 'Enviar'
  },
  loginForm: {
    submitText: 'Entrar'
  },
  editableTable: {
    action: {
      save: 'Salvar',
      cancel: 'Cancelar',
      delete: 'Apagar'
    }
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/ru_RU.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/ru_RU.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '₽',
  form: {
    lightFilter: {
      more: 'Еще',
      clear: 'Очистить',
      confirm: 'ОК',
      itemUnit: 'Позиции'
    }
  },
  tableForm: {
    search: 'Найти',
    reset: 'Сброс',
    submit: 'Отправить',
    collapsed: 'Развернуть',
    expand: 'Свернуть',
    inputPlaceholder: 'Введите значение',
    selectPlaceholder: 'Выберите значение'
  },
  alert: {
    clear: 'Очистить',
    selected: 'Выбрано',
    item: 'элементов'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'из',
      item: 'элементов'
    }
  },
  tableToolBar: {
    leftPin: 'Закрепить слева',
    rightPin: 'Закрепить справа',
    noPin: 'Открепить',
    leftFixedTitle: 'Закреплено слева',
    rightFixedTitle: 'Закреплено справа',
    noFixedTitle: 'Не закреплено',
    reset: 'Сброс',
    columnDisplay: 'Отображение столбца',
    columnSetting: 'Настройки',
    fullScreen: 'Полный экран',
    exitFullScreen: 'Выйти из полноэкранного режима',
    reload: 'Обновить',
    density: 'Размер',
    densityDefault: 'По умолчанию',
    densityLarger: 'Большой',
    densityMiddle: 'Средний',
    densitySmall: 'Сжатый'
  },
  stepsForm: {
    next: 'Следующий',
    prev: 'Предыдущий',
    submit: 'Завершить'
  },
  loginForm: {
    submitText: 'Вход'
  },
  editableTable: {
    action: {
      save: 'Сохранить',
      cancel: 'Отменить',
      delete: 'Удалить'
    }
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/sr_RS.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/sr_RS.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: 'RSD',
  form: {
    lightFilter: {
      more: 'Više',
      clear: 'Očisti',
      confirm: 'Potvrdi',
      itemUnit: 'Stavke'
    }
  },
  tableForm: {
    search: 'Pronađi',
    reset: 'Resetuj',
    submit: 'Pošalji',
    collapsed: 'Proširi',
    expand: 'Skupi',
    inputPlaceholder: 'Molimo unesite',
    selectPlaceholder: 'Molimo odaberite'
  },
  alert: {
    clear: 'Očisti',
    selected: 'Odabrano',
    item: 'Stavka'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'od',
      item: 'stavki'
    }
  },
  tableToolBar: {
    leftPin: 'Zakači levo',
    rightPin: 'Zakači desno',
    noPin: 'Nije zakačeno',
    leftFixedTitle: 'Fiksirano levo',
    rightFixedTitle: 'Fiksirano desno',
    noFixedTitle: 'Nije fiksirano',
    reset: 'Resetuj',
    columnDisplay: 'Prikaz kolona',
    columnSetting: 'Podešavanja',
    fullScreen: 'Pun ekran',
    exitFullScreen: 'Zatvori pun ekran',
    reload: 'Osveži',
    density: 'Veličina',
    densityDefault: 'Podrazumevana',
    densityLarger: 'Veća',
    densityMiddle: 'Srednja',
    densitySmall: 'Kompaktna'
  },
  stepsForm: {
    next: 'Dalje',
    prev: 'Nazad',
    submit: 'Gotovo'
  },
  loginForm: {
    submitText: 'Prijavi se'
  },
  editableTable: {
    action: {
      save: 'Sačuvaj',
      cancel: 'Poništi',
      delete: 'Obriši'
    }
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/tr_TR.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/tr_TR.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '$',
  form: {
    lightFilter: {
      more: 'Daha Fazla',
      clear: 'Temizle',
      confirm: 'Onayla',
      itemUnit: 'Öğeler'
    }
  },
  tableForm: {
    search: 'Filtrele',
    reset: 'Sıfırla',
    submit: 'Gönder',
    collapsed: 'Daha fazla',
    expand: 'Daha az',
    inputPlaceholder: 'Filtrelemek için bir değer girin',
    selectPlaceholder: 'Filtrelemek için bir değer seçin'
  },
  alert: {
    clear: 'Temizle',
    selected: 'Seçili',
    item: 'Öğe'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'Toplam',
      item: 'Öğe'
    }
  },
  tableToolBar: {
    leftPin: 'Sola sabitle',
    rightPin: 'Sağa sabitle',
    noPin: 'Sabitlemeyi kaldır',
    leftFixedTitle: 'Sola sabitlendi',
    rightFixedTitle: 'Sağa sabitlendi',
    noFixedTitle: 'Sabitlenmedi',
    reset: 'Sıfırla',
    columnDisplay: 'Kolon Görünümü',
    columnSetting: 'Ayarlar',
    fullScreen: 'Tam Ekran',
    exitFullScreen: 'Tam Ekrandan Çık',
    reload: 'Yenile',
    density: 'Kalınlık',
    densityDefault: 'Varsayılan',
    densityLarger: 'Büyük',
    densityMiddle: 'Orta',
    densitySmall: 'Küçük'
  },
  stepsForm: {
    next: 'Sıradaki',
    prev: 'Önceki',
    submit: 'Gönder'
  },
  loginForm: {
    submitText: 'Giriş Yap'
  },
  editableTable: {
    action: {
      save: 'Kaydet',
      cancel: 'Vazgeç',
      delete: 'Sil'
    }
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/vi_VN.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/vi_VN.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '₫',
  tableForm: {
    search: 'Tìm kiếm',
    reset: 'Làm lại',
    submit: 'Gửi đi',
    collapsed: 'Mở rộng',
    expand: 'Thu gọn',
    inputPlaceholder: 'nhập dữ liệu',
    selectPlaceholder: 'Vui lòng chọn'
  },
  alert: {
    clear: 'Xóa',
    selected: 'đã chọn',
    item: 'mục'
  },
  pagination: {
    total: {
      range: ' ',
      total: 'trên',
      item: 'mặt hàng'
    }
  },
  tableToolBar: {
    leftPin: 'Ghim trái',
    rightPin: 'Ghim phải',
    noPin: 'Bỏ ghim',
    leftFixedTitle: 'Cố định trái',
    rightFixedTitle: 'Cố định phải',
    noFixedTitle: 'Chưa cố định',
    reset: 'Làm lại',
    columnDisplay: 'Cột hiển thị',
    columnSetting: 'Cấu hình',
    fullScreen: 'Chế độ toàn màn hình',
    exitFullScreen: 'Thoát chế độ toàn màn hình',
    reload: 'Làm mới',
    density: 'Mật độ hiển thị',
    densityDefault: 'Mặc định',
    densityLarger: 'Mặc định',
    densityMiddle: 'Trung bình',
    densitySmall: 'Chật'
  },
  loginForm: {
    submitText: 'Đăng nhập'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/zh_CN.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/zh_CN.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '￥',
  form: {
    lightFilter: {
      more: '更多筛选',
      clear: '清除',
      confirm: '确认',
      itemUnit: '项'
    }
  },
  tableForm: {
    search: '查询',
    reset: '重置',
    submit: '提交',
    collapsed: '展开',
    expand: '收起',
    inputPlaceholder: '请输入',
    selectPlaceholder: '请选择'
  },
  alert: {
    clear: '取消选择',
    selected: '已选择',
    item: '项'
  },
  pagination: {
    total: {
      range: '第',
      total: '条/总共',
      item: '条'
    }
  },
  tableToolBar: {
    leftPin: '固定在列首',
    rightPin: '固定在列尾',
    noPin: '不固定',
    leftFixedTitle: '固定在左侧',
    rightFixedTitle: '固定在右侧',
    noFixedTitle: '不固定',
    reset: '重置',
    columnDisplay: '列展示',
    columnSetting: '列设置',
    fullScreen: '全屏',
    exitFullScreen: '退出全屏',
    reload: '刷新',
    density: '密度',
    densityDefault: '正常',
    densityLarger: '默认',
    densityMiddle: '中等',
    densitySmall: '紧凑'
  },
  editableTable: {
    action: {
      save: '保存',
      cancel: '取消',
      delete: '删除'
    }
  },
  switch: {
    open: '打开',
    close: '关闭'
  },
  loginForm: {
    submitText: '登录'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-provider/es/locale/zh_TW.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-provider/es/locale/zh_TW.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  moneySymbol: '$',
  form: {
    lightFilter: {
      more: '更多篩選',
      clear: '清除',
      confirm: '確認',
      itemUnit: '項'
    }
  },
  tableForm: {
    search: '查詢',
    reset: '重置',
    submit: '提交',
    collapsed: '展開',
    expand: '收起',
    inputPlaceholder: '請輸入',
    selectPlaceholder: '請選擇'
  },
  alert: {
    clear: '取消選擇',
    selected: '已選擇',
    item: '項'
  },
  pagination: {
    total: {
      range: '第',
      total: '條/總共',
      item: '條'
    }
  },
  tableToolBar: {
    leftPin: '固定到左邊',
    rightPin: '固定到右邊',
    noPin: '不固定',
    leftFixedTitle: '固定在左側',
    rightFixedTitle: '固定在右側',
    noFixedTitle: '不固定',
    reset: '重置',
    columnDisplay: '列展示',
    columnSetting: '列設置',
    fullScreen: '全屏',
    exitFullScreen: '退出全屏',
    reload: '刷新',
    density: '密度',
    densityDefault: '正常',
    densityLarger: '默認',
    densityMiddle: '中等',
    densitySmall: '緊湊'
  },
  editableTable: {
    action: {
      save: '保存',
      cancel: '取消',
      delete: '刪除'
    }
  },
  switch: {
    open: '打開',
    close: '關閉'
  },
  loginForm: {
    submitText: '登入'
  }
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-utils/es/isBrowser/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-utils/es/isBrowser/index.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* provided dependency */ var process = __webpack_require__(/*! ./node_modules/process/browser.js */ "./node_modules/process/browser.js");
var isNode = typeof process !== 'undefined' && process.versions != null && process.versions.node != null;

var isBrowser = function isBrowser() {
  if (false) {}

  return typeof window !== 'undefined' && typeof window.document !== 'undefined' && !isNode;
};

/* harmony default export */ __webpack_exports__["default"] = (isBrowser);

/***/ }),

/***/ "./node_modules/@ant-design/pro-utils/es/merge/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@ant-design/pro-utils/es/merge/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "merge": function() { return /* binding */ merge; }
/* harmony export */ });
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

/* eslint-disable prefer-rest-params */
var merge = function merge() {
  var obj = {};

  for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
    rest[_key] = arguments[_key];
  }

  var il = rest.length;
  var key;
  var i = 0;

  for (; i < il; i += 1) {
    // eslint-disable-next-line no-restricted-syntax
    for (key in rest[i]) {
      if (rest[i].hasOwnProperty(key)) {
        if (_typeof(obj[key]) === 'object' && _typeof(rest[i][key]) === 'object' && !Array.isArray(obj[key]) && !Array.isArray(rest[i][key])) {
          obj[key] = _objectSpread(_objectSpread({}, obj[key]), rest[i][key]);
        } else {
          obj[key] = rest[i][key];
        }
      }
    }
  }

  return obj;
};



/***/ }),

/***/ "./node_modules/@ant-design/pro-utils/es/omitUndefined/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@ant-design/pro-utils/es/omitUndefined/index.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
var omitUndefined = function omitUndefined(obj) {
  var newObj = {};
  Object.keys(obj || {}).forEach(function (key) {
    if (obj[key] !== undefined) {
      newObj[key] = obj[key];
    }
  });

  if (Object.keys(newObj).length < 1) {
    return undefined;
  }

  return newObj;
};

/* harmony default export */ __webpack_exports__["default"] = (omitUndefined);

/***/ }),

/***/ "./node_modules/@ant-design/pro-utils/es/useMountMergeState/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-utils/es/useMountMergeState/index.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ "./node_modules/rc-util/es/hooks/useMergedState.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }




function useMountMergeState(initialState, option) {
  var mountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);
  var frame = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    mountRef.current = true;
    return function () {
      mountRef.current = false;
    };
  });

  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_0__.default)(initialState, option),
      _useMergedState2 = _slicedToArray(_useMergedState, 2),
      state = _useMergedState2[0],
      setState = _useMergedState2[1];

  var mountSetState = function mountSetState(prevState) {
    cancelAnimationFrame(frame.current);
    frame.current = requestAnimationFrame(function () {
      if (mountRef.current) {
        setState(prevState);
      }
    });
  };

  return [state, mountSetState];
}

/* harmony default export */ __webpack_exports__["default"] = (useMountMergeState);

/***/ }),

/***/ "./node_modules/antd/es/avatar/style/index.less":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/avatar/style/index.less ***!
  \******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/button/style/index.less":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/button/style/index.less ***!
  \******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/config-provider/style/index.less":
/*!***************************************************************!*\
  !*** ./node_modules/antd/es/config-provider/style/index.less ***!
  \***************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/divider/style/index.less":
/*!*******************************************************!*\
  !*** ./node_modules/antd/es/divider/style/index.less ***!
  \*******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/drawer/style/index.less":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/drawer/style/index.less ***!
  \******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/dropdown/style/index.less":
/*!********************************************************!*\
  !*** ./node_modules/antd/es/dropdown/style/index.less ***!
  \********************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/empty/style/index.less":
/*!*****************************************************!*\
  !*** ./node_modules/antd/es/empty/style/index.less ***!
  \*****************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/grid/style/index.less":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/grid/style/index.less ***!
  \****************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/popover/style/index.less":
/*!*******************************************************!*\
  !*** ./node_modules/antd/es/popover/style/index.less ***!
  \*******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/select/style/index.less":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/select/style/index.less ***!
  \******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/spin/style/index.less":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/spin/style/index.less ***!
  \****************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/style/index.less":
/*!***********************************************!*\
  !*** ./node_modules/antd/es/style/index.less ***!
  \***********************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/switch/style/index.less":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/switch/style/index.less ***!
  \******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/tooltip/style/index.less":
/*!*******************************************************!*\
  !*** ./node_modules/antd/es/tooltip/style/index.less ***!
  \*******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@umijs/use-params/es/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@umijs/use-params/es/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "useUrlSearchParams": function() { return /* binding */ useUrlSearchParams; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __assign = (undefined && undefined.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
/* eslint-disable no-restricted-syntax */

/**
 *
 * @param {object} params
 * @returns {URL}
 */
function setQueryToCurrentUrl(params) {
    var _a;
    var URL = (typeof window !== 'undefined' ? window : {}).URL;
    var url = new URL((_a = window === null || window === void 0 ? void 0 : window.location) === null || _a === void 0 ? void 0 : _a.href);
    Object.keys(params).forEach(function (key) {
        var value = params[key];
        if (value !== null && value !== undefined) {
            if (Array.isArray(value)) {
                url.searchParams.delete(key);
                value.forEach(function (valueItem) {
                    url.searchParams.append(key, valueItem);
                });
            }
            else if (value instanceof Date) {
                if (!Number.isNaN(value.getTime())) {
                    url.searchParams.set(key, value.toISOString());
                }
            }
            else if (typeof value === 'object') {
                url.searchParams.set(key, JSON.stringify(value));
            }
            else {
                url.searchParams.set(key, value);
            }
        }
        else {
            url.searchParams.delete(key);
        }
    });
    return url;
}
function useUrlSearchParams(initial, config) {
    var _a;
    if (initial === void 0) { initial = {}; }
    if (config === void 0) { config = { disabled: false }; }
    /**
     * The main idea of this hook is to make things response to change of `window.location.search`,
     * so no need for introducing new state (in the mean time).
     * Whenever `window.location.search` is changed but  not cause re-render, call `forceUpdate()`.
     * Whenever the component - user of this hook - re-render, this hook should return
     * the query object that corresponse to the current `window.location.search`
     */
    var _b = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(), forceUpdate = _b[1];
    var locationSearch = typeof window !== 'undefined' && ((_a = window === null || window === void 0 ? void 0 : window.location) === null || _a === void 0 ? void 0 : _a.search);
    /**
     * @type {URLSearchParams}
     */
    var urlSearchParams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
        if (config.disabled)
            return {};
        return new URLSearchParams(locationSearch || {});
    }, [config.disabled, locationSearch]);
    var params = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
        if (config.disabled)
            return {};
        if (typeof window === 'undefined' || !window.URL)
            return {};
        var result = [];
        // @ts-ignore
        urlSearchParams.forEach(function (value, key) {
            result.push({
                key: key,
                value: value,
            });
        });
        // group by key
        result = result.reduce(function (acc, val) {
            (acc[val.key] = acc[val.key] || []).push(val);
            return acc;
        }, {});
        result = Object.keys(result).map(function (key) {
            var valueGroup = result[key];
            if (valueGroup.length === 1) {
                return [key, valueGroup[0].value];
            }
            return [key, valueGroup.map(function (_a) {
                    var value = _a.value;
                    return value;
                })];
        });
        var newParams = __assign({}, initial);
        result.forEach(function (_a) {
            var key = _a[0], value = _a[1];
            newParams[key] = parseValue(key, value, {}, initial);
        });
        return newParams;
    }, [config.disabled, initial, urlSearchParams]);
    function redirectToNewSearchParams(newParams) {
        if (typeof window === 'undefined' || !window.URL)
            return;
        var url = setQueryToCurrentUrl(newParams);
        if (window.location.search !== url.search) {
            window.history.replaceState({}, '', url.toString());
        }
        if (urlSearchParams.toString() !== url.searchParams.toString()) {
            forceUpdate({});
        }
    }
    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
        if (config.disabled)
            return;
        if (typeof window === 'undefined' || !window.URL)
            return;
        redirectToNewSearchParams(__assign(__assign({}, initial), params));
    }, [config.disabled, params]);
    var setParams = function (newParams) {
        redirectToNewSearchParams(newParams);
    };
    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
        if (config.disabled)
            return function () { };
        if (typeof window === 'undefined' || !window.URL)
            return function () { };
        var onPopState = function () {
            forceUpdate({});
        };
        window.addEventListener('popstate', onPopState);
        return function () {
            window.removeEventListener('popstate', onPopState);
        };
    }, [config.disabled]);
    return [params, setParams];
}
var booleanValues = {
    true: true,
    false: false,
};
function parseValue(key, _value, types, defaultParams) {
    if (!types)
        return _value;
    var type = types[key];
    var value = _value === undefined ? defaultParams[key] : _value;
    if (type === Number) {
        return Number(value);
    }
    if (type === Boolean || _value === 'true' || _value === 'false') {
        return booleanValues[value];
    }
    if (Array.isArray(type)) {
        // eslint-disable-next-line eqeqeq
        return type.find(function (item) { return item == value; }) || defaultParams[key];
    }
    return value;
}


/***/ }),

/***/ "./node_modules/antd/es/avatar/style/index.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/avatar/style/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/avatar/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _popover_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../popover/style */ "./node_modules/antd/es/popover/style/index.js");

 // style dependencies
// deps-lint-skip: grid



/***/ }),

/***/ "./node_modules/antd/es/button/style/index.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/button/style/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/button/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/calendar/locale/zh_CN.js":
/*!*******************************************************!*\
  !*** ./node_modules/antd/es/calendar/locale/zh_CN.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _date_picker_locale_zh_CN__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../date-picker/locale/zh_CN */ "./node_modules/antd/es/date-picker/locale/zh_CN.js");

/* harmony default export */ __webpack_exports__["default"] = (_date_picker_locale_zh_CN__WEBPACK_IMPORTED_MODULE_0__.default);

/***/ }),

/***/ "./node_modules/antd/es/config-provider/style/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/antd/es/config-provider/style/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/config-provider/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_0__);


/***/ }),

/***/ "./node_modules/antd/es/date-picker/locale/zh_CN.js":
/*!**********************************************************!*\
  !*** ./node_modules/antd/es/date-picker/locale/zh_CN.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var rc_picker_es_locale_zh_CN__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-picker/es/locale/zh_CN */ "./node_modules/rc-picker/es/locale/zh_CN.js");
/* harmony import */ var _time_picker_locale_zh_CN__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../time-picker/locale/zh_CN */ "./node_modules/antd/es/time-picker/locale/zh_CN.js");


 // 统一合并为完整的 Locale

var locale = {
  lang: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    placeholder: '请选择日期',
    yearPlaceholder: '请选择年份',
    quarterPlaceholder: '请选择季度',
    monthPlaceholder: '请选择月份',
    weekPlaceholder: '请选择周',
    rangePlaceholder: ['开始日期', '结束日期'],
    rangeYearPlaceholder: ['开始年份', '结束年份'],
    rangeMonthPlaceholder: ['开始月份', '结束月份'],
    rangeWeekPlaceholder: ['开始周', '结束周']
  }, rc_picker_es_locale_zh_CN__WEBPACK_IMPORTED_MODULE_1__.default),
  timePickerLocale: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, _time_picker_locale_zh_CN__WEBPACK_IMPORTED_MODULE_2__.default)
}; // should add whitespace between char in Button

locale.lang.ok = '确 定'; // All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json

/* harmony default export */ __webpack_exports__["default"] = (locale);

/***/ }),

/***/ "./node_modules/antd/es/divider/style/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/antd/es/divider/style/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/divider/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/drawer/style/index.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/drawer/style/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/drawer/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);
// deps-lint-skip: empty



/***/ }),

/***/ "./node_modules/antd/es/dropdown/style/index.js":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/dropdown/style/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/dropdown/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _button_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../button/style */ "./node_modules/antd/es/button/style/index.js");

 // style dependencies



/***/ }),

/***/ "./node_modules/antd/es/empty/style/index.js":
/*!***************************************************!*\
  !*** ./node_modules/antd/es/empty/style/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/empty/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/grid/style/index.js":
/*!**************************************************!*\
  !*** ./node_modules/antd/es/grid/style/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/grid/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/locale/zh_CN.js":
/*!**********************************************!*\
  !*** ./node_modules/antd/es/locale/zh_CN.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var rc_pagination_es_locale_zh_CN__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-pagination/es/locale/zh_CN */ "./node_modules/rc-pagination/es/locale/zh_CN.js");
/* harmony import */ var _date_picker_locale_zh_CN__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../date-picker/locale/zh_CN */ "./node_modules/antd/es/date-picker/locale/zh_CN.js");
/* harmony import */ var _time_picker_locale_zh_CN__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../time-picker/locale/zh_CN */ "./node_modules/antd/es/time-picker/locale/zh_CN.js");
/* harmony import */ var _calendar_locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../calendar/locale/zh_CN */ "./node_modules/antd/es/calendar/locale/zh_CN.js");
/* eslint-disable no-template-curly-in-string */




var typeTemplate = '${label}不是一个有效的${type}';
var localeValues = {
  locale: 'zh-cn',
  Pagination: rc_pagination_es_locale_zh_CN__WEBPACK_IMPORTED_MODULE_0__.default,
  DatePicker: _date_picker_locale_zh_CN__WEBPACK_IMPORTED_MODULE_1__.default,
  TimePicker: _time_picker_locale_zh_CN__WEBPACK_IMPORTED_MODULE_2__.default,
  Calendar: _calendar_locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__.default,
  // locales for all components
  global: {
    placeholder: '请选择'
  },
  Table: {
    filterTitle: '筛选',
    filterConfirm: '确定',
    filterReset: '重置',
    filterEmptyText: '无筛选项',
    selectAll: '全选当页',
    selectInvert: '反选当页',
    selectNone: '清空所有',
    selectionAll: '全选所有',
    sortTitle: '排序',
    expand: '展开行',
    collapse: '关闭行',
    triggerDesc: '点击降序',
    triggerAsc: '点击升序',
    cancelSort: '取消排序'
  },
  Modal: {
    okText: '确定',
    cancelText: '取消',
    justOkText: '知道了'
  },
  Popconfirm: {
    cancelText: '取消',
    okText: '确定'
  },
  Transfer: {
    searchPlaceholder: '请输入搜索内容',
    itemUnit: '项',
    itemsUnit: '项',
    remove: '删除',
    selectCurrent: '全选当页',
    removeCurrent: '删除当页',
    selectAll: '全选所有',
    removeAll: '删除全部',
    selectInvert: '反选当页'
  },
  Upload: {
    uploading: '文件上传中',
    removeFile: '删除文件',
    uploadError: '上传错误',
    previewFile: '预览文件',
    downloadFile: '下载文件'
  },
  Empty: {
    description: '暂无数据'
  },
  Icon: {
    icon: '图标'
  },
  Text: {
    edit: '编辑',
    copy: '复制',
    copied: '复制成功',
    expand: '展开'
  },
  PageHeader: {
    back: '返回'
  },
  Form: {
    optional: '（可选）',
    defaultValidateMessages: {
      "default": '字段验证错误${label}',
      required: '请输入${label}',
      "enum": '${label}必须是其中一个[${enum}]',
      whitespace: '${label}不能为空字符',
      date: {
        format: '${label}日期格式无效',
        parse: '${label}不能转换为日期',
        invalid: '${label}是一个无效日期'
      },
      types: {
        string: typeTemplate,
        method: typeTemplate,
        array: typeTemplate,
        object: typeTemplate,
        number: typeTemplate,
        date: typeTemplate,
        "boolean": typeTemplate,
        integer: typeTemplate,
        "float": typeTemplate,
        regexp: typeTemplate,
        email: typeTemplate,
        url: typeTemplate,
        hex: typeTemplate
      },
      string: {
        len: '${label}须为${len}个字符',
        min: '${label}最少${min}个字符',
        max: '${label}最多${max}个字符',
        range: '${label}须在${min}-${max}字符之间'
      },
      number: {
        len: '${label}必须等于${len}',
        min: '${label}最小值为${min}',
        max: '${label}最大值为${max}',
        range: '${label}须在${min}-${max}之间'
      },
      array: {
        len: '须为${len}个${label}',
        min: '最少${min}个${label}',
        max: '最多${max}个${label}',
        range: '${label}数量须在${min}-${max}之间'
      },
      pattern: {
        mismatch: '${label}与模式不匹配${pattern}'
      }
    }
  },
  Image: {
    preview: '预览'
  }
};
/* harmony default export */ __webpack_exports__["default"] = (localeValues);

/***/ }),

/***/ "./node_modules/antd/es/popover/style/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/antd/es/popover/style/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/popover/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);

 // style dependencies
// deps-lint-skip: tooltip

/***/ }),

/***/ "./node_modules/antd/es/select/style/index.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/select/style/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/select/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _empty_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../empty/style */ "./node_modules/antd/es/empty/style/index.js");

 // style dependencies



/***/ }),

/***/ "./node_modules/antd/es/spin/style/index.js":
/*!**************************************************!*\
  !*** ./node_modules/antd/es/spin/style/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/spin/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/switch/style/index.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/switch/style/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/switch/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/time-picker/locale/zh_CN.js":
/*!**********************************************************!*\
  !*** ./node_modules/antd/es/time-picker/locale/zh_CN.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
var locale = {
  placeholder: '请选择时间',
  rangePlaceholder: ['开始时间', '结束时间']
};
/* harmony default export */ __webpack_exports__["default"] = (locale);

/***/ }),

/***/ "./node_modules/antd/es/tooltip/style/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/antd/es/tooltip/style/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/tooltip/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/omit.js/es/index.js":
/*!******************************************!*\
  !*** ./node_modules/omit.js/es/index.js ***!
  \******************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
function omit(obj, fields) {
  // eslint-disable-next-line prefer-object-spread
  var shallowCopy = Object.assign({}, obj);

  for (var i = 0; i < fields.length; i += 1) {
    var key = fields[i];
    delete shallowCopy[key];
  }

  return shallowCopy;
}

/* harmony default export */ __webpack_exports__["default"] = (omit);

/***/ }),

/***/ "./node_modules/rc-picker/es/locale/zh_CN.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-picker/es/locale/zh_CN.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
var locale = {
  locale: 'zh_CN',
  today: '今天',
  now: '此刻',
  backToToday: '返回今天',
  ok: '确定',
  timeSelect: '选择时间',
  dateSelect: '选择日期',
  weekSelect: '选择周',
  clear: '清除',
  month: '月',
  year: '年',
  previousMonth: '上个月 (翻页上键)',
  nextMonth: '下个月 (翻页下键)',
  monthSelect: '选择月份',
  yearSelect: '选择年份',
  decadeSelect: '选择年代',
  yearFormat: 'YYYY年',
  dayFormat: 'D日',
  dateFormat: 'YYYY年M月D日',
  dateTimeFormat: 'YYYY年M月D日 HH时mm分ss秒',
  previousYear: '上一年 (Control键加左方向键)',
  nextYear: '下一年 (Control键加右方向键)',
  previousDecade: '上一年代',
  nextDecade: '下一年代',
  previousCentury: '上一世纪',
  nextCentury: '下一世纪'
};
/* harmony default export */ __webpack_exports__["default"] = (locale);

/***/ }),

/***/ "./node_modules/swr/dist/index.esm.js":
/*!********************************************!*\
  !*** ./node_modules/swr/dist/index.esm.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "SWRConfig": function() { return /* binding */ _; },
/* harmony export */   "default": function() { return /* binding */ ne; },
/* harmony export */   "mutate": function() { return /* binding */ z; },
/* harmony export */   "unstable_serialize": function() { return /* binding */ ee; },
/* harmony export */   "useSWRConfig": function() { return /* binding */ X; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
function f(e,n,t,r){return new(t||(t=Promise))(function(i,o){function u(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var n;e.done?i(e.value):(n=e.value,n instanceof t?n:new t(function(e){e(n)})).then(u,a)}c((r=r.apply(e,n||[])).next())})}function s(e,n){var t,r,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(i=u.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){u.label=o[1];break}if(6===o[0]&&u.label<i[1]){u.label=i[1],i=o;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(o);break}i[2]&&u.ops.pop(),u.trys.pop();continue}o=n.call(e,u)}catch(e){o=[6,e],r=0}finally{t=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var l,d=function(){},v=d(),h=Object,g=function(e){return e===v},p=function(e){return"function"==typeof e},y=function(e,n){return h.assign({},e,n)},b="undefined"!=typeof window,w="undefined"!=typeof document,m=new WeakMap,O=0,k=function(e){var n,t,r=typeof e,i=e&&e.constructor,o=i==Date;if(h(e)!==e||o||i==RegExp)n=o?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(n=m.get(e))return n;if(n=++O+"~",m.set(e,n),i==Array){for(n="@",t=0;t<e.length;t++)n+=k(e[t])+",";m.set(e,n)}if(i==h){n="#";for(var u=h.keys(e).sort();!g(t=u.pop());)g(e[t])||(n+=t+":"+k(e[t])+",");m.set(e,n)}}return n},T=!0,E=b?window.addEventListener.bind(window):d,R=w?document.addEventListener.bind(document):d,S=b&&removeEventListener||d,V=w?document.removeEventListener.bind(document):d,I={isOnline:function(){return T},isVisible:function(){var e=w&&document.visibilityState;return!!g(e)||"hidden"!==e}},x={initFocus:function(e){return R("visibilitychange",e),E("focus",e),function(){V("visibilitychange",e),S("focus",e)}},initReconnect:function(e){var n=function(){T=!0,e()},t=function(){T=!1};return E("online",n),E("offline",t),function(){S("online",n),S("offline",t)}}},D=!b||"Deno"in window,P=b&&window.requestAnimationFrame||function(e){return setTimeout(e,1)},L=D?react__WEBPACK_IMPORTED_MODULE_0__.useEffect:react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect,F="undefined"!=typeof navigator&&navigator.connection,M=!D&&F&&(["slow-2g","2g"].includes(F.effectiveType)||F.saveData),A=function(e){if(p(e))try{e=e()}catch(n){e=""}var n=[].concat(e);return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?k(e):"",n,e?"$err$"+e:"",e?"$req$"+e:""]},C=new WeakMap,W=function(e,n,t,r,i,o){for(var u=C.get(e),a=u[0],c=u[1],f=u[4],s=u[5],l=a[n],d=c[n]||[],v=0;v<d.length;++v)d[v](t,r,i);return o&&(delete f[n],delete s[n],l&&l[0])?l[0](2).then(function(){return e.get(n)}):e.get(n)},$=0,q=function(){return++$},J=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return f(void 0,void 0,void 0,function(){var n,t,r,i,o,u,a,c,f,l,d,h,g,y;return s(this,function(s){switch(s.label){case 0:if(n=e[0],t=e[1],r=!1!==e[3],i=e[2],o=A(t),u=o[0],a=o[2],!u)return[2];if(c=C.get(n),f=c[2],l=c[3],e.length<3)return[2,W(n,u,n.get(u),n.get(a),v,r)];if(g=f[u]=q(),l[u]=0,p(i))try{i=i(n.get(u))}catch(e){h=e}return i&&p(i.then)?[4,i.catch(function(e){h=e})]:[3,2];case 1:if(d=s.sent(),g!==f[u]){if(h)throw h;return[2,d]}return[3,3];case 2:d=i,s.label=3;case 3:return h||n.set(u,d),n.set(a,h),l[u]=q(),[4,W(n,u,d,h,v,r)];case 4:if(y=s.sent(),h)throw h;return[2,y]}})})},N=function(e,n){for(var t in e)e[t][0]&&e[t][0](n)},j=function(e,n){if(!C.has(e)){var t=y(x,n),r={},i=J.bind(v,e),o=d;if(C.set(e,[r,{},{},{},{},{},i]),!D){var u=t.initFocus(N.bind(v,r,0)),a=t.initReconnect(N.bind(v,r,1));o=function(){u&&u(),a&&a(),C.delete(e)}}return[e,i,o]}return[e,C.get(e)[6]]},G=j(new Map),H=G[0],z=G[1],B=y({onLoadingSlow:d,onSuccess:d,onError:d,onErrorRetry:function(e,n,t,r,i){if(I.isVisible()){var o=t.errorRetryCount,u=i.retryCount,a=~~((Math.random()+.5)*(1<<(u<8?u:8)))*t.errorRetryInterval;!g(o)&&u>o||setTimeout(r,a,i)}},onDiscarded:d,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:M?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:M?5e3:3e3,compare:function(e,n){return k(e)==k(n)},isPaused:function(){return!1},cache:H,mutate:z,fallback:{}},I),K=function(e,n){var t=y(e,n);if(n){var r=e.use,i=e.fallback,o=n.use,u=n.fallback;r&&o&&(t.use=r.concat(o)),i&&u&&(t.fallback=y(i,u))}return t},Q=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({}),U=function(e){return p(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}]},X=function(){return y(B,(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Q))},Y=function(e,n,t){var r=n[e]||(n[e]=[]);return r.push(t),function(){var e=r.indexOf(t);e>=0&&(r[e]=r[r.length-1],r.pop())}},Z={dedupe:!0},_=h.defineProperty(function(e){var n=e.value,t=K((0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Q),n),u=n&&n.provider,a=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function(){return u?j(u(t.cache||H),n):v})[0];return a&&(t.cache=a[0],t.mutate=a[1]),L(function(){return a?a[2]:v},[]),(0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Q.Provider,y(e,{value:t}))},"default",{value:B}),ee=function(e){return A(e)[0]},ne=(l=function(e,n,t){var r=t.cache,o=t.compare,l=t.fallbackData,d=t.suspense,h=t.revalidateOnMount,p=t.refreshInterval,b=t.refreshWhenHidden,w=t.refreshWhenOffline,m=C.get(r),O=m[0],k=m[1],T=m[2],E=m[3],R=m[4],S=m[5],V=A(e),I=V[0],x=V[1],F=V[2],M=V[3],$=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1),N=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1),j=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(I),G=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(t),H=function(){return G.current},z=r.get(I),B=g(l)?t.fallback[I]:l,K=g(z)?B:z,Q=r.get(F),U=function(){return g(h)?!H().isPaused()&&(d?!g(K):g(K)||t.revalidateIfStale):h},X=!(!I||!n)&&(!!r.get(M)||!$.current&&U()),_=function(e,n){var t=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({})[1],r=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(e),o=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({data:!1,error:!1,isValidating:!1}),c=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(e){var i=!1,u=r.current;for(var a in e){var c=a;u[c]!==e[c]&&(u[c]=e[c],o.current[c]&&(i=!0))}i&&!n.current&&t({})},[]);return L(function(){r.current=e}),[r,o.current,c]}({data:K,error:Q,isValidating:X},N),ee=_[0],ne=_[1],te=_[2],re=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(e){return f(void 0,void 0,void 0,function(){var i,u,a,c,f,l,d,h,p,y;return s(this,function(s){switch(s.label){case 0:if(!I||!n||N.current||H().isPaused())return[2,!1];a=!0,c=e||{},f=g(R[I])||!c.dedupe,l=function(){return!N.current&&I===j.current&&$.current},d=function(){S[I]===u&&(delete R[I],delete S[I])},h={isValidating:!1},p=function(){r.set(M,!1),l()&&te(h)},r.set(M,!0),te({isValidating:!0}),s.label=1;case 1:return s.trys.push([1,3,,4]),f&&(W(r,I,ee.current.data,ee.current.error,!0),t.loadingTimeout&&!r.get(I)&&setTimeout(function(){a&&l()&&H().onLoadingSlow(I,t)},t.loadingTimeout),S[I]=q(),R[I]=n.apply(void 0,x)),u=S[I],[4,R[I]];case 2:return i=s.sent(),f&&setTimeout(d,t.dedupingInterval),S[I]!==u?(f&&l()&&H().onDiscarded(I),[2,!1]):(r.set(F,v),h.error=v,!g(T[I])&&(u<=T[I]||u<=E[I]||0===E[I])?(p(),f&&l()&&H().onDiscarded(I),[2,!1]):(o(ee.current.data,i)||(h.data=i),o(r.get(I),i)||r.set(I,i),f&&l()&&H().onSuccess(i,I,t),[3,4]));case 3:return y=s.sent(),d(),H().isPaused()||(r.set(F,y),h.error=y,f&&l()&&(H().onError(y,I,t),t.shouldRetryOnError&&H().onErrorRetry(y,I,t,re,{retryCount:(c.retryCount||0)+1,dedupe:!0}))),[3,4];case 4:return a=!1,p(),l()&&f&&W(r,I,h.data,h.error,!1),[2,!0]}})})},[I]),ie=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(J.bind(v,r,function(){return j.current}),[]);if(L(function(){G.current=t}),L(function(){if(I){var e=$.current,n=re.bind(v,Z),t=function(){return H().isVisible()&&H().isOnline()},r=0,i=Y(I,k,function(e,n,t){te(y({error:n,isValidating:t},o(ee.current.data,e)?v:{data:e}))}),u=Y(I,O,function(e){if(0==e){var i=Date.now();H().revalidateOnFocus&&i>r&&t()&&(r=i+H().focusThrottleInterval,n())}else if(1==e)H().revalidateOnReconnect&&t()&&n();else if(2==e)return re()});return N.current=!1,j.current=I,$.current=!0,e&&te({data:K,error:Q,isValidating:X}),U()&&(g(K)||D?n():P(n)),function(){N.current=!0,i(),u()}}},[I,re]),L(function(){var e;function n(){p&&-1!==e&&(e=setTimeout(t,p))}function t(){ee.current.error||!b&&!H().isVisible()||!w&&!H().isOnline()?n():re(Z).then(n)}return n(),function(){e&&(clearTimeout(e),e=-1)}},[p,b,w,re]),(0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(K),d&&g(K))throw g(Q)?re(Z):Q;return{mutate:ie,get data(){return ne.data=!0,K},get error(){return ne.error=!0,Q},get isValidating(){return ne.isValidating=!0,X}}},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=X(),r=U(e),i=r[0],o=r[1],u=r[2],a=K(t,u),c=l,f=a.use;if(f)for(var s=f.length;s-- >0;)c=f[s](c);return c(i,o||a.fetcher,a)});


/***/ })

}]);