(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_babel_runtime_helpers_esm_objectWithoutPropertiesLoose_js-node_modules_b-7797da"],{

/***/ "./node_modules/@babel/runtime/helpers/esm/extends.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/esm/extends.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ _extends; }
/* harmony export */ });
function _extends() {
  _extends = Object.assign || function (target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];

      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }

    return target;
  };

  return _extends.apply(this, arguments);
}

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ _objectWithoutPropertiesLoose; }
/* harmony export */ });
function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null) return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;

  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0) continue;
    target[key] = source[key];
  }

  return target;
}

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ _setPrototypeOf; }
/* harmony export */ });
function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {
    o.__proto__ = p;
    return o;
  };

  return _setPrototypeOf(o, p);
}

/***/ }),

/***/ "./node_modules/@umijs/renderer-react/dist/index.esm.js":
/*!**************************************************************!*\
  !*** ./node_modules/@umijs/renderer-react/dist/index.esm.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "renderClient": function() { return /* binding */ renderClient; },
/* harmony export */   "renderRoutes": function() { return /* binding */ renderRoutes; }
/* harmony export */ });
/* harmony import */ var _umijs_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @umijs/runtime */ "./node_modules/react-router/esm/react-router.js");
/* harmony import */ var _umijs_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @umijs/runtime */ "./node_modules/@umijs/runtime/dist/index.esm.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ "./node_modules/react-dom/index.js");
/* harmony import */ var react_router_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-router-config */ "./node_modules/@umijs/renderer-react/node_modules/react-router-config/esm/react-router-config.js");





function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);

  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);

    if (enumerableOnly) {
      symbols = symbols.filter(function (sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    }

    keys.push.apply(keys, symbols);
  }

  return keys;
}

function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};

    if (i % 2) {
      ownKeys(Object(source), true).forEach(function (key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function (key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }

  return target;
}

function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
  try {
    var info = gen[key](arg);
    var value = info.value;
  } catch (error) {
    reject(error);
    return;
  }

  if (info.done) {
    resolve(value);
  } else {
    Promise.resolve(value).then(_next, _throw);
  }
}

function _asyncToGenerator(fn) {
  return function () {
    var self = this,
        args = arguments;
    return new Promise(function (resolve, reject) {
      var gen = fn.apply(self, args);

      function _next(value) {
        asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
      }

      function _throw(err) {
        asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
      }

      _next(undefined);
    });
  };
}

function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }

  return obj;
}

function _extends() {
  _extends = Object.assign || function (target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];

      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }

    return target;
  };

  return _extends.apply(this, arguments);
}

function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null) return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;

  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0) continue;
    target[key] = source[key];
  }

  return target;
}

function _objectWithoutProperties(source, excluded) {
  if (source == null) return {};

  var target = _objectWithoutPropertiesLoose(source, excluded);

  var key, i;

  if (Object.getOwnPropertySymbols) {
    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);

    for (i = 0; i < sourceSymbolKeys.length; i++) {
      key = sourceSymbolKeys[i];
      if (excluded.indexOf(key) >= 0) continue;
      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
      target[key] = source[key];
    }
  }

  return target;
}

function _slicedToArray(arr, i) {
  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();
}

function _arrayWithHoles(arr) {
  if (Array.isArray(arr)) return arr;
}

function _iterableToArrayLimit(arr, i) {
  var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];

  if (_i == null) return;
  var _arr = [];
  var _n = true;
  var _d = false;

  var _s, _e;

  try {
    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {
      _arr.push(_s.value);

      if (i && _arr.length === i) break;
    }
  } catch (err) {
    _d = true;
    _e = err;
  } finally {
    try {
      if (!_n && _i["return"] != null) _i["return"]();
    } finally {
      if (_d) throw _e;
    }
  }

  return _arr;
}

function _unsupportedIterableToArray(o, minLen) {
  if (!o) return;
  if (typeof o === "string") return _arrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor) n = o.constructor.name;
  if (n === "Map" || n === "Set") return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}

function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length) len = arr.length;

  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];

  return arr2;
}

function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

function _createForOfIteratorHelper(o, allowArrayLike) {
  var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];

  if (!it) {
    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
      if (it) o = it;
      var i = 0;

      var F = function () {};

      return {
        s: F,
        n: function () {
          if (i >= o.length) return {
            done: true
          };
          return {
            done: false,
            value: o[i++]
          };
        },
        e: function (e) {
          throw e;
        },
        f: F
      };
    }

    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }

  var normalCompletion = true,
      didErr = false,
      err;
  return {
    s: function () {
      it = it.call(o);
    },
    n: function () {
      var step = it.next();
      normalCompletion = step.done;
      return step;
    },
    e: function (e) {
      didErr = true;
      err = e;
    },
    f: function () {
      try {
        if (!normalCompletion && it.return != null) it.return();
      } finally {
        if (didErr) throw err;
      }
    }
  };
}

function Route(props) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_umijs_runtime__WEBPACK_IMPORTED_MODULE_2__.__RouterContext.Consumer, null, function (context) {
    var location = props.location || context.location;
    var match = props.computedMatch;

    var newProps = _objectSpread2(_objectSpread2({}, context), {}, {
      location: location,
      match: match
    });

    var render = props.render;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_umijs_runtime__WEBPACK_IMPORTED_MODULE_2__.__RouterContext.Provider, {
      value: newProps
    }, newProps.match ? render(_objectSpread2(_objectSpread2({}, props.layoutProps), newProps)) : null);
  });
}

var _excluded = ["children"];
function Switch(props) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_umijs_runtime__WEBPACK_IMPORTED_MODULE_2__.__RouterContext.Consumer, null, function (context) {
    var children = props.children,
        extraProps = _objectWithoutProperties(props, _excluded);

    var location = props.location || context.location;
    var element,
        match = null;
    react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, function (child) {
      if (match === null && /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) {
        element = child;
        var path = child.props.path || child.props.from;
        match = path ? (0,_umijs_runtime__WEBPACK_IMPORTED_MODULE_2__.matchPath)(location.pathname, _objectSpread2(_objectSpread2({}, child.props), {}, {
          path: path
        })) : context.match;
      }
    });
    return match ? /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(element, {
      location: location,
      computedMatch: match,
      layoutProps: extraProps
    }) : null;
  });
}

var _excluded$1 = ["component"];

function wrapInitialPropsFetch(route, opts) {
  var component = route.component,
      restRouteParams = _objectWithoutProperties(route, _excluded$1);

  var Component = route.component;

  function ComponentWithInitialPropsFetch(props) {
    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {
      return window.g_initialProps;
    }),
        _useState2 = _slicedToArray(_useState, 2),
        initialProps = _useState2[0],
        setInitialProps = _useState2[1];

    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
      /**
       * 1. 首次渲染时，此时 window.g_initialProps 变量存在，不需要再走一次 getInitialProps，这样一次 SSR 就走了 2 次 getInitialProps
       * 2. 但是路由切换时，window.getInitialProps 会被赋为 null，这时候就走 getInitialProps 逻辑
       * 3. 如果任何时候都走 2 次，配置 forceInitial: true，这个场景用于静态站点的首屏加载希望走最新数据
       * 4. 开启动态加载后，会在执行 getInitialProps 前预加载下
       */
      var handleGetInitialProps = /*#__PURE__*/function () {
        var _ref = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee() {
          var _preloadComponent;

          var preloadComponent, defaultCtx, ctx, _initialProps;

          return regeneratorRuntime.wrap(function _callee$(_context) {
            while (1) {
              switch (_context.prev = _context.next) {
                case 0:
                  // preload when enalbe dynamicImport
                  preloadComponent = Component;

                  if (!Component.preload) {
                    _context.next = 6;
                    break;
                  }

                  _context.next = 4;
                  return Component.preload();

                case 4:
                  preloadComponent = _context.sent;
                  // for test case, really use .default
                  preloadComponent = preloadComponent.default || preloadComponent;

                case 6:
                  defaultCtx = _objectSpread2(_objectSpread2({
                    isServer: false,
                    match: props === null || props === void 0 ? void 0 : props.match,
                    history: props === null || props === void 0 ? void 0 : props.history,
                    route: route
                  }, opts.getInitialPropsCtx || {}), restRouteParams);

                  if (!((_preloadComponent = preloadComponent) === null || _preloadComponent === void 0 ? void 0 : _preloadComponent.getInitialProps)) {
                    _context.next = 15;
                    break;
                  }

                  _context.next = 10;
                  return opts.plugin.applyPlugins({
                    key: 'ssr.modifyGetInitialPropsCtx',
                    type: _umijs_runtime__WEBPACK_IMPORTED_MODULE_3__.ApplyPluginsType.modify,
                    initialValue: defaultCtx,
                    async: true
                  });

                case 10:
                  ctx = _context.sent;
                  _context.next = 13;
                  return preloadComponent.getInitialProps(ctx || defaultCtx);

                case 13:
                  _initialProps = _context.sent;
                  setInitialProps(_initialProps);

                case 15:
                case "end":
                  return _context.stop();
              }
            }
          }, _callee);
        }));

        return function handleGetInitialProps() {
          return _ref.apply(this, arguments);
        };
      }(); // null 时，一定会触发 getInitialProps 执行


      if (!window.g_initialProps) {
        handleGetInitialProps();
      }
    }, [window.location.pathname, window.location.search]);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Component, _extends({}, props, initialProps));
  } // flag for having wrappered


  ComponentWithInitialPropsFetch.wrapInitialPropsLoaded = true;
  ComponentWithInitialPropsFetch.displayName = 'ComponentWithInitialPropsFetch';
  return ComponentWithInitialPropsFetch;
}

function _render(_ref2) {
  var route = _ref2.route,
      opts = _ref2.opts,
      props = _ref2.props;
  var routes = renderRoutes(_objectSpread2(_objectSpread2({}, opts), {}, {
    routes: route.routes || [],
    rootRoutes: opts.rootRoutes
  }), {
    location: props.location
  });
  var Component = route.component,
      wrappers = route.wrappers;

  if (Component) {
    var defaultPageInitialProps = opts.isServer ? {} : window.g_initialProps;

    var newProps = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, props), opts.extraProps), opts.pageInitialProps || defaultPageInitialProps), {}, {
      route: route,
      routes: opts.rootRoutes
    }); // @ts-ignore


    var ret = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Component, newProps, routes); // route.wrappers

    if (wrappers) {
      var len = wrappers.length - 1;

      while (len >= 0) {
        ret = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(wrappers[len], newProps, ret);
        len -= 1;
      }
    }

    return ret;
  } else {
    return routes;
  }
}

function getRouteElement(_ref3) {
  var route = _ref3.route,
      index = _ref3.index,
      opts = _ref3.opts;
  var routeProps = {
    key: route.key || index,
    exact: route.exact,
    strict: route.strict,
    sensitive: route.sensitive,
    path: route.path
  };

  if (route.redirect) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_umijs_runtime__WEBPACK_IMPORTED_MODULE_2__.Redirect, _extends({}, routeProps, {
      from: route.path,
      to: route.redirect
    }));
  } else {
    var _route$component, _route$component2, _route$component3;

    // avoid mount and unmount with url hash change
    if ( // only when SSR config enable
    opts.ssrProps && !opts.isServer && // make sure loaded once
    !((_route$component = route.component) === null || _route$component === void 0 ? void 0 : _route$component.wrapInitialPropsLoaded) && ( // TODO need a type
    ((_route$component2 = route.component) === null || _route$component2 === void 0 ? void 0 : _route$component2.getInitialProps) || ((_route$component3 = route.component) === null || _route$component3 === void 0 ? void 0 : _route$component3.preload))) {
      // client Render for enable ssr, but not sure SSR success
      route.component = wrapInitialPropsFetch(route, opts);
    }

    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Route, _extends({}, routeProps, {
      render: function render(props) {
        return _render({
          route: route,
          opts: opts,
          props: props
        });
      }
    }));
  }
}

function renderRoutes(opts) {
  var switchProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return opts.routes ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Switch, switchProps, opts.routes.map(function (route, index) {
    return getRouteElement({
      route: route,
      index: index,
      opts: _objectSpread2(_objectSpread2({}, opts), {}, {
        rootRoutes: opts.rootRoutes || opts.routes
      })
    });
  })) : null;
}

var _excluded$2 = ["history"];

function RouterComponent(props) {
  var history = props.history,
      renderRoutesProps = _objectWithoutProperties(props, _excluded$2);

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    // first time using window.g_initialProps
    // switch route fetching data, if exact route reset window.getInitialProps
    if (window.g_useSSR) {
      window.g_initialProps = null;
    }

    function routeChangeHandler(location, action) {
      var matchedRoutes = (0,react_router_config__WEBPACK_IMPORTED_MODULE_4__.matchRoutes)(props.routes, location.pathname); // Set title

      if (typeof document !== 'undefined' && renderRoutesProps.defaultTitle !== undefined) {
        document.title = matchedRoutes.length && // @ts-ignore
        matchedRoutes[matchedRoutes.length - 1].route.title || renderRoutesProps.defaultTitle || '';
      }

      props.plugin.applyPlugins({
        key: 'onRouteChange',
        type: _umijs_runtime__WEBPACK_IMPORTED_MODULE_3__.ApplyPluginsType.event,
        args: {
          routes: props.routes,
          matchedRoutes: matchedRoutes,
          location: location,
          action: action
        }
      });
    }

    routeChangeHandler(history.location, 'POP');
    return history.listen(routeChangeHandler);
  }, [history]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_umijs_runtime__WEBPACK_IMPORTED_MODULE_2__.Router, {
    history: history
  }, renderRoutes(renderRoutesProps));
}
/**
 * preload for SSR in dynamicImport
 * exec preload Promise function before ReactDOM.hydrate
 * @param Routes
 */


function preloadComponent(_x) {
  return _preloadComponent.apply(this, arguments);
}

function _preloadComponent() {
  _preloadComponent = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee(readyRoutes) {
    var pathname,
        matchedRoutes,
        _iterator,
        _step,
        _route$component,
        matchRoute,
        route,
        _preloadComponent2,
        _args = arguments;

    return regeneratorRuntime.wrap(function _callee$(_context) {
      while (1) {
        switch (_context.prev = _context.next) {
          case 0:
            pathname = _args.length > 1 && _args[1] !== undefined ? _args[1] : window.location.pathname;
            // using matched routes not load all routes
            matchedRoutes = (0,react_router_config__WEBPACK_IMPORTED_MODULE_4__.matchRoutes)(readyRoutes, pathname);
            _iterator = _createForOfIteratorHelper(matchedRoutes);
            _context.prev = 3;

            _iterator.s();

          case 5:
            if ((_step = _iterator.n()).done) {
              _context.next = 19;
              break;
            }

            matchRoute = _step.value;
            route = matchRoute.route; // load all preload function, because of only a chance to load

            if (!(typeof route.component !== 'string' && ((_route$component = route.component) === null || _route$component === void 0 ? void 0 : _route$component.preload))) {
              _context.next = 13;
              break;
            }

            _context.next = 11;
            return route.component.preload();

          case 11:
            _preloadComponent2 = _context.sent;
            route.component = _preloadComponent2.default || _preloadComponent2;

          case 13:
            if (!route.routes) {
              _context.next = 17;
              break;
            }

            _context.next = 16;
            return preloadComponent(route.routes, pathname);

          case 16:
            route.routes = _context.sent;

          case 17:
            _context.next = 5;
            break;

          case 19:
            _context.next = 24;
            break;

          case 21:
            _context.prev = 21;
            _context.t0 = _context["catch"](3);

            _iterator.e(_context.t0);

          case 24:
            _context.prev = 24;

            _iterator.f();

            return _context.finish(24);

          case 27:
            return _context.abrupt("return", readyRoutes);

          case 28:
          case "end":
            return _context.stop();
        }
      }
    }, _callee, null, [[3, 21, 24, 27]]);
  }));
  return _preloadComponent.apply(this, arguments);
}

function renderClient(opts) {
  var rootContainer = opts.plugin.applyPlugins({
    type: _umijs_runtime__WEBPACK_IMPORTED_MODULE_3__.ApplyPluginsType.modify,
    key: 'rootContainer',
    initialValue: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(RouterComponent, {
      history: opts.history,
      routes: opts.routes,
      plugin: opts.plugin,
      ssrProps: opts.ssrProps,
      defaultTitle: opts.defaultTitle
    }),
    args: {
      history: opts.history,
      routes: opts.routes,
      plugin: opts.plugin
    }
  });

  if (opts.rootElement) {
    var rootElement = typeof opts.rootElement === 'string' ? document.getElementById(opts.rootElement) : opts.rootElement;

    var callback = opts.callback || function () {}; // flag showing SSR successed


    if (window.g_useSSR) {
      if (opts.dynamicImport) {
        // dynamicImport should preload current route component
        // first loades);
        preloadComponent(opts.routes).then(function () {
          (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.hydrate)(rootContainer, rootElement, callback);
        });
      } else {
        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.hydrate)(rootContainer, rootElement, callback);
      }
    } else {
      (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.render)(rootContainer, rootElement, callback);
    }
  } else {
    return rootContainer;
  }
}




/***/ }),

/***/ "./node_modules/@umijs/renderer-react/node_modules/react-router-config/esm/react-router-config.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@umijs/renderer-react/node_modules/react-router-config/esm/react-router-config.js ***!
  \********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "matchRoutes": function() { return /* binding */ matchRoutes; },
/* harmony export */   "renderRoutes": function() { return /* binding */ renderRoutes; }
/* harmony export */ });
/* harmony import */ var react_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-router */ "./node_modules/react-router/esm/react-router.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");




function matchRoutes(routes, pathname,
/*not public API*/
branch) {
  if (branch === void 0) {
    branch = [];
  }

  routes.some(function (route) {
    var match = route.path ? (0,react_router__WEBPACK_IMPORTED_MODULE_2__.matchPath)(pathname, route) : branch.length ? branch[branch.length - 1].match // use parent match
    : react_router__WEBPACK_IMPORTED_MODULE_2__.Router.computeRootMatch(pathname); // use default "root" match

    if (match) {
      branch.push({
        route: route,
        match: match
      });

      if (route.routes) {
        matchRoutes(route.routes, pathname, branch);
      }
    }

    return match;
  });
  return branch;
}

function renderRoutes(routes, extraProps, switchProps) {
  if (extraProps === void 0) {
    extraProps = {};
  }

  if (switchProps === void 0) {
    switchProps = {};
  }

  return routes ? react__WEBPACK_IMPORTED_MODULE_1__.createElement(react_router__WEBPACK_IMPORTED_MODULE_2__.Switch, switchProps, routes.map(function (route, i) {
    return react__WEBPACK_IMPORTED_MODULE_1__.createElement(react_router__WEBPACK_IMPORTED_MODULE_2__.Route, {
      key: route.key || i,
      path: route.path,
      exact: route.exact,
      strict: route.strict,
      render: function render(props) {
        return route.render ? route.render((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {}, extraProps, {
          route: route
        })) : react__WEBPACK_IMPORTED_MODULE_1__.createElement(route.component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, extraProps, {
          route: route
        }));
      }
    });
  })) : null;
}


//# sourceMappingURL=react-router-config.js.map


/***/ }),

/***/ "./node_modules/react-is/cjs/react-is.development.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-is/cjs/react-is.development.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, exports) {

"use strict";
/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */





if (true) {
  (function() {
'use strict';

// The Symbol used to tag the ReactElement-like types. If there is no native Symbol
// nor polyfill, then a plain number is used for performance.
var hasSymbol = typeof Symbol === 'function' && Symbol.for;
var REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;
var REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;
var REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;
var REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;
var REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;
var REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;
var REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary
// (unstable) APIs that have been removed. Can we remove the symbols?

var REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;
var REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;
var REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;
var REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;
var REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;
var REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;
var REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;
var REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;
var REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;
var REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;
var REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;

function isValidElementType(type) {
  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.
  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);
}

function typeOf(object) {
  if (typeof object === 'object' && object !== null) {
    var $$typeof = object.$$typeof;

    switch ($$typeof) {
      case REACT_ELEMENT_TYPE:
        var type = object.type;

        switch (type) {
          case REACT_ASYNC_MODE_TYPE:
          case REACT_CONCURRENT_MODE_TYPE:
          case REACT_FRAGMENT_TYPE:
          case REACT_PROFILER_TYPE:
          case REACT_STRICT_MODE_TYPE:
          case REACT_SUSPENSE_TYPE:
            return type;

          default:
            var $$typeofType = type && type.$$typeof;

            switch ($$typeofType) {
              case REACT_CONTEXT_TYPE:
              case REACT_FORWARD_REF_TYPE:
              case REACT_LAZY_TYPE:
              case REACT_MEMO_TYPE:
              case REACT_PROVIDER_TYPE:
                return $$typeofType;

              default:
                return $$typeof;
            }

        }

      case REACT_PORTAL_TYPE:
        return $$typeof;
    }
  }

  return undefined;
} // AsyncMode is deprecated along with isAsyncMode

var AsyncMode = REACT_ASYNC_MODE_TYPE;
var ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;
var ContextConsumer = REACT_CONTEXT_TYPE;
var ContextProvider = REACT_PROVIDER_TYPE;
var Element = REACT_ELEMENT_TYPE;
var ForwardRef = REACT_FORWARD_REF_TYPE;
var Fragment = REACT_FRAGMENT_TYPE;
var Lazy = REACT_LAZY_TYPE;
var Memo = REACT_MEMO_TYPE;
var Portal = REACT_PORTAL_TYPE;
var Profiler = REACT_PROFILER_TYPE;
var StrictMode = REACT_STRICT_MODE_TYPE;
var Suspense = REACT_SUSPENSE_TYPE;
var hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated

function isAsyncMode(object) {
  {
    if (!hasWarnedAboutDeprecatedIsAsyncMode) {
      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint

      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');
    }
  }

  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;
}
function isConcurrentMode(object) {
  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;
}
function isContextConsumer(object) {
  return typeOf(object) === REACT_CONTEXT_TYPE;
}
function isContextProvider(object) {
  return typeOf(object) === REACT_PROVIDER_TYPE;
}
function isElement(object) {
  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;
}
function isForwardRef(object) {
  return typeOf(object) === REACT_FORWARD_REF_TYPE;
}
function isFragment(object) {
  return typeOf(object) === REACT_FRAGMENT_TYPE;
}
function isLazy(object) {
  return typeOf(object) === REACT_LAZY_TYPE;
}
function isMemo(object) {
  return typeOf(object) === REACT_MEMO_TYPE;
}
function isPortal(object) {
  return typeOf(object) === REACT_PORTAL_TYPE;
}
function isProfiler(object) {
  return typeOf(object) === REACT_PROFILER_TYPE;
}
function isStrictMode(object) {
  return typeOf(object) === REACT_STRICT_MODE_TYPE;
}
function isSuspense(object) {
  return typeOf(object) === REACT_SUSPENSE_TYPE;
}

exports.AsyncMode = AsyncMode;
exports.ConcurrentMode = ConcurrentMode;
exports.ContextConsumer = ContextConsumer;
exports.ContextProvider = ContextProvider;
exports.Element = Element;
exports.ForwardRef = ForwardRef;
exports.Fragment = Fragment;
exports.Lazy = Lazy;
exports.Memo = Memo;
exports.Portal = Portal;
exports.Profiler = Profiler;
exports.StrictMode = StrictMode;
exports.Suspense = Suspense;
exports.isAsyncMode = isAsyncMode;
exports.isConcurrentMode = isConcurrentMode;
exports.isContextConsumer = isContextConsumer;
exports.isContextProvider = isContextProvider;
exports.isElement = isElement;
exports.isForwardRef = isForwardRef;
exports.isFragment = isFragment;
exports.isLazy = isLazy;
exports.isMemo = isMemo;
exports.isPortal = isPortal;
exports.isProfiler = isProfiler;
exports.isStrictMode = isStrictMode;
exports.isSuspense = isSuspense;
exports.isValidElementType = isValidElementType;
exports.typeOf = typeOf;
  })();
}


/***/ }),

/***/ "./node_modules/react-is/index.js":
/*!****************************************!*\
  !*** ./node_modules/react-is/index.js ***!
  \****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


if (false) {} else {
  module.exports = __webpack_require__(/*! ./cjs/react-is.development.js */ "./node_modules/react-is/cjs/react-is.development.js");
}


/***/ })

}]);