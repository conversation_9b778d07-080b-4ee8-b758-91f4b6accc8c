(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8735],{28126:function(H,P,i){"use strict";Object.defineProperty(P,"__esModule",{value:!0}),P.getFromHTMLConfig=P.getToHTMLConfig=P.blocks=P.getHexColor=P.defaultFontFamilies=P.namedColors=void 0;var x=Object.assign||function(w){for(var z=1;z<arguments.length;z++){var j=arguments[z];for(var U in j)Object.prototype.hasOwnProperty.call(j,U)&&(w[U]=j[U])}return w},b=i(67294),v=A(b);function A(w){return w&&w.__esModule?w:{default:w}}function p(w,z){var j={};for(var U in w)z.indexOf(U)>=0||!Object.prototype.hasOwnProperty.call(w,U)||(j[U]=w[U]);return j}var I=P.namedColors={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4","indianred ":"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},E=function(z){return z.split("-")[1]},d=function(z){return z+"px"},f=function(z){return z.replace("px","")},g=["style"],s=["style","href","target","alt","title","id","controls","autoplay","loop","poster"],a=function(z){return Object.keys(z).reduce(function(j,U){return j+" "+U+'="'+z[U]+'"'},"").replace(/^\s$/,"")},c=P.defaultFontFamilies=[{name:"Araial",family:"Arial, Helvetica, sans-serif"},{name:"Georgia",family:"Georgia, serif"},{name:"Impact",family:"Impact, serif"},{name:"Monospace",family:'"Courier New", Courier, monospace'},{name:"Tahoma",family:"tahoma, arial, 'Hiragino Sans GB', \u5B8B\u4F53, sans-serif"}],o=P.getHexColor=function(z){if(z=z.replace("color:","").replace(";","").replace(" ",""),/^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/.test(z))return z;if(I[z])return I[z];if(z.indexOf("rgb")===0){var j=z.split(","),U=j.length<3?null:"#"+[j[0],j[1],j[2]].map(function(O){var ie=parseInt(O.replace(/\D/g,""),10).toString(16);return ie.length===1?"0"+ie:ie}).join("");return/^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/.test(U)?U:null}else return null},r=P.blocks={"header-one":"h1","header-two":"h2","header-three":"h3","header-four":"h4","header-five":"h5","header-six":"h6",unstyled:"p",blockquote:"blockquote"},u=Object.keys(r),S=u.map(function(w){return r[w]}),C=function(z,j,U){if(!z||!z.key)return v.default.createElement("p",null);var O=j.getBlockForKey(z.key),ie=U.class,Q=p(U,["class"]);if(Q.className=ie,!O)return v.default.createElement("p",null);var ue=O.getEntityAt(0);if(!ue)return v.default.createElement("p",null);var Ee=j.getEntity(ue),me=Ee.getType().toLowerCase(),V=z.data,we=V.float,Ne=V.alignment,Me=Ee.getData(),Ke=Me.url,qe=Me.link,it=Me.link_target,et=Me.width,Je=Me.height,Be=Me.meta;if(me==="image"){var He={},Qe="";return we?(He.float=we,Qe+=" float-"+we):Ne&&(He.textAlign=Ne,Qe+=" align-"+Ne),qe?v.default.createElement("div",{className:"media-wrap image-wrap"+Qe,style:He},v.default.createElement("a",{style:{display:"inline-block"},href:qe,target:it},v.default.createElement("img",x({},Q,Be,{src:Ke,width:et,height:Je,style:{width:et,height:Je}})))):v.default.createElement("div",{className:"media-wrap image-wrap"+Qe,style:He},v.default.createElement("img",x({},Q,Be,{src:Ke,width:et,height:Je,style:{width:et,height:Je}})))}else return me==="audio"?v.default.createElement("div",{className:"media-wrap audio-wrap"},v.default.createElement("audio",x({controls:!0},Q,Be,{src:Ke}))):me==="video"?v.default.createElement("div",{className:"media-wrap video-wrap"},v.default.createElement("video",x({controls:!0},Q,Be,{src:Ke,width:et,height:Je}))):me==="embed"?v.default.createElement("div",{className:"media-wrap embed-wrap"},v.default.createElement("div",{dangerouslySetInnerHTML:{__html:Ke}})):me==="hr"?v.default.createElement("hr",null):v.default.createElement("p",null)},l=function(z){return function(j,U){var O=z.entityExportFn,ie=j.type.toLowerCase();if(O){var Q=O(j,U);if(Q)return Q}if(ie==="link"){var ue=j.data.nodeAttributes||{},Ee=ue.class,me=p(ue,["class"]);return me.className=Ee,v.default.createElement("a",x({href:j.data.href,target:j.data.target},me))}}},h=function(z){return function(j){var U=z.unitExportFn||d;if(z.styleExportFn){var O=z.styleExportFn(j,z);if(O)return O}if(j=j.toLowerCase(),j==="strikethrough")return v.default.createElement("span",{style:{textDecoration:"line-through"}});if(j==="superscript")return v.default.createElement("sup",null);if(j==="subscript")return v.default.createElement("sub",null);if(j.indexOf("color-")===0)return v.default.createElement("span",{style:{color:"#"+E(j)}});if(j.indexOf("bgcolor-")===0)return v.default.createElement("span",{style:{backgroundColor:"#"+E(j)}});if(j.indexOf("fontsize-")===0)return v.default.createElement("span",{style:{fontSize:U(E(j),"font-size","html")}});if(j.indexOf("lineheight-")===0)return v.default.createElement("span",{style:{lineHeight:U(E(j),"line-height","html")}});if(j.indexOf("letterspacing-")===0)return v.default.createElement("span",{style:{letterSpacing:U(E(j),"letter-spacing","html")}});if(j.indexOf("fontfamily-")===0){var ie=z.fontFamilies.find(function(Q){return Q.name.toLowerCase()===E(j)});return ie?v.default.createElement("span",{style:{fontFamily:ie.family}}):void 0}}},D=function(z){return function(j){var U=z.blockExportFn,O=z.contentState;if(U){var ie=U(O,j);if(ie)return ie}var Q="",ue=j.type.toLowerCase(),Ee=j.data,me=Ee.textAlign,V=Ee.textIndent,we=Ee.nodeAttributes,Ne=we===void 0?{}:we,Me=a(Ne);if((me||V)&&(Q=' style="',me&&(Q+="text-align:"+me+";"),V&&!isNaN(V)&&V>0&&(Q+="text-indent:"+V*2+"em;"),Q+='"'),ue==="atomic")return C(j,O,Ne);if(ue==="code-block"){var Ke=O.getBlockBefore(j.key),qe=O.getBlockAfter(j.key),it=Ke&&Ke.getType(),et=qe&&qe.getType(),Je="",Be="";return it!=="code-block"?Je="<pre"+Me+"><code>":Je="",et!=="code-block"?Be="</code></pre>":Be="<br/>",{start:Je,end:Be}}else{if(r[ue])return{start:"<"+r[ue]+Q+Me+">",end:"</"+r[ue]+">"};if(ue==="unordered-list-item")return{start:"<li"+Q+Me+">",end:"</li>",nest:v.default.createElement("ul",null)};if(ue==="ordered-list-item")return{start:"<li"+Q+Me+">",end:"</li>",nest:v.default.createElement("ol",null)}}}},k=function(z,j){return function(U,O,ie){if(!O||!O.style)return ie;var Q=z.unitImportFn||f,ue=ie;return[].forEach.call(O.style,function(Ee){if(U==="span"&&Ee==="color"){var me=o(O.style.color);ue=me?ue.add("COLOR-"+me.replace("#","").toUpperCase()):ue}else if(U==="span"&&Ee==="background-color"){var V=o(O.style.backgroundColor);ue=V?ue.add("BGCOLOR-"+V.replace("#","").toUpperCase()):ue}else if(U==="span"&&Ee==="font-size")ue=ue.add("FONTSIZE-"+Q(O.style.fontSize,"font-size",j));else if(U==="span"&&Ee==="line-height"&&!isNaN(parseFloat(O.style.lineHeight,10)))ue=ue.add("LINEHEIGHT-"+Q(O.style.lineHeight,"line-height",j));else if(U==="span"&&Ee==="letter-spacing"&&!isNaN(parseFloat(O.style.letterSpacing,10)))ue=ue.add("LETTERSPACING-"+Q(O.style.letterSpacing,"letter-spacing",j));else if(U==="span"&&Ee==="text-decoration")O.style.textDecoration==="line-through"?ue=ue.add("STRIKETHROUGH"):O.style.textDecoration==="underline"&&(ue=ue.add("UNDERLINE"));else if(U==="span"&&Ee==="font-family"){var we=z.fontFamilies.find(function(Ne){return Ne.family.toLowerCase()===O.style.fontFamily.toLowerCase()});if(!we)return;ue=ue.add("FONTFAMILY-"+we.name.toUpperCase())}}),U==="sup"?ue=ue.add("SUPERSCRIPT"):U==="sub"&&(ue=ue.add("SUBSCRIPT")),z.styleImportFn&&(ue=z.styleImportFn(U,O,ue,j)||ue),ue}},y=function(z,j){return function(U,O,ie){if(z&&z.entityImportFn){var Q=z.entityImportFn(U,O,ie,j);if(Q)return Q}U=U.toLowerCase();var ue=O.alt,Ee=O.title,me=O.id,V=O.controls,we=O.autoplay,Ne=O.loop,Me=O.poster,Ke={},qe={};if(me&&(Ke.id=me),ue&&(Ke.alt=ue),Ee&&(Ke.title=Ee),V&&(Ke.controls=V),we&&(Ke.autoPlay=we),Ne&&(Ke.loop=Ne),Me&&(Ke.poster=Me),O.attributes&&Object.keys(O.attributes).forEach(function(rt){var Ce=O.attributes[rt];s.indexOf(Ce.name)===-1&&(qe[Ce.name]=Ce.value)}),U==="a"&&!O.querySelectorAll("img").length){var it=O.getAttribute("href"),et=O.getAttribute("target");return ie("LINK","MUTABLE",{href:it,target:et,nodeAttributes:qe})}else{if(U==="audio")return ie("AUDIO","IMMUTABLE",{url:O.getAttribute("src"),meta:Ke,nodeAttributes:qe});if(U==="video")return ie("VIDEO","IMMUTABLE",{url:O.getAttribute("src"),meta:Ke,nodeAttributes:qe});if(U==="img"){var Je=O.parentNode,Be={meta:Ke},He=O.style,Qe=He.width,ot=He.height;return Be.url=O.getAttribute("src"),Qe&&(Be.width=Qe),ot&&(Be.height=ot),Je.nodeName.toLowerCase()==="a"&&(Be.link=Je.getAttribute("href"),Be.link_target=Je.getAttribute("target")),ie("IMAGE","IMMUTABLE",Be)}else{if(U==="hr")return ie("HR","IMMUTABLE",{});if(O.parentNode&&O.parentNode.classList.contains("embed-wrap")){var mt=O.innerHTML||O.outerHTML;if(mt)return ie("EMBED","IMMUTABLE",{url:mt})}}}}},L=function(z,j){return function(U,O){if(z&&z.blockImportFn){var ie=z.blockImportFn(U,O,j);if(ie)return ie}var Q={},ue=O.style||{};if(O.attributes&&Object.keys(O.attributes).forEach(function(me){var V=O.attributes[me];g.indexOf(V.name)===-1&&(Q[V.name]=V.value)}),O.classList&&O.classList.contains("media-wrap"))return{type:"atomic",data:{nodeAttributes:Q,float:ue.float,alignment:ue.textAlign}};if(U==="img")return{type:"atomic",data:{nodeAttributes:Q,float:ue.float,alignment:ue.textAlign}};if(U==="hr")return{type:"atomic",data:{nodeAttributes:Q}};if(U==="pre")return O.innerHTML=O.innerHTML.replace(/<code(.*?)>/g,"").replace(/<\/code>/g,""),{type:"code-block",data:{nodeAttributes:Q}};if(S.indexOf(U)!==-1){var Ee={nodeAttributes:Q};return ue.textAlign&&(Ee.textAlign=ue.textAlign),ue.textIndent&&(Ee.textIndent=/^\d+em$/.test(ue.textIndent)?Math.ceil(parseInt(ue.textIndent,10)/2):1),{type:u[S.indexOf(U)],data:Ee}}}},T=P.getToHTMLConfig=function(z){return{styleToHTML:h(z),entityToHTML:l(z),blockToHTML:D(z)}},B=P.getFromHTMLConfig=function(z){var j=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"unknow";return{htmlToStyle:k(z,j),htmlToEntity:y(z,j),htmlToBlock:L(z,j)}}},36444:function(H,P,i){"use strict";Object.defineProperty(P,"__esModule",{value:!0}),P.convertRawToEditorState=P.convertEditorStateToRaw=P.convertHTMLToEditorState=P.convertEditorStateToHTML=P.convertHTMLToRaw=P.convertRawToHTML=void 0;var x=Object.assign||function(a){for(var c=1;c<arguments.length;c++){var o=arguments[c];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(a[r]=o[r])}return a},b=i(34561),v=i(28126),A=i(9041),p={fontFamilies:v.defaultFontFamilies},I=P.convertRawToHTML=function(c,o){o=x({},p,o);try{var r=(0,A.convertFromRaw)(c);return o.contentState=r,(0,b.convertToHTML)((0,v.getToHTMLConfig)(o))(r)}catch(u){return console.warn(u),""}},E=P.convertHTMLToRaw=function(c,o,r){o=x({},p,o);try{var u=(0,b.convertFromHTML)((0,v.getFromHTMLConfig)(o,r))(c);return(0,A.convertToRaw)(u)}catch(S){return console.warn(S),{}}},d=P.convertEditorStateToHTML=function(c,o){o=x({},p,o);try{var r=c.getCurrentContent();return o.contentState=r,(0,b.convertToHTML)((0,v.getToHTMLConfig)(o))(r)}catch(u){return console.warn(u),""}},f=P.convertHTMLToEditorState=function(c,o,r,u){r=x({},p,r);try{return A.EditorState.createWithContent((0,b.convertFromHTML)((0,v.getFromHTMLConfig)(r,u))(c),o)}catch(S){return console.warn(S),A.EditorState.createEmpty(o)}},g=P.convertEditorStateToRaw=function(c){return(0,A.convertToRaw)(c.getCurrentContent())},s=P.convertRawToEditorState=function(c,o){try{return A.EditorState.createWithContent((0,A.convertFromRaw)(c),o)}catch(r){return console.warn(r),A.EditorState.createEmpty(o)}}},85589:function(H,P,i){(function(b,v){if(!0)H.exports=v(i(67294),i(32452),i(9041),i(43393),i(36444),i(73935),i(42017),i(5252));else var A,p})(window,function(x,b,v,A,p,I,E,d){return function(f){var g={};function s(a){if(g[a])return g[a].exports;var c=g[a]={i:a,l:!1,exports:{}};return f[a].call(c.exports,c,c.exports,s),c.l=!0,c.exports}return s.m=f,s.c=g,s.d=function(a,c,o){s.o(a,c)||Object.defineProperty(a,c,{enumerable:!0,get:o})},s.r=function(a){typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},s.t=function(a,c){if(c&1&&(a=s(a)),c&8||c&4&&typeof a=="object"&&a&&a.__esModule)return a;var o=Object.create(null);if(s.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:a}),c&2&&typeof a!="string")for(var r in a)s.d(o,r,function(u){return a[u]}.bind(null,r));return o},s.n=function(a){var c=a&&a.__esModule?function(){return a.default}:function(){return a};return s.d(c,"a",c),c},s.o=function(a,c){return Object.prototype.hasOwnProperty.call(a,c)},s.p="/",s(s.s=39)}([function(f,g){f.exports=x},function(f,g){function s(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}f.exports=s},function(f,g){function s(a,c,o){return c in a?Object.defineProperty(a,c,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[c]=o,a}f.exports=s},function(f,g){f.exports=b},function(f,g){function s(a,c){if(!(a instanceof c))throw new TypeError("Cannot call a class as a function")}f.exports=s},function(f,g,s){var a=s(2);function c(o){for(var r=1;r<arguments.length;r++){var u=arguments[r]!=null?arguments[r]:{},S=Object.keys(u);typeof Object.getOwnPropertySymbols=="function"&&(S=S.concat(Object.getOwnPropertySymbols(u).filter(function(C){return Object.getOwnPropertyDescriptor(u,C).enumerable}))),S.forEach(function(C){a(o,C,u[C])})}return o}f.exports=c},function(f,g){f.exports=v},function(f,g){function s(c,o){for(var r=0;r<o.length;r++){var u=o[r];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(c,u.key,u)}}function a(c,o,r){return o&&s(c.prototype,o),r&&s(c,r),c}f.exports=a},function(f,g,s){var a=s(15),c=s(1);function o(r,u){return u&&(a(u)==="object"||typeof u=="function")?u:c(r)}f.exports=o},function(f,g){function s(a){return f.exports=s=Object.setPrototypeOf?Object.getPrototypeOf:function(o){return o.__proto__||Object.getPrototypeOf(o)},s(a)}f.exports=s},function(f,g,s){var a=s(26);function c(o,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");o.prototype=Object.create(r&&r.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),r&&a(o,r)}f.exports=c},function(f,g){function s(){return f.exports=s=Object.assign||function(a){for(var c=1;c<arguments.length;c++){var o=arguments[c];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(a[r]=o[r])}return a},s.apply(this,arguments)}f.exports=s},function(f,g,s){var a=s(27),c=s(28),o=s(29);function r(u){return a(u)||c(u)||o()}f.exports=r},function(f,g){f.exports=A},function(f,g){f.exports=p},function(f,g){function s(c){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?s=function(r){return typeof r}:s=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},s(c)}function a(c){return typeof Symbol=="function"&&s(Symbol.iterator)==="symbol"?f.exports=a=function(r){return s(r)}:f.exports=a=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":s(r)},a(c)}f.exports=a},function(f,g){f.exports=I},function(f,g){f.exports=E},function(f,g,s){var a=s(13),c="-";function o(r){this.decorators=a.List(r)}o.prototype.getDecorations=function(r){var u=Array(r.getText().length).fill(null);return this.decorators.forEach(function(S,C){var l=S.getDecorations(r);l.forEach(function(h,D){!h||(h=C+c+h,u[D]=h)})}),a.List(u)},o.prototype.getComponentForKey=function(r){var u=this.getDecoratorForKey(r);return u.getComponentForKey(this.getInnerKey(r))},o.prototype.getPropsForKey=function(r){var u=this.getDecoratorForKey(r);return u.getPropsForKey(this.getInnerKey(r))},o.prototype.getDecoratorForKey=function(r){var u=r.split(c),S=Number(u[0]);return this.decorators.get(S)},o.prototype.getInnerKey=function(r){var u=r.split(c);return u.slice(1).join(c)},f.exports=o},function(f,g,s){"use strict";function a(T,B){if(!(T instanceof B))throw new TypeError("Cannot call a class as a function")}function c(T,B){if(!T)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return B&&(typeof B=="object"||typeof B=="function")?B:T}function o(T,B){if(typeof B!="function"&&B!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof B);T.prototype=Object.create(B&&B.prototype,{constructor:{value:T,enumerable:!1,writable:!0,configurable:!0}}),B&&(Object.setPrototypeOf?Object.setPrototypeOf(T,B):T.__proto__=B)}var r=s(13),u=r.Map,S=r.OrderedSet,C=r.Record,l=S(),h={style:l,entity:null},D=C(h),k=function(T){o(B,T);function B(){return a(this,B),c(this,T.apply(this,arguments))}return B.prototype.getStyle=function(){return this.get("style")},B.prototype.getEntity=function(){return this.get("entity")},B.prototype.hasStyle=function(z){return this.getStyle().includes(z)},B.applyStyle=function(z,j){var U=z.set("style",z.getStyle().add(j));return B.create(U)},B.removeStyle=function(z,j){var U=z.set("style",z.getStyle().remove(j));return B.create(U)},B.applyEntity=function(z,j){var U=z.getEntity()===j?z:z.set("entity",j);return B.create(U)},B.create=function(z){if(!z)return y;var j={style:l,entity:null},U=u(j).merge(z),O=L.get(U);if(O)return O;var ie=new B(U);return L=L.set(U,ie),ie},B}(D),y=new k,L=u([[u(h),y]]);k.EMPTY=y,f.exports=k},function(f,g,s){"use strict";function a(c,o,r,u){if(!!c.size){var S=0;c.reduce(function(C,l,h){return o(C,l)||(r(C)&&u(S,h),S=h),l}),r(c.last())&&u(S,c.count())}}f.exports=a},function(f,g,s){var a=s(25);function c(o,r){if(o==null)return{};var u=a(o,r),S,C;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(o);for(C=0;C<l.length;C++)S=l[C],!(r.indexOf(S)>=0)&&(!Object.prototype.propertyIsEnumerable.call(o,S)||(u[S]=o[S]))}return u}f.exports=c},function(f,g,s){"use strict";var a=s(30);function c(o){var r=o.getSelection();return r.isCollapsed()?null:a(o.getCurrentContent(),r)}f.exports=c},function(f,g){f.exports=d},function(f,g,s){var a=s(36),c=s(37),o=s(38);function r(u,S){return a(u)||c(u,S)||o()}f.exports=r},function(f,g){function s(a,c){if(a==null)return{};var o={},r=Object.keys(a),u,S;for(S=0;S<r.length;S++)u=r[S],!(c.indexOf(u)>=0)&&(o[u]=a[u]);return o}f.exports=s},function(f,g){function s(a,c){return f.exports=s=Object.setPrototypeOf||function(r,u){return r.__proto__=u,r},s(a,c)}f.exports=s},function(f,g){function s(a){if(Array.isArray(a)){for(var c=0,o=new Array(a.length);c<a.length;c++)o[c]=a[c];return o}}f.exports=s},function(f,g){function s(a){if(Symbol.iterator in Object(a)||Object.prototype.toString.call(a)==="[object Arguments]")return Array.from(a)}f.exports=s},function(f,g){function s(){throw new TypeError("Invalid attempt to spread non-iterable instance")}f.exports=s},function(f,g,s){"use strict";var a=s(31),c=s(34),o=function(u,S){var C=S.getStartKey(),l=S.getStartOffset(),h=S.getEndKey(),D=S.getEndOffset(),k=c(u,S),y=k.getBlockMap(),L=y.keySeq(),T=L.indexOf(C),B=L.indexOf(h)+1;return a(y.slice(T,B).map(function(w,z){var j=w.getText(),U=w.getCharacterList();return C===h?w.merge({text:j.slice(l,D),characterList:U.slice(l,D)}):z===C?w.merge({text:j.slice(l),characterList:U.slice(l)}):z===h?w.merge({text:j.slice(0,D),characterList:U.slice(0,D)}):w}))};f.exports=o},function(f,g,s){"use strict";var a=s(32),c=s(13),o=s(33),r=c.OrderedMap,u=function(h){var D={},k=void 0;return r(h.withMutations(function(y){y.forEach(function(L,T){var B=L.getKey(),w=L.getNextSiblingKey(),z=L.getPrevSiblingKey(),j=L.getChildKeys(),U=L.getParentKey(),O=o();if(D[B]=O,w){var ie=y.get(w);ie?y.setIn([w,"prevSibling"],O):y.setIn([B,"nextSibling"],null)}if(z){var Q=y.get(z);Q?y.setIn([z,"nextSibling"],O):y.setIn([B,"prevSibling"],null)}if(U&&y.get(U)){var ue=y.get(U),Ee=ue.getChildKeys();y.setIn([U,"children"],Ee.set(Ee.indexOf(L.getKey()),O))}else y.setIn([B,"parent"],null),k&&(y.setIn([k.getKey(),"nextSibling"],O),y.setIn([B,"prevSibling"],D[k.getKey()])),k=y.get(B);j.forEach(function(me){var V=y.get(me);V?y.setIn([me,"parent"],O):y.setIn([B,"children"],L.getChildKeys().filter(function(we){return we!==me}))})})}).toArray().map(function(y){return[D[y.getKey()],y.set("key",D[y.getKey()])]}))},S=function(h){return r(h.toArray().map(function(D){var k=o();return[k,D.set("key",k)]}))},C=function(h){var D=h.first()instanceof a;return D?u(h):S(h)};f.exports=C},function(f,g,s){"use strict";function a(j,U){if(!(j instanceof U))throw new TypeError("Cannot call a class as a function")}function c(j,U){if(!j)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return U&&(typeof U=="object"||typeof U=="function")?U:j}function o(j,U){if(typeof U!="function"&&U!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof U);j.prototype=Object.create(U&&U.prototype,{constructor:{value:j,enumerable:!1,writable:!0,configurable:!0}}),U&&(Object.setPrototypeOf?Object.setPrototypeOf(j,U):j.__proto__=U)}var r=s(19),u=s(13),S=s(20),C=u.List,l=u.Map,h=u.OrderedSet,D=u.Record,k=u.Repeat,y=h(),L={parent:null,characterList:C(),data:l(),depth:0,key:"",text:"",type:"unstyled",children:C(),prevSibling:null,nextSibling:null},T=function(U,O){return U.getStyle()===O.getStyle()},B=function(U,O){return U.getEntity()===O.getEntity()},w=function(U){if(!U)return U;var O=U.characterList,ie=U.text;return ie&&!O&&(U.characterList=C(k(r.EMPTY,ie.length))),U},z=function(j){o(U,j);function U(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:L;return a(this,U),c(this,j.call(this,w(O)))}return U.prototype.getKey=function(){return this.get("key")},U.prototype.getType=function(){return this.get("type")},U.prototype.getText=function(){return this.get("text")},U.prototype.getCharacterList=function(){return this.get("characterList")},U.prototype.getLength=function(){return this.getText().length},U.prototype.getDepth=function(){return this.get("depth")},U.prototype.getData=function(){return this.get("data")},U.prototype.getInlineStyleAt=function(ie){var Q=this.getCharacterList().get(ie);return Q?Q.getStyle():y},U.prototype.getEntityAt=function(ie){var Q=this.getCharacterList().get(ie);return Q?Q.getEntity():null},U.prototype.getChildKeys=function(){return this.get("children")},U.prototype.getParentKey=function(){return this.get("parent")},U.prototype.getPrevSiblingKey=function(){return this.get("prevSibling")},U.prototype.getNextSiblingKey=function(){return this.get("nextSibling")},U.prototype.findStyleRanges=function(ie,Q){S(this.getCharacterList(),T,ie,Q)},U.prototype.findEntityRanges=function(ie,Q){S(this.getCharacterList(),B,ie,Q)},U}(D(L));f.exports=z},function(f,g,s){"use strict";var a={},c=Math.pow(2,24);function o(){for(var r=void 0;r===void 0||a.hasOwnProperty(r)||!isNaN(+r);)r=Math.floor(Math.random()*c).toString(32);return a[r]=!0,r}f.exports=o},function(f,g,s){"use strict";var a=s(19),c=s(20),o=s(35);function r(C,l){var h=C.getBlockMap(),D=C.getEntityMap(),k={},y=l.getStartKey(),L=l.getStartOffset(),T=h.get(y),B=S(D,T,L);B!==T&&(k[y]=B);var w=l.getEndKey(),z=l.getEndOffset(),j=h.get(w);y===w&&(j=B);var U=S(D,j,z);return U!==j&&(k[w]=U),Object.keys(k).length?C.merge({blockMap:h.merge(k),selectionAfter:l}):C.set("selectionAfter",l)}function u(C,l,h){var D;return c(C,function(k,y){return k.getEntity()===y.getEntity()},function(k){return k.getEntity()===l},function(k,y){k<=h&&y>=h&&(D={start:k,end:y})}),typeof D!="object"&&o(!1),D}function S(C,l,h){var D=l.getCharacterList(),k=h>0?D.get(h-1):void 0,y=h<D.count()?D.get(h):void 0,L=k?k.getEntity():void 0,T=y?y.getEntity():void 0;if(T&&T===L){var B=C.__get(T);if(B.getMutability()!=="MUTABLE"){for(var w=u(D,T,h),z=w.start,j=w.end,U;z<j;)U=D.get(z),D=D.set(z,a.applyEntity(U,null)),z++;return l.set("characterList",D)}}return l}f.exports=r},function(f,g,s){"use strict";var a=function(r){};function c(o,r,u,S,C,l,h,D){if(a(r),!o){var k;if(r===void 0)k=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var y=[u,S,C,l,h,D],L=0;k=new Error(r.replace(/%s/g,function(){return y[L++]})),k.name="Invariant Violation"}throw k.framesToPop=1,k}}f.exports=c},function(f,g){function s(a){if(Array.isArray(a))return a}f.exports=s},function(f,g){function s(a,c){var o=[],r=!0,u=!1,S=void 0;try{for(var C=a[Symbol.iterator](),l;!(r=(l=C.next()).done)&&(o.push(l.value),!(c&&o.length===c));r=!0);}catch(h){u=!0,S=h}finally{try{!r&&C.return!=null&&C.return()}finally{if(u)throw S}}return o}f.exports=s},function(f,g){function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}f.exports=s},function(f,g,s){"use strict";s.r(g);var a=s(15),c=s.n(a),o=s(5),r=s.n(o),u=s(21),S=s.n(u),C=s(4),l=s.n(C),h=s(7),D=s.n(h),k=s(8),y=s.n(k),L=s(9),T=s.n(L),B=s(1),w=s.n(B),z=s(10),j=s.n(z),U=s(2),O=s.n(U),ie=s(12),Q=s.n(ie),ue=s(40),Ee=s(42),me=s(0),V=s.n(me),we={base:{remove:"Remove",cancel:"Cancel",confirm:"Confirm",inert:"Insert",width:"Width",height:"Height"},controls:{clear:"Clear",undo:"Undo",redo:"Redo",fontSize:"Font Size",color:"Color",textColor:"Text",tempColors:"Temp Colors",backgroundColor:"Background",bold:"Bold",lineHeight:"Line Height",letterSpacing:"Letter Spacing",textIndent:"Text Indent",increaseIndent:"Increase Indent",decreaseIndent:"Decrease Indent",italic:"Italic",underline:"Underline",strikeThrough:"Strike Through",fontFamily:"Font Family",textAlign:"Text Alignment",alignLeft:"Left Alignment",alignCenter:"Center Alignment",alignRight:"Right Alignment",alignJustify:"Justify Alignment",floatLeft:"Left Float",floatRight:"Right Float",superScript:"Super Script",subScript:"Sub Script",removeStyles:"Remove Styles",headings:"Headings",header:"Header",normal:"Normal",orderedList:"Ordered List",unorderedList:"Unordered List",blockQuote:"Quote",code:"Code",link:"Link",unlink:"Unlink",hr:"Horizontal Line",media:"Media",mediaLibirary:"Media Library",emoji:"Emoji",fullscreen:"Fullscreen",exitFullscreen:"Exit Fullscreen"},linkEditor:{textInputPlaceHolder:"Input link text",linkInputPlaceHolder:"Input link URL",inputWithEnterPlaceHolder:"Input link URL and press Enter",openInNewWindow:"Open in new window",removeLink:"Remove Link"},audioPlayer:{title:"Play Audio"},videoPlayer:{title:"Play Video",embedTitle:"Embed Media"},media:{image:"Image",video:"Video",audio:"Audio",embed:"Embed"}},Ne={base:{remove:"\u5220\u9664",cancel:"\u53D6\u6D88",confirm:"\u786E\u5B9A",inert:"\u63D2\u5165",width:"\u5BBD\u5EA6",height:"\u9AD8\u5EA6"},controls:{clear:"\u6E05\u9664\u5185\u5BB9",undo:"\u64A4\u9500",redo:"\u91CD\u505A",fontSize:"\u5B57\u53F7",lineHeight:"\u884C\u9AD8",letterSpacing:"\u5B57\u95F4\u8DDD",textIndent:"\u6BB5\u843D\u7F29\u8FDB",increaseIndent:"\u589E\u52A0\u7F29\u8FDB",decreaseIndent:"\u51CF\u5C11\u7F29\u8FDB",border:"\u8FB9\u6846",color:"\u989C\u8272",textColor:"\u6587\u5B57\u989C\u8272",backgroundColor:"\u80CC\u666F\u989C\u8272",tempColors:"\u4E34\u65F6\u989C\u8272",bold:"\u52A0\u7C97",italic:"\u659C\u4F53",underline:"\u4E0B\u5212\u7EBF",strikeThrough:"\u5220\u9664\u7EBF",fontFamily:"\u5B57\u4F53",textAlign:"\u6587\u672C\u5BF9\u9F50",alignLeft:"\u5C45\u5DE6",alignCenter:"\u5C45\u4E2D",alignRight:"\u5C45\u53F3",alignJustify:"\u4E24\u7AEF",floatLeft:"\u5DE6\u6D6E\u52A8",floatRight:"\u53F3\u6D6E\u52A8",superScript:"\u4E0A\u6807",subScript:"\u4E0B\u6807",removeStyles:"\u6E05\u9664\u6837\u5F0F",headings:"\u6807\u9898",header:"\u6807\u9898",normal:"\u5E38\u89C4",orderedList:"\u6709\u5E8F\u5217\u8868",unorderedList:"\u65E0\u5E8F\u5217\u8868",blockQuote:"\u5F15\u7528",code:"\u4EE3\u7801",link:"\u94FE\u63A5",unlink:"\u6E05\u9664\u94FE\u63A5",hr:"\u6C34\u5E73\u7EBF",media:"\u5A92\u4F53",mediaLibirary:"\u5A92\u4F53\u5E93",emoji:"\u5C0F\u8868\u60C5",fullscreen:"\u5168\u5C4F",exitFullscreen:"\u9000\u51FA\u5168\u5C4F"},linkEditor:{textInputPlaceHolder:"\u8F93\u5165\u94FE\u63A5\u6587\u5B57",linkInputPlaceHolder:"\u8F93\u5165\u94FE\u63A5\u5730\u5740",inputWithEnterPlaceHolder:"\u8F93\u5165\u94FE\u63A5\u5730\u5740\u5E76\u56DE\u8F66",openInNewWindow:"\u5728\u65B0\u7A97\u53E3\u6253\u5F00",removeLink:"\u79FB\u9664\u94FE\u63A5"},audioPlayer:{title:"\u64AD\u653E\u97F3\u9891\u6587\u4EF6"},videoPlayer:{title:"\u64AD\u653E\u89C6\u9891\u6587\u4EF6",embedTitle:"\u5D4C\u5165\u5F0F\u5A92\u4F53"},media:{image:"\u56FE\u50CF",video:"\u89C6\u9891",audio:"\u97F3\u9891",embed:"\u5D4C\u5165\u5F0F\u5A92\u4F53"}},Me={base:{remove:"\u522A\u9664",cancel:"\u53D6\u6D88",confirm:"\u78BA\u5B9A",inert:"\u63D2\u5165",width:"\u5BEC\u5EA6",height:"\u9AD8\u5EA6"},controls:{clear:"\u6E05\u9664\u5185\u5BB9",undo:"\u5FA9\u539F",redo:"\u53D6\u6D88\u5FA9\u539F",fontSize:"\u5B57\u578B\u5927\u5C0F",color:"\u984F\u8272",textColor:"\u6587\u5B57\u984F\u8272",backgroundColor:"\u80CC\u666F\u984F\u8272",tempColors:"\u81E8\u6642\u984F\u8272",bold:"\u7C97\u9AD4",lineHeight:"\u884C\u9AD8",letterSpacing:"\u5B57\u9593\u8DDD",textIndent:"\u6BB5\u843D\u7E2E\u9032",increaseIndent:"\u589E\u52A0\u7E2E\u9032",decreaseIndent:"\u51CF\u5C11\u7E2E\u9032",border:"\u908A\u6846",italic:"\u659C\u9AD4",underline:"\u5E95\u7DDA",strikeThrough:"\u522A\u9664\u7DDA",fontFamily:"\u5B57\u9AD4",textAlign:"\u6587\u5B57\u5C0D\u9F4A",alignLeft:"\u7F6E\u5DE6",alignCenter:"\u7F6E\u4E2D",alignRight:"\u7F6E\u53F3",alignJustify:"\u5DE6\u53F3\u5C0D\u9F4A",floatLeft:"\u5DE6\u6D6E\u52D5",floatRight:"\u53F3\u6D6E\u52D5",superScript:"\u4E0A\u6A19",subScript:"\u4E0B\u6A19",removeStyles:"\u6E05\u9664\u6A23\u5F0F",headings:"\u6A19\u984C",header:"\u6A19\u984C",normal:"\u5E38\u898F",orderedList:"\u6709\u5E8F\u5217\u8868",unorderedList:"\u7121\u5E8F\u5217\u8868",blockQuote:"\u5F15\u7528",code:"\u7A0B\u5F0F\u78BC",link:"\u9023\u7D50",unlink:"\u6E05\u9664\u9023\u7D50",hr:"\u6C34\u5E73\u7DDA",media:"\u5A92\u9AD4",mediaLibirary:"\u5A92\u9AD4\u5EAB",emoji:"\u8868\u60C5\u7B26\u865F",fullscreen:"\u5168\u87A2\u5E55",exitFullscreen:"\u9000\u51FA\u5168\u87A2\u5E55"},linkEditor:{textInputPlaceHolder:"\u8F38\u5165\u9023\u7D50\u6587\u5B57",linkInputPlaceHolder:"\u8F38\u5165\u9023\u7D50\u5730\u5740",inputWithEnterPlaceHolder:"\u8F38\u5165\u9023\u7D50\u5730\u5740\u4E26\u63DB\u884C",openInNewWindow:"\u5728\u65B0\u8996\u7A97\u6253\u958B",removeLink:"\u79FB\u9664\u9023\u7D50"},audioPlayer:{title:"\u64AD\u653E\u97F3\u6A94"},videoPlayer:{title:"\u64AD\u653E\u5F71\u7247",embedTitle:"\u5D4C\u5165\u5F0F\u5A92\u9AD4"},media:{image:"\u5716\u7247",video:"\u5F71\u7247",audio:"\u97F3\u6A94",embed:"\u5D4C\u5165\u5F0F\u5A92\u9AD4"}},Ke={base:{remove:"Usu\u0144",cancel:"Anuluj",confirm:"Potwierd\u017A",inert:"Wstaw",width:"Szeroko\u015B\u0107",height:"Wysoko\u015B\u0107"},controls:{clear:"Wyczy\u015B\u0107",undo:"Cofnij",redo:"Przywr\xF3\u0107",fontSize:"Wielko\u015B\u0107",color:"Kolor",textColor:"Kolor tekstu",tempColors:"Kolory",backgroundColor:"T\u0142o",bold:"Pogrubienie",lineHeight:"Wysoko\u015B\u0107 linii",letterSpacing:"Odst\u0119p znak\xF3w",textIndent:"Wci\u0119cie tekstu",increaseIndent:"Zwi\u0119ksz wci\u0119cie",decreaseIndent:"Zmniejsz wci\u0119cie",italic:"Italiki",underline:"Podkre\u015Blenie",strikeThrough:"Przekre\u015Blenie",fontFamily:"Czcionka",textAlign:"Wyr\xF3wnanie tekstu",alignLeft:"Do lewej",alignCenter:"Wycentruj",alignRight:"Do prawej",alignJustify:"Wyjustuj",floatLeft:"Do lewej",floatRight:"Do prawej",superScript:"Superskrypt",subScript:"Subskrypt",removeStyles:"Usu\u0144 stylowanie",headings:"Nag\u0142\xF3wki",header:"Nag\u0142\xF3wek",normal:"Normalny",orderedList:"Lista uporz\u0105dkowana",unorderedList:"Lista nieuporz\u0105dkowana",blockQuote:"Cytat",code:"Kod",link:"Link",unlink:"Usu\u0144 link",hr:"Linia pozioma",media:"Media",mediaLibirary:"Biblioteka medi\xF3w",emoji:"Emoji"},linkEditor:{textInputPlaceHolder:"Wpisz tekst linku",linkInputPlaceHolder:"Wpisz Adres URL",inputWithEnterPlaceHolder:"Wpisz adres URL i naci\u015Bnij Enter",openInNewWindow:"Otw\xF3rz w nowym oknie",removeLink:"Usu\u0144 link"},audioPlayer:{title:"Odtw\xF3rz audio"},videoPlayer:{title:"Odtw\xF3rz wideo",embedTitle:"Tytu\u0142"},media:{image:"Obraz",video:"Wideo",audio:"Audio",embed:"Obiekt osadzony"}},qe={base:{remove:"\uC0AD\uC81C",cancel:"\uCDE8\uC18C",confirm:"\uACB0\uC815",inert:"\uC0BD\uC785",width:"\uB108\uBE44",height:"\uB192\uC774"},controls:{clear:"\uCF58\uD150\uCE20\uC9C0\uC6B0\uAE30",undo:"\uCDE8\uC18C",redo:"\uB2E4\uC2DC\uD558\uAE30",fontSize:"\uAE00\uC790\uD06C\uAE30",lineHeight:"\uD589\uB192\uC774",letterSpacing:"\uB2E8\uC5B4\uAC04\uACA9",textIndent:"\uB2E8\uB77D\uB4E4\uC5EC\uC4F0\uAE30",increaseIndent:"\uB4E4\uC5EC\uC4F0\uAE30\uB298\uB9AC\uAE30",decreaseIndent:"\uB4E4\uC5EC\uC4F0\uAE30\uC904\uC774\uAE30",border:"\uAD6D\uACBD",color:"\uC0C9\uC0C1",textColor:"\uD14D\uC2A4\uD2B8\uC0C9\uC0C1",backgroundColor:"\uBC30\uACBD\uC0C9\uC0C1",tempColors:"\uC784\uC2DC\uC0C9",bold:"\uAD75\uAC8C",italic:"\uAE30\uC6B8\uC784\uAF34",underline:"\uBC11\uC904",strikeThrough:"\uCDE8\uC18C\uC120",fontFamily:"\uAE00\uAF34",textAlign:"\uD14D\uC2A4\uD2B8\uC815\uB82C",alignLeft:"\uC67C\uCABD",alignCenter:"\uC911\uC2EC",alignRight:"\uC624\uB978\uCABD",alignJustify:"\uC591\uCABD\uB05D",floatLeft:"\uB5A0\uB2E4\uB2C8\uAE30",floatRight:"\uC624\uB978\uCABD\uBD80\uB3D9",superScript:"\uC704\uCCA8\uC790",subScript:"\uCCA8\uC790",removeStyles:"\uC2A4\uD0C0\uC77C\uC9C0\uC6B0\uAE30",headings:"\uC81C\uBAA9",header:"\uC81C\uBAA9",normal:"\uC7AC\uB798\uC2DD",orderedList:"\uC21C\uC11C\uAC00\uC9C0\uC815\uB41C\uBAA9\uB85D",unorderedList:"\uC815\uB82C\uB418\uC9C0\uC54A\uC740\uBAA9\uB85D",blockQuote:"\uCC38\uACE0\uBB38\uD5CC",code:"\uCF54\uB4DC",link:"\uB9C1\uD06C",unlink:"\uB9C1\uD06C\uC0AD\uC81C",hr:"\uC218\uD3C9\uC120",media:"\uBBF8\uB514\uC5B4",mediaLibirary:"\uBBF8\uB514\uC5B4\uB77C\uC774\uBE0C\uB7EC\uB9AC",emoji:"\uC791\uC740\uD45C\uD604",fullscreen:"\uC804\uCCB4\uD654\uBA74",exitFullscreen:"\uC804\uCCB4\uD654\uBA74\uC885\uB8CC"},linkEditor:{textInputPlaceHolder:"\uB9C1\uD06C\uD14D\uC2A4\uD2B8\uC785\uB825",linkInputPlaceHolder:"\uB9C1\uD06C\uC8FC\uC18C\uC785\uB825",inputWithEnterPlaceHolder:"\uB9C1\uD06C\uC8FC\uC18C\uC785\uB825.",openInNewWindow:"\uC0C8\uCC3D\uC5F4\uAE30",removeLink:"\uB9C1\uD06C\uC0AD\uC81C"},audioPlayer:{title:"\uC624\uB514\uC624\uD30C\uC77C\uC7AC\uC0DD"},videoPlayer:{title:"\uBE44\uB514\uC624\uD30C\uC77C\uC7AC\uC0DD",embedTitle:"\uC784\uBCA0\uB514\uB4DC\uBBF8\uB514\uC5B4"},media:{image:"\uC774\uBBF8\uC9C0",video:"\uBE44\uB514\uC624",audio:"\uC624\uB514\uC624",embed:"\uC784\uBCA0\uB514\uB4DC\uBBF8\uB514\uC5B4"}},it={base:{remove:"Kald\u0131r",cancel:"\u0130ptal",confirm:"Onayla",inert:"Ekle",width:"Geni\u015Flik",height:"Y\xFCkseklik"},controls:{clear:"Temizle",undo:"Geri al",redo:"\u0130leri al",fontSize:"Yaz\u0131 boyutu",color:"Renk",textColor:"Yaz\u0131 rengi",tempColors:"Ge\xE7ici renkler",backgroundColor:"Arkaplan",bold:"Kal\u0131n",lineHeight:"Sat\u0131r y\xFCksekli\u011Fi",letterSpacing:"Harf aral\u0131\u011F\u0131",textIndent:"\xC7entik aral\u0131\u011F\u0131",increaseIndent:"\xC7enti\u011Fi geni\u015Flet",decreaseIndent:"\xC7enti\u011Fi daralt",italic:"E\u011Fik",underline:"Alt\u0131 \xE7izili",strikeThrough:"\xDCst\xFC \xE7izili",fontFamily:"Yaz\u0131 tipi",textAlign:"Metin Hizalama",alignLeft:"Sola hizala",alignCenter:"Ortaya hizala",alignRight:"Sa\u011Fa hizala",alignJustify:"Her iki tarafa hizala",floatLeft:"Sola yat\u0131r",floatRight:"Sa\u011Fa yat\u0131r",superScript:"Ana kod",subScript:"Alt kod",removeStyles:"Stilleri kald\u0131r",headings:"Ba\u015Fl\u0131klar",header:"Ba\u015Fl\u0131k",normal:"Normal",orderedList:"S\u0131ral\u0131 liste",unorderedList:"S\u0131ras\u0131z liste",blockQuote:"Al\u0131nt\u0131",code:"Kod",link:"Ba\u011Flant\u0131",unlink:"Ba\u011Flant\u0131y\u0131 kald\u0131r",hr:"Yatay \xE7izgi",media:"Medya",mediaLibirary:"K\xFCt\xFCphane",emoji:"\u0130fade",fullscreen:"Tam ekran",exitFullscreen:"Tam ekrandan \xE7\u0131k"},linkEditor:{textInputPlaceHolder:"Ba\u011Flant\u0131 metnini girin",linkInputPlaceHolder:"Ba\u011Flant\u0131 URL' si girin",inputWithEnterPlaceHolder:"Ba\u011Flant\u0131 URL'si girin ve Enter' a bas\u0131n",openInNewWindow:"Yeni pencerede a\xE7",removeLink:"Ba\u011Flant\u0131y\u0131 kald\u0131r"},audioPlayer:{title:"Ses \xE7al"},videoPlayer:{title:"G\xF6r\xFCnt\xFC oynat",embedTitle:"G\xF6r\xFCnt\xFCy\xFC g\xF6m"},media:{image:"Resim",video:"G\xF6r\xFCnt\xFC",audio:"Ses",embed:"G\xF6m\xFCl\xFC nesne"}},et={base:{remove:"\u524A\u9664",cancel:"\u30AD\u30E3\u30F3\u30BB\u30EB",confirm:"\u6C7A\u5B9A",inert:"\u633F\u5165",width:"\u5E45",height:"\u9AD8\u3055"},controls:{clear:"\u30AF\u30EA\u30A2\u30B3\u30F3\u30C6\u30F3\u30C4",undo:"\u30AD\u30E3\u30F3\u30BB\u30EB",redo:"\u30AD\u30E3\u30F3\u30BB\u30EB",fontSize:"\u30D5\u30A9\u30F3\u30C8\u30B5\u30A4\u30BA",lineHeight:"\u30D5\u30A9\u30F3\u30C8\u30B5\u30A4\u30BA",letterSpacing:"\u30EF\u30FC\u30C9\u9593\u9694",textIndent:"\u6BB5\u843D\u306E\u30A4\u30F3\u30C7\u30F3\u30C8",increaseIndent:"\u30A4\u30F3\u30C7\u30F3\u30C8\u3092\u5897\u3084\u3059",decreaseIndent:"\u30A4\u30F3\u30C7\u30F3\u30C8\u3092\u6E1B\u3089\u3059",border:"\u56FD\u5883",color:"\u8272",textColor:"\u30C6\u30AD\u30B9\u30C8\u306E\u8272",backgroundColor:"\u80CC\u666F\u8272",tempColors:"\u4EEE\u8272",bold:"\u592A\u5B57",italic:"\u30A4\u30BF\u30EA\u30C3\u30AF\u4F53",underline:"\u4E0B\u7DDA",strikeThrough:"\u53D6\u308A\u6D88\u3057\u7DDA",fontFamily:"\u30D5\u30A9\u30F3\u30C8",textAlign:"\u30C6\u30AD\u30B9\u30C8\u306E\u914D\u7F6E",alignLeft:"\u5DE6",alignCenter:"\u4E2D\u592E\u63C3\u3048",alignRight:"\u53F3\u306B\u7ACB\u3064",alignJustify:"\u4E21\u7AEF",floatLeft:"\u5DE6\u30D5\u30ED\u30FC\u30C6\u30A3\u30F3\u30B0",floatRight:"\u53F3\u30D5\u30ED\u30FC\u30C6\u30A3\u30F3\u30B0",superScript:"\u4E0A\u4ED8\u304D",subScript:"\u4E0B\u4ED8\u304D\u6587\u5B57",removeStyles:"\u30AF\u30EA\u30A2\u30B9\u30BF\u30A4\u30EB",headings:"\u5F79\u8077",header:"\u5F79\u8077",normal:"\u5F93\u6765\u306E",orderedList:"\u9806\u5E8F\u4ED8\u304D\u30EA\u30B9\u30C8",unorderedList:"\u756A\u53F7\u306A\u3057\u30EA\u30B9\u30C8",blockQuote:"\u53C2\u7167",code:"\u30B3\u30FC\u30C9",link:"\u30EA\u30F3\u30AF",unlink:"\u30EA\u30F3\u30AF\u3092\u89E3\u9664",hr:"\u6A2A\u7DDA",media:"\u30E1\u30C7\u30A3\u30A2",mediaLibirary:"\u30E1\u30C7\u30A3\u30A2\u30E9\u30A4\u30D6\u30E9\u30EA\u30FC",emoji:"\u5C0F\u3055\u306A\u8868\u73FE",fullscreen:"\u5168\u753B\u9762",exitFullscreen:"\u5168\u753B\u9762\u3092\u9000\u304F"},linkEditor:{textInputPlaceHolder:"\u30EA\u30F3\u30AF\u30C6\u30AD\u30B9\u30C8\u3092\u5165\u529B",linkInputPlaceHolder:"\u30EA\u30F3\u30AF\u30A2\u30C9\u30EC\u30B9\u3092\u5165\u529B",inputWithEnterPlaceHolder:"\u30EA\u30F3\u30AF\u30A2\u30C9\u30EC\u30B9\u3092\u5165\u529B\u3057\u3066\u623B\u308A\u307E\u3059",openInNewWindow:"\u65B0\u3057\u3044\u30A6\u30A3\u30F3\u30C9\u30A6\u3067\u958B\u304F",removeLink:"\u65B0\u3057\u3044\u30A6\u30A3\u30F3\u30C9\u30A6\u3067\u958B\u304F"},audioPlayer:{title:"\u30AA\u30FC\u30C7\u30A3\u30AA\u30D5\u30A1\u30A4\u30EB\u3092\u518D\u751F\u3059\u308B"},videoPlayer:{title:"\u30D3\u30C7\u30AA\u30D5\u30A1\u30A4\u30EB\u3092\u518D\u751F\u3059\u308B",embedTitle:"\u57CB\u3081\u8FBC\u307F\u30E1\u30C7\u30A3\u30A2"},media:{image:"\u753B\u50CF",video:"\u30D3\u30C7\u30AA",audio:"\u97F3\u58F0",embed:"\u57CB\u3081\u8FBC\u307F\u30E1\u30C7\u30A3\u30A2"}},Je={base:{remove:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0430",confirm:"\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u044C",insert:"\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C",width:"\u0428\u0438\u0440\u0438\u043D\u0430",height:"\u0412\u044B\u0441\u043E\u0442\u0430"},controls:{clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",undo:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C",redo:"\u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C",fontSize:"\u0420\u0430\u0437\u043C\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430",color:"\u0426\u0432\u0435\u0442",textColor:"\u0426\u0432\u0435\u0442 \u0442\u0435\u043A\u0441\u0442\u0430",tempColors:"Temp Colors",backgroundColor:"\u0426\u0432\u0435\u0442 \u0444\u043E\u043D\u0430",bold:"\u0416\u0438\u0440\u043D\u044B\u0439",lineHeight:"\u041C\u0435\u0436\u0441\u0442\u0440\u043E\u0447\u043D\u044B\u0439 \u0438\u043D\u0442\u0435\u0440\u0432\u0430\u043B",letterSpacing:"\u041C\u0435\u0436\u0431\u0443\u043A\u0432\u0435\u043D\u043D\u044B\u0439 \u0438\u043D\u0442\u0435\u0440\u0432\u0430\u043B",textIndent:"\u041E\u0442\u0441\u0442\u0443\u043F",increaseIndent:"\u0423\u0432\u0435\u043B\u0438\u0447\u0438\u0442\u044C \u043E\u0442\u0441\u0442\u0443\u043F",decreaseIndent:"\u0423\u043C\u0435\u043D\u044C\u0448\u0438\u0442\u044C \u043E\u0442\u0441\u0442\u0443\u043F",italic:"\u041A\u0443\u0440\u0441\u0438\u0432",underline:"\u041F\u043E\u0434\u0447\u0435\u0440\u043A\u043D\u0443\u0442\u044B\u0439",strikeThrough:"\u041F\u0435\u0440\u0435\u0447\u0435\u0440\u043A\u043D\u0443\u0442\u044B\u0439",fontFamily:"\u0428\u0440\u0438\u0444\u0442",textAlign:"\u0420\u0430\u0441\u043F\u043E\u043B\u043E\u0436\u0435\u043D\u0438\u0435 \u0442\u0435\u043A\u0441\u0442\u0430",alignLeft:"\u041F\u043E \u043B\u0435\u0432\u043E\u043C\u0443 \u043A\u0440\u0430\u044E",alignCenter:"\u041F\u043E \u0446\u0435\u043D\u0442\u0440\u0443",alignRight:"\u041F\u043E \u043F\u0440\u0430\u0432\u043E\u043C\u0443 \u043A\u0440\u0430\u044E",alignJustify:"\u041F\u043E \u0448\u0438\u0440\u0438\u043D\u0435",floatLeft:"\u041E\u0431\u0442\u0435\u043A\u0430\u043D\u0438\u0435 \u0441\u043B\u0435\u0432\u0430",floatRight:"\u041E\u0431\u0442\u0435\u043A\u0430\u043D\u0438\u0435 \u0441\u043F\u0440\u0430\u0432\u0430",superScript:"\u041D\u0430\u0434\u0441\u0442\u0440\u043E\u0447\u043D\u044B\u0439 \u0438\u043D\u0434\u0435\u043A\u0441",subScript:"\u041F\u043E\u0434\u0441\u0442\u0440\u043E\u0447\u043D\u044B\u0439 \u0438\u043D\u0434\u0435\u043A\u0441",removeStyles:"\u0423\u0431\u0440\u0430\u0442\u044C \u0441\u0442\u0438\u043B\u0438",headings:"\u0417\u0430\u0433\u043E\u043B\u043E\u0432\u043A\u0438",header:"\u0417\u0430\u0433\u043E\u043B\u043E\u0432\u043E\u043A",normal:"\u041E\u0431\u044B\u0447\u043D\u044B\u0439",orderedList:"\u0423\u043F\u043E\u0440\u044F\u0434\u043E\u0447\u0435\u043D\u043D\u044B\u0439 \u0441\u043F\u0438\u0441\u043E\u043A",unorderedList:"\u041D\u0435\u0443\u043F\u043E\u0440\u044F\u0434\u043E\u0447\u0435\u043D\u043D\u044B\u0439 \u0441\u043F\u0438\u0441\u043E\u043A",blockQuote:"\u0426\u0438\u0442\u0430\u0442\u0430",code:"\u041A\u043E\u0434",link:"\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443",unlink:"\u0423\u0431\u0440\u0430\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443",hr:"\u0413\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u044C\u043D\u0430\u044F \u043B\u0438\u043D\u0438\u044F",media:"\u041C\u0435\u0434\u0438\u0430",mediaLibirary:"\u041C\u0435\u0434\u0438\u0430 \u0431\u0438\u0431\u043B\u0438\u043E\u0442\u0435\u043A\u0430",emoji:"Emoji",fullscreen:"\u041F\u043E\u043B\u043D\u043E\u044D\u043A\u0440\u0430\u043D\u043D\u044B\u0439 \u0440\u0435\u0436\u0438\u043C",exitFullscreen:"\u0412\u044B\u0439\u0442\u0438 \u0438\u0437 \u043F\u043E\u043B\u043D\u043E\u044D\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0430"},linkEditor:{textInputPlaceHolder:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0442\u0435\u043A\u0441\u0442 \u0441\u0441\u044B\u043B\u043A\u0438",linkInputPlaceHolder:"\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443",inputWithEnterPlaceHolder:"\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443 \u0438 \u043D\u0430\u0436\u0430\u0442\u044C Enter",openInNewWindow:"\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0432 \u043D\u043E\u0432\u043E\u043C \u043E\u043A\u043D\u0435",removeLink:"\u0423\u0431\u0440\u0430\u0442\u044C \u0441\u0441\u044B\u043B\u043A\u0443"},audioPlayer:{title:"\u0412\u043E\u0441\u043F\u0440\u043E\u0438\u0437\u0432\u0435\u0441\u0442\u0438 \u0430\u0443\u0434\u0438\u043E\u0444\u0430\u0439\u043B"},videoPlayer:{title:"\u0412\u043E\u0441\u043F\u0440\u043E\u0438\u0437\u0432\u0435\u0441\u0442\u0438 \u0432\u0438\u0434\u0435\u043E\u0444\u0430\u0439\u043B",embedTitle:"Embed Media"},media:{image:"\u041A\u0430\u0440\u0442\u0438\u043D\u043A\u0430",video:"\u0412\u0438\u0434\u0435\u043E",audio:"\u0410\u0443\u0434\u0438\u043E",embed:"\u0412\u0441\u0442\u0440\u043E\u0435\u043D\u043D\u043E\u0435"}},Be={base:{remove:"Supprimer",cancel:"Annuler",confirm:"Confirmer",inert:"Ins\xE9rer",width:"Largeur",height:"Hauteur"},controls:{clear:"Effacer",undo:"Annuler",redo:"Refaire",fontSize:"Taille de police",color:"Couleur",textColor:"Texte",tempColors:"Couleurs temporaire",backgroundColor:"Couleur d'arri\xE8re plan",bold:"Gras",lineHeight:"Hauteur de ligne",letterSpacing:"Espacement des lettres",textIndent:"Indentation du texte",increaseIndent:"Augmenter l'indentation",decreaseIndent:"R\xE9duire l'indentation",italic:"Italique",underline:"Souligner",strikeThrough:"Barrer",fontFamily:"Police d'\xE9criture",textAlign:"Alignement du texte",alignLeft:"Aligner \xE0 gauche",alignCenter:"Aligner au centre",alignRight:"Aligner \xE0 droite",alignJustify:"Justifier",floatLeft:"D\xE9placer \xE0 gauche",floatRight:"D\xE9placer \xE0 droite",superScript:"Super-script",subScript:"Sous-script",removeStyles:"Supprimer les styles",headings:"Titres",header:"Ent\xEAtes",normal:"Normal",orderedList:"Liste ordonn\xE9e",unorderedList:"Liste non-ordonn\xE9e",blockQuote:"Citation",code:"Code",link:"Ins\xE9rer un lien",unlink:"Supprimer le lien",hr:"Ligne horizontale",media:"M\xE9dia",mediaLibirary:"Biblioth\xEAque",emoji:"Emoji",fullscreen:"Plein \xE9cran",exitFullscreen:"Quitter le plein \xE9cran"},linkEditor:{textInputPlaceHolder:"Ins\xE9rer le texte \xE0 afficher",linkInputPlaceHolder:"Ins\xE9rer le lien URL",inputWithEnterPlaceHolder:"Ins\xE9rer le lien URL puis appuyer sur Entr\xE9e",openInNewWindow:"Ouvrir dans une nouvelle fen\xEAtre",removeLink:"Supprimer le lien"},audioPlayer:{title:"Lancer le son audio"},videoPlayer:{title:"Lancer la video",embedTitle:"Int\xE9grer m\xE9dia"},media:{image:"Image",video:"Vid\xE9o",audio:"Audio",embed:"Int\xE9gr\xE9"}},He={base:{remove:"Remover",cancel:"Cancelar",confirm:"Confirmar",inert:"Inserir",width:"Largura",height:"Altura"},controls:{clear:"Limpar",undo:"Desfazer",redo:"Refazer",fontSize:"Tamanho da Fonte",color:"Cor",textColor:"Texto",tempColors:"Temp Colors",backgroundColor:"Cor de Fundo",bold:"Negrito",lineHeight:"Altura da LinhaLine Height",letterSpacing:"Espa\xE7amento entre Letras",textIndent:"Identa\xE7\xE3o de Texto",increaseIndent:"Aumentar Identa\xE7\xE3o",decreaseIndent:"Diminuir Ident\xE7\xE3o",italic:"It\xE1lico",underline:"Sublinhado",strikeThrough:"Riscado",fontFamily:"Fam\xEDlia da Fonte",textAlign:"Alinhamento de Texto",alignLeft:"Alinhamento \xE0 Esquerda",alignCenter:"Alinhamento Centralizado",alignRight:"Alinhamento \xE0 Direita",alignJustify:"Alinhamento Justificado",floatLeft:"Flutua\xE7\xE3o \xE0 Esquerda",floatRight:"Flutua\xE7\xE3o \xE0 Direita",superScript:"Sobrescrito",subScript:"Subscrito",removeStyles:"Remover Estilos",headings:"Cabe\xE7alhos",header:"Cabe\xE7alho",normal:"Normal",orderedList:"Lista Ordenada",unorderedList:"Lista N\xE3o Ordenada",blockQuote:"Cita\xE7\xE3o",code:"C\xF3digo",link:"Link",unlink:"Remover Link",hr:"Linha Horizontal",media:"M\xEDdia",mediaLibirary:"Biblioteca de M\xEDdia",emoji:"Emoji",fullscreen:"Tela Cheia",exitFullscreen:"Sair de Tela Cheia"},linkEditor:{textInputPlaceHolder:"Insira o texto do link",linkInputPlaceHolder:"Insira a URL do link",inputWithEnterPlaceHolder:"Insira a URL do link e aperte Enter",openInNewWindow:"Abrir em nova janela",removeLink:"Remover Link"},audioPlayer:{title:"Tocar \xC1udio"},videoPlayer:{title:"Tocar V\xEDdeo",embedTitle:"M\xEDdia Embutida"},media:{image:"Imagem",video:"V\xEDdeo",audio:"\xC1udio",embed:"Embutido"}},Qe={base:{remove:"X\xF3a b\u1ECF",cancel:"H\u1EE7y b\u1ECF",confirm:"X\xE1c nh\u1EADn",inert:"Ch\xE8n v\xE0o",width:"\u0110\u1ED9 r\u1ED9ng",height:"\u0110\u1ED9 cao"},controls:{clear:"X\xF3a to\xE0n b\u1ED9 n\u1ED9i dung",undo:"H\u1EE7y b\u1ECF",redo:"L\xE0m l\u1EA1i",fontSize:"Size ch\u1EEF",lineHeight:"\u0110\u1ED9 cao h\xE0ng",letterSpacing:"Kho\u1EA3ng c\xE1ch ch\u1EEF",textIndent:"Kho\u1EA3ng c\xE1ch \u0111o\u1EA1n v\u0103n",increaseIndent:"T\u0103ng kho\u1EA3ng c\xE1ch",decreaseIndent:"Gi\u1EA3m kho\u1EA3ng c\xE1ch",border:"\u0110\u01B0\u1EDDng vi\u1EC1n",color:"M\xE0u s\u1EAFc",textColor:"M\xE0u ch\u1EEF",backgroundColor:"M\xE0u n\u1EC1n",tempColors:"M\xE0u t\u1EA1m th\u1EDDi",bold:"T\xF4 \u0111\u1EADm",italic:"In nghi\xEAng",underline:"G\u1EA1ch d\u01B0\u1EDBi",strikeThrough:"X\xF3a g\u1EA1ch d\u01B0\u1EDBi",fontFamily:"Font ch\u1EEF",textAlign:"C\u0103n ch\u1EC9nh v\u0103n b\u1EA3n",alignLeft:"C\u0103n tr\xE1i",alignCenter:"C\u0103n gi\u1EEFa",alignRight:"C\u0103n ph\u1EA3i",alignJustify:"Hai l\u1EC1",floatLeft:"Float left",floatRight:"Float right",superScript:"Ch\u1EC9 s\u1ED1 tr\xEAn",subScript:"Ch\u1EC9 s\u1ED1 d\u01B0\u1EDBi",removeStyles:"X\xF3a style",headings:"Ti\xEAu \u0111\u1EC1",header:"Ti\xEAu \u0111\u1EC1",normal:"Quy t\u1EAFc th\xF4ng th\u01B0\u1EDDng",orderedList:"Ki\u1EC3u s\u1EAFp x\u1EBFp",unorderedList:"Ki\u1EC3u kh\xF4ng s\u1EAFp x\u1EBFp",blockQuote:"Tr\xEDch d\u1EABn",code:"Code",link:"Li\xEAn k\u1EBFt",unlink:"G\u1EE1 li\xEAn k\u1EBFt",hr:"Horizontal line",media:"Media",mediaLibirary:"Kho media",emoji:"Bi\u1EC3u t\u01B0\u1EE3ng c\u1EA3m x\xFAc",fullscreen:"To\xE0n m\xE0n h\xECnh",exitFullscreen:"Tho\xE1t kh\u1ECFi ch\u1EBF \u0111\u1ED9 to\xE0n m\xE0n h\xECnh"},linkEditor:{textInputPlaceHolder:"Nh\u1EADp v\u0103n b\u1EA3n li\xEAn k\u1EBFt",linkInputPlaceHolder:"Nh\u1EADp \u0111\u1ECBa ch\u1EC9 li\xEAn k\u1EBFt",inputWithEnterPlaceHolder:"Nh\u1EADp \u0111\u1ECBa ch\u1EC9 li\xEAn k\u1EBFt v\xE0 Enter",openInNewWindow:"M\u1EDF trong tab m\u1EDBi",removeLink:"G\u1EE1 li\xEAn k\u1EBFt"},audioPlayer:{title:"Ph\xE1t t\u1EC7p \xE2m thanh"},videoPlayer:{title:"Ph\xE1t t\u1EC7p video",embedTitle:"Media nh\xFAng"},media:{image:"H\xECnh \u1EA3nh",video:"Video",audio:"\xC2m thanh",embed:"Media nh\xFAng"}},ot={en:we,zh:Ne,"zh-hant":Me,pl:Ke,kr:qe,tr:it,jpn:et,ru:Je,fr:Be,"pt-br":He,"vi-vn":Qe},mt=s(17),rt=s.n(mt),Ce=s(3),$e=s(6),Ft=s(13),Et=s.n(Ft),De=function(ee){return function(R){return R.keyCode===83&&($e.KeyBindingUtil.hasCommandModifier(R)||$e.KeyBindingUtil.isCtrlKeyCommand(R))?"braft-save":ee&&ee(R)||Object($e.getDefaultKeyBinding)(R)}},ne={language:"zh",controls:["undo","redo","separator","font-size","line-height","letter-spacing","separator","text-color","bold","italic","underline","strike-through","separator","superscript","subscript","remove-styles","emoji","separator","text-indent","text-align","separator","headings","list-ul","list-ol","blockquote","code","separator","media","link","table","split","hr","separator","clear","separator","fullscreen"],excludeControls:[],extendControls:[],extendAtomics:[],componentBelowControlBar:null,media:{pasteImage:!0,imagePasteLimit:5,image:!0,video:!0,audio:!0,uploadFn:null,validateFn:null,onBeforeDeselect:null,onDeselect:null,onBeforeSelect:null,onSelect:null,onBeforeRemove:null,onRemove:null,onCancel:null,onFileSelect:null,onBeforeInsert:null,onInsert:null,onChange:null,accepts:{image:"image/png,image/jpeg,image/gif,image/webp,image/apng,image/svg",video:"video/mp4",audio:"audio/mp3"},externals:{audio:!0,video:!0,image:!0,embed:!0}},imageControls:["float-left","float-right","align-left","align-center","align-right","link","size","remove"],imageResizable:!0,imageEqualRatio:!0,colors:["#000000","#333333","#666666","#999999","#cccccc","#ffffff","#61a951","#16a085","#07a9fe","#003ba5","#8e44ad","#f32784","#c0392b","#d35400","#f39c12","#fdda00"],colorPicker:null,colorPickerTheme:"dark",colorPickerAutoHide:!0,codeTabIndents:2,headings:["header-one","header-two","header-three","header-four","header-five","header-six","unstyled"],textAligns:["left","center","right","justify"],textBackgroundColor:!0,allowInsertLinkText:!1,defaultLinkTarget:"",letterSpacings:[0,1,2,3,4,5,6],lineHeights:[1,1.2,1.5,1.75,2,2.5,3,4],fontSizes:[12,14,16,18,20,24,28,30,32,36,40,48,56,64,72,96,120,144],fontFamilies:[{name:"Araial",family:"Arial, Helvetica, sans-serif"},{name:"Georgia",family:"Georgia, serif"},{name:"Impact",family:"Impact, serif"},{name:"Monospace",family:'"Courier New", Courier, monospace'},{name:"Tahoma",family:'tahoma, arial, "Hiragino Sans GB", \u5B8B\u4F53, sans-serif'}],converts:{unitExportFn:function(R,W){return W==="line-height"?R:"".concat(R,"px")}},emojis:["\u{1F923}","\u{1F64C}","\u{1F49A}","\u{1F49B}","\u{1F44F}","\u{1F609}","\u{1F4AF}","\u{1F495}","\u{1F49E}","\u{1F498}","\u{1F499}","\u{1F49D}","\u{1F5A4}","\u{1F49C}","\u2764\uFE0F","\u{1F60D}","\u{1F63B}","\u{1F493}","\u{1F497}","\u{1F60B}","\u{1F607}","\u{1F602}","\u{1F639}","\u{1F618}","\u{1F496}","\u{1F601}","\u{1F600}","\u{1F91E}","\u{1F632}","\u{1F604}","\u{1F60A}","\u{1F44D}","\u{1F60C}","\u{1F603}","\u{1F605}","\u270C\uFE0F","\u{1F917}","\u{1F48B}","\u{1F617}","\u{1F63D}","\u{1F61A}","\u{1F920}","\u{1F619}","\u{1F63A}","\u{1F444}","\u{1F638}","\u{1F60F}","\u{1F63C}","\u{1F44C}","\u{1F60E}","\u{1F606}","\u{1F61B}","\u{1F64F}","\u{1F91D}","\u{1F642}","\u{1F911}","\u{1F61D}","\u{1F610}","\u{1F611}","\u{1F924}","\u{1F624}","\u{1F643}","\u{1F921}","\u{1F636}","\u{1F62A}","\u{1F634}","\u{1F635}","\u{1F613}","\u{1F44A}","\u{1F626}","\u{1F637}","\u{1F910}","\u{1F61C}","\u{1F913}","\u{1F47B}","\u{1F625}","\u{1F644}","\u{1F914}","\u{1F912}","\u{1F641}","\u{1F614}","\u{1F62F}","\u2639\uFE0F","\u2620\uFE0F","\u{1F630}","\u{1F629}","\u{1F616}","\u{1F615}","\u{1F612}","\u{1F623}","\u{1F622}","\u{1F62E}","\u{1F63F}","\u{1F927}","\u{1F62B}","\u{1F925}","\u{1F61E}","\u{1F62C}","\u{1F44E}","\u{1F480}","\u{1F633}","\u{1F628}","\u{1F915}","\u{1F922}","\u{1F631}","\u{1F62D}","\u{1F620}","\u{1F608}","\u{1F627}","\u{1F494}","\u{1F61F}","\u{1F640}","\u{1F4A9}","\u{1F47F}","\u{1F621}","\u{1F63E}","\u{1F595}"],stripPastedStyles:!1,triggerChangeOnMount:!0,className:"",style:{},controlBarClassName:"",controlBarStyle:{},contentClassName:"",contentStyle:{},draftProps:{},hooks:{},onChange:null,onFocus:null,onBlur:null,onTab:null,onDelete:null,onSave:null,fixPlaceholder:!1},_=s(22),le=s.n(_),se=s(23),ge=function(R,W,n){if(n.editorProps.handleKeyCommand&&n.editorProps.handleKeyCommand(R,W,n)==="handled")return"handled";if(R==="braft-save")return n.editorProps.onSave&&n.editorProps.onSave(W),"handled";var K=n.editorProps,X=K.controls,oe=K.excludeControls,Y=(X.indexOf("text-indent")!==0||X.find(function(Ht){return Ht.key==="text-indent"}))&&oe.indexOf("text-indent")===-1,ce=W.getSelection().getStartOffset(),pe=W.getSelection().getEndOffset(),je=ce===0&&pe===0;if(R==="backspace"){if(n.editorProps.onDelete&&n.editorProps.onDelete(W)===!1)return"handled";var Ye=Ce.ContentUtils.getSelectionBlockType(W);Y&&je&&Ye!=="code-block"&&n.setValue(Ce.ContentUtils.decreaseSelectionIndent(W))}if(R==="tab"){var Xe=Ce.ContentUtils.getSelectionBlockType(W);if(Xe==="code-block")return n.setValue(Ce.ContentUtils.insertText(W," ".repeat(n.editorProps.codeTabIndents))),"handled";if(Xe==="ordered-list-item"||Xe==="unordered-list-item"){var st=$e.RichUtils.onTab(event,W,4);return st!==W&&n.setValue(st),"handled"}else if(Xe!=="atomic"&&Y&&je)return n.setValue(Ce.ContentUtils.increaseSelectionIndent(W)),"handled"}var At=Ce.ContentUtils.handleKeyCommand(W,R);return At?(n.setValue(At),"handled"):"not-handled"},ye=function(R,W,n){if(n.editorProps.handleReturn&&n.editorProps.handleReturn(R,W,n)==="handled")return"handled";var K=Ce.ContentUtils.getSelectionBlock(W),X=K.getType();if(X==="unordered-list-item"||X==="ordered-list-item")return K.getLength()===0?(n.setValue(Ce.ContentUtils.toggleSelectionBlockType(W,"unstyled")),"handled"):"not-handled";if(X==="code-block")return R.which===13&&(R.getModifierState("Shift")||R.getModifierState("Alt")||R.getModifierState("Control"))?(n.setValue(Ce.ContentUtils.toggleSelectionBlockType(W,"unstyled")),"handled"):"not-handled";if(X==="blockquote"&&R.which===13)if(R.getModifierState("Shift")||R.getModifierState("Alt")||R.getModifierState("Control"))R.which=0;else return n.setValue($e.RichUtils.insertSoftNewline(W)),"handled";var oe=Object(se.handleNewLine)(W,R);return oe?(n.setValue(oe),"handled"):"not-handled"},We=function(R,W,n){return n.editorProps.handleBeforeInput&&n.editorProps.handleBeforeInput(R,W,n)==="handled"?"handled":"not-handled"},Fe=function(R,W){var n=W.state.editorState,K=Ce.ContentUtils.getSelectedBlocks(n);if(K&&K.length>1){var X=$e.EditorState.push(n,$e.Modifier.removeRange(n.getCurrentContent(),n.getSelection(),"backward"),"remove-range");W.setValue(X)}},Ve=function(R,W,n){if(n.editorProps.readOnly||n.editorProps.disabled)return"handled";if(window&&window.__BRAFT_DRAGING__IMAGE__){var K=$e.EditorState.forceSelection(n.state.editorState,R);return K=Ce.ContentUtils.insertMedias(K,[window.__BRAFT_DRAGING__IMAGE__.mediaData]),K=Ce.ContentUtils.removeBlock(K,window.__BRAFT_DRAGING__IMAGE__.block,K.getSelection()),window.__BRAFT_DRAGING__IMAGE__=null,n.lockOrUnlockEditor(!0),n.setValue(K),"handled"}else if(!W||!W.getText())return"handled";return"not-handled"},Ue=function(R,W){var n=r()({},W.constructor.defaultProps.media,W.editorProps.media),K=n.pasteImage,X=n.validateFn,oe=n.imagePasteLimit;return K&&R.slice(0,oe).forEach(function(Y){if(Y&&Y.type.indexOf("image")>-1&&W.braftFinder){var ce=X?X(Y):!0;ce instanceof Promise?ce.then(function(){W.braftFinder.uploadImage(Y,function(pe){W.isLiving&&W.setValue(Ce.ContentUtils.insertMedias(W.state.editorState,[pe]))})}):ce&&W.braftFinder.uploadImage(Y,function(pe){W.isLiving&&W.setValue(Ce.ContentUtils.insertMedias(W.state.editorState,[pe]))})}}),R[0]&&R[0].type.indexOf("image")>-1&&K?"handled":"not-handled"},ct=function(R,W,n){return n.editorProps.handleDroppedFiles&&n.editorProps.handleDroppedFiles(R,W,n)==="handled"?"handled":Ue(W,n)},Ze=function(R,W){return W.editorProps.handlePastedFiles&&W.editorProps.handlePastedFiles(R,W)==="handled"?"handled":Ue(R,W)},ut=function(R,W){var n=le()(W.state.editorState);if(n&&n.toArray)try{var K=$e.ContentState.createFromBlockArray(n.toArray()),X=$e.EditorState.createWithContent(K),oe=R.clipboardData||window.clipboardData||R.originalEvent.clipboardData;X.setConvertOptions(W.state.editorState.convertOptions),oe.setData("text/html",X.toHTML()),oe.setData("text/plain",X.toText()),R.preventDefault()}catch(Y){console.warn(Y)}},lt=function(R,W,n,K){if(K.editorProps.handlePastedText&&K.editorProps.handlePastedText(R,W,n,K)==="handled")return"handled";if(!W||K.editorProps.stripPastedStyles)return!1;var X=Ce.ColorUtils.detectColorsFromHTMLString(W);return K.setState({tempColors:[].concat(Q()(K.state.tempColors),Q()(X)).filter(function(oe){return K.editorProps.colors.indexOf(oe)===-1}).filter(function(oe,Y,ce){return ce.indexOf(oe)===Y})},function(){K.setValue(Ce.ContentUtils.insertHTML(n,W,"paste"))}),"handled"},gt=[],pt=[],ft=[],Dt=[],wt=[],Rt=[],Mt=[],jt=[],It=[],Qt=[],J=[],ae=[],N=function(R,W){return W?R.map(function(n){return!n.includeEditors&&!n.excludeEditors?n.data:n.includeEditors?n.includeEditors.indexOf(W)!==-1?n.data:!1:n.excludeEditors?n.excludeEditors.indexOf(W)!==-1?!1:n.data:!1}).filter(function(n){return n}):R.filter(function(n){return!n.includeEditors}).map(function(n){return n.data})},Z=function(R){return N(ft,R)},q=function(R){return N(gt,R)},de=function(R){return N(pt,R,"decorators")},be=function(R){return N(Dt,R)},Se=function(R){return N(wt,R)},Ae=function(R){var W={};return N(Rt,R).forEach(function(n){W[n.inlineStyleName]=n.styleMap}),W},xe=function(R){return N(Mt,R)},ve=function(R,W){return function(n,K,X){return N(It,W).forEach(function(oe){oe.importer&&oe.importer(n,K)&&(X=X.add(oe.inlineStyleName))}),R?R(n,K,X):X}},ze=function(R,W){return function(n){n=n.toUpperCase();var K=R?R(n):void 0;return K||(N(Qt,W).find(function(X){if(X.inlineStyleName===n)return K=X.exporter,!0}),K)}},Ge=function(R,W){return function(n,K,X,oe){var Y=R?R(n,K,X,oe):null;return Y||(N(jt,W).find(function(ce){var pe=ce.importer?ce.importer(n,K,oe):null;return pe&&(Y=X(ce.entityType,pe.mutability||"MUTABLE",pe.data||{})),!!pe}),Y)}},Te=function(R,W){return function(n,K){var X=R?R(n,K):void 0;if(X)return X;var oe=n.type.toUpperCase();return N(jt,W).find(function(Y){if(Y.entityType===oe)return X=Y.exporter?Y.exporter(n,K):void 0,!0}),X}},Pe=function(R,W){return function(n,K,X){var oe=R?R(n,K,X):null;return oe||(N(J,W).find(function(Y){var ce=Y.importer?Y.importer(n,K,X):void 0;return ce&&(oe=ce),!!ce}),oe)}},Oe=function(R,W){return function(n,K){var X=R?R(n,K):null;return X||(N(ae,W).find(function(oe){var Y=oe.exporter?oe.exporter(n,K):void 0;return Y&&(X=Y),!!Y}),X)}},Ie=function ee(R){if(R instanceof Array)return R.forEach(ee),!1;if(!R||!R.type||typeof R.type!="string")return!1;var W=R.includeEditors,n=R.excludeEditors;if(R.type==="control")gt.push({includeEditors:W,excludeEditors:n,data:R.control});else if(R.type==="inline-style"){var K=R.name.toUpperCase();R.control&&gt.push({includeEditors:W,excludeEditors:n,data:r()({key:K,type:"inline-style",command:K},R.control)}),R.style&&Rt.push({includeEditors:W,excludeEditors:n,data:{inlineStyleName:K,styleMap:R.style}}),R.styleFn&&Mt.push({includeEditors:W,excludeEditors:n,data:{inlineStyleName:K,styleFn:R.styleFn}}),R.importer&&It.push({includeEditors:W,excludeEditors:n,data:{inlineStyleName:K,importer:R.importer}}),Qt.push({includeEditors:W,excludeEditors:n,data:{inlineStyleName:K,exporter:R.exporter?R.exporter(R):V.a.createElement("span",{style:R.style})}})}else if(R.type!=="block-style"){if(R.type==="entity"){var X=R.name.toUpperCase();R.control&&gt.push({includeEditors:W,excludeEditors:n,data:typeof R.control=="function"?R.control:r()({key:X,type:"entity",command:X,data:{mutability:R.mutability||"MUTABLE",data:R.data||{}}},R.control)}),jt.push({includeEditors:W,excludeEditors:n,data:{entityType:X,importer:R.importer,exporter:R.exporter}}),pt.push({includeEditors:W,excludeEditors:n,data:{type:"entity",decorator:{key:X,component:R.component}}})}else if(R.type==="block"){var oe=R.name;R.renderMap&&Dt.push({includeEditors:W,excludeEditors:n,data:{blockType:oe,renderMap:R.renderMap}}),R.rendererFn&&wt.push({includeEditors:W,excludeEditors:n,data:{blockType:oe,rendererFn:R.rendererFn}}),R.importer&&J.push({includeEditors:W,excludeEditors:n,data:{blockType:oe,importer:R.importer}}),R.exporter&&ae.push({includeEditors:W,excludeEditors:n,data:{blockType:oe,exporter:R.exporter}})}else if(R.type!=="atomic")if(R.type==="decorator"){var Y=R.decorator;Y&&Y.strategy&&Y.component?pt.push({includeEditors:W,excludeEditors:n,data:{type:"strategy",decorator:Y}}):Y&&Y.getDecorations&&pt.push({includeEditors:W,excludeEditors:n,data:{type:"class",decorator:Y}})}else R.type==="prop-interception"&&ft.push({includeEditors:W,excludeEditors:n,data:R.interceptor})}},ht=function(R){return R.use=Ie,R},yt=function(ee,R){var W=Object(Ft.Map)({atomic:{element:""},"code-block":{element:"code",wrapper:V.a.createElement("pre",{className:"braft-code-block"})}});try{var n=be(ee.editorId);W=n.reduce(function(K,X){return K.merge(typeof X.renderMap=="function"?X.renderMap(ee):X.renderMap)},W),R&&(typeof R=="function"?W=W.merge(R(ee)):W=W.merge(R)),W=$e.DefaultDraftBlockRenderMap.merge(W)}catch(K){console.warn(K)}return W},Re=s(11),nt=s.n(Re),Bt=s(24),_e=s.n(Bt),dt=s(45),kt=s(46),Zt=function(ee){var R=ee.active,W=ee.onClick,n=ee.className;return V.a.createElement("div",{onClick:function(){return W()},className:"bf-switch "+n+(R?" active":"")})},Ut=function(ee,R){return[{key:"undo",title:ee.controls.undo,text:V.a.createElement("i",{className:"bfi-undo"}),type:"editor-method",command:"undo"},{key:"redo",title:ee.controls.redo,text:V.a.createElement("i",{className:"bfi-redo"}),type:"editor-method",command:"redo"},{key:"remove-styles",title:ee.controls.removeStyles,text:V.a.createElement("i",{className:"bfi-format_clear"}),type:"editor-method",command:"removeSelectionInlineStyles"},{key:"hr",title:ee.controls.hr,text:V.a.createElement("i",{className:"bfi-hr"}),type:"editor-method",command:"insertHorizontalLine"},{key:"bold",title:ee.controls.bold,text:V.a.createElement("i",{className:"bfi-bold"}),type:"inline-style",command:"bold"},{key:"italic",title:ee.controls.italic,text:V.a.createElement("i",{className:"bfi-italic"}),type:"inline-style",command:"italic"},{key:"underline",title:ee.controls.underline,text:V.a.createElement("i",{className:"bfi-underlined"}),type:"inline-style",command:"underline"},{key:"strike-through",title:ee.controls.strikeThrough,text:V.a.createElement("i",{className:"bfi-strikethrough"}),type:"inline-style",command:"strikethrough"},{key:"superscript",title:ee.controls.superScript,text:V.a.createElement("i",{className:"bfi-superscript"}),type:"inline-style",command:"superscript"},{key:"subscript",title:ee.controls.subScript,text:V.a.createElement("i",{className:"bfi-subscript"}),type:"inline-style",command:"subscript"},{key:"headings",title:ee.controls.headings,type:"headings"},{key:"blockquote",title:ee.controls.blockQuote,text:V.a.createElement("i",{className:"bfi-quote"}),type:"block-type",command:"blockquote"},{key:"code",title:ee.controls.code,text:V.a.createElement("i",{className:"bfi-code"}),type:"block-type",command:"code-block"},{key:"list-ul",title:ee.controls.unorderedList,text:V.a.createElement("i",{className:"bfi-list"}),type:"block-type",command:"unordered-list-item"},{key:"list-ol",title:ee.controls.orderedList,text:V.a.createElement("i",{className:"bfi-list-numbered"}),type:"block-type",command:"ordered-list-item"},{key:"link",title:ee.controls.link,type:"link"},{key:"text-color",title:ee.controls.color,type:"text-color"},{key:"line-height",title:ee.controls.lineHeight,type:"line-height"},{key:"letter-spacing",title:ee.controls.letterSpacing,type:"letter-spacing"},{key:"text-indent",title:ee.controls.textIndent,type:"text-indent"},{key:"font-size",title:ee.controls.fontSize,type:"font-size"},{key:"font-family",title:ee.controls.fontFamily,type:"font-family"},{key:"text-align",title:ee.controls.textAlign,type:"text-align"},{key:"media",title:ee.controls.media,text:V.a.createElement("i",{className:"bfi-media"}),type:"media"},{key:"emoji",title:ee.controls.emoji,text:V.a.createElement("i",{className:"bfi-emoji"}),type:"emoji"},{key:"clear",title:ee.controls.clear,text:V.a.createElement("i",{className:"bfi-clear_all"}),type:"editor-method",command:"clearEditorContent"},{key:"fullscreen",title:R.state.isFullscreen?ee.controls.exitFullscreen:ee.controls.fullscreen,text:V.a.createElement("i",{className:R.state.isFullscreen?"bfi-fullscreen-exit":"bfi-fullscreen"}),type:"editor-method",command:"toggleFullscreen"},{key:"modal",type:"modal"},{key:"button",type:"button"},{key:"dropdown",type:"dropdown"},{key:"component",type:"component"}]},Vt={"float-left":{text:V.a.createElement("span",{"data-float":"left"},"\uE91E"),command:"setImageFloat|left"},"float-right":{text:V.a.createElement("span",{"data-float":"right"},"\uE914"),command:"setImageFloat|right"},"align-left":{text:V.a.createElement("span",{"data-align":"left"},"\uE027"),command:"setImageAlignment|left"},"align-center":{text:V.a.createElement("span",{"data-align":"center"},"\uE028"),command:"setImageAlignment|center"},"align-right":{text:V.a.createElement("span",{"data-align":"right"},"\uE029"),command:"setImageAlignment|right"},size:{text:V.a.createElement("span",null,"\uE3C2"),command:"toggleSizeEditor"},link:{text:V.a.createElement("span",null,"\uE91A"),command:"toggleLinkEditor"},remove:{text:V.a.createElement("span",null,"\uE9AC"),command:"removeImage"}},cn=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"state",{toolbarVisible:!1,toolbarOffset:0,linkEditorVisible:!1,sizeEditorVisible:!1,tempLink:null,tempWidth:null,tempHeight:null}),O()(w()(n),"initialLeft",void 0),O()(w()(n),"initialTop",void 0),O()(w()(n),"initialWidth",void 0),O()(w()(n),"initialHeight",void 0),O()(w()(n),"reSizeType",void 0),O()(w()(n),"zoom",void 0),O()(w()(n),"changeSize",function(Y){var ce=n.reSizeType;n.initialLeft||(n.initialLeft=Y.screenX,n.initialTop=Y.screenY),ce==="rightbottom"&&(n.initialHeight+=Y.screenY-n.initialTop,n.initialWidth+=Y.screenX-n.initialLeft),ce==="leftbottom"&&(n.initialHeight+=Y.screenY-n.initialTop,n.initialWidth+=-Y.screenX+n.initialLeft),n.initialLeft=Y.screenX,n.initialTop=Y.screenY}),O()(w()(n),"moveImage",function(Y){n.changeSize(Y),n.setState({tempWidth:Math.abs(n.initialWidth),tempHeight:Math.abs(n.initialHeight)})}),O()(w()(n),"upImage",function(){var Y=n.props.imageEqualRatio;Y?n.confirmImageSizeEqualRatio():n.confirmImageSize(),document.removeEventListener("mousemove",n.moveImage),document.removeEventListener("mouseup",n.upImage)}),O()(w()(n),"repareChangeSize",function(Y){return function(ce){n.reSizeType=Y;var pe=n.imageElement.getBoundingClientRect();n.initialLeft=n.initialTop=0,n.initialWidth=pe.width,n.initialHeight=pe.height,n.zoom=pe.width/pe.height,ce.preventDefault(),document.addEventListener("mousemove",n.moveImage),document.addEventListener("mouseup",n.upImage)}}),O()(w()(n),"preventDragEvent",function(Y){Y.stopPropagation(),Y.preventDefault()}),O()(w()(n),"handleDragStart",function(){return n.props.editor.editorProps.readOnly||n.props.editor.editorProps.disabled?!1:(window.__BRAFT_DRAGING__IMAGE__={block:n.props.block,mediaData:r()({type:"IMAGE"},n.props.mediaData)},n.setState({toolbarVisible:!1},function(){n.unlockEditor()}),!0)}),O()(w()(n),"handleDragEnd",function(){return window.__BRAFT_DRAGING__IMAGE__=null,!1}),O()(w()(n),"executeCommand",function(Y){if(typeof Y=="string"){var ce=Y.split("|"),pe=_e()(ce,2),je=pe[0],Ye=pe[1];n[je]&&n[je](Ye)}else typeof Y=="function"&&Y(n.props.block,n.props.mediaData,n.props.editor.getValue())}),O()(w()(n),"removeImage",function(){n.props.editor.setValue(Ce.ContentUtils.removeBlock(n.props.editor.getValue(),n.props.block)),n.unlockEditor()}),O()(w()(n),"toggleLinkEditor",function(){n.setState({linkEditorVisible:!n.state.linkEditorVisible,sizeEditorVisible:!1})}),O()(w()(n),"toggleSizeEditor",function(){n.setState({linkEditorVisible:!1,sizeEditorVisible:!n.state.sizeEditorVisible})}),O()(w()(n),"handleLinkInputKeyDown",function(Y){if(Y.keyCode===13)n.confirmImageLink();else return}),O()(w()(n),"setImageLink",function(Y){n.setState({tempLink:Y.currentTarget.value})}),O()(w()(n),"confirmImageLink",function(){var Y=n.state.tempLink,ce=n.props.hooks("set-image-link",Y)(Y);if(ce===!1)return!1;typeof ce=="string"&&(Y=ce),Y!==null&&(n.props.editor.setValue(Ce.ContentUtils.setMediaData(n.props.editor.getValue(),n.props.entityKey,{link:Y})),window.setImmediate(n.props.editor.forceRender))}),O()(w()(n),"handleSizeInputKeyDown",function(Y){if(Y.keyCode===13)n.confirmImageSize();else return}),O()(w()(n),"setImageWidth",function(Y){var ce=Y.currentTarget,pe=ce.value;pe&&!isNaN(pe)&&(pe=pe+"px"),n.setState({tempWidth:pe})}),O()(w()(n),"setImageHeight",function(Y){var ce=Y.currentTarget,pe=ce.value;pe&&!isNaN(pe)&&(pe=pe+"px"),n.setState({tempHeight:pe})}),O()(w()(n),"confirmImageSize",function(){var Y=n.state,ce=Y.tempWidth,pe=Y.tempHeight,je={};ce!==null&&(je.width=ce),pe!==null&&(je.height=pe);var Ye=n.props.hooks("set-image-size",je)(je);if(Ye===!1)return!1;Ye&&(Ye.width||Ye.height)&&(je=Ye),n.props.editor.setValue(Ce.ContentUtils.setMediaData(n.props.editor.getValue(),n.props.entityKey,je)),window.setImmediate(n.props.editor.forceRender)}),O()(w()(n),"confirmImageSizeEqualRatio",function(){var Y=n.state,ce=Y.tempWidth,pe=Y.tempHeight,je,Ye,Xe={};ce/pe>n.zoom?(je=Math.floor(pe*n.zoom),n.setState({tempWidth:je}),Ye=pe):ce/pe<n.zoom&&(Ye=Math.floor(ce/n.zoom),n.setState({tempHeight:Ye}),je=ce),je!==null&&(Xe.width=je),Ye!==null&&(Xe.height=Ye);var st=n.props.hooks("set-image-size",Xe)(Xe);if(st===!1)return!1;st&&(st.width||st.height)&&(Xe=st),n.props.editor.setValue(Ce.ContentUtils.setMediaData(n.props.editor.getValue(),n.props.entityKey,Xe)),window.setImmediate(n.props.editor.forceRender)}),O()(w()(n),"setImageFloat",function(Y){var ce=n.props.hooks("set-image-float",Y)(Y);if(ce===!1)return!1;typeof ce=="string"&&(Y=ce),n.props.editor.setValue(Ce.ContentUtils.setMediaPosition(n.props.editor.getValue(),n.props.block,{float:Y})),n.unlockEditor()}),O()(w()(n),"setImageAlignment",function(Y){var ce=n.props.hooks("set-image-alignment",Y)(Y);if(ce===!1)return!1;typeof ce=="string"&&(Y=ce),n.props.editor.setValue(Ce.ContentUtils.setMediaPosition(n.props.editor.getValue(),n.props.block,{alignment:Y})),n.unlockEditor()}),O()(w()(n),"showToolbar",function(Y){if(n.props.editor.editorProps.readOnly||n.props.editor.editorProps.disabled)return!1;Y.preventDefault(),n.state.toolbarVisible||n.setState({toolbarVisible:!0},function(){n.lockEditor(),n.setState({toolbarOffset:n.calcToolbarOffset()})})}),O()(w()(n),"hideToolbar",function(Y){Y.preventDefault(),n.setState({toolbarVisible:!1},function(){n.unlockEditor()})}),n}return D()(R,[{key:"render",value:function(){var n=this,K=this.props,X=K.mediaData,oe=K.language,Y=K.imageControls,ce=K.imageResizable,pe=this.state,je=pe.toolbarVisible,Ye=pe.toolbarOffset,Xe=pe.linkEditorVisible,st=pe.sizeEditorVisible,At=pe.tempWidth,Ht=pe.tempHeight,Kt=this.props.block.getData(),bt=Kt.get("float"),Tt=Kt.get("alignment"),vn=X.url,ln=X.link,sn=X.link_target,tn=X.width,mn=X.height,On=X.meta,nn={},yn=!1;bt?Tt=null:Tt==="left"?(nn.float="left",yn=!0):Tt==="right"?(nn.float="right",yn=!0):Tt==="center"?nn.textAlign="center":(nn.float="left",yn=!0);var Rn=Y.map(function(Ot,$t){return typeof Ot=="string"&&Vt[Ot]?V.a.createElement("a",{className:Ot==="link"&&ln?"active":"",key:$t,onClick:function(){return n.executeCommand(Vt[Ot].command)}},Vt[Ot].text):Ot&&(Ot.render||Ot.text)?Ot.render?Ot.render(X,n.props.block):V.a.createElement("a",{key:$t,onClick:function(){return Ot.onClick&&n.executeCommand(Ot.onClick)}},Ot.text):null});return V.a.createElement("div",{className:"bf-media"},V.a.createElement("div",{style:nn,draggable:!0,onMouseEnter:this.showToolbar,onMouseMove:this.showToolbar,onMouseLeave:this.hideToolbar,onDragStart:this.handleDragStart,onDragEnd:this.handleDragEnd,ref:function($t){return n.mediaEmbederInstance=$t},className:"bf-image"},je?V.a.createElement("div",{style:{marginLeft:Ye},ref:function($t){return n.toolbarElement=$t},"data-float":bt,"data-align":Tt,className:"bf-media-toolbar"},Xe?V.a.createElement("div",{className:"bf-image-link-editor"},V.a.createElement("div",{className:"editor-input-group"},V.a.createElement("input",{type:"text",placeholder:oe.linkEditor.inputWithEnterPlaceHolder,onKeyDown:this.handleLinkInputKeyDown,onChange:this.setImageLink,defaultValue:ln}),V.a.createElement("button",{type:"button",onClick:this.confirmImageLink},oe.base.confirm)),V.a.createElement("div",{className:"switch-group"},V.a.createElement(Zt,{active:sn==="_blank",onClick:function(){return n.setImageLinkTarget(sn)}}),V.a.createElement("label",null,oe.linkEditor.openInNewWindow))):null,st?V.a.createElement("div",{className:"bf-image-size-editor"},V.a.createElement("div",{className:"editor-input-group"},V.a.createElement("input",{type:"text",placeholder:oe.base.width,onKeyDown:this.handleSizeInputKeyDown,onChange:this.setImageWidth,defaultValue:tn}),V.a.createElement("input",{type:"text",placeholder:oe.base.height,onKeyDown:this.handleSizeInputKeyDown,onChange:this.setImageHeight,defaultValue:mn}),V.a.createElement("button",{type:"button",onClick:this.confirmImageSize},oe.base.confirm))):null,Rn,V.a.createElement("i",{style:{marginLeft:Ye*-1},className:"bf-media-toolbar-arrow"})):null,V.a.createElement("div",{style:{position:"relative",width:"".concat(tn,"px"),height:"".concat(mn,"px"),display:"inline-block"}},V.a.createElement("img",nt()({ref:function($t){return n.imageElement=$t},src:vn,width:tn,height:mn},On)),je&&ce?V.a.createElement("div",{className:"bf-csize-icon right-bottom",onMouseDown:this.repareChangeSize("rightbottom")}):null,je&&ce?V.a.createElement("div",{className:"bf-csize-icon left-bottom",onMouseDown:this.repareChangeSize("leftbottom")}):null,V.a.createElement("div",{className:"bf-pre-csize ".concat(this.reSizeType),style:{width:"".concat(At,"px"),height:"".concat(Ht,"px")}}))),yn&&V.a.createElement("div",{className:"clearfix",style:{clear:"both",height:0,lineHeight:0,float:"none"}}))}},{key:"lockEditor",value:function(){this.props.editor.lockOrUnlockEditor(!0)}},{key:"unlockEditor",value:function(){this.props.editor.lockOrUnlockEditor(!1)}},{key:"calcToolbarOffset",value:function(){var n=this.props,K=n.getContainerNode,X=n.containerNode,oe=K?K():X;if(!oe)return 0;var Y=oe.querySelector(".bf-content").getBoundingClientRect(),ce=this.toolbarElement.getBoundingClientRect(),pe=this.imageElement.getBoundingClientRect(),je=Y.right-(pe.right-pe.width/2+ce.width/2),Ye=pe.left+pe.width/2-ce.width/2-Y.left;return je<10?je-10:Ye<10?Ye*-1+10:0}},{key:"setImageLinkTarget",value:function(n){var K=this.props.hooks("set-image-link-target",n)(n);if(K===!1)return!1;typeof K=="string"&&(n=K),n=n==="_blank"?"":"_blank",this.props.editor.setValue(Ce.ContentUtils.setMediaData(this.props.editor.getValue(),this.props.entityKey,{link_target:n})),window.setImmediate(this.props.editor.forceRender)}}]),R}(V.a.Component),Ln=s(47),Lt=s(48),Bn=s(16),_t=s.n(Bn),Jt=function(ee){j()(R,ee);function R(W){var n;return l()(this,R),n=y()(this,T()(R).call(this,W)),O()(w()(n),"handleTransitionEnd",function(){if(!n.rootElement||!n.rootElement.classList)return!1;n.rootElement.classList.contains("active")||_t.a.unmountComponentAtNode(n.rootElement)&&n.rootElement.parentNode.removeChild(n.rootElement)}),O()(w()(n),"handleMouseDown",function(K){var X=K.target.tagName.toLowerCase();if(X==="input"||X==="textarea")return!1;K.preventDefault()}),O()(w()(n),"handleCancel",function(){n.props.closeOnCancel&&n.close(),n.props.onCancel&&n.props.onCancel()}),O()(w()(n),"handleConfirm",function(){n.props.closeOnConfirm&&n.close(),n.props.onConfirm&&n.props.onConfirm()}),O()(w()(n),"handleMaskClick",function(){n.props.closeOnBlur&&n.close(),n.props.onBlue&&n.props.onBlue()}),O()(w()(n),"close",function(){n.unrenderComponent(),n.props.onClose&&n.props.onClose()}),n.active=!1,n.componentId="BRAFT-MODAL-"+Ce.BaseUtils.UniqueIndex(),n}return D()(R,[{key:"componentDidMount",value:function(){this.props.visible&&(this.active=!0,this.renderComponent(this.props))}},{key:"componentWillReceiveProps",value:function(n){this.props.visible&&!n.visible?this.unrenderComponent():(this.props.visible||n.visible)&&(this.active=!0,this.renderComponent(n))}},{key:"render",value:function(){return null}},{key:"unrenderComponent",value:function(){this.active=!1,this.activeId&&window.clearImmediate(this.activeId),this.rootElement&&this.rootElement.classList&&this.rootElement.classList.remove("active")}},{key:"renderComponent",value:function(n){var K=this;if(!this.active)return!1;var X=n.title,oe=n.className,Y=n.width,ce=n.height,pe=n.children,je=n.component,Ye=n.confirmable,Xe=n.showFooter,st=n.showCancel,At=n.showConfirm,Ht=n.showClose,Kt=n.cancelText,bt=n.confirmText,Tt=n.bottomText,vn=n.language;typeof st=="undefined"&&(st=!0),typeof Ht=="undefined"&&(Ht=!0),typeof At=="undefined"&&(At=!0),typeof Xe=="undefined"&&(Xe=!0);var ln=V.a.createElement("div",{onMouseDown:this.handleMouseDown,className:"bf-modal "+(oe||"")},V.a.createElement("div",{className:"bf-modal-mask",onClick:this.handleMaskClick}),V.a.createElement("div",{onTransitionEnd:this.handleTransitionEnd,style:{width:Y,height:ce},className:"bf-modal-content"},V.a.createElement("div",{className:"bf-modal-header"},V.a.createElement("h3",{className:"bf-modal-caption"},X),Ht&&V.a.createElement("button",{type:"button",onClick:this.close,className:"bf-modal-close-button"},V.a.createElement("i",{className:"bfi-close"}))),V.a.createElement("div",{className:"bf-modal-body"},pe||je),Xe?V.a.createElement("div",{className:"bf-modal-footer"},V.a.createElement("div",{className:"bf-modal-addon-text"},Tt),V.a.createElement("div",{className:"bf-modal-buttons"},st&&V.a.createElement("button",{type:"button",onClick:this.handleCancel,className:"bf-modal-cancel"},Kt||vn.base.cancel),At&&V.a.createElement("button",{type:"button",onClick:this.handleConfirm,className:"bf-modal-confirm "+(Ye?"":"disabled")},bt||vn.base.confirm))):null));this.rootElement=document.querySelector("#"+this.componentId),this.rootElement||(this.rootElement=document.createElement("div"),this.rootElement.id=this.componentId,this.rootElement.className="bf-modal-root",document.body.appendChild(this.rootElement)),_t.a.render(ln,this.rootElement),this.activeId=window.setImmediate(function(){K.rootElement.classList.add("active")})}}]),R}(V.a.Component);O()(Jt,"defaultProps",{showFooter:!0,closeOnBlur:!0});var on=function(R){var W=document.createElement("div");W.style.display="none",document.body.appendChild(W),R=r()({visible:!0,closeOnConfirm:!0,closeOnCancel:!0},R);var n=function(){_t.a.unmountComponentAtNode(W)&&W.parentNode.removeChild(W)},K=function(){R.onConfirm&&R.onConfirm()},X=function(){R.onCancel&&R.onCancel()},oe=function(){n(),R.onClose&&R.onClose()},Y=_t.a.render(V.a.createElement(Jt,nt()({},R,{onConfirm:K,onCancel:X,onClose:oe})),W);return Y.destroy=n,Y.update=Y.renderComponent,Y},Nn=s(49),Sn=function(R,W,n){return on({title:R,component:W,language:n,showFooter:!1})},Hn={video:"bfi-film",audio:"bfi-music",embed:"bfi-code"},fn=function(ee){var R=ee.title,W=ee.type,n=ee.language,K=ee.name,X=ee.url,oe=ee.poster,Y=ee.children,ce=ee.onRemove;return V.a.createElement("div",{className:"bf-player-holder ".concat(W)},V.a.createElement("div",{className:"icon-badge"},V.a.createElement("i",{className:Hn[W]}),V.a.createElement("span",{className:"text"},n.media[W])),V.a.createElement("button",{onMouseDown:ce,className:"button-remove"},V.a.createElement("i",{className:"bfi-close"})),V.a.createElement("button",{onMouseDown:function(){return Sn(K?"".concat(R,":").concat(K):R,Y,n)},className:"button-play"},V.a.createElement("i",{className:"bfi-play_arrow"})),K?V.a.createElement("h5",{className:"bf-name"},K):null,V.a.createElement("h6",{className:"bf-url"},X),oe?V.a.createElement("div",{className:"bf-poster",style:{backgroundImage:"url(".concat(oe,")")}}):null)},An=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"removeVideo",function(){n.props.editor.setValue(Ce.ContentUtils.removeBlock(n.props.editorState,n.props.block))}),n}return D()(R,[{key:"render",value:function(){var n=this.props,K=n.mediaData,X=n.language,oe=K.url,Y=K.name,ce=K.meta;return V.a.createElement("div",{className:"bf-video-wrap"},V.a.createElement(fn,{type:"video",onRemove:this.removeVideo,poster:ce&&ce.poster||"",language:X,url:oe,name:Y,title:X.videoPlayer.title},V.a.createElement("div",{className:"bf-video-player"},V.a.createElement("video",{controls:!0,poster:ce&&ce.poster||""},V.a.createElement("source",{src:oe})))))}}]),R}(V.a.Component),dn=s(50),Cn=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"removeAudio",function(){n.props.editor.setValue(Ce.ContentUtils.removeBlock(n.props.editorState,n.props.block))}),n}return D()(R,[{key:"render",value:function(){var n=this.props,K=n.mediaData,X=n.language,oe=K.url,Y=K.name,ce=K.meta;return V.a.createElement("div",{className:"bf-audio-wrap"},V.a.createElement(fn,{type:"audio",onRemove:this.removeAudio,poster:ce&&ce.poster||"",language:X,url:oe,name:Y,title:X.audioPlayer.title},V.a.createElement("div",{className:"bf-audio-player"},V.a.createElement("audio",{controls:!0,src:oe}))))}}]),R}(V.a.Component),bn=s(51),Xn=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"removeEmbed",function(){n.props.editor.setValue(Ce.ContentUtils.removeBlock(n.props.editorState,n.props.block))}),n}return D()(R,[{key:"render",value:function(){var n=this.props,K=n.mediaData,X=n.language,oe=K.name,Y=K.url,ce=K.meta;return V.a.createElement("div",{className:"bf-embed-wrap"},V.a.createElement(fn,{type:"embed",onRemove:this.removeEmbed,poster:ce&&ce.poster||"",language:X,url:Y,name:oe,title:X.videoPlayer.embedTitle},V.a.createElement("div",{className:"bf-embed-player",dangerouslySetInnerHTML:{__html:Y}})))}}]),R}(V.a.Component),gr=s(52),hr=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"removeHorizontalLine",function(){n.props.editor.setValue(Ce.ContentUtils.removeBlock(n.props.editorState,n.props.block))}),n}return D()(R,[{key:"render",value:function(){return V.a.createElement("div",{className:"bf-hr"},V.a.createElement("div",{className:"bf-media-toolbar"},V.a.createElement("a",{onClick:this.removeHorizontalLine},"\uE9AC")))}}]),R}(V.a.Component),vr=function ee(){var R=this;l()(this,ee),O()(this,"superProps",void 0),O()(this,"customBlockRendererFn",void 0),O()(this,"getRenderFn",function(W,n){return R.superProps=W,R.customBlockRendererFn=n,R.blockRendererFn}),O()(this,"renderAtomicBlock",function(W){var n=R.superProps,K=W.block.getEntityAt(0);if(!K)return null;var X=W.contentState.getEntity(K),oe=X.getData(),Y=X.getType(),ce=r()({},n,{block:W.block,mediaData:oe,entityKey:K});if(Y==="IMAGE")return V.a.createElement(cn,ce);if(Y==="AUDIO")return V.a.createElement(Cn,ce);if(Y==="VIDEO")return V.a.createElement(An,ce);if(Y==="EMBED")return V.a.createElement(Xn,ce);if(Y==="HR")return V.a.createElement(hr,ce);if(n.extendAtomics){for(var pe=n.extendAtomics,je=0;je<pe.length;je++)if(Y===pe[je].type){var Ye=pe[je].component;return V.a.createElement(Ye,ce)}}return null}),O()(this,"blockRendererFn",function(W){var n=R.customBlockRendererFn,K=R.superProps,X=W.getType(),oe=null;if(n&&(oe=n(W,K)||null),oe)return oe;var Y=Se(K.editorId);return Y.find(function(ce){if(ce.blockType===X||ce.blockType instanceof RegExp&&ce.blockType.test(X))return oe=ce.rendererFn?ce.rendererFn(K):null,!0}),oe||(X==="atomic"&&(oe={component:R.renderAtomicBlock,editable:!1}),oe)})},mr=new vr,yr=mr.getRenderFn,Ar=function(ee){return function(R){var W=R.getData()&&R.getData().get("textAlign"),n=R.getData()&&R.getData().get("textIndent"),K=R.getData()&&R.getData().get("float"),X="";return W&&(X="bfa-".concat(W)),n&&n!==0&&(X+=" bftd-".concat(n)),K&&(X+=" bff-".concat(K)),ee&&(X+=ee(R)||""),X}},xt=function(ee){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},W=Ae(ee.editorId);return r()({SUPERSCRIPT:{position:"relative",top:"-8px",fontSize:"11px"},SUBSCRIPT:{position:"relative",bottom:"-8px",fontSize:"11px"}},W,R)},pn=function(R){return R.split("-")[1]},Jn=function(ee,R){return function(W,n){var K={},X=R.fontFamilies,oe=R.unitExportFn,Y=R.customStyleFn,ce=xe(ee.editorId);return ce.forEach(function(pe){K=pe.styleFn?pe.styleFn(W,n,K):K}),K=Y?Y(W,n,K):{},W.forEach(function(pe){pe.indexOf("COLOR-")===0?K.color="#"+pn(pe):pe.indexOf("BGCOLOR-")===0?K.backgroundColor="#"+pn(pe):pe.indexOf("FONTSIZE-")===0?K.fontSize=oe(pn(pe),"font-size","editor"):pe.indexOf("LINEHEIGHT-")===0?K.lineHeight=oe(pn(pe),"line-height","editor"):pe.indexOf("LETTERSPACING-")===0?K.letterSpacing=oe(pn(pe),"letter-spacing","editor"):pe.indexOf("TEXTINDENT-")===0?K.textIndent=oe(pn(pe),"text-indent","editor"):pe.indexOf("FONTFAMILY-")===0&&(K.fontFamily=(X.find(function(je){return je.name.toUpperCase()===pn(pe)})||{}).family||"")}),K}},Wt=s(18),an=s.n(Wt),Fn=function(ee){var R=ee.children,W=ee.entityKey,n=ee.contentState,K=n.getEntity(W).getData(),X=K.href,oe=K.target;return V.a.createElement("span",{className:"bf-link-wrap"},V.a.createElement("a",{onClick:function(ce){return $n(ce,X)},className:"bf-link",href:X,target:oe},R))},$n=function(R,W){if(R.getModifierState("Control")||R.getModifierState("Meta")){var n=document.createElement("a");n.href=W,n.target=R.currentTarget.target,n.click()}},kn="-";an.a.prototype.getDecorations=function(ee,R){var W=Array(ee.getText().length).fill(null);return this.decorators.forEach(function(n,K){n.getDecorations(ee,R).forEach(function(X,oe){!X||(X=K+kn+X,W[oe]=X)})}),Et.a.List(W)};var Zn=[{type:"entity",decorator:{key:"LINK",component:Fn}}],jn=function(R){return function(W,n,K){W.findEntityRanges(function(X){var oe=X.getEntity();return oe!==null&&K.getEntity(oe).getType()===R},n)}},Er=function(ee){var R=de(ee),W=[].concat(Zn,Q()(R.filter(function(X){return X.type==="entity"}))),n=R.filter(function(X){return X.type==="strategy"}),K=R.filter(function(X){return X.type==="class"});return new an.a([].concat(Q()(K.map(function(X){return X.decorator})),[new $e.CompositeDecorator(n.map(function(X){return X.decorator})),new $e.CompositeDecorator(W.map(function(X){return{strategy:jn(X.decorator.key),component:X.decorator.component}}))]))},Wn=yt,xn=yr,_n=Ar,gn=xt,Pn=Jn,qt=Er,Nt=s(53),Sr=s(54),qn=s(55),Tn=[],Dn=!1,Kn=!1,Yt={resolve:function(R){var W=Ce.BaseUtils.UniqueIndex();return Tn.push({id:W,eventHandler:R}),W},unresolve:function(R){Tn=Tn.filter(function(W){return W.id!==R})}};!Dn&&(typeof window=="undefined"?"undefined":c()(window))==="object"&&(window.addEventListener("resize",function(ee){clearTimeout(Kn),Kn=setTimeout(function(){Tn.map(function(R){typeof R.eventHandler=="function"&&R.eventHandler(ee)}),Kn=!1},100)}),Dn=!0);var Gt=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"responsiveResolveId",null),O()(w()(n),"dropDownHandlerElement",null),O()(w()(n),"dropDownContentElement",null),O()(w()(n),"state",{active:!1,offset:0}),O()(w()(n),"fixDropDownPosition",function(){var Y=n.props.getContainerNode().getBoundingClientRect(),ce=n.dropDownHandlerElement.getBoundingClientRect(),pe=n.dropDownContentElement.getBoundingClientRect(),je=0,Ye=ce.right-ce.width/2+pe.width/2,Xe=ce.left+ce.width/2-pe.width/2;Ye=Y.right-Ye,Xe=Xe-Y.left,Ye<10?je=Ye-10:Xe<10&&(je=Xe*-1+10),je!==n.state.offset&&n.setState({offset:je})}),O()(w()(n),"registerClickEvent",function(Y){var ce=n.props.autoHide,pe=n.state.active;if(n.dropDownContentElement.contains(Y.target)||n.dropDownHandlerElement.contains(Y.target))return!1;ce&&pe&&n.hide()}),O()(w()(n),"toggle",function(){n.setState({active:!n.state.active})}),O()(w()(n),"show",function(){n.setState({active:!0})}),O()(w()(n),"hide",function(){n.setState({active:!1})}),n}return D()(R,[{key:"componentDidMount",value:function(){document&&(document.body.addEventListener("click",this.registerClickEvent),this.responsiveResolveId=Yt.resolve(this.fixDropDownPosition))}},{key:"componentWillReceiveProps",value:function(n){!this.props.disabled&&n.disabled&&this.hide()}},{key:"componentDidUpdate",value:function(n){!n.active&&this.state.active&&this.fixDropDownPosition()}},{key:"componentWillUnmount",value:function(){document&&(document.body.removeEventListener("click",this.registerClickEvent),Yt.unresolve(this.responsiveResolveId))}},{key:"render",value:function(){var n=this,K=this.state,X=K.active,oe=K.offset,Y=this.props,ce=Y.caption,pe=Y.htmlCaption,je=Y.title,Ye=Y.disabled,Xe=Y.showArrow,st=Y.arrowActive,At=Y.className,Ht=Y.children,Kt=Y.theme;return Ye&&(X=!1),Kt==="light"&&(At=" light-theme "+At),V.a.createElement("div",{className:"bf-dropdown "+(X?"active ":"")+(Ye?"disabled ":"")+At},pe?V.a.createElement("button",{type:"button",className:"dropdown-handler","data-title":je,onClick:this.toggle,dangerouslySetInnerHTML:pe?{__html:pe}:null,ref:function(Tt){return n.dropDownHandlerElement=Tt}}):V.a.createElement("button",{type:"button",className:"dropdown-handler","data-title":je,onClick:this.toggle,ref:function(Tt){return n.dropDownHandlerElement=Tt}},V.a.createElement("span",null,ce),Xe!==!1?V.a.createElement("i",{className:"bfi-drop-down"}):null),V.a.createElement("div",{className:"dropdown-content",style:{marginLeft:oe},ref:function(Tt){return n.dropDownContentElement=Tt}},V.a.createElement("i",{style:{marginLeft:oe*-1},className:"dropdown-arrow"+(st?" active":"")}),V.a.createElement("div",{className:"dropdown-content-inner"},Ht)))}}]),R}(V.a.Component),hn=function(ee){return V.a.Fragment?V.a.createElement(V.a.Fragment,null,ee.children):V.a.createElement("div",{className:"control-item-group"},ee.children)},wn=function(ee){j()(R,ee);function R(W){var n;return l()(this,R),n=y()(this,T()(R).call(this,W)),O()(w()(n),"dropDownInstance",null),O()(w()(n),"handeKeyDown",function(K){if(K.keyCode===13)return n.handleConfirm(),K.preventDefault(),!1}),O()(w()(n),"handleTnputText",function(K){n.setState({text:K.currentTarget.value})}),O()(w()(n),"handleInputLink",function(K){n.setState({href:K.currentTarget.value})}),O()(w()(n),"setTarget",function(){n.setState({target:n.state.target==="_blank"?"":"_blank"})}),O()(w()(n),"handleCancel",function(){n.dropDownInstance.hide()}),O()(w()(n),"handleUnlink",function(){n.dropDownInstance.hide(),n.props.editor.setValue(Ce.ContentUtils.toggleSelectionLink(n.props.editorState,!1))}),O()(w()(n),"handleConfirm",function(){var K=n.state,X=K.text,oe=K.href,Y=K.target,ce=K.textSelected,pe=n.props.hooks("toggle-link",{href:oe,target:Y})({href:oe,target:Y});if(n.dropDownInstance.hide(),n.props.editor.requestFocus(),pe===!1)return!1;pe&&(typeof pe.href=="string"&&(oe=pe.href),typeof pe.target=="string"&&(Y=pe.target)),ce?oe?n.props.editor.setValue(Ce.ContentUtils.toggleSelectionLink(n.props.editorState,oe,Y)):n.props.editor.setValue(Ce.ContentUtils.toggleSelectionLink(n.props.editorState,!1)):n.props.editor.setValue(Ce.ContentUtils.insertText(n.props.editorState,X||oe,null,{type:"LINK",data:{href:oe,target:Y}}))}),n.state={text:"",href:"",target:W.defaultLinkTarget||"",textSelected:!1},n}return D()(R,[{key:"componentWillReceiveProps",value:function(n){var K=Ce.ContentUtils.getSelectionEntityData(n.editorState,"LINK"),X=K.href,oe=K.target,Y=!Ce.ContentUtils.isSelectionCollapsed(this.props.editorState)&&Ce.ContentUtils.getSelectionBlockType(this.props.editorState)!=="atomic",ce="";Y&&(ce=Ce.ContentUtils.getSelectionText(this.props.editorState)),this.setState({textSelected:Y,text:ce,href:X||"",target:typeof oe=="undefined"?n.defaultLinkTarget||"":oe||""})}},{key:"render",value:function(){var n=this,K=this.props.allowInsertLinkText,X=this.state,oe=X.text,Y=X.href,ce=X.target,pe=X.textSelected,je=V.a.createElement("i",{className:"bfi-link"});return V.a.createElement(hn,null,V.a.createElement(Gt,{key:0,caption:je,title:this.props.language.controls.link,autoHide:!0,getContainerNode:this.props.getContainerNode,showArrow:!1,ref:function(Xe){return n.dropDownInstance=Xe},className:"control-item dropdown link-editor-dropdown"},V.a.createElement("div",{className:"bf-link-editor"},K?V.a.createElement("div",{className:"input-group"},V.a.createElement("input",{type:"text",value:oe,spellCheck:!1,disabled:pe,placeholder:this.props.language.linkEditor.textInputPlaceHolder,onKeyDown:this.handeKeyDown,onChange:this.handleTnputText})):null,V.a.createElement("div",{className:"input-group"},V.a.createElement("input",{type:"text",value:Y,spellCheck:!1,placeholder:this.props.language.linkEditor.linkInputPlaceHolder,onKeyDown:this.handeKeyDown,onChange:this.handleInputLink})),V.a.createElement("div",{className:"switch-group"},V.a.createElement(Zt,{active:ce==="_blank",onClick:this.setTarget}),V.a.createElement("label",null,this.props.language.linkEditor.openInNewWindow)),V.a.createElement("div",{className:"buttons"},V.a.createElement("a",{onClick:this.handleUnlink,className:"primary button-remove-link pull-left"},V.a.createElement("i",{className:"bfi-close"}),V.a.createElement("span",null,this.props.language.linkEditor.removeLink)),V.a.createElement("button",{type:"button",onClick:this.handleConfirm,className:"primary pull-right"},this.props.language.base.confirm),V.a.createElement("button",{type:"button",onClick:this.handleCancel,className:"default pull-right"},this.props.language.base.cancel)))),V.a.createElement("button",{key:1,type:"button","data-title":this.props.language.controls.unlink,className:"control-item button",onClick:this.handleUnlink,disabled:!pe||!Y},V.a.createElement("i",{className:"bfi-link-off"})))}}]),R}(V.a.Component),Cr=s(56),er=function(R){return[{key:"header-one",title:R.controls.header+" 1",text:V.a.createElement("h1",null,R.controls.header," 1"),type:"block-type",command:"header-one"},{key:"header-two",title:R.controls.header+" 2",text:V.a.createElement("h2",null,R.controls.header," 2"),type:"block-type",command:"header-two"},{key:"header-three",title:R.controls.header+" 3",text:V.a.createElement("h3",null,R.controls.header," 3"),type:"block-type",command:"header-three"},{key:"header-four",title:R.controls.header+" 4",text:V.a.createElement("h4",null,R.controls.header," 4"),type:"block-type",command:"header-four"},{key:"header-five",title:R.controls.header+" 5",text:V.a.createElement("h5",null,R.controls.header," 5"),type:"block-type",command:"header-five"},{key:"header-six",title:R.controls.header+" 6",text:V.a.createElement("h6",null,R.controls.header," 6"),type:"block-type",command:"header-six"},{key:"unstyled",title:R.controls.normal,text:R.controls.normal,type:"block-type",command:"unstyled"}]},tr={"header-one":"h1","header-two":"h2","header-three":"h3","header-four":"h4","header-fiv":"h5","header-six":"h6",unstyled:"p",blockquote:"blockquote"},nr=function(ee){var R=null,W=er(ee.language).filter(function(X){return ee.headings.indexOf(X.key)!==-1}),n=W.findIndex(function(X){return X.command===ee.current}),K=W[n]?W[n].title:ee.language.controls.normal;return V.a.createElement(Gt,{caption:K,autoHide:!0,getContainerNode:ee.getContainerNode,title:ee.language.controls.headings,arrowActive:n===0,ref:function(oe){return R=oe},className:"control-item dropdown headings-dropdown"},V.a.createElement("ul",{className:"menu"},W.map(function(X,oe){var Y=ee.current===X.command;return V.a.createElement("li",{key:oe,className:"menu-item"+(Y?" active":""),onClick:function(){ee.onChange(X.command,X.type),R.hide()}},X.text)})))},Tr=s(57),Dr=s(58),Vn=function(ee){return V.a.createElement("div",{className:"bf-colors-wrap"},V.a.createElement("ul",{className:"bf-colors"},ee.presetColors.map(function(R,W){var n=ee.color&&R.toLowerCase()===ee.color.toLowerCase()?"color-item active":"color-item";return V.a.createElement("li",{key:W,title:R,className:n,style:{color:R},"data-color":R.replace("#",""),onClick:function(X){ee.onChange(X.currentTarget.dataset.color,!0)}})})))},br=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"state",{colorType:"color"}),O()(w()(n),"switchColorType",function(Y){var ce=Y.currentTarget;n.setState({colorType:ce.dataset.type})}),O()(w()(n),"toggleColor",function(Y,ce){if(Y){var pe=n.props.hooks("toggle-text-".concat(n.state.colorType),Y)(Y);if(pe===!1)return!1;typeof pe=="string"&&(Y=pe),n.state.colorType==="color"?n.props.editor.setValue(Ce.ContentUtils.toggleSelectionColor(n.props.editorState,Y)):n.props.editor.setValue(Ce.ContentUtils.toggleSelectionBackgroundColor(n.props.editorState,Y))}ce&&(n.dropDownInstance.hide(),n.props.editor.requestFocus())}),n}return D()(R,[{key:"render",value:function(){var n=this,K={},X=null,oe=this.state.colorType,Y=this.props.editorState.getCurrentInlineStyle().toJS();Y.forEach(function(je){je.indexOf("COLOR-")===0&&(K.color="#"+je.split("-")[1],oe==="color"&&(X=K.color)),je.indexOf("BGCOLOR-")===0&&(K.backgroundColor="#"+je.split("-")[1],oe==="background-color"&&(X=K.backgroundColor))});var ce=V.a.createElement("i",{style:K,className:"bfi-text-color"},V.a.createElement("span",{className:"path1"}),V.a.createElement("span",{className:"path2"})),pe=this.props.colorPicker||Vn;return V.a.createElement(Gt,{caption:ce,title:this.props.language.controls.color,showArrow:!1,autoHide:this.props.autoHide,theme:this.props.theme,getContainerNode:this.props.getContainerNode,ref:function(Ye){return n.dropDownInstance=Ye},className:"control-item dropdown text-color-dropdown"},V.a.createElement("div",{className:"bf-text-color-picker-wrap"},V.a.createElement("div",{className:"bf-color-switch-buttons",style:this.props.enableBackgroundColor?{}:{display:"none"}},V.a.createElement("button",{type:"button","data-type":"color",className:oe==="color"?"active":"",onClick:this.switchColorType},this.props.language.controls.textColor),V.a.createElement("button",{type:"button","data-type":"background-color",className:oe==="background-color"?"active":"",onClick:this.switchColorType},this.props.language.controls.backgroundColor)),V.a.createElement(pe,{width:200,color:X,disableAlpha:!0,presetColors:this.props.colors,onChange:this.toggleColor})))}}]),R}(V.a.Component),xr=s(59),wr=function(R,W){var n=R.currentTarget.dataset.size,K=W.hooks("toggle-font-size",n)(n);if(K===!1)return!1;isNaN(n)||(n=K),W.editor.setValue(Ce.ContentUtils.toggleSelectionFontSize(W.editorState,n)),W.editor.requestFocus()},rr=function(ee){var R=null,W=null,n=null;return ee.fontSizes.find(function(K){return Ce.ContentUtils.selectionHasInlineStyle(ee.editorState,"FONTSIZE-"+K)?(R=K,W=K,!0):!1}),V.a.createElement(Gt,{autoHide:!0,caption:R||ee.defaultCaption,getContainerNode:ee.getContainerNode,title:ee.language.controls.fontSize,ref:function(X){return n=X},className:"control-item dropdown bf-font-size-dropdown"},V.a.createElement("ul",{className:"bf-font-sizes"},ee.fontSizes.map(function(K,X){return V.a.createElement("li",{key:X,className:K===W?"active":null,"data-size":K,onClick:function(Y){wr(Y,ee),n.hide()}},K)})))},Mr=s(60),Ir=function(R,W){var n=R.currentTarget.dataset.size,K=W.hooks("toggle-line-height",n)(n);if(K===!1)return!1;isNaN(K)||(n=K),W.editor.setValue(Ce.ContentUtils.toggleSelectionLineHeight(W.editorState,n)),W.editor.requestFocus()},In=function(ee){var R=null,W=null,n=null;return ee.lineHeights.find(function(K){return Ce.ContentUtils.selectionHasInlineStyle(ee.editorState,"LINEHEIGHT-"+K)?(R=K,W=K,!0):!1}),V.a.createElement(Gt,{autoHide:!0,caption:R||ee.defaultCaption,getContainerNode:ee.getContainerNode,title:ee.language.controls.lineHeight,ref:function(X){return n=X},className:"control-item dropdown bf-line-height-dropdown"},V.a.createElement("ul",{className:"bf-line-heights"},ee.lineHeights.map(function(K,X){return V.a.createElement("li",{key:X,className:K===W?"active":null,"data-size":K,onClick:function(Y){Ir(Y,ee),n.hide()}},K)})))},Gn=s(61),ir=function(R,W){var n=R.currentTarget.dataset.name,K=W.hooks("toggle-font-family",n)(n,W.fontFamilies);if(K===!1)return!1;typeof K=="string"&&(n=K),W.editor.setValue(Ce.ContentUtils.toggleSelectionFontFamily(W.editorState,n)),W.editor.requestFocus()},Qn=function(ee){var R=null,W=null,n=null;return ee.fontFamilies.find(function(K,X){return Ce.ContentUtils.selectionHasInlineStyle(ee.editorState,"FONTFAMILY-"+K.name)?(R=K.name,W=X,!0):!1}),V.a.createElement(Gt,{caption:R||ee.defaultCaption,getContainerNode:ee.getContainerNode,title:ee.language.controls.fontFamily,autoHide:!0,arrowActive:W===0,ref:function(X){return n=X},className:"control-item dropdown font-family-dropdown"},V.a.createElement("ul",{className:"menu"},ee.fontFamilies.map(function(K,X){return V.a.createElement("li",{key:X,className:"menu-item "+(X===W?"active":""),"data-name":K.name,onClick:function(Y){ir(Y,ee),n.hide()}},V.a.createElement("span",{style:{fontFamily:K.family}},K.name))})))},St=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"state",{currentAlignment:void 0}),O()(w()(n),"setAlignment",function(Y){var ce=Y.currentTarget.dataset.alignment,pe=n.props.hooks("toggle-text-alignment",ce)(ce);n.props.textAligns.indexOf(pe)>-1&&(ce=pe),n.props.editor.setValue(Ce.ContentUtils.toggleSelectionAlignment(n.props.editorState,ce)),n.props.editor.requestFocus()}),n}return D()(R,[{key:"componentWillReceiveProps",value:function(n){this.setState({currentAlignment:Ce.ContentUtils.getSelectionBlockData(n.editorState,"textAlign")})}},{key:"render",value:function(){var n=this,K=[this.props.language.controls.alignLeft,this.props.language.controls.alignCenter,this.props.language.controls.alignRight,this.props.language.controls.alignJustify];return V.a.createElement(hn,null,this.props.textAligns.map(function(X,oe){return V.a.createElement("button",{type:"button",key:oe,"data-title":K[oe],"data-alignment":X,className:"control-item button "+(X===n.state.currentAlignment?"active":null),onClick:n.setAlignment},V.a.createElement("i",{className:"bfi-align-"+X}))}))}}]),R}(V.a.Component),Br=s(62),or=function(R,W){var n=R.currentTarget.dataset.emoji,K=W.hooks("insert-emoji",n)(n);if(K===!1)return!1;typeof K=="string"&&(n=K),W.editor.setValue(Ce.ContentUtils.insertText(W.editorState,n)),W.editor.requestFocus()},ar=function(ee){return V.a.createElement(Gt,{caption:ee.defaultCaption,autoHide:!0,showArrow:!1,getContainerNode:ee.getContainerNode,title:ee.language.controls.emoji,className:"control-item dropdown bf-emoji-dropdown"},V.a.createElement("div",{className:"bf-emojis-wrap"},V.a.createElement("ul",{className:"bf-emojis"},ee.emojis.map(function(R,W){return V.a.createElement("li",{key:W,"data-emoji":R,onClick:function(K){return or(K,ee)}},R)}))))},un=s(63),Yn=function(R,W){var n=R.currentTarget.dataset.size,K=W.hooks("toggle-letter-spacing",n)(n);if(K===!1)return!1;isNaN(K)||(n=K),W.editor.setValue(Ce.ContentUtils.toggleSelectionLetterSpacing(W.editorState,n)),W.editor.requestFocus()},ur=function(ee){var R=null,W=null,n=null;return ee.letterSpacings.find(function(K){return Ce.ContentUtils.selectionHasInlineStyle(ee.editorState,"LETTERSPACING-"+K)?(R=K,W=K,!0):!1}),V.a.createElement(Gt,{autoHide:!0,caption:R||ee.defaultCaption,getContainerNode:ee.getContainerNode,title:ee.language.controls.letterSpacing,ref:function(X){return n=X},className:"control-item dropdown bf-letter-spacing-dropdown"},V.a.createElement("ul",{className:"bf-letter-spacings"},ee.letterSpacings.map(function(K,X){return V.a.createElement("li",{key:X,className:K===W?"active":null,"data-size":K,onClick:function(Y){Yn(Y,ee),n.hide()}},K)})))},lr=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"state",{currentIndent:0}),O()(w()(n),"increaseIndent",function(){n.props.editor.setValue(Ce.ContentUtils.increaseSelectionIndent(n.props.editorState)),n.props.editor.requestFocus()}),O()(w()(n),"decreaseIndent",function(){n.props.editor.setValue(Ce.ContentUtils.decreaseSelectionIndent(n.props.editorState)),n.props.editor.requestFocus()}),n}return D()(R,[{key:"componentWillReceiveProps",value:function(n){this.setState({currentIndent:Ce.ContentUtils.getSelectionBlockData(n.editorState,"textIndent")||0})}},{key:"render",value:function(){var n=this.state.currentIndent,K=this.props.language;return V.a.createElement(hn,null,V.a.createElement("button",{key:0,type:"button","data-title":K.controls.increaseIndent,disabled:n>=6,className:"control-item button button-indent-increase".concat(n>0&&n<6?" active":""),onClick:this.increaseIndent},V.a.createElement("i",{className:"bfi-indent-increase"})),V.a.createElement("button",{key:1,type:"button","data-title":K.controls.decreaseIndent,disabled:n<=0,className:"control-item button button-indent-decrease",onClick:this.decreaseIndent},V.a.createElement("i",{className:"bfi-indent-decrease"})))}}]),R}(V.a.Component),Xt={"inline-style":"toggle-inline-style","block-type":"change-block-type","editor-method":"exec-editor-command"},zt={superscript:"subscript",subscript:"superscript"},Un=function(R,W,n,K){return n=n.map(function(X){return typeof X=="function"?X(R):X}),K=K.map(function(X){return typeof X=="function"?X(R):X}),n.length===0&&K.length===0?W:W.map(function(X){return K.find(function(oe){return oe.replace===(X.key||X)})||n.find(function(oe){return oe.replace===(X.key||X)})||X}).concat(n.length?"separator":"").concat(n.filter(function(X){return!X.replace})).concat(K.filter(function(X){return typeof X=="string"||!X.replace}))},sr=function(ee){j()(R,ee);function R(){var W,n;l()(this,R);for(var K=arguments.length,X=new Array(K),oe=0;oe<K;oe++)X[oe]=arguments[oe];return n=y()(this,(W=T()(R)).call.apply(W,[this].concat(X))),O()(w()(n),"allControls",[]),O()(w()(n),"mediaLibiraryModal",null),O()(w()(n),"extendedModals",{}),O()(w()(n),"openBraftFinder",function(){if(!n.props.braftFinder||!n.props.braftFinder.ReactComponent||n.props.hooks("open-braft-finder")()===!1)return!1;var Y=n.props.media,ce=n.props.braftFinder.ReactComponent;n.mediaLibiraryModal=on({title:n.props.language.controls.mediaLibirary,language:n.props.language,width:640,showFooter:!1,className:Y.modalClassName,component:V.a.createElement(ce,{accepts:Y.accepts,onCancel:n.closeBraftFinder,onInsert:n.insertMedias,onChange:Y.onChange,externals:Y.externals,onBeforeSelect:n.bindBraftFinderHook("select-medias"),onBeforeDeselect:n.bindBraftFinderHook("deselect-medias"),onBeforeRemove:n.bindBraftFinderHook("remove-medias"),onBeforeInsert:n.bindBraftFinderHook("insert-medias"),onFileSelect:n.bindBraftFinderHook("select-files")})})}),O()(w()(n),"bindBraftFinderHook",function(Y){return function(){return n.props.hooks(Y,arguments.length<=0?void 0:arguments[0]).apply(void 0,arguments)}}),O()(w()(n),"insertMedias",function(Y){n.props.editor.setValue(Ce.ContentUtils.insertMedias(n.props.editorState,Y)),n.props.editor.requestFocus(),n.props.media.onInsert&&n.props.media.onInsert(Y),n.closeBraftFinder()}),O()(w()(n),"closeBraftFinder",function(){n.props.media.onCancel&&n.props.media.onCancel(),n.mediaLibiraryModal&&n.mediaLibiraryModal.close()}),n}return D()(R,[{key:"componentDidUpdate",value:function(){var n=this,K=this.props.language;this.allControls.forEach(function(X){X.type==="modal"&&X.modal&&X.modal.id&&n.extendedModals[X.modal.id]&&n.extendedModals[X.modal.id].update(r()({},X.modal,{language:K}))})}},{key:"getControlItemClassName",value:function(n){var K="control-item button",X=n.type,oe=n.command;return(X==="inline-style"&&Ce.ContentUtils.selectionHasInlineStyle(this.props.editorState,oe)||X==="block-type"&&Ce.ContentUtils.getSelectionBlockType(this.props.editorState)===oe||X==="entity"&&Ce.ContentUtils.getSelectionEntityType(this.props.editorState)===oe)&&(K+=" active"),K}},{key:"applyControl",value:function(n,K){var X=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},oe=this.props.hooks(Xt[K]||K,n)(n),Y=this.props.editorState;if(oe===!1)return!1;if(typeof oe=="string"&&(n=oe),K==="inline-style"){var ce=zt[n];ce&&Ce.ContentUtils.selectionHasInlineStyle(Y,ce)&&(Y=Ce.ContentUtils.toggleSelectionInlineStyle(Y,ce)),this.props.editor.setValue(Ce.ContentUtils.toggleSelectionInlineStyle(Y,n))}else K==="block-type"?this.props.editor.setValue(Ce.ContentUtils.toggleSelectionBlockType(Y,n)):K==="entity"?this.props.editor.setValue(Ce.ContentUtils.toggleSelectionEntity(Y,{type:n,mutability:X.mutability||"MUTABLE",data:X.data||{}})):K==="editor-method"&&this.props.editor[n]&&this.props.editor[n]()}},{key:"render",value:function(){var n=this,K=this.props,X=K.editor,oe=K.editorId,Y=K.editorState,ce=K.className,pe=K.style,je=K.controls,Ye=K.media,Xe=K.extendControls,st=K.language,At=K.hooks,Ht=K.colors,Kt=K.colorPicker,bt=K.colorPickerTheme,Tt=K.colorPickerAutoHide,vn=K.headings,ln=K.fontSizes,sn=K.fontFamilies,tn=K.emojis,mn=K.getContainerNode,On=K.lineHeights,nn=K.letterSpacings,yn=K.textAligns,Rn=K.textBackgroundColor,Ot=K.allowInsertLinkText,$t=K.defaultLinkTarget,e=Ce.ContentUtils.getSelectionBlockType(Y),t={editor:X,editorId:oe,editorState:Y,language:st,getContainerNode:mn,hooks:At},m=[],F=Ut(st,X),M=q(oe),G=Un(t,je,M,Xe);return this.allControls=G,V.a.createElement("div",{className:"bf-controlbar ".concat(ce||""),style:pe,onMouseDown:this.preventDefault},G.map(function($,re){var fe=typeof $=="string"?$:$.key;if(typeof fe!="string"||m.indexOf(fe)>-1)return null;if(fe.toLowerCase()==="separator")return V.a.createElement("span",{key:re,className:"separator-line"});var te=F.find(function(ke){return ke.key.toLowerCase()===fe.toLowerCase()});if(typeof $!="string"&&(te=r()({},te,$)),!te)return null;if(m.push(fe),te.type==="headings")return V.a.createElement(nr,nt()({key:re,headings:vn,current:e,onChange:function(Le){return n.applyControl(Le,"block-type")}},t));if(te.type==="text-color")return V.a.createElement(br,nt()({key:re,colors:Ht,colorPicker:Kt,theme:bt,autoHide:Tt,enableBackgroundColor:Rn},t));if(te.type==="font-size")return V.a.createElement(rr,nt()({key:re,fontSizes:ln,defaultCaption:te.title},t));if(te.type==="line-height")return V.a.createElement(In,nt()({key:re,lineHeights:On,defaultCaption:te.title},t));if(te.type==="letter-spacing")return V.a.createElement(ur,nt()({key:re,letterSpacings:nn,defaultCaption:te.title},t));if(te.type==="text-indent")return V.a.createElement(lr,nt()({key:re,defaultCaption:te.title},t));if(te.type==="font-family")return V.a.createElement(Qn,nt()({key:re,fontFamilies:sn,defaultCaption:te.title},t));if(te.type==="emoji")return V.a.createElement(ar,nt()({key:re,emojis:tn,defaultCaption:te.text},t));if(te.type==="link")return V.a.createElement(wn,nt()({key:re,defaultLinkTarget:$t,allowInsertLinkText:Ot},t));if(te.type==="text-align")return V.a.createElement(St,nt()({key:re,textAligns:yn},t));if(te.type==="media")return!Ye.image&&!Ye.video&&!Ye.audio?null:V.a.createElement("button",{type:"button",key:re,"data-title":te.title,disabled:te.disabled,className:"control-item media button",onClick:n.openBraftFinder},te.text);if(te.type==="dropdown")return V.a.createElement(Gt,nt()({key:re,className:"control-item extend-control-item dropdown ".concat(te.className||""),caption:te.text,htmlCaption:te.html,showArrow:te.showArrow,title:te.title,arrowActive:te.arrowActive,theme:te.theme,autoHide:te.autoHide,disabled:te.disabled,ref:te.ref},t),te.component);if(te.type==="modal")return V.a.createElement("button",{type:"button",key:re,"data-title":te.title,disabled:te.disabled,className:"control-item extend-control-item button ".concat(te.className||""),dangerouslySetInnerHTML:te.html?{__html:te.html}:null,onClick:function(Le){te.modal&&te.modal.id&&(n.extendedModals[te.modal.id]?(n.extendedModals[te.modal.id].active=!0,n.extendedModals[te.modal.id].update(r()({},te.modal,{language:st}))):(n.extendedModals[te.modal.id]=on(r()({},te.modal,{language:st})),te.modal.onCreate&&te.modal.onCreate(n.extendedModals[te.modal.id]))),te.onClick&&te.onClick(Le)}},te.html?null:te.text);if(te.type==="component")return V.a.createElement("div",{key:re,className:"component-wrapper ".concat(te.className||"")},te.component);if(te.type==="button")return V.a.createElement("button",{type:"button",key:re,"data-title":te.title,disabled:te.disabled,className:"control-item button ".concat(te.className||""),dangerouslySetInnerHTML:te.html?{__html:te.html}:null,onClick:function(Le){return te.onClick&&te.onClick(Le)}},te.html?null:te.text);if(te){var he=!1;return te.command==="undo"?he=Y.getUndoStack().size===0:te.command==="redo"&&(he=Y.getRedoStack().size===0),V.a.createElement("button",{type:"button",key:re,disabled:he,"data-title":te.title,className:n.getControlItemClassName({type:te.type,command:te.command}),onClick:function(){return n.applyControl(te.command,te.type,te.data)}},te.text)}}))}},{key:"preventDefault",value:function(n){var K=n.target.tagName.toLowerCase();K==="input"||K==="label"||n.preventDefault()}}]),R}(V.a.Component),Fr=function(R){return function(W){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return R[W]||function(){return n}}},cr=function(R,W){return R.filter(function(n){return!W.find(function(K){return K.toLowerCase()===n.toLowerCase()})}).filter(function(n,K,X){return X.indexOf(n)===K})},Ct=function(R,W){return[].concat(Q()(R.controls),Q()(R.extendControls)).find(function(n){return n===W||n.key===W})&&R.excludeControls.indexOf(W)===-1},En=function(R){var W=R.editorId||R.id,n=r()({},ne.converts,R.converts,{fontFamilies:R.fontFamilies});return n.styleImportFn=ve(n.styleImportFn,W),n.styleExportFn=ze(n.styleExportFn,W),n.entityImportFn=Ge(n.entityImportFn,W),n.entityExportFn=Te(n.entityExportFn,W),n.blockImportFn=Pe(n.blockImportFn,W),n.blockExportFn=Oe(n.blockExportFn,W),n},Mn=function(ee){j()(R,ee);function R(W){var n;l()(this,R),n=y()(this,T()(R).call(this,W)),O()(w()(n),"onChange",function(Y,ce){Y instanceof $e.EditorState||(Y=$e.EditorState.set(Y,{decorator:n.editorDecorators})),Y.convertOptions||Y.setConvertOptions(En(n.editorProps)),n.setState({editorState:Y},function(){n.props.onChange&&n.props.onChange(Y),ce&&ce(Y)})}),O()(w()(n),"getDraftInstance",function(){return n.draftInstance}),O()(w()(n),"getFinderInstance",function(){return n.braftFinder}),O()(w()(n),"getValue",function(){return n.state.editorState}),O()(w()(n),"setValue",function(Y,ce){return n.onChange(Y,ce)}),O()(w()(n),"forceRender",function(){var Y=n.state.editorState.getSelection();n.setValue($e.EditorState.set(n.state.editorState,{decorator:n.editorDecorators}),function(){n.setValue($e.EditorState.forceSelection(n.state.editorState,Y))})}),O()(w()(n),"onTab",function(Y){ge("tab",n.state.editorState,w()(n))==="handled"&&Y.preventDefault(),n.editorProps.onTab&&n.editorProps.onTab(Y)}),O()(w()(n),"onFocus",function(){n.isFocused=!0,n.editorProps.onFocus&&n.editorProps.onFocus(n.state.editorState)}),O()(w()(n),"onBlur",function(){n.isFocused=!1,n.editorProps.onBlur&&n.editorProps.onBlur(n.state.editorState)}),O()(w()(n),"requestFocus",function(){setTimeout(function(){return n.draftInstance.focus()},0)}),O()(w()(n),"handleKeyCommand",function(Y,ce){return ge(Y,ce,w()(n))}),O()(w()(n),"handleReturn",function(Y,ce){return ye(Y,ce,w()(n))}),O()(w()(n),"handleBeforeInput",function(Y,ce){return We(Y,ce,w()(n))}),O()(w()(n),"handleDrop",function(Y,ce){return Ve(Y,ce,w()(n))}),O()(w()(n),"handleDroppedFiles",function(Y,ce){return ct(Y,ce,w()(n))}),O()(w()(n),"handlePastedFiles",function(Y){return Ze(Y,w()(n))}),O()(w()(n),"handleCopyContent",function(Y){return ut(Y,w()(n))}),O()(w()(n),"handlePastedText",function(Y,ce,pe){return lt(Y,ce,pe,w()(n))}),O()(w()(n),"handleCompositionStart",function(Y){return Fe(Y,w()(n))}),O()(w()(n),"undo",function(){n.setValue(Ce.ContentUtils.undo(n.state.editorState))}),O()(w()(n),"redo",function(){n.setValue(Ce.ContentUtils.redo(n.state.editorState))}),O()(w()(n),"removeSelectionInlineStyles",function(){n.setValue(Ce.ContentUtils.removeSelectionInlineStyles(n.state.editorState))}),O()(w()(n),"insertHorizontalLine",function(){n.setValue(Ce.ContentUtils.insertHorizontalLine(n.state.editorState))}),O()(w()(n),"clearEditorContent",function(){n.setValue(Ce.ContentUtils.clear(n.state.editorState),function(Y){n.setValue(Ce.ContentUtils.toggleSelectionIndent(Y,0))})}),O()(w()(n),"toggleFullscreen",function(Y){n.setState({isFullscreen:typeof Y!="undefined"?Y:!n.state.isFullscreen},function(){n.editorProps.onFullscreen&&n.editorProps.onFullscreen(n.state.isFullscreen)})}),O()(w()(n),"setEditorContainerNode",function(Y){n.containerNode=Y}),n.editorProps=n.getEditorProps(W),n.editorDecorators=qt(n.editorProps.editorId||n.editorProps.id),n.isFocused=!1,n.isLiving=!1,n.braftFinder=null,n.valueInitialized=!!(n.props.defaultValue||n.props.value);var K=(n.props.defaultValue||n.props.value)instanceof $e.EditorState?n.props.defaultValue||n.props.value:$e.EditorState.createEmpty(n.editorDecorators);K.setConvertOptions(En(n.editorProps));var X=[];if(Ce.ContentUtils.isEditorState(K)){var oe=Ce.ColorUtils.detectColorsFromDraftState(K.toRAW(!0));K.setConvertOptions(En(n.editorProps)),X=cr(oe,n.editorProps.colors)}return n.state={tempColors:X,editorState:K,isFullscreen:!1,draftProps:{}},n.containerNode=null,n}return D()(R,[{key:"getEditorProps",value:function(n){var K=this;n=n||this.props;var X=n,oe=X.value,Y=X.defaultValue,ce=X.onChange,pe=S()(X,["value","defaultValue","onChange"]),je=Z(pe.editorId||pe.id);if(je.length===0)return pe;var Ye=Object(Ft.Map)(pe);return je.forEach(function(Xe){Ye=Ye.merge(Object(Ft.Map)(Xe(Ye.toJS(),K)||{}))}),Ye.toJS()}},{key:"componentWillMount",value:function(){if(Ct(this.editorProps,"media")){var n=this.editorProps,K=n.language,X=n.media,oe=r()({},ne.media,X),Y=oe.uploadFn,ce=oe.validateFn,pe=oe.items;this.braftFinder=new rt.a({items:pe,language:K,uploader:Y,validator:ce}),this.forceUpdate()}}},{key:"componentDidMount",value:function(){this.isLiving=!0}},{key:"componentDidUpdate",value:function(n,K){K.editorState!==this.state.editorState&&this.state.editorState.setConvertOptions(En(this.editorProps))}},{key:"componentWillReceiveProps",value:function(n){var K=this;this.editorProps=this.getEditorProps(n);var X=n.value,oe=this.editorProps,Y=oe.media,ce=oe.language,pe=this.getEditorProps();if(!Ct(pe,"media")&&Ct(this.editorProps,"media")&&!this.braftFinder){var je=r()({},ne.media,Y),Ye=je.uploadFn,Xe=je.validateFn,st=je.items;this.braftFinder=new rt.a({items:st,language:ce,uploader:Ye,validator:Xe}),this.forceUpdate()}Y&&Y.items&&this.braftFinder&&this.braftFinder.setItems(Y.items);var At;if(!this.valueInitialized&&typeof this.props.defaultValue=="undefined"&&Ce.ContentUtils.isEditorState(n.defaultValue)?At=n.defaultValue:Ce.ContentUtils.isEditorState(X)&&(At=X),At)if(At&&At!==this.state.editorState){var Ht=Ce.ColorUtils.detectColorsFromDraftState(At.toRAW(!0));At.setConvertOptions(En(this.editorProps)),this.setState({tempColors:cr([].concat(Q()(this.state.tempColors),Q()(Ht)),pe.colors),editorState:At},function(){K.props.onChange&&K.props.onChange(At)})}else this.setState({editorState:At})}},{key:"componentWillUnmount",value:function(){this.isLiving=!1,this.controlBarInstance&&this.controlBarInstance.closeBraftFinder()}},{key:"lockOrUnlockEditor",value:function(n){this.setState({editorLocked:n})}},{key:"render",value:function(){var n=this,K=this.editorProps,X=K.id,oe=K.editorId,Y=K.controls,ce=K.excludeControls,pe=K.extendControls,je=K.readOnly,Ye=K.disabled,Xe=K.media,st=K.language,At=K.colors,Ht=K.colorPicker,Kt=K.colorPickerTheme,bt=K.colorPickerAutoHide,Tt=K.hooks,vn=K.fontSizes,ln=K.fontFamilies,sn=K.emojis,tn=K.placeholder,mn=K.fixPlaceholder,On=K.headings,nn=K.imageControls,yn=K.imageResizable,Rn=K.imageEqualRatio,Ot=K.lineHeights,$t=K.letterSpacings,e=K.textAligns,t=K.textBackgroundColor,m=K.allowInsertLinkText,F=K.defaultLinkTarget,M=K.extendAtomics,G=K.className,$=K.style,re=K.controlBarClassName,fe=K.controlBarStyle,te=K.contentClassName,he=K.contentStyle,ke=K.stripPastedStyles,Le=K.componentBelowControlBar,tt=this.state,at=tt.isFullscreen,vt=tt.editorState;oe=oe||X,Tt=Fr(Tt),Y=Y.filter(function(pr){return ce.indexOf(pr)===-1}),st=(typeof st=="function"?st(ot,"braft-editor"):ot[st])||ot[ne.language];var en=Xe&&Xe.externals?r()({},ne.media.externals,Xe.externals):ne.media.externals,rn=Xe&&Xe.accepts?r()({},ne.media.accepts,Xe.accepts):ne.media.accepts;Xe=r()({},ne.media,Xe,{externalMedias:en,accepts:rn}),Xe.uploadFn||(Xe.video=!1,Xe.audio=!1);var zn={editor:this,editorState:vt,braftFinder:this.braftFinder,ref:function(kr){return n.controlBarInstance=kr},getContainerNode:function(){return n.containerNode},className:re,style:fe,colors:[].concat(Q()(At),Q()(this.state.tempColors)),colorPicker:Ht,colorPickerTheme:Kt,colorPickerAutoHide:bt,hooks:Tt,editorId:oe,media:Xe,controls:Y,language:st,extendControls:pe,headings:On,fontSizes:vn,fontFamilies:ln,emojis:sn,lineHeights:Ot,letterSpacings:$t,textAligns:e,textBackgroundColor:t,allowInsertLinkText:m,defaultLinkTarget:F},Rr=vt.convertOptions.unitExportFn,dr={editor:this,editorId:oe,hooks:Tt,editorState:vt,containerNode:this.containerNode,imageControls:nn,imageResizable:yn,language:st,extendAtomics:M,imageEqualRatio:Rn},Lr=xn(dr,this.editorProps.blockRendererFn),Nr=Wn(dr,this.editorProps.blockRenderMap),Pr=_n(this.editorProps.blockStyleFn),Kr=gn(dr,this.editorProps.customStyleMap),Ur=Pn(dr,{fontFamilies:ln,unitExportFn:Rr,customStyleFn:this.editorProps.customStyleFn}),zr=De(this.editorProps.keyBindingFn),Or={};(this.state.editorLocked||this.editorProps.disabled||this.editorProps.readOnly||this.editorProps.draftProps.readOnly)&&(Or.readOnly=!0),tn&&mn&&vt.isEmpty()&&vt.getCurrentContent().getFirstBlock().getType()!=="unstyled"&&(tn="");var Hr=r()({ref:function(kr){n.draftInstance=kr},editorState:vt,handleKeyCommand:this.handleKeyCommand,handleReturn:this.handleReturn,handleBeforeInput:this.handleBeforeInput,handleDrop:this.handleDrop,handleDroppedFiles:this.handleDroppedFiles,handlePastedText:this.handlePastedText,handlePastedFiles:this.handlePastedFiles,onChange:this.onChange,onTab:this.onTab,onFocus:this.onFocus,onBlur:this.onBlur,blockRenderMap:Nr,blockRendererFn:Lr,blockStyleFn:Pr,customStyleMap:Kr,customStyleFn:Ur,keyBindingFn:zr,placeholder:tn,stripPastedStyles:ke},this.editorProps.draftProps,Or);return V.a.createElement("div",{style:$,ref:this.setEditorContainerNode,className:"bf-container ".concat(G).concat(Ye?" disabled":"").concat(je?" read-only":"").concat(at?" fullscreen":"")},V.a.createElement(sr,zn),Le,V.a.createElement("div",{onCompositionStart:this.handleCompositionStart,className:"bf-content ".concat(te),onCopy:this.handleCopyContent,style:he},V.a.createElement($e.Editor,Hr)))}}]),R}(V.a.Component);O()(Mn,"defaultProps",ne);var Pt=s(14);s.d(g,"EditorState",function(){return $e.EditorState}),s.d(g,"getDecorators",function(){return qt}),$e.EditorState.prototype.setConvertOptions=function(){var ee=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.convertOptions=ee},$e.EditorState.prototype.toHTML=function(){var ee=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},R=this.convertOptions||{};return Object(Pt.convertEditorStateToHTML)(this,r()({},R,ee))},$e.EditorState.prototype.toRAW=function(ee){return ee?Object(Pt.convertEditorStateToRaw)(this):JSON.stringify(Object(Pt.convertEditorStateToRaw)(this))},$e.EditorState.prototype.toText=function(){return this.getCurrentContent().getPlainText()},$e.EditorState.prototype.isEmpty=function(){return!this.getCurrentContent().hasText()},Mn.createEditorState=$e.EditorState.createFrom=function(ee){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};R.unitExportFn=R.unitExportFn||Mn.defaultProps.converts.unitExportFn,R.styleImportFn=ve(R.styleImportFn,R.editorId),R.entityImportFn=Ge(R.entityImportFn,R.editorId),R.blockImportFn=Pe(R.blockImportFn,R.editorId);var W=null;if(ee instanceof $e.EditorState)W=ee;else if(c()(ee)==="object"&&ee&&ee.blocks&&ee.entityMap)W=Object(Pt.convertRawToEditorState)(ee,qt(R.editorId));else if(typeof ee=="string")try{/^(-)?\d+$/.test(ee)?W=Object(Pt.convertHTMLToEditorState)(ee,qt(R.editorId),R,"create"):W=$e.EditorState.createFrom(JSON.parse(ee),R)}catch(n){W=Object(Pt.convertHTMLToEditorState)(ee,qt(R.editorId),R,"create")}else typeof ee=="number"?W=Object(Pt.convertHTMLToEditorState)(ee.toLocaleString().replace(/,/g,""),qt(R.editorId),R,"create"):W=$e.EditorState.createEmpty(qt(R.editorId));return R.styleExportFn=ze(R.styleExportFn,R.editorId),R.entityExportFn=Te(R.entityExportFn,R.editorId),R.blockExportFn=Oe(R.blockExportFn,R.editorId),W.setConvertOptions(R),W};var fr=g.default=ht(Mn)},function(f,g){},,function(f,g){},,,function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){},function(f,g){}])})},42017:function(H,P,i){(function(b,v){if(!0)H.exports=v(i(67294));else var A,p})(window,function(x){return function(b){var v={};function A(p){if(v[p])return v[p].exports;var I=v[p]={i:p,l:!1,exports:{}};return b[p].call(I.exports,I,I.exports,A),I.l=!0,I.exports}return A.m=b,A.c=v,A.d=function(p,I,E){A.o(p,I)||Object.defineProperty(p,I,{enumerable:!0,get:E})},A.r=function(p){typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(p,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(p,"__esModule",{value:!0})},A.t=function(p,I){if(I&1&&(p=A(p)),I&8||I&4&&typeof p=="object"&&p&&p.__esModule)return p;var E=Object.create(null);if(A.r(E),Object.defineProperty(E,"default",{enumerable:!0,value:p}),I&2&&typeof p!="string")for(var d in p)A.d(E,d,function(f){return p[f]}.bind(null,d));return E},A.n=function(p){var I=p&&p.__esModule?function(){return p.default}:function(){return p};return A.d(I,"a",I),I},A.o=function(p,I){return Object.prototype.hasOwnProperty.call(p,I)},A.p="/",A(A.s=20)}([function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0});var p=v.compressImage=function(E){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1280,f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:800;return new Promise(function(g,s){var a=new Image;a.src=E,a.onerror=function(c){s(c)},a.onload=function(){try{var c=document.createElement("canvas"),o=this.width>d||this.height>f?this.width>this.height?d/this.width:f/this.height:1;c.width=this.width*o,c.height=this.height*o;var r=c.getContext("2d");r.drawImage(this,0,0,c.width,c.height),g({url:c.toDataURL("image/png",1),width:c.width,height:c.height})}catch(u){s(u)}}})}},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0});var p=v.UniqueIndex=function(){return isNaN(window.__BRAFT_MM_UNIQUE_INDEX__)?window.__BRAFT_MM_UNIQUE_INDEX__=1:window.__BRAFT_MM_UNIQUE_INDEX__+=1,window.__BRAFT_MM_UNIQUE_INDEX__}},function(b,v){b.exports=x},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0}),v.default={remove:"Kald\u0131r",cancel:"\u0130ptal",confirm:"Onayla",insert:"Se\xE7ilenleri ekle",width:"Geni\u015Flik",height:"Y\xFCkseklik",image:"Resim",video:"G\xF6r\xFCnt\xFC",audio:"Ses",embed:"Nesne g\xF6m",caption:"Kitapl\u0131k",dragTip:"T\u0131kla ya da dosya s\xFCr\xFCkle",dropTip:"Y\xFCklemek i\xE7in s\xFCr\xFCkleyin",selectAll:"T\xFCm\xFCn\xFC se\xE7",deselect:"Se\xE7imi kald\u0131r",removeSelected:"Se\xE7ilenleri kald\u0131r",externalInputPlaceHolder:"Kaynak ad\u0131|Kaynak ba\u011Flant\u0131s\u0131",externalInputTip:`Kaynak as\u0131n\u0131 ve ba\u011Flant\u0131s\u0131n\u0131 "|" ile ay\u0131r\u0131n ve Enter' a bas\u0131n.`,addLocalFile:"Yerel' den ekle",addExternalSource:"Harici kaynaktan ekle",unnamedItem:"Adland\u0131r\u0131lmam\u0131\u015F giri\u015F",confirmInsert:"Se\xE7ilenleri ekle"}},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0}),v.default={remove:"\u524A\u9664\u3059\u308B",cancel:"\u30AD\u30E3\u30F3\u30BB\u30EB",confirm:"\u78BA\u8A8D\u3059\u308B",insert:"\u9078\u629E\u3057\u305F\u30A2\u30A4\u30C6\u30E0\u3092\u633F\u5165",width:"\u5E45",height:"\u8EAB\u9577",image:"\u7D75",video:"\u30D3\u30C7\u30AA",audio:"\u97F3\u58F0",embed:"\u57CB\u3081\u8FBC\u307F\u30E1\u30C7\u30A3\u30A2",caption:"\u30E1\u30C7\u30A3\u30A2\u30E9\u30A4\u30D6\u30E9\u30EA\u30FC",dragTip:"\u30D5\u30A1\u30A4\u30EB\u3092\u3053\u306E\u4F4D\u7F6E\u307E\u3067\u30AF\u30EA\u30C3\u30AF\u307E\u305F\u306F\u30C9\u30E9\u30C3\u30B0\u3057\u307E\u3059",dropTip:"\u30A2\u30C3\u30D7\u30ED\u30FC\u30C9\u3059\u308B\u30DE\u30A6\u30B9\u3092\u653E\u3057\u307E\u3059",selectAll:"\u3059\u3079\u3066\u9078\u629E",deselect:"\u9078\u629E\u3092\u89E3\u9664",removeSelected:"\u9078\u629E\u3057\u305F\u30A2\u30A4\u30C6\u30E0\u3092\u524A\u9664",externalInputPlaceHolder:"\u30EA\u30BD\u30FC\u30B9\u540D|\u30EA\u30BD\u30FC\u30B9\u30A2\u30C9\u30EC\u30B9",externalInputTip:'\u30EA\u30BD\u30FC\u30B9\u540D\u3068\u30EA\u30BD\u30FC\u30B9\u30A2\u30C9\u30EC\u30B9\u306F "|"\u3067\u533A\u5207\u308A\u307E\u3059\u3002',addLocalFile:"\u30ED\u30FC\u30AB\u30EB\u30EA\u30BD\u30FC\u30B9\u3092\u8FFD\u52A0\u3059\u308B",addExternalSource:"\u30CD\u30C3\u30C8\u30EF\u30FC\u30AF\u30EA\u30BD\u30FC\u30B9\u3092\u8FFD\u52A0\u3059\u308B",unnamedItem:"\u540D\u524D\u306E\u306A\u3044\u30A2\u30A4\u30C6\u30E0",confirmInsert:"\u9078\u629E\u3057\u305F\u30A2\u30A4\u30C6\u30E0\u3092\u633F\u5165"}},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0}),v.default={remove:"\uC0AD\uC81C",cancel:"\uCDE8\uC18C",confirm:"\uD655\uC778",insert:"\uC120\uD0DD\uD55C\uD56D\uBAA9\uC0BD\uC785",width:"\uB108\uBE44",height:"\uB192\uC774",image:"\uADF8\uB9BC",video:"\uBE44\uB514\uC624",audio:"\uC624\uB514\uC624",embed:"\uC784\uBCA0\uB514\uB4DC\uBBF8\uB514\uC5B4",caption:"\uBBF8\uB514\uC5B4\uB77C\uC774\uBE0C\uB7EC\uB9AC",dragTip:"\uD30C\uC77C\uC744 \uD074\uB9AD\uD558\uAC70\uB098\uC774 \uC9C0\uC810\uC73C\uB85C \uB4DC\uB798\uADF8\uD558\uC2ED\uC2DC\uC624.",dropTip:"\uC5C5\uB85C\uB4DC\uD558\uB824\uBA74\uB9C8\uC6B0\uC2A4\uB97C\uB193\uC73C\uC2ED\uC2DC\uC624.",selectAll:"\uBAA8\uB450 \uC120\uD0DD",deselect:"\uC120\uD0DD \uCDE8\uC18C",removeSelected:"\uC120\uD0DD\uD55C \uD56D\uBAA9 \uC0AD\uC81C",externalInputPlaceHolder:"\uB9AC\uC18C\uC2A4 \uC774\uB984 | \uB9AC\uC18C\uC2A4 \uC8FC\uC18C",externalInputTip:'\uC790\uC6D0 \uC774\uB984\uACFC \uC790\uC6D0 \uC8FC\uC18C\uB97C "|"',addLocalFile:"\uB85C\uCEEC \uB9AC\uC18C\uC2A4 \uCD94\uAC00",addExternalSource:"\uB124\uD2B8\uC6CC\uD06C \uB9AC\uC18C\uC2A4 \uCD94\uAC00",unnamedItem:"\uC774\uB984\uC5C6\uB294 \uD56D\uBAA9",confirmInsert:"\uC120\uD0DD\uD55C \uD56D\uBAA9 \uC0BD\uC785"}},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0}),v.default={remove:"Usu\u0144",cancel:"Anuluj",confirm:"Potwierd\u017A",insert:"Wstaw wybrane elementy",width:"Szeroko\u015B\u0107",height:"Wysoko\u015B\u0107",image:"Obraz",video:"Wideo",audio:"D\u017Awi\u0119k",embed:"Obiekt",caption:"Biblioteka medi\xF3w",dragTip:"Kliknij lub przenie\u015B tu pliki",dropTip:"Upu\u015B\u0107 aby doda\u0107 plik",selectAll:"Zaznacz wszystko",deselect:"Odznacz",removeSelected:"Usu\u0144 wybrane",externalInputPlaceHolder:"Nazwa \u017Ar\xF3d\u0142a|Adres URL",externalInputTip:'Oddziel nazw\u0119 i adres URL \u017Ar\xF3d\u0142a z pomoc\u0105 "|", Potwierd\u017A Enter-em',addLocalFile:"Dodaj z komputera",addExternalSource:"Dodaj z Internetu",unnamedItem:"Bez nazwy",confirmInsert:"Dodaj wybrane elementy"}},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0}),v.default={remove:"\u5220\u9664",cancel:"\u53D6\u6D88",confirm:"\u786E\u8BA4",insert:"\u63D2\u5165\u6240\u9009\u9879\u76EE",width:"\u5BBD\u5EA6",height:"\u9AD8\u5EA6",image:"\u56FE\u7247",video:"\u89C6\u9891",audio:"\u97F3\u9891",embed:"\u5D4C\u5165\u5F0F\u5A92\u4F53",caption:"\u5A92\u4F53\u5E93",dragTip:"\u70B9\u51FB\u6216\u62D6\u52A8\u6587\u4EF6\u81F3\u6B64",dropTip:"\u653E\u5F00\u9F20\u6807\u4EE5\u4E0A\u4F20",selectAll:"\u9009\u62E9\u5168\u90E8",deselect:"\u53D6\u6D88\u9009\u62E9",removeSelected:"\u5220\u9664\u9009\u4E2D\u9879\u76EE",externalInputPlaceHolder:"\u8D44\u6E90\u540D\u79F0|\u8D44\u6E90\u5730\u5740",externalInputTip:"\u4F7F\u7528\u201C|\u201D\u5206\u9694\u8D44\u6E90\u540D\u79F0\u548C\u8D44\u6E90\u5730\u5740",addLocalFile:"\u6DFB\u52A0\u672C\u5730\u8D44\u6E90",addExternalSource:"\u6DFB\u52A0\u7F51\u7EDC\u8D44\u6E90",unnamedItem:"\u672A\u547D\u540D\u9879\u76EE",confirmInsert:"\u63D2\u5165\u9009\u4E2D\u9879\u76EE"}},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0}),v.default={remove:"\u5220\u9664",cancel:"\u53D6\u6D88",confirm:"\u786E\u8BA4",insert:"\u63D2\u5165\u6240\u9009\u9879\u76EE",width:"\u5BBD\u5EA6",height:"\u9AD8\u5EA6",image:"\u56FE\u7247",video:"\u89C6\u9891",audio:"\u97F3\u9891",embed:"\u5D4C\u5165\u5F0F\u5A92\u4F53",caption:"\u5A92\u4F53\u5E93",dragTip:"\u70B9\u51FB\u6216\u62D6\u52A8\u6587\u4EF6\u81F3\u6B64",dropTip:"\u653E\u5F00\u9F20\u6807\u4EE5\u4E0A\u4F20",selectAll:"\u9009\u62E9\u5168\u90E8",deselect:"\u53D6\u6D88\u9009\u62E9",removeSelected:"\u5220\u9664\u9009\u4E2D\u9879\u76EE",externalInputPlaceHolder:"\u8D44\u6E90\u540D\u79F0|\u8D44\u6E90\u5730\u5740",externalInputTip:"\u4F7F\u7528\u201C|\u201D\u5206\u9694\u8D44\u6E90\u540D\u79F0\u548C\u8D44\u6E90\u5730\u5740",addLocalFile:"\u6DFB\u52A0\u672C\u5730\u8D44\u6E90",addExternalSource:"\u6DFB\u52A0\u7F51\u7EDC\u8D44\u6E90",unnamedItem:"\u672A\u547D\u540D\u9879\u76EE",confirmInsert:"\u63D2\u5165\u9009\u4E2D\u9879\u76EE"}},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0}),v.default={remove:"Remove",cancel:"Cancel",confirm:"Confirm",insert:"Insert Selected Items",width:"Width",height:"Height",image:"Image",video:"Video",audio:"Audio",embed:"Embed",caption:"Media Library",dragTip:"Click Or Drag Files Here",dropTip:"Drop To Upload",selectAll:"Select All",deselect:"Deselect",removeSelected:"Remove Selected Items",externalInputPlaceHolder:"Source Name|Source URL",externalInputTip:'Split source name and source URL with "|", confirm by hit Enter.',addLocalFile:"Add from local",addExternalSource:"Add from Internet",unnamedItem:"Unnamed Item",confirmInsert:"Insert selected items"}},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0});var p=A(9),I=l(p),E=A(8),d=l(E),f=A(7),g=l(f),s=A(6),a=l(s),c=A(5),o=l(c),r=A(4),u=l(r),S=A(3),C=l(S);function l(h){return h&&h.__esModule?h:{default:h}}v.default={en:I.default,zh:d.default,"zh-hant":g.default,pl:a.default,kr:o.default,jpn:u.default,tr:C.default}},function(b,v){b.exports=function(A){var p=typeof window!="undefined"&&window.location;if(!p)throw new Error("fixUrls requires window.location");if(!A||typeof A!="string")return A;var I=p.protocol+"//"+p.host,E=I+p.pathname.replace(/\/[^\/]*$/,"/"),d=A.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(f,g){var s=g.trim().replace(/^"(.*)"$/,function(c,o){return o}).replace(/^'(.*)'$/,function(c,o){return o});if(/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(s))return f;var a;return s.indexOf("//")===0?a=s:s.indexOf("/")===0?a=I+s:a=E+s.replace(/^\.\//,""),"url("+JSON.stringify(a)+")"});return d}},function(b,v,A){var p={},I=function(B){var w;return function(){return typeof w=="undefined"&&(w=B.apply(this,arguments)),w}},E=I(function(){return window&&document&&document.all&&!window.atob}),d=function(B){return document.querySelector(B)},f=function(B){var w={};return function(z){if(typeof z=="function")return z();if(typeof w[z]=="undefined"){var j=d.call(this,z);if(window.HTMLIFrameElement&&j instanceof window.HTMLIFrameElement)try{j=j.contentDocument.head}catch(U){j=null}w[z]=j}return w[z]}}(),g=null,s=0,a=[],c=A(11);b.exports=function(B,w){if(typeof DEBUG!="undefined"&&DEBUG&&typeof document!="object")throw new Error("The style-loader cannot be used in a non-browser environment");w=w||{},w.attrs=typeof w.attrs=="object"?w.attrs:{},!w.singleton&&typeof w.singleton!="boolean"&&(w.singleton=E()),w.insertInto||(w.insertInto="head"),w.insertAt||(w.insertAt="bottom");var z=r(B,w);return o(z,w),function(U){for(var O=[],ie=0;ie<z.length;ie++){var Q=z[ie],ue=p[Q.id];ue.refs--,O.push(ue)}if(U){var Ee=r(U,w);o(Ee,w)}for(var ie=0;ie<O.length;ie++){var ue=O[ie];if(ue.refs===0){for(var me=0;me<ue.parts.length;me++)ue.parts[me]();delete p[ue.id]}}}};function o(B,w){for(var z=0;z<B.length;z++){var j=B[z],U=p[j.id];if(U){U.refs++;for(var O=0;O<U.parts.length;O++)U.parts[O](j.parts[O]);for(;O<j.parts.length;O++)U.parts.push(D(j.parts[O],w))}else{for(var ie=[],O=0;O<j.parts.length;O++)ie.push(D(j.parts[O],w));p[j.id]={id:j.id,refs:1,parts:ie}}}}function r(B,w){for(var z=[],j={},U=0;U<B.length;U++){var O=B[U],ie=w.base?O[0]+w.base:O[0],Q=O[1],ue=O[2],Ee=O[3],me={css:Q,media:ue,sourceMap:Ee};j[ie]?j[ie].parts.push(me):z.push(j[ie]={id:ie,parts:[me]})}return z}function u(B,w){var z=f(B.insertInto);if(!z)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var j=a[a.length-1];if(B.insertAt==="top")j?j.nextSibling?z.insertBefore(w,j.nextSibling):z.appendChild(w):z.insertBefore(w,z.firstChild),a.push(w);else if(B.insertAt==="bottom")z.appendChild(w);else if(typeof B.insertAt=="object"&&B.insertAt.before){var U=f(B.insertInto+" "+B.insertAt.before);z.insertBefore(w,U)}else throw new Error(`[Style Loader]

 Invalid value for parameter 'insertAt' ('options.insertAt') found.
 Must be 'top', 'bottom', or Object.
 (https://github.com/webpack-contrib/style-loader#insertat)
`)}function S(B){if(B.parentNode===null)return!1;B.parentNode.removeChild(B);var w=a.indexOf(B);w>=0&&a.splice(w,1)}function C(B){var w=document.createElement("style");return B.attrs.type===void 0&&(B.attrs.type="text/css"),h(w,B.attrs),u(B,w),w}function l(B){var w=document.createElement("link");return B.attrs.type===void 0&&(B.attrs.type="text/css"),B.attrs.rel="stylesheet",h(w,B.attrs),u(B,w),w}function h(B,w){Object.keys(w).forEach(function(z){B.setAttribute(z,w[z])})}function D(B,w){var z,j,U,O;if(w.transform&&B.css)if(O=w.transform(B.css),O)B.css=O;else return function(){};if(w.singleton){var ie=s++;z=g||(g=C(w)),j=y.bind(null,z,ie,!1),U=y.bind(null,z,ie,!0)}else B.sourceMap&&typeof URL=="function"&&typeof URL.createObjectURL=="function"&&typeof URL.revokeObjectURL=="function"&&typeof Blob=="function"&&typeof btoa=="function"?(z=l(w),j=T.bind(null,z,w),U=function(){S(z),z.href&&URL.revokeObjectURL(z.href)}):(z=C(w),j=L.bind(null,z),U=function(){S(z)});return j(B),function(ue){if(ue){if(ue.css===B.css&&ue.media===B.media&&ue.sourceMap===B.sourceMap)return;j(B=ue)}else U()}}var k=function(){var B=[];return function(w,z){return B[w]=z,B.filter(Boolean).join(`
`)}}();function y(B,w,z,j){var U=z?"":j.css;if(B.styleSheet)B.styleSheet.cssText=k(w,U);else{var O=document.createTextNode(U),ie=B.childNodes;ie[w]&&B.removeChild(ie[w]),ie.length?B.insertBefore(O,ie[w]):B.appendChild(O)}}function L(B,w){var z=w.css,j=w.media;if(j&&B.setAttribute("media",j),B.styleSheet)B.styleSheet.cssText=z;else{for(;B.firstChild;)B.removeChild(B.firstChild);B.appendChild(document.createTextNode(z))}}function T(B,w,z){var j=z.css,U=z.sourceMap,O=w.convertToAbsoluteUrls===void 0&&U;(w.convertToAbsoluteUrls||O)&&(j=c(j)),U&&(j+=`
/*# sourceMappingURL=data:application/json;base64,`+btoa(unescape(encodeURIComponent(JSON.stringify(U))))+" */");var ie=new Blob([j],{type:"text/css"}),Q=B.href;B.href=URL.createObjectURL(ie),Q&&URL.revokeObjectURL(Q)}},function(b,v){b.exports="data:font/ttf;base64,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"},function(b,v){b.exports=function(I){var E=[];return E.toString=function(){return this.map(function(f){var g=A(f,I);return f[2]?"@media "+f[2]+"{"+g+"}":g}).join("")},E.i=function(d,f){typeof d=="string"&&(d=[[null,d,""]]);for(var g={},s=0;s<this.length;s++){var a=this[s][0];typeof a=="number"&&(g[a]=!0)}for(s=0;s<d.length;s++){var c=d[s];(typeof c[0]!="number"||!g[c[0]])&&(f&&!c[2]?c[2]=f:f&&(c[2]="("+c[2]+") and ("+f+")"),E.push(c))}},E};function A(I,E){var d=I[1]||"",f=I[3];if(!f)return d;if(E&&typeof btoa=="function"){var g=p(f),s=f.sources.map(function(a){return"/*# sourceURL="+f.sourceRoot+a+" */"});return[d].concat(s).concat([g]).join(`
`)}return[d].join(`
`)}function p(I){var E=btoa(unescape(encodeURIComponent(JSON.stringify(I)))),d="sourceMappingURL=data:application/json;charset=utf-8;base64,"+E;return"/*# "+d+" */"}},function(b,v){b.exports=function(p){return typeof p!="string"?p:(/^['"].*['"]$/.test(p)&&(p=p.slice(1,-1)),/["'() \t\n]/.test(p)?'"'+p.replace(/"/g,'\\"').replace(/\n/g,"\\n")+'"':p)}},function(b,v,A){var p=A(15);v=b.exports=A(14)(!1),v.push([b.i,`@font-face {
  font-family: 'bf-icons';
  src: url(`+p(A(13))+`) format("truetype");
  font-weight: normal;
  font-style: normal; }

.braft-finder [class^="braft-icon-"], .braft-finder [class*=" braft-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'bf-icons' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.braft-finder .braft-icon-code:before {
  content: "\\E903"; }

.braft-finder .braft-icon-pause:before {
  content: "\\E034"; }

.braft-finder .braft-icon-play_arrow:before {
  content: "\\E037"; }

.braft-finder .braft-icon-bin:before {
  content: "\\E9AC"; }

.braft-finder .braft-icon-replay:before {
  content: "\\E042"; }

.braft-finder .braft-icon-close:before {
  content: "\\E913"; }

.braft-finder .braft-icon-music:before {
  content: "\\E90E"; }

.braft-finder .braft-icon-camera:before {
  content: "\\E911"; }

.braft-finder .braft-icon-file-text:before {
  content: "\\E926"; }

.braft-finder .braft-icon-film:before {
  content: "\\E91C"; }

.braft-finder .braft-icon-paste:before {
  content: "\\E92D"; }

.braft-finder .braft-icon-spinner:before {
  content: "\\E980"; }

.braft-finder .braft-icon-media:before {
  content: "\\E90F"; }

.braft-finder .braft-icon-add:before {
  content: "\\E918"; }

.braft-finder .braft-icon-done:before {
  content: "\\E912"; }

.braft-finder .braft-icon-drop-down:before {
  content: "\\E906"; }

.braft-finder .braft-icon-drop-up:before {
  content: "\\E909"; }

.braft-finder .braft-icon-help:before {
  content: "\\E902"; }

.braft-finder .braft-icon-info:before {
  content: "\\E901"; }

.braft-finder .braft-icon-menu:before {
  content: "\\E908"; }

.pull-left {
  float: left; }

.pull-right {
  float: right; }

.braft-finder .bf-uploader {
  position: relative;
  height: 370px;
  margin: 0; }
  .braft-finder .bf-uploader.draging .bf-list-wrap,
  .braft-finder .bf-uploader.draging .bf-add-external {
    pointer-events: none; }
  .braft-finder .bf-uploader input::-webkit-input-placeholder {
    color: #ccc; }
  .braft-finder .bf-uploader input::-moz-placeholder {
    color: #ccc; }
  .braft-finder .bf-uploader input::-ms-input-placeholder {
    color: #ccc; }

.braft-finder .bf-list-wrap {
  position: relative;
  height: 370px; }

.braft-finder .bf-list-tools {
  z-index: 1;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 20px;
  padding: 0 15px;
  background-color: #fff; }
  .braft-finder .bf-list-tools span {
    height: 26px;
    font-size: 12px;
    line-height: 20px;
    cursor: pointer;
    user-select: none; }
    .braft-finder .bf-list-tools span[disabled] {
      opacity: .3;
      pointer-events: none; }
  .braft-finder .bf-list-tools .bf-select-all,
  .braft-finder .bf-list-tools .bf-deselect-all {
    float: left;
    margin-right: 5px;
    color: #bbb; }
    .braft-finder .bf-list-tools .bf-select-all:hover,
    .braft-finder .bf-list-tools .bf-deselect-all:hover {
      color: #3498db; }
  .braft-finder .bf-list-tools .bf-remove-selected {
    float: right;
    color: #e74c3c; }
    .braft-finder .bf-list-tools .bf-remove-selected:hover {
      color: #c92e1e; }

.braft-finder .bf-list {
  position: absolute;
  z-index: 1;
  top: 30px;
  right: 0;
  left: 0;
  bottom: 0;
  margin: 0;
  padding: 0 10px;
  list-style: none;
  overflow: auto; }
  .braft-finder .bf-list::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    background-color: #fff; }
  .braft-finder .bf-list::-webkit-scrollbar-track {
    background-color: #fff; }
  .braft-finder .bf-list::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1); }

.braft-finder .bf-item,
.braft-finder .bf-add-item {
  position: relative;
  display: block;
  float: left;
  width: 113px;
  height: 113px;
  margin: 5px;
  overflow: hidden;
  border-radius: 3px; }

.braft-finder .bf-item.uploading {
  pointer-events: none; }

.braft-finder .bf-item.error::before {
  display: block;
  content: "\\E901"; }

.braft-finder .bf-item.error::after {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(231, 76, 60, 0.8);
  content: ''; }

.braft-finder .bf-item.error:hover::after {
  background-color: rgba(231, 76, 60, 0.9); }

.braft-finder .bf-item.error .bf-item-uploading {
  display: none; }

.braft-finder .bf-add-item {
  background-color: #ecedef;
  color: #999; }
  .braft-finder .bf-add-item:hover {
    background-color: #e1e2e3; }
  .braft-finder .bf-add-item i {
    display: block;
    width: 113px;
    height: 113px;
    font-size: 48px;
    line-height: 113px;
    text-align: center; }
  .braft-finder .bf-add-item input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer; }

.braft-finder .bf-item::before {
  display: none;
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  width: 113px;
  height: 113px;
  color: #fff;
  font-size: 48px;
  font-family: 'bf-icons';
  line-height: 113px;
  text-align: center; }

.braft-finder .bf-item::after {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(52, 152, 219, 0);
  content: ''; }

.braft-finder .bf-item:hover::after {
  background-color: rgba(52, 152, 219, 0.3); }

.braft-finder .bf-item:hover .bf-item-remove {
  display: block; }

.braft-finder .bf-item.active::before {
  display: block;
  content: "\\E912"; }

.braft-finder .bf-item.active::after {
  background-color: rgba(52, 152, 219, 0.6); }

.braft-finder .bf-item.active:hover::after {
  background-color: rgba(52, 152, 219, 0.8); }

.braft-finder .bf-item.active:hover .bf-item-remove {
  display: none; }

.braft-finder .bf-item-uploading {
  box-sizing: border-box;
  position: absolute;
  z-index: 3;
  top: 52px;
  left: 10px;
  width: 93px;
  height: 10px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  box-shadow: 0 0 0 100px rgba(0, 0, 0, 0.5); }

.braft-finder .bf-item-uploading-bar {
  height: 10px;
  background-color: #3498db;
  border-radius: 0; }

.braft-finder .bf-item-remove {
  display: none;
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  width: 28px;
  height: 28px;
  color: #fff;
  font-size: 18px;
  line-height: 28px;
  text-align: center;
  cursor: pointer; }
  .braft-finder .bf-item-remove:hover {
    color: #e74c3c; }

.braft-finder .bf-item-title {
  display: none;
  box-sizing: border-box;
  position: absolute;
  z-index: 2;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  padding: 0 5px;
  overflow: hidden;
  background-image: linear-gradient(rgba(0, 0, 0, 0), black);
  color: #fff;
  font-size: 12px;
  line-height: 55px;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap; }

.braft-finder .bf-image {
  width: 100%;
  height: 100%;
  background-color: #eee;
  user-select: none; }
  .braft-finder .bf-image img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover; }

.braft-finder .bf-video {
  background-color: #8e44ad; }

.braft-finder .bf-audio {
  background-color: #f39c12; }

.braft-finder .bf-embed {
  background-color: #f1c40f; }

.braft-finder .bf-icon {
  display: block;
  width: 113px;
  height: 113px;
  overflow: hidden;
  color: #fff;
  text-align: center;
  text-decoration: none; }
  .braft-finder .bf-icon i, .braft-finder .bf-icon span {
    display: block; }
  .braft-finder .bf-icon i {
    margin-top: 35px;
    font-size: 24px; }
  .braft-finder .bf-icon span {
    width: 103px;
    margin: 10px auto;
    overflow: hidden;
    font-size: 12px;
    text-overflow: ellipsis;
    white-space: nowrap; }

.braft-finder .bf-drag-uploader {
  box-sizing: border-box;
  position: absolute;
  z-index: 2;
  top: 0;
  right: 15px;
  left: 15px;
  height: 100%;
  background-color: #fff;
  border: dashed 1px #bbb;
  text-align: center;
  opacity: 0;
  pointer-events: none; }
  .braft-finder .bf-drag-uploader:hover, .braft-finder .bf-drag-uploader.draging {
    background-color: #f1f2f3; }
  .braft-finder .bf-drag-uploader.active {
    opacity: 1;
    pointer-events: auto; }

.braft-finder .bf-uploader-buttons {
  height: 370px;
  margin: auto;
  text-align: center; }

.braft-finder .bf-drag-tip {
  display: inline-block;
  margin-top: 150px;
  color: #ccc;
  text-align: center;
  font-size: 28px;
  font-weight: normal;
  line-height: 40px; }
  .braft-finder .bf-drag-tip input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    color: #fff;
    text-indent: -100px;
    cursor: pointer; }

.braft-finder .bf-manager-footer {
  height: 36px;
  margin: 10px 0;
  padding: 0 15px; }
  .braft-finder .bf-manager-footer .button {
    float: right;
    height: 36px;
    margin-left: 5px;
    padding: 0 35px;
    font-size: 12px;
    font-weight: 700;
    border: none;
    border-radius: 3px;
    cursor: pointer; }
  .braft-finder .bf-manager-footer .button-insert {
    color: #fff;
    background-color: #3498db; }
    .braft-finder .bf-manager-footer .button-insert:hover {
      background-color: #2084c7; }
    .braft-finder .bf-manager-footer .button-insert[disabled] {
      opacity: .3;
      pointer-events: none;
      filter: grayscale(0.4); }
  .braft-finder .bf-manager-footer .button-cancel {
    color: #999;
    background-color: #e8e8e9; }
    .braft-finder .bf-manager-footer .button-cancel:hover {
      background-color: #d8d8d9; }

.braft-finder .bf-toggle-external-form {
  color: #666;
  font-size: 12px;
  line-height: 36px; }
  .braft-finder .bf-toggle-external-form span {
    color: #bbb;
    line-height: 16px;
    cursor: pointer;
    user-select: none; }
    .braft-finder .bf-toggle-external-form span:hover {
      color: #3498db; }
    .braft-finder .bf-toggle-external-form span i {
      position: relative;
      top: 2px;
      font-size: 16px; }

.braft-finder .bf-add-external {
  position: absolute;
  z-index: 3;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #fff; }
  .braft-finder .bf-add-external input {
    border: solid 1px rgba(0, 0, 0, 0.3);
    border: solid 0.5px rgba(0, 0, 0, 0.3);
    box-shadow: none; }
    .braft-finder .bf-add-external input:focus {
      border-color: #3498db;
      box-shadow: none; }

.braft-finder .bf-external-form {
  width: 500px;
  max-width: 90%;
  margin: 91px auto 0 auto; }

.braft-finder .bf-external-input {
  position: relative;
  width: 100%;
  height: 40px;
  margin-bottom: 10px; }
  .braft-finder .bf-external-input div {
    position: absolute;
    top: 0;
    right: 85px;
    left: 0;
    height: 40px; }
  .braft-finder .bf-external-input input,
  .braft-finder .bf-external-input textarea {
    display: block;
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    padding: 0 10px;
    border: none;
    border-radius: 3px;
    outline: none;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.3);
    color: #999;
    font-size: 18px; }
    .braft-finder .bf-external-input input:focus,
    .braft-finder .bf-external-input textarea:focus {
      box-shadow: inset 0 0 0 1px #3498db; }
  .braft-finder .bf-external-input textarea {
    height: 100px;
    font-size: 14px; }
  .braft-finder .bf-external-input button {
    position: absolute;
    top: 0;
    right: 0;
    width: 80px;
    height: 40px;
    background-color: #3498db;
    border: none;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer; }
    .braft-finder .bf-external-input button:disabled {
      opacity: .3;
      pointer-events: none;
      filter: grayscale(0.4); }
    .braft-finder .bf-external-input button:hover {
      background-color: #2084c7; }

.braft-finder .bf-switch-external-type {
  overflow: hidden;
  text-align: center; }
  .braft-finder .bf-switch-external-type button {
    width: auto;
    height: 30px;
    margin: 10px 5px;
    padding: 0 10px;
    background-color: #e8e9ea;
    border: none;
    border-radius: 3px;
    color: #999;
    font-size: 12px;
    cursor: pointer; }
    .braft-finder .bf-switch-external-type button:hover {
      background-color: #d8d9da; }
    .braft-finder .bf-switch-external-type button:only-child {
      display: none; }
  .braft-finder .bf-switch-external-type[data-type="IMAGE"] [data-type="IMAGE"],
  .braft-finder .bf-switch-external-type[data-type="VIDEO"] [data-type="VIDEO"],
  .braft-finder .bf-switch-external-type[data-type="AUDIO"] [data-type="AUDIO"],
  .braft-finder .bf-switch-external-type[data-type="EMBED"] [data-type="EMBED"],
  .braft-finder .bf-switch-external-type[data-type="FILE"] [data-type="FILE"] {
    background-color: #3498db;
    color: #fff; }

.braft-finder .bf-external-tip {
  display: block;
  margin-top: 15px;
  color: #ccc;
  font-size: 12px;
  text-align: center; }
`,""])},function(b,v,A){var p=A(16);typeof p=="string"&&(p=[[b.i,p,""]]);var I,E,d={hmr:!0};d.transform=I,d.insertInto=void 0;var f=A(12)(p,d);p.locals&&(b.exports=p.locals)},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0});var p=Object.assign||function(u){for(var S=1;S<arguments.length;S++){var C=arguments[S];for(var l in C)Object.prototype.hasOwnProperty.call(C,l)&&(u[l]=C[l])}return u},I=function(){function u(S,C){for(var l=0;l<C.length;l++){var h=C[l];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(S,h.key,h)}}return function(S,C,l){return C&&u(S.prototype,C),l&&u(S,l),S}}();A(17);var E=A(2),d=g(E),f=A(1);function g(u){return u&&u.__esModule?u:{default:u}}function s(u,S){if(!(u instanceof S))throw new TypeError("Cannot call a class as a function")}function a(u,S){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:u}function c(u,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);u.prototype=Object.create(S&&S.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(u,S):u.__proto__=S)}var o={image:"image/png,image/jpeg,image/gif,image/webp,image/apng,image/svg",video:"video/mp4",audio:"audio/mp3"},r=function(u){c(S,u);function S(C){s(this,S);var l=a(this,(S.__proto__||Object.getPrototypeOf(S)).call(this,C));l.toggleSelectItem=function(D){var k=D.currentTarget.dataset.id,y=l.controller.getMediaItem(k);if(!y)return!1;y.selected?(!l.props.onBeforeDeselect||l.props.onBeforeDeselect([y],l.controller.getItems())!==!1)&&(l.controller.deselectMediaItem(k),l.props.onDeselect&&l.props.onDeselect([y],l.controller.getItems())):(!l.props.onBeforeSelect||l.props.onBeforeSelect([y],l.controller.getItems())!==!1)&&(l.controller.selectMediaItem(k),l.props.onSelect&&l.props.onSelect([y],l.controller.getItems()))},l.removeItem=function(D){var k=D.currentTarget.dataset.id,y=l.controller.getMediaItem(k);if(!y)return!1;(!l.props.onBeforeRemove||l.props.onBeforeRemove([y],l.controller.getItems())!==!1)&&(l.controller.removeMediaItem(k),l.props.onRemove&&l.props.onRemove([y],l.controller.getItems())),D.stopPropagation()},l.selectAllItems=function(){var D=l.controller.getItems();(!l.props.onBeforeSelect||l.props.onBeforeSelect(D,D)!==!1)&&(l.controller.selectAllItems(),l.props.onSelect&&l.props.onSelect(D,D))},l.deselectAllItems=function(){var D=l.controller.getItems();(!l.props.onBeforeDeselect||l.props.onBeforeDeselect(D,D)!==!1)&&(l.controller.deselectAllItems(),l.props.onDeselect&&l.props.onDeselect(D,D))},l.removeSelectedItems=function(){var D=l.controller.getSelectedItems();(!l.props.onBeforeRemove||l.props.onBeforeRemove(D,l.controller.getItems())!==!1)&&(l.controller.removeSelectedItems(),l.props.onRemove&&l.props.onRemove(D,l.controller.getItems()))},l.handleDragLeave=function(D){D.preventDefault(),l.dragCounter--,l.dragCounter===0&&l.setState({draging:!1})},l.handleDragDrop=function(D){D.preventDefault(),l.dragCounter=0,l.setState({draging:!1}),l.reslovePickedFiles(D)},l.handleDragEnter=function(D){D.preventDefault(),l.dragCounter++,l.setState({draging:!0})},l.reslovePickedFiles=function(D){D.persist();var k=D.type==="drop"?D.dataTransfer:D.target,y=k.files;if(l.props.onFileSelect){var L=l.props.onFileSelect(y);if(L===!1)return!1;(L instanceof FileList||L instanceof Array)&&(y=L)}var T=p({},o,l.props.accepts);l.controller.resolveFiles({files:y,onItemReady:function(w){var z=w.id;return l.controller.selectMediaItem(z)},onAllReady:function(){return D.target.value=null}},0,T)},l.inputExternal=function(D){l.setState({external:p({},l.state.external,{url:D.target.value})})},l.switchExternalType=function(D){l.setState({external:p({},l.state.external,{type:D.target.dataset.type})})},l.confirmAddExternal=function(D){if(D.target.nodeName.toLowerCase()==="button"||D.keyCode===13){var k=l.state.external,y=k.url,L=k.type;y=y.split("|");var T=y.length>1?y[0]:l.props.language.unnamedItem;y=y.length>1?y[1]:y[0];var B=L==="IMAGE"?y:null;l.controller.addItems([{thumbnail:B,url:y,name:T,type:L,id:new Date().getTime()+"_"+(0,f.UniqueIndex)(),uploading:!1,uploadProgress:1,selected:!0}]),l.setState({showExternalForm:!1,external:{url:"",type:"IMAGE"}})}},l.toggleExternalForm=function(){l.setState({showExternalForm:!l.state.showExternalForm})},l.cancelInsert=function(){l.props.onCancel&&l.props.onCancel()},l.confirmInsert=function(){var D=l.controller.getSelectedItems();if(l.props.onBeforeInsert){var k=l.props.onBeforeInsert(D);k&&k instanceof Array?(l.controller.deselectAllItems(),l.props.onInsert&&l.props.onInsert(k)):k!==!1&&(l.controller.deselectAllItems(),l.props.onInsert&&l.props.onInsert(D))}else l.controller.deselectAllItems(),l.props.onInsert&&l.props.onInsert(D)},l.dragCounter=0,l.controller=l.props.controller;var h=l.controller.getItems();return l.state={draging:!1,error:!1,confirmable:h.find(function(D){var k=D.selected;return k}),external:{url:"",type:"IMAGE"},fileAccept:"",showExternalForm:!1,allowExternal:!1,items:h},l.changeListenerId=l.controller.onChange(function(D){l.setState({items:D,confirmable:D.find(function(k){var y=k.selected;return y})}),l.props.onChange&&l.props.onChange(D)}),l}return I(S,[{key:"mapPropsToState",value:function(l){var h=l.accepts,D=l.externals;h=p({},o,h);var k=h?[h.image,h.video,h.audio].filter(function(L){return L}).join(","):[o.image,o.video,o.audio].join(","),y={url:"",type:D.image?"IMAGE":D.audio?"AUDIO":D.video?"VIDEO":D.embed?"EMBED":""};return{fileAccept:k,external:y,allowExternal:D&&(D.image||D.audio||D.video||D.embed)}}},{key:"componentDidMount",value:function(){this.setState(this.mapPropsToState(this.props))}},{key:"componentWillReceiveProps",value:function(l){this.setState(this.mapPropsToState(l))}},{key:"componentWillUnmount",value:function(){this.controller.offChange(this.changeListenerId)}},{key:"render",value:function(){var l=this.props,h=l.language,D=l.externals,k=this.state,y=k.items,L=k.draging,T=k.confirmable,B=k.fileAccept,w=k.external,z=k.showExternalForm,j=k.allowExternal;return d.default.createElement("div",{className:"braft-finder"},d.default.createElement("div",{onDragEnter:this.handleDragEnter,onDragOver:this.handleDragEnter,onDragLeave:this.handleDragLeave,onDrop:this.handleDragDrop,className:"bf-uploader"},d.default.createElement("div",{className:"bf-drag-uploader "+(L||!y.length?"active ":" ")+(L?"draging":"")},d.default.createElement("span",{className:"bf-drag-tip"},d.default.createElement("input",{accept:B,onChange:this.reslovePickedFiles,multiple:!0,type:"file"}),L?h.dropTip:h.dragTip)),y.length?d.default.createElement("div",{className:"bf-list-wrap"},d.default.createElement("div",{className:"bf-list-tools"},d.default.createElement("span",{onClick:this.selectAllItems,className:"bf-select-all"},d.default.createElement("i",{className:"braft-icon-done"})," ",h.selectAll),d.default.createElement("span",{onClick:this.deselectAllItems,disabled:!T,className:"bf-deselect-all"},d.default.createElement("i",{className:"braft-icon-close"})," ",h.deselect),d.default.createElement("span",{onClick:this.removeSelectedItems,disabled:!T,className:"bf-remove-selected"},d.default.createElement("i",{className:"braft-icon-bin"})," ",h.removeSelected)),this.buildItemList()):null,z&&j?d.default.createElement("div",{className:"bf-add-external"},d.default.createElement("div",{className:"bf-external-form"},d.default.createElement("div",{className:"bf-external-input"},d.default.createElement("div",null,d.default.createElement("input",{onKeyDown:this.confirmAddExternal,value:w.url,onChange:this.inputExternal,placeholder:h.externalInputPlaceHolder})),d.default.createElement("button",{type:"button",onClick:this.confirmAddExternal,disabled:!w.url.trim().length},h.confirm)),d.default.createElement("div",{"data-type":w.type,className:"bf-switch-external-type"},D.image?d.default.createElement("button",{type:"button",onClick:this.switchExternalType,"data-type":"IMAGE"},h.image):null,D.audio?d.default.createElement("button",{type:"button",onClick:this.switchExternalType,"data-type":"AUDIO"},h.audio):null,D.video?d.default.createElement("button",{type:"button",onClick:this.switchExternalType,"data-type":"VIDEO"},h.video):null,D.embed?d.default.createElement("button",{type:"button",onClick:this.switchExternalType,"data-type":"EMBED"},h.embed):null),d.default.createElement("span",{className:"bf-external-tip"},h.externalInputTip))):null),d.default.createElement("footer",{className:"bf-manager-footer"},d.default.createElement("div",{className:"pull-left"},j?d.default.createElement("span",{onClick:this.toggleExternalForm,className:"bf-toggle-external-form"},z?d.default.createElement("span",{className:"bf-bottom-text"},d.default.createElement("i",{className:"braft-icon-add"})," ",h.addLocalFile):d.default.createElement("span",{className:"bf-bottom-text"},d.default.createElement("i",{className:"braft-icon-add"})," ",h.addExternalSource)):null),d.default.createElement("div",{className:"pull-right"},d.default.createElement("button",{onClick:this.confirmInsert,className:"button button-insert",disabled:!T},h.insert),d.default.createElement("button",{onClick:this.cancelInsert,className:"button button-cancel"},h.cancel))))}},{key:"buildItemList",value:function(){var l=this;return d.default.createElement("ul",{className:"bf-list"},d.default.createElement("li",{className:"bf-add-item"},d.default.createElement("i",{className:"braft-icon-add"}),d.default.createElement("input",{accept:this.state.fileAccept,onChange:this.reslovePickedFiles,multiple:!0,type:"file"})),this.state.items.map(function(h,D){var k=null,y=h.uploading&&!l.props.hideProgress?d.default.createElement("div",{className:"bf-item-uploading"},d.default.createElement("div",{className:"bf-item-uploading-bar",style:{width:h.uploadProgress/1+"%"}})):"";switch(h.type){case"IMAGE":k=d.default.createElement("div",{className:"bf-image"},y,d.default.createElement("img",{src:h.thumbnail||h.url}));break;case"VIDEO":k=d.default.createElement("div",{className:"bf-icon bf-video",title:h.url},y,d.default.createElement("i",{className:"braft-icon-film"}),d.default.createElement("span",null,h.name||h.url));break;case"AUDIO":k=d.default.createElement("div",{className:"bf-icon bf-audio",title:h.url},y,d.default.createElement("i",{className:"braft-icon-music"}),d.default.createElement("span",null,h.name||h.url));break;case"EMBED":k=d.default.createElement("div",{className:"bf-icon bf-embed",title:h.url},y,d.default.createElement("i",{className:"braft-icon-code"}),d.default.createElement("span",null,h.name||l.props.language.embed));break;default:k=d.default.createElement("a",{className:"bf-icon bf-file",title:h.url,href:h.url},y,d.default.createElement("i",{className:"braft-icon-file-text"}),d.default.createElement("span",null,h.name||h.url));break}var L=["bf-item"];return h.selected&&L.push("active"),h.uploading&&L.push("uploading"),h.error&&L.push("error"),d.default.createElement("li",{key:D,title:h.name,"data-id":h.id,className:L.join(" "),onClick:l.toggleSelectItem},k,d.default.createElement("span",{"data-id":h.id,onClick:l.removeItem,className:"bf-item-remove braft-icon-close"}),d.default.createElement("span",{className:"bf-item-title"},h.name))}))}}]),S}(d.default.Component);r.defaultProps={accepts:o,externals:{image:!0,video:!0,audio:!0,embed:!0}},v.default=r},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0});var p=Object.assign||function(c){for(var o=1;o<arguments.length;o++){var r=arguments[o];for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&(c[u]=r[u])}return c},I=A(1),E=A(0);function d(c){if(Array.isArray(c)){for(var o=0,r=Array(c.length);o<c.length;o++)r[o]=c[o];return r}else return Array.from(c)}function f(c,o){if(!(c instanceof o))throw new TypeError("Cannot call a class as a function")}var g=function(){return!0},s=function c(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};f(this,c),a.call(this),this.items=o.items||[],this.uploadFn=o.uploader,this.validateFn=o.validator||g,this.changeListeners=[]},a=function(){var o=this;this.setProps=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};o.items=r.items||o.items||[],o.uploadFn=r.uploader,o.validateFn=r.validator||g},this.getMediaItem=function(r){return o.items.find(function(u){return u.id===r})},this.getSelectedItems=function(){return o.items.filter(function(r){return r.selected})},this.getItems=function(){return o.items},this.setItems=function(r){o.items=r.map(function(u){return p({},u,{id:u.id.toString()})})||[],o.applyChange(),o.uploadItems()},this.addMediaItem=function(r){o.addItems([r])},this.addItems=function(r){o.items=[].concat(d(o.items),d(r.map(function(u){return p({},u,{id:u.id.toString()})}))),o.applyChange(),o.uploadItems()},this.selectMediaItem=function(r){var u=o.getMediaItem(r);if(u&&(u.uploading||u.error))return!1;o.setMediaItemState(r,{selected:!0})},this.selectAllItems=function(){o.items=o.items.filter(function(r){return!r.error&&!r.uploading}).map(function(r){return p({},r,{selected:!0})}),o.applyChange()},this.deselectMediaItem=function(r){o.setMediaItemState(r,{selected:!1})},this.deselectAllItems=function(){o.items=o.items.map(function(r){return p({},r,{selected:!1})}),o.applyChange()},this.removeMediaItem=function(r){o.items=o.items.filter(function(u){return u.id!==r}),o.applyChange()},this.removeItems=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];o.items=o.items.filter(function(u){return!r.includes(u.id)}),o.applyChange()},this.removeSelectedItems=function(){o.items=o.items.filter(function(r){return!r.selected}),o.applyChange()},this.removeErrorItems=function(){o.items=o.items.filter(function(r){return!r.error}),o.applyChange()},this.removeAllItems=function(){o.items=[],o.applyChange()},this.setMediaItemState=function(r,u){o.items=o.items.map(function(S){return S.id===r?p({},S,u):S}),o.applyChange()},this.reuploadErrorItems=function(){o.uploadItems(!0)},this.uploadItems=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;o.items.forEach(function(u,S){if(u.uploading||u.url||!r&&u.error)return!1;if(u.type==="IMAGE")o.createThumbnail(u),o.uploadFn=o.uploadFn||o.createInlineImage;else if(!o.uploadFn)return o.setMediaItemState(u.id,{error:1}),!1;o.setMediaItemState(u.id,{uploading:!0,uploadProgress:0,error:0}),o.uploadFn({id:u.id,file:u.file,success:function(l){o.handleUploadSuccess(u.id,l)},progress:function(l){o.setMediaItemState(u.id,{uploading:!0,uploadProgress:l})},error:function(l){o.setMediaItemState(u.id,{uploading:!1,error:2})}})})},this.createThumbnail=function(r){var u=r.id,S=r.file;(0,E.compressImage)(URL.createObjectURL(S),226,226).then(function(C){o.setMediaItemState(u,{thumbnail:C.url})})},this.createInlineImage=function(r){(0,E.compressImage)(URL.createObjectURL(r.file),1280,800).then(function(u){r.success({url:u.url})}).catch(function(u){r.error(u)})},this.handleUploadSuccess=function(r,u){o.setMediaItemState(r,p({},u,{file:null,uploadProgress:1,uploading:!1,selected:!1}));var S=o.getMediaItem(u.id||r);S.onReady&&S.onReady(S)},this.applyChange=function(){o.changeListeners.forEach(function(r){var u=r.callback;return u(o.items)})},this.uploadImage=function(r,u){var S=new Date().getTime()+"_"+(0,I.UniqueIndex)();o.addMediaItem({type:"IMAGE",id:S,file:r,name:S,size:r.size,uploadProgress:0,uploading:!1,selected:!1,error:0,onReady:u})},this.uploadImageRecursively=function(r,u){var S=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;r[S]&&r[S].type.indexOf("image")>-1?o.uploadImage(r[S],function(C){u&&u(C),S<r.length-1&&o.uploadImageRecursively(r,u,S+1)}):S<r.length-1&&o.uploadImageRecursively(r,u,S+1)},this.addResolvedFiles=function(r,u,S){var C={id:new Date().getTime()+"_"+(0,I.UniqueIndex)(),file:r.files[u],name:r.files[u].name,size:r.files[u].size,uploadProgress:0,uploading:!1,selected:!1,error:0,onReady:function(h){r.onItemReady&&r.onItemReady(h)}};r.files[u].type.indexOf("image/")===0&&S.image?(C.type="IMAGE",o.addMediaItem(C)):r.files[u].type.indexOf("video/")===0&&S.video?(C.type="VIDEO",o.addMediaItem(C)):r.files[u].type.indexOf("audio/")===0&&S.audio&&(C.type="AUDIO",o.addMediaItem(C)),setTimeout(function(){o.resolveFiles(r,u+1,S)},60)},this.resolveFiles=function(r,u,S){if(u<r.files.length){var C=o.validateFn(r.files[u]);C instanceof Promise?C.then(function(){o.addResolvedFiles(r,u,S)}):C&&o.addResolvedFiles(r,u,S)}else r.onAllReady&&r.onAllReady()},this.onChange=function(r){var u=(0,I.UniqueIndex)();return o.changeListeners.push({id:u,callback:r}),u},this.offChange=function(r){o.changeListeners=o.changeListeners.filter(function(u){var S=u.id;return S!==r})}};v.default=s},function(b,v,A){"use strict";Object.defineProperty(v,"__esModule",{value:!0}),v.ImageUtils=void 0;var p=Object.assign||function(y){for(var L=1;L<arguments.length;L++){var T=arguments[L];for(var B in T)Object.prototype.hasOwnProperty.call(T,B)&&(y[B]=T[B])}return y},I=A(2),E=S(I),d=A(19),f=S(d),g=A(18),s=S(g),a=A(10),c=S(a),o=A(0),r=u(o);function u(y){if(y&&y.__esModule)return y;var L={};if(y!=null)for(var T in y)Object.prototype.hasOwnProperty.call(y,T)&&(L[T]=y[T]);return L.default=y,L}function S(y){return y&&y.__esModule?y:{default:y}}function C(y,L){if(!(y instanceof L))throw new TypeError("Cannot call a class as a function")}function l(y,L){if(!y)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return L&&(typeof L=="object"||typeof L=="function")?L:y}function h(y,L){if(typeof L!="function"&&L!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof L);y.prototype=Object.create(L&&L.prototype,{constructor:{value:y,enumerable:!1,writable:!0,configurable:!0}}),L&&(Object.setPrototypeOf?Object.setPrototypeOf(y,L):y.__proto__=L)}var D=function(y){h(L,y);function L(T){C(this,L);var B=l(this,(L.__proto__||Object.getPrototypeOf(L)).call(this,T));return k.call(B),B.superProps=T,B}return L}(f.default),k=function(){var L=this;this.ReactComponent=function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=p({},L.superProps,T),w=(typeof B.language=="function"?B.language(c.default,"braft-finder"):c.default[B.language])||c.default.zh;return E.default.createElement(s.default,p({},B,{language:w,controller:L}))}};v.default=D,v.ImageUtils=r}])})},74652:function(H,P){"use strict";Object.defineProperty(P,"__esModule",{value:!0});var i=0,x=P.UniqueIndex=function(){return i+=1}},29884:function(H,P){"use strict";Object.defineProperty(P,"__esModule",{value:!0});var i={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4","indianred ":"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},x=function(E){if(E=E.replace("color:","").replace(";","").replace(" ",""),/^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/.test(E))return E;if(b[E])return b[E];if(E.indexOf("rgb")===0){var d=E.split(","),f=d.length<3?null:"#"+[d[0],d[1],d[2]].map(function(g){var s=parseInt(g.replace(/\D/g,""),10).toString(16);return s.length===1?"0"+s:s}).join("");return/^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/.test(f)?f:null}else return null},b=P.namedColors=i,v=P.getHexColor=x,A=P.detectColorsFromHTMLString=function(E){return typeof E!="string"?[]:(E.match(/color:[^;]{3,24};/g)||[]).map(v).filter(function(d){return d})},p=P.detectColorsFromDraftState=function(E){var d=[];return!E||!E.blocks||!E.blocks.length?d:(E.blocks.forEach(function(f){f&&f.inlineStyleRanges&&f.inlineStyleRanges.length&&f.inlineStyleRanges.forEach(function(g){g.style&&g.style.indexOf("COLOR-")>=0&&d.push("#"+g.style.split("COLOR-")[1])})}),d.filter(function(f){return f}))}},17517:function(H,P,i){"use strict";Object.defineProperty(P,"__esModule",{value:!0}),P.redo=P.undo=P.handleKeyCommand=P.clear=P.setMediaPosition=P.removeMedia=P.setMediaData=P.insertMedias=P.insertHorizontalLine=P.insertAtomicBlock=P.insertHTML=P.insertText=P.toggleSelectionLetterSpacing=P.toggleSelectionFontFamily=P.toggleSelectionLineHeight=P.toggleSelectionFontSize=P.toggleSelectionBackgroundColor=P.toggleSelectionColor=P.decreaseSelectionIndent=P.increaseSelectionIndent=P.toggleSelectionIndent=P.toggleSelectionAlignment=P.removeSelectionInlineStyles=P.toggleSelectionInlineStyle=P.selectionHasInlineStyle=P.getSelectionInlineStyle=P.toggleSelectionLink=P.toggleSelectionEntity=P.getSelectionEntityData=P.getSelectionEntityType=P.toggleSelectionBlockType=P.getSelectionText=P.getSelectionBlockType=P.getSelectionBlockData=P.setSelectionBlockData=P.getSelectedBlocks=P.updateEachCharacterOfSelection=P.getSelectionBlock=P.removeBlock=P.selectNextBlock=P.selectBlock=P.selectionContainsStrictBlock=P.selectionContainsBlockType=P.isSelectionCollapsed=P.createEditorState=P.createEmptyEditorState=P.isEditorState=P.registerStrictBlockType=void 0;var x=i(9041),b=i(5252),v=i(36444),A=i(43393),p=I(A);function I(De){return De&&De.__esModule?De:{default:De}}var E=["atomic"],d=P.registerStrictBlockType=function(ne){E.indexOf(ne)===-1&&E.push(ne)},f=P.isEditorState=function(ne){return ne instanceof x.EditorState},g=P.createEmptyEditorState=function(ne){return x.EditorState.createEmpty(ne)},s=P.createEditorState=function(ne,_){return x.EditorState.createWithContent(ne,_)},a=P.isSelectionCollapsed=function(ne){return ne.getSelection().isCollapsed()},c=P.selectionContainsBlockType=function(ne,_){return h(ne).find(function(le){return le.getType()===_})},o=P.selectionContainsStrictBlock=function(ne){return h(ne).find(function(_){return~E.indexOf(_.getType())})},r=P.selectBlock=function(ne,_){var le=_.getKey();return x.EditorState.forceSelection(ne,new x.SelectionState({anchorKey:le,anchorOffset:0,focusKey:le,focusOffset:_.getLength()}))},u=P.selectNextBlock=function(ne,_){var le=ne.getCurrentContent().getBlockAfter(_.getKey());return le?r(ne,le):ne},S=P.removeBlock=function(ne,_){var le=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,se=void 0,ge=void 0,ye=_.getKey();return se=x.Modifier.removeRange(ne.getCurrentContent(),new x.SelectionState({anchorKey:ye,anchorOffset:0,focusKey:ye,focusOffset:_.getLength()}),"backward"),se=x.Modifier.setBlockType(se,se.getSelectionAfter(),"unstyled"),ge=x.EditorState.push(ne,se,"remove-range"),x.EditorState.forceSelection(ge,le||se.getSelectionAfter())},C=P.getSelectionBlock=function(ne){return ne.getCurrentContent().getBlockForKey(ne.getSelection().getAnchorKey())},l=P.updateEachCharacterOfSelection=function(ne,_){var le=ne.getSelection(),se=ne.getCurrentContent(),ge=se.getBlockMap(),ye=h(ne);if(ye.length===0)return ne;var We=le.getStartKey(),Fe=le.getStartOffset(),Ve=le.getEndKey(),Ue=le.getEndOffset(),ct=ge.map(function(Ze){if(ye.indexOf(Ze)===-1)return Ze;var ut=Ze.getKey(),lt=Ze.getCharacterList(),gt=null;return ut===We&&ut===Ve?gt=lt.map(function(pt,ft){return ft>=Fe&&ft<Ue?_(pt):pt}):ut===We?gt=lt.map(function(pt,ft){return ft>=Fe?_(pt):pt}):ut===Ve?gt=lt.map(function(pt,ft){return ft<Ue?_(pt):pt}):gt=lt.map(function(pt){return _(pt)}),Ze.merge({characterList:gt})});return x.EditorState.push(ne,se.merge({blockMap:ct,selectionBefore:le,selectionAfter:le}),"update-selection-character-list")},h=P.getSelectedBlocks=function(ne){var _=ne.getSelection(),le=ne.getCurrentContent(),se=_.getStartKey(),ge=_.getEndKey(),ye=se===ge,We=le.getBlockForKey(se),Fe=[We];if(!ye)for(var Ve=se;Ve!==ge;){var Ue=le.getBlockAfter(Ve);Fe.push(Ue),Ve=Ue.getKey()}return Fe},D=P.setSelectionBlockData=function(ne,_,le){var se=le?_:Object.assign({},k(ne).toJS(),_);return Object.keys(se).forEach(function(ge){se.hasOwnProperty(ge)&&se[ge]===void 0&&delete se[ge]}),(0,b.setBlockData)(ne,se)},k=P.getSelectionBlockData=function(ne,_){var le=C(ne).getData();return _?le.get(_):le},y=P.getSelectionBlockType=function(ne){return C(ne).getType()},L=P.getSelectionText=function(ne){var _=ne.getSelection(),le=ne.getCurrentContent();if(_.isCollapsed()||y(ne)==="atomic")return"";var se=_.getAnchorKey(),ge=le.getBlockForKey(se),ye=_.getStartOffset(),We=_.getEndOffset();return ge.getText().slice(ye,We)},T=P.toggleSelectionBlockType=function(ne,_){return o(ne)?ne:x.RichUtils.toggleBlockType(ne,_)},B=P.getSelectionEntityType=function(ne){var _=(0,b.getSelectionEntity)(ne);if(_){var le=ne.getCurrentContent().getEntity(_);return le?le.get("type"):null}return null},w=P.getSelectionEntityData=function(ne,_){var le=(0,b.getSelectionEntity)(ne);if(le){var se=ne.getCurrentContent().getEntity(le);return se&&se.get("type")===_?se.getData():{}}else return{}},z=P.toggleSelectionEntity=function(ne,_){var le=ne.getCurrentContent(),se=ne.getSelection();if(se.isCollapsed()||y(ne)==="atomic")return ne;if(!_||!_.type||B(ne)===_.type)return x.EditorState.push(ne,x.Modifier.applyEntity(le,se,null),"apply-entity");try{var ge=le.createEntity(_.type,_.mutability,_.data),ye=ge.getLastCreatedEntityKey(),We=x.EditorState.set(ne,{currentContent:ge});return x.EditorState.push(We,x.Modifier.applyEntity(ge,se,ye),"apply-entity")}catch(Fe){return console.warn(Fe),ne}},j=P.toggleSelectionLink=function(ne,_,le){var se=ne.getCurrentContent(),ge=ne.getSelection(),ye={href:_,target:le};if(ge.isCollapsed()||y(ne)==="atomic")return ne;if(_===!1)return x.RichUtils.toggleLink(ne,ge,null);_===null&&delete ye.href;try{var We=se.createEntity("LINK","MUTABLE",ye),Fe=We.getLastCreatedEntityKey(),Ve=x.EditorState.set(ne,{currentContent:We});return Ve=x.RichUtils.toggleLink(Ve,ge,Fe),Ve=x.EditorState.forceSelection(Ve,ge.merge({anchorOffset:ge.getEndOffset(),focusOffset:ge.getEndOffset()})),Ve=x.EditorState.push(Ve,x.Modifier.insertText(Ve.getCurrentContent(),Ve.getSelection(),""),"insert-text"),Ve}catch(Ue){return console.warn(Ue),ne}},U=P.getSelectionInlineStyle=function(ne){return ne.getCurrentInlineStyle()},O=P.selectionHasInlineStyle=function(ne,_){return U(ne).has(_.toUpperCase())},ie=P.toggleSelectionInlineStyle=function(ne,_){var le=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"",se=ne;return _=le+_.toUpperCase(),le&&(se=l(se,function(ge){return ge.toJS().style.reduce(function(ye,We){return We.indexOf(le)===0&&_!==We?x.CharacterMetadata.removeStyle(ye,We):ye},ge)})),x.RichUtils.toggleInlineStyle(se,_)},Q=P.removeSelectionInlineStyles=function(ne){return l(ne,function(_){return _.merge({style:p.default.OrderedSet([])})})},ue=P.toggleSelectionAlignment=function(ne,_){return D(ne,{textAlign:k(ne,"textAlign")!==_?_:void 0})},Ee=P.toggleSelectionIndent=function(ne,_){var le=arguments.length>2&&arguments[2]!==void 0?arguments[2]:6;return _<0||_>le||isNaN(_)?ne:D(ne,{textIndent:_||void 0})},me=P.increaseSelectionIndent=function(ne){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,le=k(ne,"textIndent")||0;return Ee(ne,le+1,_)},V=P.decreaseSelectionIndent=function(ne){var _=k(ne,"textIndent")||0;return Ee(ne,_-1)},we=P.toggleSelectionColor=function(ne,_){return ie(ne,_.replace("#",""),"COLOR-")},Ne=P.toggleSelectionBackgroundColor=function(ne,_){return ie(ne,_.replace("#",""),"BGCOLOR-")},Me=P.toggleSelectionFontSize=function(ne,_){return ie(ne,_,"FONTSIZE-")},Ke=P.toggleSelectionLineHeight=function(ne,_){return ie(ne,_,"LINEHEIGHT-")},qe=P.toggleSelectionFontFamily=function(ne,_){return ie(ne,_,"FONTFAMILY-")},it=P.toggleSelectionLetterSpacing=function(ne,_){return ie(ne,_,"LETTERSPACING-")},et=P.insertText=function(ne,_,le,se){var ge=ne.getSelection(),ye=y(ne);if(ye==="atomic")return ne;var We=void 0,Fe=ne.getCurrentContent();return se&&se.type&&(Fe=Fe.createEntity(se.type,se.mutability||"MUTABLE",se.data||entityData),We=Fe.getLastCreatedEntityKey()),ge.isCollapsed()?x.EditorState.push(ne,x.Modifier.insertText(Fe,ge,_,le,We),"insert-text"):x.EditorState.push(ne,x.Modifier.replaceText(Fe,ge,_,le,We),"replace-text")},Je=P.insertHTML=function(ne,_,le){if(!_)return ne;var se=ne.getSelection(),ge=ne.getCurrentContent(),ye=ne.convertOptions||{};try{var We=(0,x.convertFromRaw)((0,v.convertHTMLToRaw)(_,ye,le)),Fe=We.blockMap;return x.EditorState.push(ne,x.Modifier.replaceWithFragment(ge,se,Fe),"insert-fragment")}catch(Ve){return console.warn(Ve),ne}},Be=P.insertAtomicBlock=function De(ne,_){var le=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,se=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};if(o(ne))return De(u(ne,C(ne)),_,le,se);var ge=ne.getSelection(),ye=ne.getCurrentContent();if(!ge.isCollapsed()||y(ne)==="atomic")return ne;var We=ye.createEntity(_,le?"IMMUTABLE":"MUTABLE",se),Fe=We.getLastCreatedEntityKey(),Ve=x.AtomicBlockUtils.insertAtomicBlock(ne,Fe," ");return Ve},He=P.insertHorizontalLine=function(ne){return Be(ne,"HR")},Qe=P.insertMedias=function(ne){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return _.length?_.reduce(function(le,se){var ge=se.url,ye=se.link,We=se.link_target,Fe=se.name,Ve=se.type,Ue=se.width,ct=se.height,Ze=se.meta;return Be(le,Ve,!0,{url:ge,link:ye,link_target:We,name:Fe,type:Ve,width:Ue,height:ct,meta:Ze})},ne):ne},ot=P.setMediaData=function(ne,_,le){return x.EditorState.push(ne,ne.getCurrentContent().mergeEntityData(_,le),"change-block-data")},mt=P.removeMedia=function(ne,_){return S(ne,_)},rt=P.setMediaPosition=function(ne,_,le){var se={},ge=le.float,ye=le.alignment;return typeof ge!="undefined"&&(se.float=_.getData().get("float")===ge?null:ge),typeof ye!="undefined"&&(se.alignment=_.getData().get("alignment")===ye?null:ye),D(r(ne,_),se)},Ce=P.clear=function(ne){var _=ne.getCurrentContent(),le=_.getFirstBlock(),se=_.getLastBlock(),ge=new x.SelectionState({anchorKey:le.getKey(),anchorOffset:0,focusKey:se.getKey(),focusOffset:se.getLength(),hasFocus:!0});return x.RichUtils.toggleBlockType(x.EditorState.push(ne,x.Modifier.removeRange(_,ge,"backward"),"remove-range"),"unstyled")},$e=P.handleKeyCommand=function(ne,_){return x.RichUtils.handleKeyCommand(ne,_)},Ft=P.undo=function(ne){return x.EditorState.undo(ne)},Et=P.redo=function(ne){return x.EditorState.redo(ne)}},32452:function(H,P,i){"use strict";Object.defineProperty(P,"__esModule",{value:!0}),P.ColorUtils=P.BaseUtils=P.ContentUtils=void 0;var x=i(17517),b=E(x),v=i(74652),A=E(v),p=i(29884),I=E(p);function E(s){if(s&&s.__esModule)return s;var a={};if(s!=null)for(var c in s)Object.prototype.hasOwnProperty.call(s,c)&&(a[c]=s[c]);return a.default=s,a}var d=P.ContentUtils=b,f=P.BaseUtils=A,g=P.ColorUtils=I},34561:function(H,P,i){"use strict";i.r(P),i.d(P,{convertFromHTML:function(){return Qt},convertToHTML:function(){return Be},parseHTML:function(){return ot}});var x=i(41143),b=i.n(x),v=i(67294),A=i(97762),p=i(9041);function I(J,ae){(ae==null||ae>J.length)&&(ae=J.length);for(var N=0,Z=new Array(ae);N<ae;N++)Z[N]=J[N];return Z}function E(J){if(Array.isArray(J))return I(J)}function d(J){if(typeof Symbol!="undefined"&&J[Symbol.iterator]!=null||J["@@iterator"]!=null)return Array.from(J)}function f(J,ae){if(!!J){if(typeof J=="string")return I(J,ae);var N=Object.prototype.toString.call(J).slice(8,-1);if(N==="Object"&&J.constructor&&(N=J.constructor.name),N==="Map"||N==="Set")return Array.from(J);if(N==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(N))return I(J,ae)}}function g(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function s(J){return E(J)||d(J)||f(J)||g()}function a(J,ae,N,Z,q,de){var be=Z-N,Se=ae+N<=J.offset;if(Se)return Object.assign({},J,{offset:J.offset+be});var Ae=ae>=J.offset&&ae+N<=J.offset+J.length;if(Ae)return Object.assign({},J,{length:J.length+be});var xe=J.offset>=ae&&J.offset+J.length<=ae+N&&q>0;if(xe)return Object.assign({},J,{offset:J.offset+q});var ve=J.offset<ae&&J.offset+J.length<=ae+N&&J.offset+J.length>ae&&q>0;if(ve)return[Object.assign({},J,{length:ae-J.offset}),Object.assign({},J,{offset:ae+q,length:J.offset-ae+J.length})];var ze=J.offset>=ae&&J.offset+J.length>ae+N&&ae+N>J.offset&&de>0;return ze?[Object.assign({},J,{offset:J.offset+q,length:ae+N-J.offset}),Object.assign({},J,{offset:ae+N+q+de,length:J.offset+J.length-(ae+N)})]:J}var c=function(J,ae){return J.offset===ae.offset?ae.length-J.length:J.offset-ae.offset},o={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","\n":"<br/>"},r=function(J){for(var ae=s(J.text),N=J.entityRanges.sort(c),Z=J.inlineStyleRanges.sort(c),q="",de=function(Ae){var xe=ae[Ae];if(o[xe]!==void 0){var ve=o[xe],ze=s(q).length;q+=ve;var Ge=function(Pe){return a(Pe,ze,xe.length,ve.length,0,0)};N=N.map(Ge),Z=Z.map(Ge)}else q+=xe},be=0;be<ae.length;be++)de(be);return Object.assign({},J,{text:q,inlineStyleRanges:Z,entityRanges:N})};function u(J){return u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(ae){return typeof ae}:function(ae){return ae&&typeof Symbol=="function"&&ae.constructor===Symbol&&ae!==Symbol.prototype?"symbol":typeof ae},u(J)}var S=["area","base","br","col","embed","hr","img","input","link","meta","param","source","track","wbr"];function C(J){if(S.indexOf(J.type)!==-1)return A.renderToStaticMarkup(J);var ae=A.renderToStaticMarkup(v.cloneElement(J,{},"\r")).split("\r");return b()(ae.length>1,"convertToHTML: Element of type ".concat(J.type," must render children")),b()(ae.length<3,"convertToHTML: Element of type ".concat(J.type," cannot use carriage return character")),{start:ae[0],end:ae[1]}}function l(J){return v.isValidElement(J)&&v.Children.count(J.props.children)>0}function h(J){var ae=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(J==null||typeof J=="string")return J;if(v.isValidElement(J)){if(l(J))return A.renderToStaticMarkup(J);var N=C(J);if(ae!==null&&u(N)==="object"){var Z=N.start,q=N.end;return Z+ae+q}return N}if(b()(Object.prototype.hasOwnProperty.call(J,"start")&&Object.prototype.hasOwnProperty.call(J,"end"),"convertToHTML: received conversion data without either an HTML string, ReactElement or an object with start/end tags"),ae!==null){var de=J.start,be=J.end;return de+ae+be}return J}var D=function J(ae){var N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"start";if(v.isValidElement(ae)){var Z=C(ae);if(typeof Z=="string")return 0;var q=Z[N].length,de=v.Children.toArray(ae.props.children)[0];return q+(de&&v.isValidElement(de)?J(de,N):0)}return u(ae)==="object"&&ae[N]?ae[N].length:0},k=D,y=function(){var ae=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},N=arguments.length>1?arguments[1]:void 0;return N},L=function(J,ae){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:y,Z=s(J.text),q=N;if(N.__isMiddleware&&(q=N(y)),Object.prototype.hasOwnProperty.call(J,"entityRanges")&&J.entityRanges.length>0){for(var de=J.entityRanges.sort(c),be=J.inlineStyleRanges,Se=function(ve){var ze=de[ve],Ge=ae[ze.key],Te=Z.slice(ze.offset,ze.offset+ze.length).join(""),Pe=q(Ge,Te),Oe=h(Pe,Te),Ie=void 0;!!Oe||Oe===""?Ie=s(Oe):Ie=Te;var ht=k(Pe,"start"),yt=k(Pe,"end"),Re=function(_e,dt){return dt>ve||Object.prototype.hasOwnProperty.call(_e,"style")?a(_e,ze.offset,ze.length,Ie.length,ht,yt):_e},nt=function(_e){return _e.reduce(function(dt,kt,Zt){var Ut=Re(kt,Zt);return Array.isArray(Ut)?dt.concat(Ut):dt.concat([Ut])},[])};de=nt(de),be=nt(be),Z=[].concat(s(Z.slice(0,ze.offset)),s(Ie),s(Z.slice(ze.offset+ze.length)))},Ae=0;Ae<de.length;Ae++)Se(Ae);return Object.assign({},J,{text:Z.join(""),inlineStyleRanges:be,entityRanges:de})}return J},T=function(J){return function(ae){return typeof J=="function"?J(ae):J[ae]}},B=function(J,ae){return function(){var N=J.apply(void 0,arguments);return N!=null?N:ae.apply(void 0,arguments)}};function w(J){switch(J){case"BOLD":return v.createElement("strong",null);case"ITALIC":return v.createElement("em",null);case"UNDERLINE":return v.createElement("u",null);case"CODE":return v.createElement("code",null);default:return{start:"",end:""}}}var z=function(ae,N){return ae.filter(function(Z){return!N.some(function(q){return q.style===Z.style})})},j=function(ae,N){return N.reduceRight(function(Z,q){var de=Z[Z.length-1];return b()(de.style===q.style,"Style ".concat(de.style," to be removed doesn't match expected ").concat(q.style)),Z.slice(0,-1)},ae)},U=function(ae,N){return N.filter(function(Z){return ae>=Z.offset&&ae<Z.offset+Z.length})},O=function(ae,N){var Z=ae.offset<=N.offset,q=ae.offset+ae.length>=N.offset+N.length;return Z&&q},ie=function(ae,N){var Z=N.offset+N.length,q=ae.offset+ae.length;return Z-q},Q=function(ae,N){for(var Z=0;Z<ae.length;)if(N.every(O.bind(null,ae[Z])))Z++;else return ae.slice(Z);return[]},ue=function(ae,N,Z){return N+h(ae(Z.style)).start},Ee=function(ae,N,Z){return h(ae(Z.style)).end+N},me=function(ae){return function(N){return ae(N)}};me.__isMiddleware=!0;var V=function(J){var ae=arguments.length>1&&arguments[1]!==void 0?arguments[1]:me;b()(J!=null,"Expected raw block to be non-null");var N;ae.__isMiddleware===!0?N=ae(w):N=B(T(ae),T(w));for(var Z="",q=[],de=J.inlineStyleRanges.sort(c),be=s(J.text),Se=0;Se<be.length;Se++){var Ae=U(Se,de),xe=z(q,Ae),ve=z(Ae,q),ze=z(q,xe),Ge=Q(ze,ve),Te=Ge.concat(ve).sort(ie),Pe=Te.reduce(ue.bind(null,N),""),Oe=xe.concat(Ge).reduce(Ee.bind(null,N),"");Z+=Oe+Pe+be[Se],q=j(q,Ge.concat(xe)),q=q.concat(Te),b()(q.length===Ae.length,"Character ".concat(Se,": ").concat(q.length-Ae.length," styles left on stack that should no longer be there"))}return Z=q.reduceRight(function(Ie,ht){return Ie+h(N(ht.style)).end},Z),Z},we=function(J){return function(ae){return typeof J=="function"?J(ae):J[ae.type]}};function Ne(J){return v.isValidElement(J)&&v.Children.count(J.props.children)>0}function Me(J){return b()(J!=null,"Expected block HTML value to be non-null"),typeof J=="string"?J:v.isValidElement(J)?Ne(J)?A.renderToStaticMarkup(J):C(J):Object.prototype.hasOwnProperty.call(J,"element")&&v.isValidElement(J.element)?Object.assign({},J,C(J.element)):(b()(Object.prototype.hasOwnProperty.call(J,"start")&&Object.prototype.hasOwnProperty.call(J,"end"),"convertToHTML: received block information without either a ReactElement or an object with start/end tags"),J)}function Ke(J,ae){if(b()(J!=null,"Expected block HTML value to be non-null"),typeof J.nest=="function"){var N=C(J.nest(ae)),Z=N.start,q=N.end;return Object.assign({},J,{nestStart:Z,nestEnd:q})}if(v.isValidElement(J.nest)){var de=C(J.nest),be=de.start,Se=de.end;return Object.assign({},J,{nestStart:be,nestEnd:Se})}return b()(Object.prototype.hasOwnProperty.call(J,"nestStart")&&Object.prototype.hasOwnProperty.call(J,"nestEnd"),"convertToHTML: received block information without either a ReactElement or an object with start/end tags"),J}var qe=["1","a","i"],it={unstyled:v.createElement("p",null),paragraph:v.createElement("p",null),"header-one":v.createElement("h1",null),"header-two":v.createElement("h2",null),"header-three":v.createElement("h3",null),"header-four":v.createElement("h4",null),"header-five":v.createElement("h5",null),"header-six":v.createElement("h6",null),"code-block":v.createElement("pre",null),blockquote:v.createElement("blockquote",null),"unordered-list-item":{element:v.createElement("li",null),nest:v.createElement("ul",null)},"ordered-list-item":{element:v.createElement("li",null),nest:function(ae){var N=qe[ae%3];return v.createElement("ol",{type:N})}},media:v.createElement("figure",null),atomic:v.createElement("figure",null)},et=function(ae,N){return N},Je=function(ae){var N=ae.styleToHTML,Z=N===void 0?{}:N,q=ae.blockToHTML,de=q===void 0?{}:q,be=ae.entityToHTML,Se=be===void 0?et:be;return function(Ae){b()(Ae!=null,"Expected contentState to be non-null");var xe;de.__isMiddleware===!0?xe=de(we(it)):xe=B(we(de),we(it));var ve=(0,p.convertToRaw)(Ae),ze=[],Ge=ve.blocks.map(function(Te){var Pe=Te.type,Oe=Te.depth,Ie="",ht="",yt=xe(Te);if(!yt)throw new Error("convertToHTML: missing HTML definition for block with type ".concat(Te.type));if(!yt.nest)Ie=ze.reduceRight(function(kt,Zt){return kt+Ke(xe(Zt),Oe).nestEnd},""),ze=[];else for(;Oe+1!==ze.length||Pe!==ze[Oe].type;)if(Oe+1===ze.length){var Re=ze[Oe];Ie+=Ke(xe(Re),Oe).nestEnd,ht+=Ke(xe(Te),Oe).nestStart,ze[Oe]=Te}else if(Oe+1<ze.length){var nt=ze[ze.length-1];Ie+=Ke(xe(nt),Oe).nestEnd,ze=ze.slice(0,-1)}else ht+=Ke(xe(Te),Oe).nestStart,ze.push(Te);var Bt=V(L(r(Te),ve.entityMap,Se),Z),_e=Me(xe(Te)),dt;return typeof _e=="string"?dt=_e:dt=_e.start+Bt+_e.end,Bt.length===0&&Object.prototype.hasOwnProperty.call(_e,"empty")&&(v.isValidElement(_e.empty)?dt=A.renderToStaticMarkup(_e.empty):dt=_e.empty),Ie+ht+dt}).join("");return Ge=ze.reduce(function(Te,Pe){return Te+Ke(xe(Pe),Pe.depth).nestEnd},Ge),Ge}},Be=function(){for(var J=arguments.length,ae=new Array(J),N=0;N<J;N++)ae[N]=arguments[N];return ae.length===1&&Object.prototype.hasOwnProperty.call(ae[0],"_map")&&ae[0].getBlockMap!=null?Je({}).apply(void 0,ae):Je.apply(void 0,ae)},He=i(43393),Qe=function(ae){var N=document.implementation.createHTMLDocument("");return N.documentElement.innerHTML=ae,N};function ot(J){var ae;if(typeof DOMParser!="undefined"){var N=new DOMParser;ae=N.parseFromString(J,"text/html"),(ae===null||ae.body===null)&&(ae=Qe(J))}else ae=Qe(J);return ae.body}var mt="&nbsp;",rt=" ",Ce=4,$e=new RegExp("\r","g"),Ft=new RegExp(`
`,"g"),Et=new RegExp(mt,"g"),De=new RegExp("\r","g"),ne=["p","h1","h2","h3","h4","h5","h6","li","blockquote","pre"],_={b:"BOLD",code:"CODE",del:"STRIKETHROUGH",em:"ITALIC",i:"ITALIC",s:"STRIKETHROUGH",strike:"STRIKETHROUGH",strong:"BOLD",u:"UNDERLINE"},le=function(ae,N){return ae&&ae.__isMiddleware===!0?ae(N):ae},se=function(ae,N,Z){},ge=function(ae,N,Z){return Z},ye=function(ae,N){},We=function(ae){return[]},Fe=function(ae){if(ae!=null)return ae;throw new Error("Got unexpected null or undefined")},Ve=function(ae){return ae.replace(De,"")};function Ue(){return{text:"",inlines:[],entities:[],blocks:[]}}function ct(J){var ae=new Array(1);return J&&(ae[0]=J),{text:rt,inlines:[(0,He.OrderedSet)()],entities:ae,blocks:[]}}function Ze(J,ae){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,Z=arguments.length>3&&arguments[3]!==void 0?arguments[3]:(0,He.Map)();return N===!0?{text:"\r",inlines:[(0,He.OrderedSet)()],entities:new Array(1),blocks:[{type:J,data:Z,depth:Math.max(0,Math.min(Ce,ae))}],isNewline:!0}:{text:`
`,inlines:[(0,He.OrderedSet)()],entities:new Array(1),blocks:[]}}function ut(J,ae){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:(0,He.Map)();return{text:"\r",inlines:[(0,He.OrderedSet)()],entities:new Array(1),blocks:[{type:J,data:N,depth:Math.max(0,Math.min(Ce,ae))}]}}function lt(J,ae){switch(J){case"h1":return"header-one";case"h2":return"header-two";case"h3":return"header-three";case"h4":return"header-four";case"h5":return"header-five";case"h6":return"header-six";case"li":return ae==="ol"?"ordered-list-item":"unordered-list-item";case"blockquote":return"blockquote";case"pre":return"code-block";case"div":case"p":return"unstyled";default:return null}}function gt(J,ae,N){return lt(J,N)}function pt(J,ae,N){var Z=_[J];if(Z)N=N.add(Z).toOrderedSet();else if(ae instanceof HTMLElement){var q=ae;N=N.withMutations(function(de){q.style.fontWeight==="bold"&&de.add("BOLD"),q.style.fontStyle==="italic"&&de.add("ITALIC"),q.style.textDecoration==="underline"&&de.add("UNDERLINE"),q.style.textDecoration==="line-through"&&de.add("STRIKETHROUGH")}).toOrderedSet()}return N}function ft(J,ae){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:(0,He.OrderedSet)();return pt(J,ae,N)}function Dt(J,ae){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,Z=ae.text.slice(0,1),q=J.text.slice(-1),de=q==="\r"&&Z==="\r",be=J.text!=="\r"&&ae.text!=="\r",Se=J.text==="\r"&&!J.isNewline&&ae.isNewline;if(de&&(be||Se)&&(J.text=J.text.slice(0,-1),J.inlines.pop(),J.entities.pop(),J.blocks.pop()),J.text.slice(-1)==="\r"&&N===!0){if(ae.text===rt||ae.text===`
`)return J;(Z===rt||Z===`
`)&&(ae.text=ae.text.slice(1),ae.inlines.shift(),ae.entities.shift())}var Ae=J.text.length===0&&ae.isNewline;return{text:J.text+ae.text,inlines:J.inlines.concat(ae.inlines),entities:J.entities.concat(ae.entities),blocks:J.blocks.concat(ae.blocks),isNewline:Ae}}function wt(J){return ne.some(function(ae){return J.indexOf("<".concat(ae))!==-1})}function Rt(J,ae,N,Z,q,de,be,Se,Ae,xe,ve,ze,Ge,Te,Pe,Oe){var Ie=J.nodeName.toLowerCase(),ht=!1,yt="unstyled";if(Ie==="#text"){var Re=J.textContent;if(Re.trim()===""&&Z===null)return Ue();if(Re.trim()===""&&Z!=="code-block")return ct(Oe);Z!=="code-block"&&(Re=Re.replace(Ft,rt));var nt=Array(Re.length).fill(Oe),Bt=0,_e=Ae(Re,ve,ze,Ge,Te).sort(c);return _e.forEach(function(Sn){var Hn=Sn.entity,fn=Sn.offset,An=Sn.length,dn=Sn.result,Cn=fn+Bt;dn==null&&(dn=Re.substr(Cn,An));var bn=Re.split("");bn.splice.bind(bn,Cn,An).apply(bn,dn.split("")),Re=bn.join(""),nt.splice.bind(nt,Cn,An).apply(nt,Array(dn.length).fill(Hn)),Bt+=dn.length-An}),{text:Re,inlines:Array(Re.length).fill(ae),entities:nt,blocks:[]}}if(Ie==="br"){var dt=Z;return dt===null?Ze("unstyled",de,!0):Ze(dt||"unstyled",de,Pe.flat)}var kt=Ue(),Zt=null;ae=pt(Ie,J,ae),ae=be(Ie,J,ae),(Ie==="ul"||Ie==="ol")&&(N&&(de+=1),N=Ie,Z=null);var Ut=xe(Ie,J,N,Z),Vt,cn;if(Ut===!1)return Ue();if(Ut=Ut||{},typeof Ut=="string"?(Vt=Ut,cn=(0,He.Map)()):(Vt=typeof Ut=="string"?Ut:Ut.type,cn=Ut.data?(0,He.Map)(Ut.data):(0,He.Map)()),!Z&&(q.indexOf(Ie)!==-1||Vt))kt=ut(Vt||lt(Ie,N),de,cn),Z=Vt||lt(Ie,N),ht=!0;else if(N&&(Z==="ordered-list-item"||Z==="unordered-list-item")&&Ie==="li"){var Ln=lt(Ie,N);kt=ut(Ln,de),Z=Ln,ht=!0,yt=N==="ul"?"unordered-list-item":"ordered-list-item"}else Z&&Z!=="atomic"&&Vt==="atomic"&&(Z=Vt,ht=!0,kt=Ze(Vt,de,!0,cn));var Lt=J.firstChild;Lt==null&&Oe&&(Vt==="atomic"||Z==="atomic")&&(Lt=document.createTextNode("a")),Lt!=null&&(Ie=Lt.nodeName.toLowerCase());for(var Bn=null;Lt;){Bn=Se(Ie,Lt,ve,ze,Ge,Te),Zt=Rt(Lt,ae,N,Z,q,de,be,Se,Ae,xe,ve,ze,Ge,Te,Pe,Bn||Oe),kt=Dt(kt,Zt,Pe.flat);var _t=Lt.nextSibling;if(_t&&q.indexOf(Ie)>=0&&Z){var Jt=xe(Ie,Lt,N,Z),on=void 0,Nn=void 0;Jt!==!1&&(Jt=Jt||{},typeof Jt=="string"?(on=Jt,Nn=(0,He.Map)()):(on=Jt.type||lt(Ie,N),Nn=Jt.data?(0,He.Map)(Jt.data):(0,He.Map)()),kt=Dt(kt,Ze(on,de,Pe.flat,Nn),Pe.flat))}_t&&(Ie=_t.nodeName.toLowerCase()),Lt=_t}return ht&&(kt=Dt(kt,ut(yt,de,(0,He.Map)()),Pe.flat)),kt}function Mt(J,ae,N,Z,q,de,be,Se,Ae,xe,ve){J=J.trim().replace($e,"").replace(Et,rt);var ze=ve(J);if(!ze)return null;var Ge=wt(J)?ne.concat(["div"]):["div"],Te=Rt(ze,(0,He.OrderedSet)(),"ul",null,Ge,-1,ae,N,Z,q,de,be,Se,Ae,xe);return Te.text.indexOf("\r")===0&&(Te={text:Te.text.slice(1),inlines:Te.inlines.slice(1),entities:Te.entities.slice(1),blocks:Te.blocks}),Te.text.slice(-1)==="\r"&&(Te.text=Te.text.slice(0,-1),Te.inlines=Te.inlines.slice(0,-1),Te.entities=Te.entities.slice(0,-1),Te.blocks.pop()),Te.blocks.length===0&&Te.blocks.push({type:"unstyled",data:(0,He.Map)(),depth:0}),Te.text.split("\r").length===Te.blocks.length+1&&Te.blocks.unshift({type:"unstyled",data:(0,He.Map)(),depth:0}),Te}function jt(J,ae,N,Z,q,de,be,Se,Ae,xe,ve,ze){var Ge=Mt(J,ae,N,Z,q,de,be,Se,Ae,xe,ve,ze);if(Ge==null)return[];var Te=0;return Ge.text.split("\r").map(function(Pe,Oe){Pe=Ve(Pe);var Ie=Te+Pe.length,ht=Fe(Ge).inlines.slice(Te,Ie),yt=Fe(Ge).entities.slice(Te,Ie),Re=(0,He.List)(ht.map(function(nt,Bt){var _e={style:nt,entity:null};return yt[Bt]&&(_e.entity=yt[Bt]),p.CharacterMetadata.create(_e)}));return Te=Ie+1,new p.ContentBlock({key:ze(),type:Fe(Ge).blocks[Oe].type,data:Fe(Ge).blocks[Oe].data,depth:Fe(Ge).blocks[Oe].depth,text:Pe,characterList:Re})})}var It=function(ae){var N=ae.htmlToStyle,Z=N===void 0?ge:N,q=ae.htmlToEntity,de=q===void 0?ye:q,be=ae.textToEntity,Se=be===void 0?We:be,Ae=ae.htmlToBlock,xe=Ae===void 0?se:Ae;return function(ve){var ze=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{flat:!1},Ge=arguments.length>2&&arguments[2]!==void 0?arguments[2]:ot,Te=arguments.length>3&&arguments[3]!==void 0?arguments[3]:p.genKey,Pe=p.ContentState.createFromText(""),Oe=function(){if(Pe.createEntity){var dt;return Pe=(dt=Pe).createEntity.apply(dt,arguments),Pe.getLastCreatedEntityKey()}return p.Entity.create.apply(p.Entity,arguments)},Ie=function(){if(Pe.getEntity){var dt;return(dt=Pe).getEntity.apply(dt,arguments)}return p.Entity.get.apply(p.Entity,arguments)},ht=function(){if(Pe.mergeEntityData){var dt;Pe=(dt=Pe).mergeEntityData.apply(dt,arguments);return}p.Entity.mergeData.apply(p.Entity,arguments)},yt=function(){if(Pe.replaceEntityData){var dt;Pe=(dt=Pe).replaceEntityData.apply(dt,arguments);return}p.Entity.replaceData.apply(p.Entity,arguments)},Re=jt(ve,le(Z,ft),le(de,ye),le(Se,We),le(xe,gt),Oe,Ie,ht,yt,ze,Ge,Te),nt=p.BlockMapBuilder.createFromArray(Re),Bt=Re[0].getKey();return Pe.merge({blockMap:nt,selectionBefore:p.SelectionState.createEmpty(Bt),selectionAfter:p.SelectionState.createEmpty(Bt)})}},Qt=function(){return arguments.length>=1&&typeof(arguments.length<=0?void 0:arguments[0])=="string"?It({}).apply(void 0,arguments):It.apply(void 0,arguments)}},19785:function(H,P,i){"use strict";var x=i(27418),b=x||function(l){for(var h=1;h<arguments.length;h++){var D=arguments[h];for(var k in D)Object.prototype.hasOwnProperty.call(D,k)&&(l[k]=D[k])}return l},v=i(10329),A=i(4516),p=i(2641),I=i(67953),E=i(1065),d=i(42307),f=i(14289),g=i(43393),s=i(25110),a=i(25027),c=i(61173),o=E.draft_tree_data_support,r=o?I:p,u=g.List,S=g.Repeat,C={insertAtomicBlock:function(h,D,k){var y=h.getCurrentContent(),L=h.getSelection(),T=d.removeRange(y,L,"backward"),B=T.getSelectionAfter(),w=d.splitBlock(T,B),z=w.getSelectionAfter(),j=d.setBlockType(w,z,"atomic"),U=A.create({entity:D}),O={key:a(),type:"atomic",text:k,characterList:u(S(U,k.length))},ie={key:a(),type:"unstyled"};o&&(O=b({},O,{nextSibling:ie.key}),ie=b({},ie,{prevSibling:O.key}));var Q=[new r(O),new r(ie)],ue=v.createFromArray(Q),Ee=d.replaceWithFragment(j,z,ue),me=Ee.merge({selectionBefore:L,selectionAfter:Ee.getSelectionAfter().set("hasFocus",!0)});return f.push(h,me,"insert-fragment")},moveAtomicBlock:function(h,D,k,y){var L=h.getCurrentContent(),T=h.getSelection(),B=void 0;if(y==="before"||y==="after"){var w=L.getBlockForKey(y==="before"?k.getStartKey():k.getEndKey());B=c(L,D,w,y)}else{var z=d.removeRange(L,k,"backward"),j=z.getSelectionAfter(),U=z.getBlockForKey(j.getFocusKey());if(j.getStartOffset()===0)B=c(z,D,U,"before");else if(j.getEndOffset()===U.getLength())B=c(z,D,U,"after");else{var O=d.splitBlock(z,j),ie=O.getSelectionAfter(),Q=O.getBlockForKey(ie.getFocusKey());B=c(O,D,Q,"before")}}var ue=B.merge({selectionBefore:T,selectionAfter:B.getSelectionAfter().set("hasFocus",!0)});return f.push(h,ue,"move-block")}};H.exports=C},10329:function(H,P,i){"use strict";var x=i(43393),b=x.OrderedMap,v={createFromArray:function(p){return b(p.map(function(I){return[I.getKey(),I]}))}};H.exports=v},34365:function(H,P,i){"use strict";var x=i(43393),b=i(60139),v=i(29407),A=x.List,p=x.Repeat,I=x.Record,E=b.thatReturnsTrue,d="-",f={start:null,end:null},g=I(f),s={start:null,end:null,decoratorKey:null,leaves:null},a=I(s),c={generate:function(S,C,l){var h=C.getLength();if(!h)return A.of(new a({start:0,end:0,decoratorKey:null,leaves:A.of(new g({start:0,end:0}))}));var D=[],k=l?l.getDecorations(C,S):A(p(null,h)),y=C.getCharacterList();return v(k,r,E,function(L,T){D.push(new a({start:L,end:T,decoratorKey:k.get(L),leaves:o(y.slice(L,T).toList(),L)}))}),A(D)},getFingerprint:function(S){return S.map(function(C){var l=C.get("decoratorKey"),h=l!==null?l+"."+(C.get("end")-C.get("start")):"";return""+h+"."+C.get("leaves").size}).join(d)}};function o(u,S){var C=[],l=u.map(function(h){return h.getStyle()}).toList();return v(l,r,E,function(h,D){C.push(new g({start:h+S,end:D+S}))}),A(C)}function r(u,S){return u===S}H.exports=c},4516:function(H,P,i){"use strict";function x(o,r){if(!(o instanceof r))throw new TypeError("Cannot call a class as a function")}function b(o,r){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:o}function v(o,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);o.prototype=Object.create(r&&r.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(o,r):o.__proto__=r)}var A=i(43393),p=A.Map,I=A.OrderedSet,E=A.Record,d=I(),f={style:d,entity:null},g=E(f),s=function(o){v(r,o);function r(){return x(this,r),b(this,o.apply(this,arguments))}return r.prototype.getStyle=function(){return this.get("style")},r.prototype.getEntity=function(){return this.get("entity")},r.prototype.hasStyle=function(S){return this.getStyle().includes(S)},r.applyStyle=function(S,C){var l=S.set("style",S.getStyle().add(C));return r.create(l)},r.removeStyle=function(S,C){var l=S.set("style",S.getStyle().remove(C));return r.create(l)},r.applyEntity=function(S,C){var l=S.getEntity()===C?S:S.set("entity",C);return r.create(l)},r.create=function(S){if(!S)return a;var C={style:d,entity:null},l=p(C).merge(S),h=c.get(l);if(h)return h;var D=new r(l);return c=c.set(l,D),D},r}(g),a=new s,c=p([[p(f),a]]);s.EMPTY=a,H.exports=s},25369:function(H,P,i){"use strict";function x(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}var b=i(43393),v=b.List,A=".",p=function(){function d(f){x(this,d),this._decorators=f.slice()}return d.prototype.getDecorations=function(g,s){var a=Array(g.getText().length).fill(null);return this._decorators.forEach(function(c,o){var r=0,u=c.strategy,S=function(l,h){I(a,l,h)&&(E(a,l,h,o+A+r),r++)};u(g,S,s)}),v(a)},d.prototype.getComponentForKey=function(g){var s=parseInt(g.split(A)[0],10);return this._decorators[s].component},d.prototype.getPropsForKey=function(g){var s=parseInt(g.split(A)[0],10);return this._decorators[s].props},d}();function I(d,f,g){for(var s=f;s<g;s++)if(d[s]!=null)return!1;return!0}function E(d,f,g,s){for(var a=f;a<g;a++)d[a]=s}H.exports=p},2641:function(H,P,i){"use strict";function x(l,h){if(!(l instanceof h))throw new TypeError("Cannot call a class as a function")}function b(l,h){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:l}function v(l,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);l.prototype=Object.create(h&&h.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(l,h):l.__proto__=h)}var A=i(4516),p=i(43393),I=i(29407),E=p.List,d=p.Map,f=p.OrderedSet,g=p.Record,s=p.Repeat,a=f(),c={key:"",type:"unstyled",text:"",characterList:E(),depth:0,data:d()},o=g(c),r=function(h){if(!h)return h;var D=h.characterList,k=h.text;return k&&!D&&(h.characterList=E(s(A.EMPTY,k.length))),h},u=function(l){v(h,l);function h(D){return x(this,h),b(this,l.call(this,r(D)))}return h.prototype.getKey=function(){return this.get("key")},h.prototype.getType=function(){return this.get("type")},h.prototype.getText=function(){return this.get("text")},h.prototype.getCharacterList=function(){return this.get("characterList")},h.prototype.getLength=function(){return this.getText().length},h.prototype.getDepth=function(){return this.get("depth")},h.prototype.getData=function(){return this.get("data")},h.prototype.getInlineStyleAt=function(k){var y=this.getCharacterList().get(k);return y?y.getStyle():a},h.prototype.getEntityAt=function(k){var y=this.getCharacterList().get(k);return y?y.getEntity():null},h.prototype.findStyleRanges=function(k,y){I(this.getCharacterList(),S,k,y)},h.prototype.findEntityRanges=function(k,y){I(this.getCharacterList(),C,k,y)},h}(o);function S(l,h){return l.getStyle()===h.getStyle()}function C(l,h){return l.getEntity()===h.getEntity()}H.exports=u},67953:function(H,P,i){"use strict";function x(C,l){if(!(C instanceof l))throw new TypeError("Cannot call a class as a function")}function b(C,l){if(!C)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:C}function v(C,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);C.prototype=Object.create(l&&l.prototype,{constructor:{value:C,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(C,l):C.__proto__=l)}var A=i(4516),p=i(43393),I=i(29407),E=p.List,d=p.Map,f=p.OrderedSet,g=p.Record,s=p.Repeat,a=f(),c={parent:null,characterList:E(),data:d(),depth:0,key:"",text:"",type:"unstyled",children:E(),prevSibling:null,nextSibling:null},o=function(l,h){return l.getStyle()===h.getStyle()},r=function(l,h){return l.getEntity()===h.getEntity()},u=function(l){if(!l)return l;var h=l.characterList,D=l.text;return D&&!h&&(l.characterList=E(s(A.EMPTY,D.length))),l},S=function(C){v(l,C);function l(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c;return x(this,l),b(this,C.call(this,u(h)))}return l.prototype.getKey=function(){return this.get("key")},l.prototype.getType=function(){return this.get("type")},l.prototype.getText=function(){return this.get("text")},l.prototype.getCharacterList=function(){return this.get("characterList")},l.prototype.getLength=function(){return this.getText().length},l.prototype.getDepth=function(){return this.get("depth")},l.prototype.getData=function(){return this.get("data")},l.prototype.getInlineStyleAt=function(D){var k=this.getCharacterList().get(D);return k?k.getStyle():a},l.prototype.getEntityAt=function(D){var k=this.getCharacterList().get(D);return k?k.getEntity():null},l.prototype.getChildKeys=function(){return this.get("children")},l.prototype.getParentKey=function(){return this.get("parent")},l.prototype.getPrevSiblingKey=function(){return this.get("prevSibling")},l.prototype.getNextSiblingKey=function(){return this.get("nextSibling")},l.prototype.findStyleRanges=function(D,k){I(this.getCharacterList(),o,D,k)},l.prototype.findEntityRanges=function(D,k){I(this.getCharacterList(),r,D,k)},l}(g(c));H.exports=S},66912:function(H,P,i){"use strict";function x(k,y){if(!(k instanceof y))throw new TypeError("Cannot call a class as a function")}function b(k,y){if(!k)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return y&&(typeof y=="object"||typeof y=="function")?y:k}function v(k,y){if(typeof y!="function"&&y!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof y);k.prototype=Object.create(y&&y.prototype,{constructor:{value:k,enumerable:!1,writable:!0,configurable:!0}}),y&&(Object.setPrototypeOf?Object.setPrototypeOf(k,y):k.__proto__=y)}var A=i(10329),p=i(4516),I=i(2641),E=i(67953),d=i(82222),f=i(1065),g=i(43393),s=i(25110),a=i(25027),c=i(55283),o=g.List,r=g.Record,u=g.Repeat,S=f.draft_tree_data_support,C={entityMap:null,blockMap:null,selectionBefore:null,selectionAfter:null},l=S?E:I,h=r(C),D=function(k){v(y,k);function y(){return x(this,y),b(this,k.apply(this,arguments))}return y.prototype.getEntityMap=function(){return d},y.prototype.getBlockMap=function(){return this.get("blockMap")},y.prototype.getSelectionBefore=function(){return this.get("selectionBefore")},y.prototype.getSelectionAfter=function(){return this.get("selectionAfter")},y.prototype.getBlockForKey=function(T){var B=this.getBlockMap().get(T);return B},y.prototype.getKeyBefore=function(T){return this.getBlockMap().reverse().keySeq().skipUntil(function(B){return B===T}).skip(1).first()},y.prototype.getKeyAfter=function(T){return this.getBlockMap().keySeq().skipUntil(function(B){return B===T}).skip(1).first()},y.prototype.getBlockAfter=function(T){return this.getBlockMap().skipUntil(function(B,w){return w===T}).skip(1).first()},y.prototype.getBlockBefore=function(T){return this.getBlockMap().reverse().skipUntil(function(B,w){return w===T}).skip(1).first()},y.prototype.getBlocksAsArray=function(){return this.getBlockMap().toArray()},y.prototype.getFirstBlock=function(){return this.getBlockMap().first()},y.prototype.getLastBlock=function(){return this.getBlockMap().last()},y.prototype.getPlainText=function(T){return this.getBlockMap().map(function(B){return B?B.getText():""}).join(T||`
`)},y.prototype.getLastCreatedEntityKey=function(){return d.__getLastCreatedEntityKey()},y.prototype.hasText=function(){var T=this.getBlockMap();return T.size>1||T.first().getLength()>0},y.prototype.createEntity=function(T,B,w){return d.__create(T,B,w),this},y.prototype.mergeEntityData=function(T,B){return d.__mergeData(T,B),this},y.prototype.replaceEntityData=function(T,B){return d.__replaceData(T,B),this},y.prototype.addEntity=function(T){return d.__add(T),this},y.prototype.getEntity=function(T){return d.__get(T)},y.createFromBlockArray=function(T,B){var w=Array.isArray(T)?T:T.contentBlocks,z=A.createFromArray(w),j=z.isEmpty()?new s:s.createEmpty(z.first().getKey());return new y({blockMap:z,entityMap:B||d,selectionBefore:j,selectionAfter:j})},y.createFromText=function(T){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:/\r\n?|\n/g,w=T.split(B),z=w.map(function(j){return j=c(j),new l({key:a(),text:j,type:"unstyled",characterList:o(u(p.EMPTY,j.length))})});return y.createFromBlockArray(z)},y}(h);H.exports=D},13483:function(H,P,i){"use strict";var x=i(4516),b=i(43393),v=b.Map,A={add:function(E,d,f){return p(E,d,f,!0)},remove:function(E,d,f){return p(E,d,f,!1)}};function p(I,E,d,f){var g=I.getBlockMap(),s=E.getStartKey(),a=E.getStartOffset(),c=E.getEndKey(),o=E.getEndOffset(),r=g.skipUntil(function(u,S){return S===s}).takeUntil(function(u,S){return S===c}).concat(v([[c,g.get(c)]])).map(function(u,S){var C,l;s===c?(C=a,l=o):(C=S===s?a:0,l=S===c?o:u.getLength());for(var h=u.getCharacterList(),D;C<l;)D=h.get(C),h=h.set(C,f?x.applyStyle(D,d):x.removeStyle(D,d)),C++;return u.set("characterList",h)});return I.merge({blockMap:g.merge(r),selectionBefore:E,selectionAfter:E})}H.exports=A},526:function(H,P,i){"use strict";var x=i(43393),b=x.Map,v=i(67294),A=i(62620),p=v.createElement("ul",{className:A("public/DraftStyleDefault/ul")}),I=v.createElement("ol",{className:A("public/DraftStyleDefault/ol")}),E=v.createElement("pre",{className:A("public/DraftStyleDefault/pre")}),d=b({"header-one":{element:"h1"},"header-two":{element:"h2"},"header-three":{element:"h3"},"header-four":{element:"h4"},"header-five":{element:"h5"},"header-six":{element:"h6"},"unordered-list-item":{element:"li",wrapper:p},"ordered-list-item":{element:"li",wrapper:I},blockquote:{element:"blockquote"},atomic:{element:"figure"},"code-block":{element:"pre",wrapper:E},unstyled:{element:"div",aliasedElements:["p"]}});H.exports=d},37619:function(H){"use strict";H.exports={BOLD:{fontWeight:"bold"},CODE:{fontFamily:"monospace",wordWrap:"break-word"},ITALIC:{fontStyle:"italic"},STRIKETHROUGH:{textDecoration:"line-through"},UNDERLINE:{textDecoration:"underline"}}},9041:function(H,P,i){"use strict";var x=i(19785),b=i(10329),v=i(4516),A=i(25369),p=i(2641),I=i(66912),E=i(526),d=i(37619),f=i(87210),g=i(37898),s=i(82222),a=i(42307),c=i(39006),o=i(14289),r=i(47387),u=i(41947),S=i(25110),C=i(79981),l=i(67841),h=i(99607),D=i(25027),k=i(41714),y=i(96629),L={Editor:f,EditorBlock:g,EditorState:o,CompositeDecorator:A,Entity:s,EntityInstance:c,BlockMapBuilder:b,CharacterMetadata:v,ContentBlock:p,ContentState:I,SelectionState:S,AtomicBlockUtils:x,KeyBindingUtil:r,Modifier:a,RichUtils:u,DefaultDraftBlockRenderMap:E,DefaultDraftInlineStyle:d,convertFromHTML:l,convertFromRaw:h,convertToRaw:C,genKey:D,getDefaultKeyBinding:k,getVisibleSelectionRect:y};H.exports=L},87210:function(H,P,i){"use strict";var x=i(27418),b=x||function(U){for(var O=1;O<arguments.length;O++){var ie=arguments[O];for(var Q in ie)Object.prototype.hasOwnProperty.call(ie,Q)&&(U[Q]=ie[Q])}return U};function v(U,O){if(!(U instanceof O))throw new TypeError("Cannot call a class as a function")}function A(U,O){if(!U)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O&&(typeof O=="object"||typeof O=="function")?O:U}function p(U,O){if(typeof O!="function"&&O!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof O);U.prototype=Object.create(O&&O.prototype,{constructor:{value:U,enumerable:!1,writable:!0,configurable:!0}}),O&&(Object.setPrototypeOf?Object.setPrototypeOf(U,O):U.__proto__=O)}var I=i(526),E=i(37619),d=i(33418),f=i(87791),g=i(61494),s=i(19394),a=i(28094),c=i(14289),o=i(67294),r=i(73935),u=i(65994),S=i(19051),C=i(4856),l=i(62620),h=i(60139),D=i(25027),k=i(41714),y=i(79749),L=i(73759),T=i(22045),B=C.isBrowser("IE"),w=!B,z={edit:s,composite:d,drag:g,cut:null,render:null},j=function(U){p(O,U);function O(ie){v(this,O);var Q=A(this,U.call(this,ie));return Q.focus=function(ue){var Ee=Q.props.editorState,me=Ee.getSelection().getHasFocus(),V=r.findDOMNode(Q.editor);if(!!V){var we=S.getScrollParent(V),Ne=ue||y(we),Me=Ne.x,Ke=Ne.y;V instanceof HTMLElement||L(!1),V.focus(),we===window?window.scrollTo(Me,Ke):u.setTop(we,Ke),me||Q.update(c.forceSelection(Ee,Ee.getSelection()))}},Q.blur=function(){var ue=r.findDOMNode(Q.editor);ue instanceof HTMLElement||L(!1),ue.blur()},Q.setMode=function(ue){Q._handler=z[ue]},Q.exitCurrentMode=function(){Q.setMode("edit")},Q.restoreEditorDOM=function(ue){Q.setState({contentsKey:Q.state.contentsKey+1},function(){Q.focus(ue)})},Q.setClipboard=function(ue){Q._clipboard=ue},Q.getClipboard=function(){return Q._clipboard},Q.update=function(ue){Q._latestEditorState=ue,Q.props.onChange(ue)},Q.onDragEnter=function(){Q._dragCount++},Q.onDragLeave=function(){Q._dragCount--,Q._dragCount===0&&Q.exitCurrentMode()},Q._blockSelectEvents=!1,Q._clipboard=null,Q._handler=null,Q._dragCount=0,Q._editorKey=ie.editorKey||D(),Q._placeholderAccessibilityID="placeholder-"+Q._editorKey,Q._latestEditorState=ie.editorState,Q._latestCommittedEditorState=ie.editorState,Q._onBeforeInput=Q._buildHandler("onBeforeInput"),Q._onBlur=Q._buildHandler("onBlur"),Q._onCharacterData=Q._buildHandler("onCharacterData"),Q._onCompositionEnd=Q._buildHandler("onCompositionEnd"),Q._onCompositionStart=Q._buildHandler("onCompositionStart"),Q._onCopy=Q._buildHandler("onCopy"),Q._onCut=Q._buildHandler("onCut"),Q._onDragEnd=Q._buildHandler("onDragEnd"),Q._onDragOver=Q._buildHandler("onDragOver"),Q._onDragStart=Q._buildHandler("onDragStart"),Q._onDrop=Q._buildHandler("onDrop"),Q._onInput=Q._buildHandler("onInput"),Q._onFocus=Q._buildHandler("onFocus"),Q._onKeyDown=Q._buildHandler("onKeyDown"),Q._onKeyPress=Q._buildHandler("onKeyPress"),Q._onKeyUp=Q._buildHandler("onKeyUp"),Q._onMouseDown=Q._buildHandler("onMouseDown"),Q._onMouseUp=Q._buildHandler("onMouseUp"),Q._onPaste=Q._buildHandler("onPaste"),Q._onSelect=Q._buildHandler("onSelect"),Q.getEditorKey=function(){return Q._editorKey},Q.state={contentsKey:0},Q}return O.prototype._buildHandler=function(Q){var ue=this;return function(Ee){if(!ue.props.readOnly){var me=ue._handler&&ue._handler[Q];me&&me(ue,Ee)}}},O.prototype._showPlaceholder=function(){return!!this.props.placeholder&&!this.props.editorState.isInCompositionMode()&&!this.props.editorState.getCurrentContent().hasText()},O.prototype._renderPlaceholder=function(){if(this._showPlaceholder()){var Q={text:T(this.props.placeholder),editorState:this.props.editorState,textAlignment:this.props.textAlignment,accessibilityID:this._placeholderAccessibilityID};return o.createElement(a,Q)}return null},O.prototype.render=function(){var Q=this,ue=this.props,Ee=ue.blockRenderMap,me=ue.blockRendererFn,V=ue.blockStyleFn,we=ue.customStyleFn,Ne=ue.customStyleMap,Me=ue.editorState,Ke=ue.readOnly,qe=ue.textAlignment,it=ue.textDirectionality,et=l({"DraftEditor/root":!0,"DraftEditor/alignLeft":qe==="left","DraftEditor/alignRight":qe==="right","DraftEditor/alignCenter":qe==="center"}),Je={outline:"none",userSelect:"text",WebkitUserSelect:"text",whiteSpace:"pre-wrap",wordWrap:"break-word"},Be=this.props.role||"textbox",He=Be==="combobox"?!!this.props.ariaExpanded:null,Qe={blockRenderMap:Ee,blockRendererFn:me,blockStyleFn:V,customStyleMap:b({},E,Ne),customStyleFn:we,editorKey:this._editorKey,editorState:Me,key:"contents"+this.state.contentsKey,textDirectionality:it};return o.createElement("div",{className:et},this._renderPlaceholder(),o.createElement("div",{className:l("DraftEditor/editorContainer"),ref:function(mt){return Q.editorContainer=mt}},o.createElement("div",{"aria-activedescendant":Ke?null:this.props.ariaActiveDescendantID,"aria-autocomplete":Ke?null:this.props.ariaAutoComplete,"aria-controls":Ke?null:this.props.ariaControls,"aria-describedby":this.props.ariaDescribedBy||this._placeholderAccessibilityID,"aria-expanded":Ke?null:He,"aria-label":this.props.ariaLabel,"aria-labelledby":this.props.ariaLabelledBy,"aria-multiline":this.props.ariaMultiline,autoCapitalize:this.props.autoCapitalize,autoComplete:this.props.autoComplete,autoCorrect:this.props.autoCorrect,className:l({notranslate:!Ke,"public/DraftEditor/content":!0}),contentEditable:!Ke,"data-testid":this.props.webDriverTestID,onBeforeInput:this._onBeforeInput,onBlur:this._onBlur,onCompositionEnd:this._onCompositionEnd,onCompositionStart:this._onCompositionStart,onCopy:this._onCopy,onCut:this._onCut,onDragEnd:this._onDragEnd,onDragEnter:this.onDragEnter,onDragLeave:this.onDragLeave,onDragOver:this._onDragOver,onDragStart:this._onDragStart,onDrop:this._onDrop,onFocus:this._onFocus,onInput:this._onInput,onKeyDown:this._onKeyDown,onKeyPress:this._onKeyPress,onKeyUp:this._onKeyUp,onMouseUp:this._onMouseUp,onPaste:this._onPaste,onSelect:this._onSelect,ref:function(mt){return Q.editor=mt},role:Ke?null:Be,spellCheck:w&&this.props.spellCheck,style:Je,suppressContentEditableWarning:!0,tabIndex:this.props.tabIndex},o.createElement(f,Qe))))},O.prototype.componentDidMount=function(){this.setMode("edit"),B&&document.execCommand("AutoUrlDetect",!1,!1)},O.prototype.componentWillUpdate=function(Q){this._blockSelectEvents=!0,this._latestEditorState=Q.editorState},O.prototype.componentDidUpdate=function(){this._blockSelectEvents=!1,this._latestCommittedEditorState=this.props.editorState},O}(o.Component);j.defaultProps={blockRenderMap:I,blockRendererFn:h.thatReturnsNull,blockStyleFn:h.thatReturns(""),keyBindingFn:k,readOnly:!1,spellCheck:!1,stripPastedStyles:!1},H.exports=j},37898:function(H,P,i){"use strict";var x=i(27418),b=x||function(y){for(var L=1;L<arguments.length;L++){var T=arguments[L];for(var B in T)Object.prototype.hasOwnProperty.call(T,B)&&(y[B]=T[B])}return y};function v(y,L){if(!(y instanceof L))throw new TypeError("Cannot call a class as a function")}function A(y,L){if(!y)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return L&&(typeof L=="object"||typeof L=="function")?L:y}function p(y,L){if(typeof L!="function"&&L!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof L);y.prototype=Object.create(L&&L.prototype,{constructor:{value:y,enumerable:!1,writable:!0,configurable:!0}}),L&&(Object.setPrototypeOf?Object.setPrototypeOf(y,L):y.__proto__=L)}var I=i(42282),E=i(22146),d=i(67294),f=i(73935),g=i(65994),s=i(19051),a=i(54191),c=i(16633),o=i(62620),r=i(55258),u=i(79749),S=i(70746),C=i(73759),l=i(22045),h=10,D=function(L,T){return L.getAnchorKey()===T||L.getFocusKey()===T},k=function(y){p(L,y);function L(){return v(this,L),A(this,y.apply(this,arguments))}return L.prototype.shouldComponentUpdate=function(B){return this.props.block!==B.block||this.props.tree!==B.tree||this.props.direction!==B.direction||D(B.selection,B.block.getKey())&&B.forceSelection},L.prototype.componentDidMount=function(){var B=this.props.selection,w=B.getEndKey();if(!(!B.getHasFocus()||w!==this.props.block.getKey())){var z=f.findDOMNode(this),j=s.getScrollParent(z),U=u(j),O=void 0;if(j===window){var ie=r(z),Q=ie.y+ie.height,ue=S().height;O=Q-ue,O>0&&window.scrollTo(U.x,U.y+O+h)}else{z instanceof HTMLElement||C(!1);var Ee=z.offsetHeight+z.offsetTop,me=j.offsetHeight+U.y;O=Ee-me,O>0&&g.setTop(j,g.getTop(j)+O+h)}}},L.prototype._renderChildren=function(){var B=this,w=this.props.block,z=w.getKey(),j=w.getText(),U=this.props.tree.size-1,O=D(this.props.selection,z);return this.props.tree.map(function(ie,Q){var ue=ie.get("leaves"),Ee=ue.size-1,me=ue.map(function(et,Je){var Be=E.encode(z,Q,Je),He=et.get("start"),Qe=et.get("end");return d.createElement(I,{key:Be,offsetKey:Be,block:w,start:He,selection:O?B.props.selection:null,forceSelection:B.props.forceSelection,text:j.slice(He,Qe),styleSet:w.getInlineStyleAt(He),customStyleMap:B.props.customStyleMap,customStyleFn:B.props.customStyleFn,isLast:Q===U&&Je===Ee})}).toArray(),V=ie.get("decoratorKey");if(V==null||!B.props.decorator)return me;var we=l(B.props.decorator),Ne=we.getComponentForKey(V);if(!Ne)return me;var Me=we.getPropsForKey(V),Ke=E.encode(z,Q,0),qe=j.slice(ue.first().get("start"),ue.last().get("end")),it=c.getHTMLDirIfDifferent(a.getDirection(qe),B.props.direction);return d.createElement(Ne,b({},Me,{contentState:B.props.contentState,decoratedText:qe,dir:it,key:Ke,entityKey:w.getEntityAt(ie.get("start")),offsetKey:Ke}),me)}).toArray()},L.prototype.render=function(){var B=this.props,w=B.direction,z=B.offsetKey,j=o({"public/DraftStyleDefault/block":!0,"public/DraftStyleDefault/ltr":w==="LTR","public/DraftStyleDefault/rtl":w==="RTL"});return d.createElement("div",{"data-offset-key":z,className:j},this._renderChildren())},L}(d.Component);H.exports=k},33418:function(H,P,i){"use strict";var x=i(1065),b=i(42307),v=i(14289),A=i(25399),p=i(42128),I=i(42177),E=i(40258),d=20,f=!1,g=!1,s="",a={onBeforeInput:function(o,r){s=(s||"")+r.data},onCompositionStart:function(o){g=!0},onCompositionEnd:function(o){f=!1,g=!1,setTimeout(function(){f||a.resolveComposition(o)},d)},onKeyDown:function(o,r){if(!g){a.resolveComposition(o),o._onKeyDown(r);return}(r.which===A.RIGHT||r.which===A.LEFT)&&r.preventDefault()},onKeyPress:function(o,r){r.which===A.RETURN&&r.preventDefault()},resolveComposition:function(o){if(!g){f=!0;var r=s;s="";var u=v.set(o._latestEditorState,{inCompositionMode:!1}),S=u.getCurrentInlineStyle(),C=p(u.getCurrentContent(),u.getSelection()),l=!r||E(u)||S.size>0||C!==null;if(l&&o.restoreEditorDOM(),o.exitCurrentMode(),r){if(x.draft_handlebeforeinput_composed_text&&o.props.handleBeforeInput&&I(o.props.handleBeforeInput(r,u)))return;var h=b.replaceText(u.getCurrentContent(),u.getSelection(),r,S,C);o.update(v.push(u,h,"insert-characters"));return}l&&o.update(v.set(u,{nativelyRenderedContent:null,forceSelection:!0}))}}};H.exports=a},88795:function(H,P,i){"use strict";var x=i(27418),b=x||function(r){for(var u=1;u<arguments.length;u++){var S=arguments[u];for(var C in S)Object.prototype.hasOwnProperty.call(S,C)&&(r[C]=S[C])}return r};function v(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")}function A(r,u){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:r}function p(r,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(r,u):r.__proto__=u)}var I=i(37898),E=i(22146),d=i(14289),f=i(67294),g=i(62620),s=i(71108),a=i(22045),c=function(u,S,C,l){return g({"public/DraftStyleDefault/unorderedListItem":u==="unordered-list-item","public/DraftStyleDefault/orderedListItem":u==="ordered-list-item","public/DraftStyleDefault/reset":C,"public/DraftStyleDefault/depth0":S===0,"public/DraftStyleDefault/depth1":S===1,"public/DraftStyleDefault/depth2":S===2,"public/DraftStyleDefault/depth3":S===3,"public/DraftStyleDefault/depth4":S===4,"public/DraftStyleDefault/listLTR":l==="LTR","public/DraftStyleDefault/listRTL":l==="RTL"})},o=function(r){p(u,r);function u(){return v(this,u),A(this,r.apply(this,arguments))}return u.prototype.shouldComponentUpdate=function(C){var l=this.props.editorState,h=C.editorState,D=l.getDirectionMap(),k=h.getDirectionMap();if(D!==k)return!0;var y=l.getSelection().getHasFocus(),L=h.getSelection().getHasFocus();if(y!==L)return!0;var T=h.getNativelyRenderedContent(),B=l.isInCompositionMode(),w=h.isInCompositionMode();if(l===h||T!==null&&h.getCurrentContent()===T||B&&w)return!1;var z=l.getCurrentContent(),j=h.getCurrentContent(),U=l.getDecorator(),O=h.getDecorator();return B!==w||z!==j||U!==O||h.mustForceSelection()},u.prototype.render=function(){for(var C=this.props,l=C.blockRenderMap,h=C.blockRendererFn,D=C.blockStyleFn,k=C.customStyleMap,y=C.customStyleFn,L=C.editorState,T=C.editorKey,B=C.textDirectionality,w=L.getCurrentContent(),z=L.getSelection(),j=L.mustForceSelection(),U=L.getDecorator(),O=a(L.getDirectionMap()),ie=w.getBlocksAsArray(),Q=[],ue=null,Ee=null,me=0;me<ie.length;me++){var V=ie[me],we=V.getKey(),Ne=V.getType(),Me=h(V),Ke=void 0,qe=void 0,it=void 0;Me&&(Ke=Me.component,qe=Me.props,it=Me.editable);var et=B||O.get(we),Je=E.encode(we,0,0),Be={contentState:w,block:V,blockProps:qe,blockStyleFn:D,customStyleMap:k,customStyleFn:y,decorator:U,direction:et,forceSelection:j,key:we,offsetKey:Je,selection:z,tree:L.getBlockTree(we)},He=l.get(Ne)||l.get("unstyled"),Qe=He.wrapper,ot=He.element||l.get("unstyled").element,mt=V.getDepth(),rt="";if(D&&(rt=D(V)),ot==="li"){var Ce=Ee!==Qe||ue===null||mt>ue;rt=s(rt,c(Ne,mt,Ce,et))}var $e=Ke||I,Ft={className:rt,"data-block":!0,"data-editor":T,"data-offset-key":Je,key:we};it!==void 0&&(Ft=b({},Ft,{contentEditable:it,suppressContentEditableWarning:!0}));var Et=f.createElement(ot,Ft,f.createElement($e,Be));Q.push({block:Et,wrapperTemplate:Qe,key:we,offsetKey:Je}),Qe?ue=V.getDepth():ue=null,Ee=Qe}for(var De=[],ne=0;ne<Q.length;){var _=Q[ne];if(_.wrapperTemplate){var le=[];do le.push(Q[ne].block),ne++;while(ne<Q.length&&Q[ne].wrapperTemplate===_.wrapperTemplate);var se=f.cloneElement(_.wrapperTemplate,{key:_.key+"-wrap","data-offset-key":_.offsetKey},le);De.push(se)}else De.push(_.block),ne++}return f.createElement("div",{"data-contents":"true"},De)},u}(f.Component);H.exports=o},87791:function(H,P,i){"use strict";var x=i(88795);H.exports=x},61494:function(H,P,i){"use strict";var x=i(44891),b=i(42307),v=i(14289),A=i(69270),p=i(21738),I=i(94486),E=i(42177),d=i(22045);function f(c,o){var r=null,u=null;if(typeof document.caretRangeFromPoint=="function"){var S=document.caretRangeFromPoint(c.x,c.y);r=S.startContainer,u=S.startOffset}else if(c.rangeParent)r=c.rangeParent,u=c.rangeOffset;else return null;r=d(r),u=d(u);var C=d(A(r));return I(o,C,u,C,u)}var g={onDragEnd:function(o){o.exitCurrentMode()},onDrop:function(o,r){var u=new x(r.nativeEvent.dataTransfer),S=o._latestEditorState,C=f(r.nativeEvent,S);if(r.preventDefault(),o.exitCurrentMode(),C!=null){var l=u.getFiles();if(l.length>0){if(o.props.handleDroppedFiles&&E(o.props.handleDroppedFiles(C,l)))return;p(l,function(D){D&&o.update(a(S,C,D))});return}var h=o._internalDrag?"internal":"external";if(!(o.props.handleDrop&&E(o.props.handleDrop(C,u,h)))){if(o._internalDrag){o.update(s(S,C));return}o.update(a(S,C,u.getText()))}}}};function s(c,o){var r=b.moveText(c.getCurrentContent(),c.getSelection(),o);return v.push(c,r,"insert-fragment")}function a(c,o,r){var u=b.insertText(c.getCurrentContent(),o,r,c.getCurrentInlineStyle());return v.push(c,u,"insert-fragment")}H.exports=g},19394:function(H,P,i){"use strict";var x=i(26396),b=i(43421),v=i(6155),A=i(69328),p=i(88922),I=i(39499),E=i(80981),d=i(62186),f=i(29971),g=i(46397),s=i(6089),a=i(14507),c={onBeforeInput:x,onBlur:b,onCompositionStart:v,onCopy:A,onCut:p,onDragOver:I,onDragStart:E,onFocus:d,onInput:f,onKeyDown:g,onPaste:s,onSelect:a};H.exports=c},42282:function(H,P,i){"use strict";var x=i(27418);function b(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function v(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function A(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var p=i(80052),I=i(67294),E=i(73935),d=i(73759),f=i(45412),g=function(s){A(a,s);function a(){return b(this,a),v(this,s.apply(this,arguments))}return a.prototype._setSelection=function(){var o=this.props.selection;if(!(o==null||!o.getHasFocus())){var r=this.props,u=r.block,S=r.start,C=r.text,l=u.getKey(),h=S+C.length;if(!!o.hasEdgeWithin(l,S,h)){var D=E.findDOMNode(this);D||d(!1);var k=D.firstChild;k||d(!1);var y=void 0;k.nodeType===Node.TEXT_NODE?y=k:k.tagName==="BR"?y=D:(y=k.firstChild,y||d(!1)),f(o,y,l,S,h)}}},a.prototype.shouldComponentUpdate=function(o){var r=E.findDOMNode(this.leaf);return r||d(!1),r.textContent!==o.text||o.styleSet!==this.props.styleSet||o.forceSelection},a.prototype.componentDidUpdate=function(){this._setSelection()},a.prototype.componentDidMount=function(){this._setSelection()},a.prototype.render=function(){var o=this,r=this.props.block,u=this.props.text;u.endsWith(`
`)&&this.props.isLast&&(u+=`
`);var S=this.props,C=S.customStyleMap,l=S.customStyleFn,h=S.offsetKey,D=S.styleSet,k=D.reduce(function(L,T){var B={},w=C[T];return w!==void 0&&L.textDecoration!==w.textDecoration&&(B.textDecoration=[L.textDecoration,w.textDecoration].join(" ").trim()),x(L,w,B)},{});if(l){var y=l(D,r);k=x(k,y)}return I.createElement("span",{"data-offset-key":h,ref:function(T){return o.leaf=T},style:k},I.createElement(p,null,u))},a}(I.Component);H.exports=g},28094:function(H,P,i){"use strict";function x(E,d){if(!(E instanceof d))throw new TypeError("Cannot call a class as a function")}function b(E,d){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:E}function v(E,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);E.prototype=Object.create(d&&d.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(E,d):E.__proto__=d)}var A=i(67294),p=i(62620),I=function(E){v(d,E);function d(){return x(this,d),b(this,E.apply(this,arguments))}return d.prototype.shouldComponentUpdate=function(g){return this.props.text!==g.text||this.props.editorState.getSelection().getHasFocus()!==g.editorState.getSelection().getHasFocus()},d.prototype.render=function(){var g=this.props.editorState.getSelection().getHasFocus(),s=p({"public/DraftEditorPlaceholder/root":!0,"public/DraftEditorPlaceholder/hasFocus":g}),a={whiteSpace:"pre-wrap"};return A.createElement("div",{className:s},A.createElement("div",{className:p("public/DraftEditorPlaceholder/inner"),id:this.props.accessibilityID,style:a},this.props.text))},d}(A.Component);H.exports=I},80052:function(H,P,i){"use strict";function x(c,o){if(!(c instanceof o))throw new TypeError("Cannot call a class as a function")}function b(c,o){if(!c)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o&&(typeof o=="object"||typeof o=="function")?o:c}function v(c,o){if(typeof o!="function"&&o!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof o);c.prototype=Object.create(o&&o.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}}),o&&(Object.setPrototypeOf?Object.setPrototypeOf(c,o):c.__proto__=o)}var A=i(67294),p=i(73935),I=i(4856),E=i(73759),d=I.isBrowser("IE <= 11");function f(c){return d?c.textContent===`
`:c.tagName==="BR"}var g=d?A.createElement("span",{key:"A","data-text":"true"},`
`):A.createElement("br",{key:"A","data-text":"true"}),s=d?A.createElement("span",{key:"B","data-text":"true"},`
`):A.createElement("br",{key:"B","data-text":"true"}),a=function(c){v(o,c);function o(r){x(this,o);var u=b(this,c.call(this,r));return u._forceFlag=!1,u}return o.prototype.shouldComponentUpdate=function(u){var S=p.findDOMNode(this),C=u.children==="";return S instanceof Element||E(!1),C?!f(S):S.textContent!==u.children},o.prototype.componentDidMount=function(){this._forceFlag=!this._forceFlag},o.prototype.componentDidUpdate=function(){this._forceFlag=!this._forceFlag},o.prototype.render=function(){return this.props.children===""?this._forceFlag?g:s:A.createElement("span",{key:this._forceFlag?"A":"B","data-text":"true"},this.props.children)},o}(A.Component);H.exports=a},82222:function(H,P,i){"use strict";var x=i(27418),b=x||function(s){for(var a=1;a<arguments.length;a++){var c=arguments[a];for(var o in c)Object.prototype.hasOwnProperty.call(c,o)&&(s[o]=c[o])}return s},v=i(39006),A=i(43393),p=i(73759),I=A.Map,E=I(),d=0;function f(s,a){console.warn("WARNING: "+s+` will be deprecated soon!
Please use "`+a+'" instead.')}var g={getLastCreatedEntityKey:function(){return f("DraftEntity.getLastCreatedEntityKey","contentState.getLastCreatedEntityKey"),g.__getLastCreatedEntityKey()},create:function(a,c,o){return f("DraftEntity.create","contentState.createEntity"),g.__create(a,c,o)},add:function(a){return f("DraftEntity.add","contentState.addEntity"),g.__add(a)},get:function(a){return f("DraftEntity.get","contentState.getEntity"),g.__get(a)},mergeData:function(a,c){return f("DraftEntity.mergeData","contentState.mergeEntityData"),g.__mergeData(a,c)},replaceData:function(a,c){return f("DraftEntity.replaceData","contentState.replaceEntityData"),g.__replaceData(a,c)},__getLastCreatedEntityKey:function(){return""+d},__create:function(a,c,o){return g.__add(new v({type:a,mutability:c,data:o||{}}))},__add:function(a){var c=""+ ++d;return E=E.set(c,a),c},__get:function(a){var c=E.get(a);return c||p(!1),c},__mergeData:function(a,c){var o=g.__get(a),r=b({},o.getData(),c),u=o.set("data",r);return E=E.set(a,u),u},__replaceData:function(a,c){var o=g.__get(a),r=o.set("data",c);return E=E.set(a,r),r}};H.exports=g},39006:function(H,P,i){"use strict";function x(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function b(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function v(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var A=i(43393),p=A.Record,I=p({type:"TOKEN",mutability:"IMMUTABLE",data:Object}),E=function(d){v(f,d);function f(){return x(this,f),b(this,d.apply(this,arguments))}return f.prototype.getType=function(){return this.get("type")},f.prototype.getMutability=function(){return this.get("mutability")},f.prototype.getData=function(){return this.get("data")},f}(I);H.exports=E},5195:function(H){"use strict";var P={getRemovalRange:function(x,b,v,A,p){var I=v.split(" ");I=I.map(function(u,S){if(p==="forward"){if(S>0)return" "+u}else if(S<I.length-1)return u+" ";return u});for(var E=A,d,f,g=null,s=null,a=0;a<I.length;a++){if(f=I[a],d=E+f.length,x<d&&E<b)g!==null||(g=E),s=d;else if(g!==null)break;E=d}var c=A+v.length,o=g===A,r=s===c;return(!o&&r||o&&!r)&&(p==="forward"?s!==c&&s++:g!==A&&g--),{start:g,end:s}}};H.exports=P},44247:function(H){"use strict";var P={draft_killswitch_allow_nontextnodes:!1,draft_segmented_entities_behavior:!1,draft_handlebeforeinput_composed_text:!1,draft_tree_data_support:!1};H.exports=P},1065:function(H,P,i){"use strict";var x=i(44247);H.exports=x},97432:function(H){"use strict";H.exports={logSelectionStateFailure:function(){return null}}},42307:function(H,P,i){"use strict";var x=i(4516),b=i(13483),v=i(1065),A=i(43393),p=i(68750),I=i(81446),E=i(88687),d=i(54542),f=i(18467),g=i(73759),s=i(57429),a=i(14017),c=i(54879),o=i(36043),r=A.OrderedSet,u={replaceText:function(C,l,h,D,k){var y=a(C,l),L=c(y,l),T=x.create({style:D||r(),entity:k||null});return f(L,L.getSelectionAfter(),h,T)},insertText:function(C,l,h,D,k){return l.isCollapsed()||g(!1),u.replaceText(C,l,h,D,k)},moveText:function(C,l,h){var D=E(C,l),k=u.removeRange(C,l,"backward");return u.replaceWithFragment(k,h,D)},replaceWithFragment:function(C,l,h){var D=a(C,l),k=c(D,l);return d(k,k.getSelectionAfter(),h)},removeRange:function(C,l,h){var D=void 0,k=void 0,y=void 0,L=void 0;l.getIsBackward()&&(l=l.merge({anchorKey:l.getFocusKey(),anchorOffset:l.getFocusOffset(),focusKey:l.getAnchorKey(),focusOffset:l.getAnchorOffset(),isBackward:!1})),D=l.getAnchorKey(),k=l.getFocusKey(),y=C.getBlockForKey(D),L=C.getBlockForKey(k);var T=l.getStartOffset(),B=l.getEndOffset(),w=y.getEntityAt(T),z=L.getEntityAt(B-1);if(D===k&&w&&w===z){var j=I(C.getEntityMap(),y,L,l,h);return c(C,j)}var U=l;v.draft_segmented_entities_behavior&&(U=I(C.getEntityMap(),y,L,l,h));var O=a(C,U);return c(O,U)},splitBlock:function(C,l){var h=a(C,l),D=c(h,l);return o(D,D.getSelectionAfter())},applyInlineStyle:function(C,l,h){return b.add(C,l,h)},removeInlineStyle:function(C,l,h){return b.remove(C,l,h)},setBlockType:function(C,l,h){return s(C,l,function(D){return D.merge({type:h,depth:0})})},setBlockData:function(C,l,h){return s(C,l,function(D){return D.merge({data:h})})},mergeBlockData:function(C,l,h){return s(C,l,function(D){return D.merge({data:D.getData().merge(h)})})},applyEntity:function(C,l,h){var D=a(C,l);return p(D,l,h)}};H.exports=u},22146:function(H){"use strict";var P="-",i={encode:function(b,v,A){return b+P+v+P+A},decode:function(b){var v=b.split(P),A=v[0],p=v[1],I=v[2];return{blockKey:A,decoratorKey:parseInt(p,10),leafKey:parseInt(I,10)}}};H.exports=i},45712:function(H,P,i){"use strict";var x=i(27418),b=x||function(S){for(var C=1;C<arguments.length;C++){var l=arguments[C];for(var h in l)Object.prototype.hasOwnProperty.call(l,h)&&(S[h]=l[h])}return S},v=i(4516),A=i(2641),p=i(67953),I=i(1065),E=i(43393),d=i(67841),f=i(25027),g=i(69769),s=i(55283),a=E.List,c=E.Repeat,o=I.draft_tree_data_support,r=o?p:A,u={processHTML:function(C,l){return d(C,g,l)},processText:function(C,l,h){return C.reduce(function(D,k,y){k=s(k);var L=f(),T={key:L,type:h,text:k,characterList:a(c(l,k.length))};if(o&&y!==0){var B=y-1,w=D[B]=D[B].merge({nextSibling:L});T=b({},T,{prevSibling:w.getKey()})}return D.push(new r(T)),D},[])}};H.exports=u},73932:function(H,P,i){"use strict";var x=i(65724),b=x.getPunctuation(),v="['\u2018\u2019]",A="\\s|(?![_])"+b,p="^(?:"+A+")*(?:"+v+"|(?!"+A+").)*(?:(?!"+A+").)",I=new RegExp(p),E="(?:(?!"+A+").)(?:"+v+"|(?!"+A+").)*(?:"+A+")*$",d=new RegExp(E);function f(s,a){var c=a?d.exec(s):I.exec(s);return c?c[0]:s}var g={getBackward:function(a){return f(a,!0)},getForward:function(a){return f(a,!1)}};H.exports=g},86155:function(H){"use strict";var P={stringify:function(x){return"_"+String(x)},unstringify:function(x){return x.slice(1)}};H.exports=P},68957:function(H,P,i){"use strict";var x=i(27418),b=x||function(d){for(var f=1;f<arguments.length;f++){var g=arguments[f];for(var s in g)Object.prototype.hasOwnProperty.call(g,s)&&(d[s]=g[s])}return d},v=i(73759),A=function(f,g){for(var s=[].concat(f).reverse();s.length;){var a=s.pop();g(a);var c=a.children;Array.isArray(c)||v(!1),s=s.concat([].concat(c.reverse()))}},p=function(f){if(!(f&&f.type))return!1;var g=f.type;return g==="unordered-list-item"||g==="ordered-list-item"},I=function(f){Array.isArray(f.children)&&(f.children=f.children.map(function(g){return g.type===f.type?b({},g,{depth:(f.depth||0)+1}):g}))},E={fromRawTreeStateToRawState:function(f){var g=f.blocks,s=[];return Array.isArray(g)||v(!1),!Array.isArray(g)||!g.length?f:(A(g,function(a){var c=b({},a);p(a)&&(c.depth=c.depth||0,I(a)),delete c.children,s.push(c)}),f.blocks=s,b({},f,{blocks:s}))},fromRawStateToRawTreeState:function(f){var g={},s=[];return f.blocks.forEach(function(a){var c=p(a),o=a.depth||0,r=b({},a,{children:[]});if(!c){g={},s.push(r);return}if(g[o]=r,o>0){var u=g[o-1];u||v(!1),u.children.push(r);return}s.push(r)}),b({},f,{blocks:s})}};H.exports=E},33337:function(H,P,i){"use strict";var x=i(43393),b=i(7902),v=i(22045),A=x.OrderedMap,p,I={getDirectionMap:function(d,f){p?p.reset():p=new b;var g=d.getBlockMap(),s=g.valueSeq().map(function(c){return v(p).getDirection(c.getText())}),a=A(g.keySeq().zip(s));return f!=null&&x.is(f,a)?f:a}};H.exports=I},14289:function(H,P,i){"use strict";var x=i(27418),b=x||function(y){for(var L=1;L<arguments.length;L++){var T=arguments[L];for(var B in T)Object.prototype.hasOwnProperty.call(T,B)&&(y[B]=T[B])}return y};function v(y,L){if(!(y instanceof L))throw new TypeError("Cannot call a class as a function")}var A=i(34365),p=i(66912),I=i(33337),E=i(43393),d=i(25110),f=E.OrderedSet,g=E.Record,s=E.Stack,a={allowUndo:!0,currentContent:null,decorator:null,directionMap:null,forceSelection:!1,inCompositionMode:!1,inlineStyleOverride:null,lastChangeType:null,nativelyRenderedContent:null,redoStack:s(),selection:null,treeMap:null,undoStack:s()},c=g(a),o=function(){y.createEmpty=function(T){return y.createWithContent(p.createFromText(""),T)},y.createWithContent=function(T,B){var w=T.getBlockMap().first().getKey();return y.create({currentContent:T,undoStack:s(),redoStack:s(),decorator:B||null,selection:d.createEmpty(w)})},y.create=function(T){var B=T.currentContent,w=T.decorator,z=b({},T,{treeMap:u(B,w),directionMap:I.getDirectionMap(B)});return new y(new c(z))},y.set=function(T,B){var w=T.getImmutable().withMutations(function(z){var j=z.get("decorator"),U=j;B.decorator===null?U=null:B.decorator&&(U=B.decorator);var O=B.currentContent||T.getCurrentContent();if(U!==j){var ie=z.get("treeMap"),Q;U&&j?Q=C(O,O.getBlockMap(),ie,U,j):Q=u(O,U),z.merge({decorator:U,treeMap:Q,nativelyRenderedContent:null});return}var ue=T.getCurrentContent();O!==ue&&z.set("treeMap",S(T,O.getBlockMap(),O.getEntityMap(),U)),z.merge(B)});return new y(w)},y.prototype.toJS=function(){return this.getImmutable().toJS()},y.prototype.getAllowUndo=function(){return this.getImmutable().get("allowUndo")},y.prototype.getCurrentContent=function(){return this.getImmutable().get("currentContent")},y.prototype.getUndoStack=function(){return this.getImmutable().get("undoStack")},y.prototype.getRedoStack=function(){return this.getImmutable().get("redoStack")},y.prototype.getSelection=function(){return this.getImmutable().get("selection")},y.prototype.getDecorator=function(){return this.getImmutable().get("decorator")},y.prototype.isInCompositionMode=function(){return this.getImmutable().get("inCompositionMode")},y.prototype.mustForceSelection=function(){return this.getImmutable().get("forceSelection")},y.prototype.getNativelyRenderedContent=function(){return this.getImmutable().get("nativelyRenderedContent")},y.prototype.getLastChangeType=function(){return this.getImmutable().get("lastChangeType")},y.prototype.getInlineStyleOverride=function(){return this.getImmutable().get("inlineStyleOverride")},y.setInlineStyleOverride=function(T,B){return y.set(T,{inlineStyleOverride:B})},y.prototype.getCurrentInlineStyle=function(){var T=this.getInlineStyleOverride();if(T!=null)return T;var B=this.getCurrentContent(),w=this.getSelection();return w.isCollapsed()?h(B,w):D(B,w)},y.prototype.getBlockTree=function(T){return this.getImmutable().getIn(["treeMap",T])},y.prototype.isSelectionAtStartOfContent=function(){var T=this.getCurrentContent().getBlockMap().first().getKey();return this.getSelection().hasEdgeWithin(T,0,0)},y.prototype.isSelectionAtEndOfContent=function(){var T=this.getCurrentContent(),B=T.getBlockMap(),w=B.last(),z=w.getLength();return this.getSelection().hasEdgeWithin(w.getKey(),z,z)},y.prototype.getDirectionMap=function(){return this.getImmutable().get("directionMap")},y.acceptSelection=function(T,B){return r(T,B,!1)},y.forceSelection=function(T,B){return B.getHasFocus()||(B=B.set("hasFocus",!0)),r(T,B,!0)},y.moveSelectionToEnd=function(T){var B=T.getCurrentContent(),w=B.getLastBlock(),z=w.getKey(),j=w.getLength();return y.acceptSelection(T,new d({anchorKey:z,anchorOffset:j,focusKey:z,focusOffset:j,isBackward:!1}))},y.moveFocusToEnd=function(T){var B=y.moveSelectionToEnd(T);return y.forceSelection(B,B.getSelection())},y.push=function(T,B,w){if(T.getCurrentContent()===B)return T;var z=w!=="insert-characters",j=I.getDirectionMap(B,T.getDirectionMap());if(!T.getAllowUndo())return y.set(T,{currentContent:B,directionMap:j,lastChangeType:w,selection:B.getSelectionAfter(),forceSelection:z,inlineStyleOverride:null});var U=T.getSelection(),O=T.getCurrentContent(),ie=T.getUndoStack(),Q=B;U!==O.getSelectionAfter()||l(T,w)?(ie=ie.push(O),Q=Q.set("selectionBefore",U)):(w==="insert-characters"||w==="backspace-character"||w==="delete-character")&&(Q=Q.set("selectionBefore",O.getSelectionBefore()));var ue=T.getInlineStyleOverride(),Ee=["adjust-depth","change-block-type","split-block"];Ee.indexOf(w)===-1&&(ue=null);var me={currentContent:Q,directionMap:j,undoStack:ie,redoStack:s(),lastChangeType:w,selection:B.getSelectionAfter(),forceSelection:z,inlineStyleOverride:ue};return y.set(T,me)},y.undo=function(T){if(!T.getAllowUndo())return T;var B=T.getUndoStack(),w=B.peek();if(!w)return T;var z=T.getCurrentContent(),j=I.getDirectionMap(w,T.getDirectionMap());return y.set(T,{currentContent:w,directionMap:j,undoStack:B.shift(),redoStack:T.getRedoStack().push(z),forceSelection:!0,inlineStyleOverride:null,lastChangeType:"undo",nativelyRenderedContent:null,selection:z.getSelectionBefore()})},y.redo=function(T){if(!T.getAllowUndo())return T;var B=T.getRedoStack(),w=B.peek();if(!w)return T;var z=T.getCurrentContent(),j=I.getDirectionMap(w,T.getDirectionMap());return y.set(T,{currentContent:w,directionMap:j,undoStack:T.getUndoStack().push(z),redoStack:B.shift(),forceSelection:!0,inlineStyleOverride:null,lastChangeType:"redo",nativelyRenderedContent:null,selection:w.getSelectionAfter()})};function y(L){v(this,y),this._immutable=L}return y.prototype.getImmutable=function(){return this._immutable},y}();function r(y,L,T){return o.set(y,{selection:L,forceSelection:T,nativelyRenderedContent:null,inlineStyleOverride:null})}function u(y,L){return y.getBlockMap().map(function(T){return A.generate(y,T,L)}).toOrderedMap()}function S(y,L,T,B){var w=y.getCurrentContent().set("entityMap",T),z=w.getBlockMap(),j=y.getImmutable().get("treeMap");return j.merge(L.toSeq().filter(function(U,O){return U!==z.get(O)}).map(function(U){return A.generate(w,U,B)}))}function C(y,L,T,B,w){return T.merge(L.toSeq().filter(function(z){return B.getDecorations(z,y)!==w.getDecorations(z,y)}).map(function(z){return A.generate(y,z,B)}))}function l(y,L){var T=y.getLastChangeType();return L!==T||L!=="insert-characters"&&L!=="backspace-character"&&L!=="delete-character"}function h(y,L){var T=L.getStartKey(),B=L.getStartOffset(),w=y.getBlockForKey(T);return B>0?w.getInlineStyleAt(B-1):w.getLength()?w.getInlineStyleAt(0):k(y,T)}function D(y,L){var T=L.getStartKey(),B=L.getStartOffset(),w=y.getBlockForKey(T);return B<w.getLength()?w.getInlineStyleAt(B):B>0?w.getInlineStyleAt(B-1):k(y,T)}function k(y,L){var T=y.getBlockMap().reverse().skipUntil(function(B,w){return w===L}).skip(1).skipUntil(function(B,w){return B.getLength()}).first();return T?T.getInlineStyleAt(T.getLength()-1):f()}H.exports=o},47387:function(H,P,i){"use strict";var x=i(4856),b=x.isPlatform("Mac OS X"),v={isCtrlKeyCommand:function(p){return!!p.ctrlKey&&!p.altKey},isOptionKeyCommand:function(p){return b&&p.altKey},hasCommandModifier:function(p){return b?!!p.metaKey&&!p.altKey:v.isCtrlKeyCommand(p)}};H.exports=v},41947:function(H,P,i){"use strict";var x=i(42307),b=i(14289),v=i(25110),A=i(1665),p=i(22045),I={currentBlockContainsLink:function(d){var f=d.getSelection(),g=d.getCurrentContent(),s=g.getEntityMap();return g.getBlockForKey(f.getAnchorKey()).getCharacterList().slice(f.getStartOffset(),f.getEndOffset()).some(function(a){var c=a.getEntity();return!!c&&s.__get(c).getType()==="LINK"})},getCurrentBlockType:function(d){var f=d.getSelection();return d.getCurrentContent().getBlockForKey(f.getStartKey()).getType()},getDataObjectForLinkURL:function(d){return{url:d.toString()}},handleKeyCommand:function(d,f){switch(f){case"bold":return I.toggleInlineStyle(d,"BOLD");case"italic":return I.toggleInlineStyle(d,"ITALIC");case"underline":return I.toggleInlineStyle(d,"UNDERLINE");case"code":return I.toggleCode(d);case"backspace":case"backspace-word":case"backspace-to-start-of-line":return I.onBackspace(d);case"delete":case"delete-word":case"delete-to-end-of-block":return I.onDelete(d);default:return null}},insertSoftNewline:function(d){var f=x.insertText(d.getCurrentContent(),d.getSelection(),`
`,d.getCurrentInlineStyle(),null),g=b.push(d,f,"insert-characters");return b.forceSelection(g,f.getSelectionAfter())},onBackspace:function(d){var f=d.getSelection();if(!f.isCollapsed()||f.getAnchorOffset()||f.getFocusOffset())return null;var g=d.getCurrentContent(),s=f.getStartKey(),a=g.getBlockBefore(s);if(a&&a.getType()==="atomic"){var c=g.getBlockMap().delete(a.getKey()),o=g.merge({blockMap:c,selectionAfter:f});if(o!==g)return b.push(d,o,"remove-range")}var r=I.tryToRemoveBlockStyle(d);return r?b.push(d,r,"change-block-type"):null},onDelete:function(d){var f=d.getSelection();if(!f.isCollapsed())return null;var g=d.getCurrentContent(),s=f.getStartKey(),a=g.getBlockForKey(s),c=a.getLength();if(f.getStartOffset()<c)return null;var o=g.getBlockAfter(s);if(!o||o.getType()!=="atomic")return null;var r=f.merge({focusKey:o.getKey(),focusOffset:o.getLength()}),u=x.removeRange(g,r,"forward");return u!==g?b.push(d,u,"remove-range"):null},onTab:function(d,f,g){var s=f.getSelection(),a=s.getAnchorKey();if(a!==s.getFocusKey())return f;var c=f.getCurrentContent(),o=c.getBlockForKey(a),r=o.getType();if(r!=="unordered-list-item"&&r!=="ordered-list-item")return f;d.preventDefault();var u=c.getBlockBefore(a);if(!u)return f;var S=u.getType();if(S!=="unordered-list-item"&&S!=="ordered-list-item")return f;var C=o.getDepth();if(!d.shiftKey&&C===g)return f;g=Math.min(u.getDepth()+1,g);var l=A(c,s,d.shiftKey?-1:1,g);return b.push(f,l,"adjust-depth")},toggleBlockType:function(d,f){var g=d.getSelection(),s=g.getStartKey(),a=g.getEndKey(),c=d.getCurrentContent(),o=g;if(s!==a&&g.getEndOffset()===0){var r=p(c.getBlockBefore(a));a=r.getKey(),o=o.merge({anchorKey:s,anchorOffset:g.getStartOffset(),focusKey:a,focusOffset:r.getLength(),isBackward:!1})}var u=c.getBlockMap().skipWhile(function(C,l){return l!==s}).reverse().skipWhile(function(C,l){return l!==a}).some(function(C){return C.getType()==="atomic"});if(u)return d;var S=c.getBlockForKey(s).getType()===f?"unstyled":f;return b.push(d,x.setBlockType(c,o,S),"change-block-type")},toggleCode:function(d){var f=d.getSelection(),g=f.getAnchorKey(),s=f.getFocusKey();return f.isCollapsed()||g!==s?I.toggleBlockType(d,"code-block"):I.toggleInlineStyle(d,"CODE")},toggleInlineStyle:function(d,f){var g=d.getSelection(),s=d.getCurrentInlineStyle();if(g.isCollapsed())return b.setInlineStyleOverride(d,s.has(f)?s.remove(f):s.add(f));var a=d.getCurrentContent(),c;return s.has(f)?c=x.removeInlineStyle(a,g,f):c=x.applyInlineStyle(a,g,f),b.push(d,c,"change-inline-style")},toggleLink:function(d,f,g){var s=x.applyEntity(d.getCurrentContent(),f,g);return b.push(d,s,"apply-entity")},tryToRemoveBlockStyle:function(d){var f=d.getSelection(),g=f.getAnchorOffset();if(f.isCollapsed()&&g===0){var s=f.getAnchorKey(),a=d.getCurrentContent(),c=a.getBlockForKey(s),o=a.getFirstBlock();if(c.getLength()>0&&c!==o)return null;var r=c.getType(),u=a.getBlockBefore(s);if(r==="code-block"&&u&&u.getType()==="code-block"&&u.getLength()!==0)return null;if(r!=="unstyled")return x.setBlockType(a,f,"unstyled")}return null}};H.exports=I},83751:function(H,P,i){"use strict";var x=i(42307),b=i(14289),v=i(88687),A=i(22045),p=null,I={cut:function(d){var f=d.getCurrentContent(),g=d.getSelection(),s=null;if(g.isCollapsed()){var a=g.getAnchorKey(),c=f.getBlockForKey(a).getLength();if(c===g.getAnchorOffset())return d;s=g.set("focusOffset",c)}else s=g;s=A(s),p=v(f,s);var o=x.removeRange(f,s,"forward");return o===f?d:b.push(d,o,"remove-range")},paste:function(d){if(!p)return d;var f=x.replaceWithFragment(d.getCurrentContent(),d.getSelection(),p);return b.push(d,f,"insert-fragment")}};H.exports=I},25110:function(H,P,i){"use strict";function x(f,g){if(!(f instanceof g))throw new TypeError("Cannot call a class as a function")}function b(f,g){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:f}function v(f,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);f.prototype=Object.create(g&&g.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(f,g):f.__proto__=g)}var A=i(43393),p=A.Record,I={anchorKey:"",anchorOffset:0,focusKey:"",focusOffset:0,isBackward:!1,hasFocus:!1},E=p(I),d=function(f){v(g,f);function g(){return x(this,g),b(this,f.apply(this,arguments))}return g.prototype.serialize=function(){return"Anchor: "+this.getAnchorKey()+":"+this.getAnchorOffset()+", Focus: "+this.getFocusKey()+":"+this.getFocusOffset()+", Is Backward: "+String(this.getIsBackward())+", Has Focus: "+String(this.getHasFocus())},g.prototype.getAnchorKey=function(){return this.get("anchorKey")},g.prototype.getAnchorOffset=function(){return this.get("anchorOffset")},g.prototype.getFocusKey=function(){return this.get("focusKey")},g.prototype.getFocusOffset=function(){return this.get("focusOffset")},g.prototype.getIsBackward=function(){return this.get("isBackward")},g.prototype.getHasFocus=function(){return this.get("hasFocus")},g.prototype.hasEdgeWithin=function(a,c,o){var r=this.getAnchorKey(),u=this.getFocusKey();if(r===u&&r===a){var S=this.getStartOffset(),C=this.getEndOffset();return c<=C&&S<=o}if(a!==r&&a!==u)return!1;var l=a===r?this.getAnchorOffset():this.getFocusOffset();return c<=l&&o>=l},g.prototype.isCollapsed=function(){return this.getAnchorKey()===this.getFocusKey()&&this.getAnchorOffset()===this.getFocusOffset()},g.prototype.getStartKey=function(){return this.getIsBackward()?this.getFocusKey():this.getAnchorKey()},g.prototype.getStartOffset=function(){return this.getIsBackward()?this.getFocusOffset():this.getAnchorOffset()},g.prototype.getEndKey=function(){return this.getIsBackward()?this.getAnchorKey():this.getFocusKey()},g.prototype.getEndOffset=function(){return this.getIsBackward()?this.getAnchorOffset():this.getFocusOffset()},g.createEmpty=function(a){return new g({anchorKey:a,anchorOffset:0,focusKey:a,focusOffset:0,isBackward:!1,hasFocus:!1})},g}(E);H.exports=d},1665:function(H){"use strict";function P(i,x,b,v){var A=x.getStartKey(),p=x.getEndKey(),I=i.getBlockMap(),E=I.toSeq().skipUntil(function(d,f){return f===A}).takeUntil(function(d,f){return f===p}).concat([[p,I.get(p)]]).map(function(d){var f=d.getDepth()+b;return f=Math.max(0,Math.min(f,v)),d.set("depth",f)});return I=I.merge(E),i.merge({blockMap:I,selectionBefore:x,selectionAfter:x})}H.exports=P},2835:function(H,P,i){"use strict";var x=i(4516);function b(v,A,p,I){for(var E=v.getCharacterList();A<p;)E=E.set(A,x.applyEntity(E.get(A),I)),A++;return v.set("characterList",E)}H.exports=b},68750:function(H,P,i){"use strict";var x=i(43393),b=i(2835);function v(A,p,I){var E=A.getBlockMap(),d=p.getStartKey(),f=p.getStartOffset(),g=p.getEndKey(),s=p.getEndOffset(),a=E.skipUntil(function(c,o){return o===d}).takeUntil(function(c,o){return o===g}).toOrderedMap().merge(x.OrderedMap([[g,E.get(g)]])).map(function(c,o){var r=o===d?f:0,u=o===g?s:c.getLength();return b(c,r,u,I)});return A.merge({blockMap:E.merge(a),selectionBefore:p,selectionAfter:p})}H.exports=v},79981:function(H,P,i){"use strict";var x=i(27418),b=x||function(o){for(var r=1;r<arguments.length;r++){var u=arguments[r];for(var S in u)Object.prototype.hasOwnProperty.call(u,S)&&(o[S]=u[S])}return o},v=i(2641),A=i(67953),p=i(86155),I=i(56265),E=i(31487),d=i(73759),f=function(r,u){return{key:r.getKey(),text:r.getText(),type:r.getType(),depth:r.getDepth(),inlineStyleRanges:E(r),entityRanges:I(r,u),data:r.getData().toObject()}},g=function(r,u,S,C){if(r instanceof v){S.push(f(r,u));return}r instanceof A||d(!1);var l=r.getParentKey(),h=C[r.getKey()]=b({},f(r,u),{children:[]});if(l){C[l].children.push(h);return}S.push(h)},s=function(r,u){var S=u.entityMap,C=[],l={},h={},D=0;return r.getBlockMap().forEach(function(k){k.findEntityRanges(function(y){return y.getEntity()!==null},function(y){var L=k.getEntityAt(y),T=p.stringify(L);h[T]||(h[T]=L,S[T]=""+D,D++)}),g(k,S,C,l)}),{blocks:C,entityMap:S}},a=function(r,u){var S=u.blocks,C=u.entityMap,l={};return Object.keys(C).forEach(function(h,D){var k=r.getEntity(p.unstringify(h));l[D]={type:k.getType(),mutability:k.getMutability(),data:k.getData()}}),{blocks:S,entityMap:l}},c=function(r){var u={entityMap:{},blocks:[]};return u=s(r,u),u=a(r,u),u};H.exports=c},67841:function(H,P,i){"use strict";var x=v||function(ne){for(var _=1;_<arguments.length;_++){var le=arguments[_];for(var se in le)Object.prototype.hasOwnProperty.call(le,se)&&(ne[se]=le[se])}return ne},b,v=i(27418);function A(ne,_,le){return _ in ne?Object.defineProperty(ne,_,{value:le,enumerable:!0,configurable:!0,writable:!0}):ne[_]=le,ne}var p=i(4516),I=i(2641),E=i(67953),d=i(526),f=i(82222),g=i(1065),s=i(43393),a=i(43393),c=a.Set,o=i(61425),r=i(62620),u=i(25027),S=i(69769),C=i(73759),l=i(55283),h=g.draft_tree_data_support,D=s.List,k=s.OrderedSet,y="&nbsp;",L=" ",T=4,B=new RegExp("\r","g"),w=new RegExp(`
`,"g"),z=new RegExp(y,"g"),j=new RegExp("&#13;?","g"),U=new RegExp("&#8203;?","g"),O=["bold","bolder","500","600","700","800","900"],ie=["light","lighter","100","200","300","400"],Q={b:"BOLD",code:"CODE",del:"STRIKETHROUGH",em:"ITALIC",i:"ITALIC",s:"STRIKETHROUGH",strike:"STRIKETHROUGH",strong:"BOLD",u:"UNDERLINE"},ue=(b={},A(b,r("public/DraftStyleDefault/depth0"),0),A(b,r("public/DraftStyleDefault/depth1"),1),A(b,r("public/DraftStyleDefault/depth2"),2),A(b,r("public/DraftStyleDefault/depth3"),3),A(b,r("public/DraftStyleDefault/depth4"),4),b),Ee=["className","href","rel","target","title"],me=["alt","className","height","src","width"],V=void 0,we={text:"",inlines:[],entities:[],blocks:[]},Ne={children:D(),depth:0,key:"",type:""},Me=function(_,le){return _==="li"?le==="ol"?"ordered-list-item":"unordered-list-item":null},Ke=function(_){var le=_.get("unstyled").element,se=c([]);return _.forEach(function(ge){ge.aliasedElements&&ge.aliasedElements.forEach(function(ye){se=se.add(ye)}),se=se.add(ge.element)}),se.filter(function(ge){return ge&&ge!==le}).toArray().sort()},qe=function(_,le,se){for(var ge=0;ge<se.length;ge++){var ye=se[ge](_,le);if(ye)return ye}return null},it=function(_,le,se){var ge=se.filter(function(ye){return ye.element===_||ye.wrapper===_||ye.aliasedElements&&ye.aliasedElements.some(function(We){return We===_})}).keySeq().toSet().toArray().sort();switch(ge.length){case 0:return"unstyled";case 1:return ge[0];default:return qe(_,le,[Me])||"unstyled"}},et=function(_,le,se){var ge=Q[_];if(ge)se=se.add(ge).toOrderedSet();else if(le instanceof HTMLElement){var ye=le;se=se.withMutations(function(We){var Fe=ye.style.fontWeight,Ve=ye.style.fontStyle,Ue=ye.style.textDecoration;O.indexOf(Fe)>=0?We.add("BOLD"):ie.indexOf(Fe)>=0&&We.remove("BOLD"),Ve==="italic"?We.add("ITALIC"):Ve==="normal"&&We.remove("ITALIC"),Ue==="underline"&&We.add("UNDERLINE"),Ue==="line-through"&&We.add("STRIKETHROUGH"),Ue==="none"&&(We.remove("UNDERLINE"),We.remove("STRIKETHROUGH"))}).toOrderedSet()}return se},Je=function(_,le,se){var ge=_.text.slice(-1),ye=le.text.slice(0,1);if(ge==="\r"&&ye==="\r"&&!se&&(_.text=_.text.slice(0,-1),_.inlines.pop(),_.entities.pop(),_.blocks.pop()),ge==="\r"){if(le.text===L||le.text===`
`)return _;(ye===L||ye===`
`)&&(le.text=le.text.slice(1),le.inlines.shift(),le.entities.shift())}return{text:_.text+le.text,inlines:_.inlines.concat(le.inlines),entities:_.entities.concat(le.entities),blocks:_.blocks.concat(le.blocks)}},Be=function(_,le){return le.some(function(se){return _.indexOf("<"+se)!==-1})},He=function(_){_ instanceof HTMLAnchorElement||C(!1);var le=_.protocol;return le==="http:"||le==="https:"||le==="mailto:"},Qe=function(_){var le=new Array(1);return _&&(le[0]=_),x({},we,{text:L,inlines:[k()],entities:le})},ot=function(){return x({},we,{text:`
`,inlines:[k()],entities:new Array(1)})},mt=function(){var _=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return x({},Ne,_)},rt=function(_,le){var se=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;return{text:"\r",inlines:[k()],entities:new Array(1),blocks:[mt({parent:se,key:u(),type:_,depth:Math.max(0,Math.min(T,le))})]}},Ce=function(_){var le=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return Object.keys(ue).some(function(se){_.classList.contains(se)&&(le=ue[se])}),le},$e=function ne(_,le,se,ge,ye,We,Fe,Ve,Ue,ct){var Ze=V,ut=le.nodeName.toLowerCase(),lt=_,gt="unstyled",pt=!1,ft=ye&&it(ye,ge,Ve),Dt=x({},we),wt=null,Rt=void 0;if(ut==="#text"){var Mt=le.textContent,jt=Mt.trim();if(ge&&jt===""&&le.parentElement){var It=le.parentElement.nodeName.toLowerCase();if(It==="ol"||It==="ul")return{chunk:x({},we),entityMap:_}}return jt===""&&ye!=="pre"?{chunk:Qe(Ue),entityMap:_}:(ye!=="pre"&&(Mt=Mt.replace(w,L)),V=ut,{chunk:{text:Mt,inlines:Array(Mt.length).fill(se),entities:Array(Mt.length).fill(Ue),blocks:[]},entityMap:_})}if(V=ut,ut==="br")return Ze==="br"&&(!ye||ft==="unstyled")?{chunk:rt("unstyled",Fe,ct),entityMap:_}:{chunk:ot(),entityMap:_};if(ut==="img"&&le instanceof HTMLImageElement&&le.attributes.getNamedItem("src")&&le.attributes.getNamedItem("src").value){var Qt=le,J={};me.forEach(function(ve){var ze=Qt.getAttribute(ve);ze&&(J[ve]=ze)}),le.textContent="\u{1F4F7}",Ue=f.__create("IMAGE","MUTABLE",J||{})}se=et(ut,le,se),(ut==="ul"||ut==="ol")&&(ge&&(Fe+=1),ge=ut),!h&&ut==="li"&&le instanceof HTMLElement&&(Fe=Ce(le,Fe));var ae=it(ut,ge,Ve),N=ge&&ye==="li"&&ut==="li",Z=(!ye||h)&&We.indexOf(ut)!==-1;(N||Z)&&(Dt=rt(ae,Fe,ct),Rt=Dt.blocks[0].key,ye=ut,pt=!h),N&&(gt=ge==="ul"?"unordered-list-item":"ordered-list-item");var q=le.firstChild;q!=null&&(ut=q.nodeName.toLowerCase());for(var de=null;q;){q instanceof HTMLAnchorElement&&q.href&&He(q)?function(){var ve=q,ze={};Ee.forEach(function(Ge){var Te=ve.getAttribute(Ge);Te&&(ze[Ge]=Te)}),ze.url=new o(ve.href).toString(),de=f.__create("LINK","MUTABLE",ze||{})}():de=void 0;var be=ne(lt,q,se,ge,ye,We,Fe,Ve,de||Ue,h?Rt:null),Se=be.chunk,Ae=be.entityMap;wt=Se,lt=Ae,Dt=Je(Dt,wt,h);var xe=q.nextSibling;!ct&&xe&&We.indexOf(ut)>=0&&ye&&(Dt=Je(Dt,ot())),xe&&(ut=xe.nodeName.toLowerCase()),q=xe}return pt&&(Dt=Je(Dt,rt(gt,Fe,ct))),{chunk:Dt,entityMap:lt}},Ft=function(_,le,se,ge){_=_.trim().replace(B,"").replace(z,L).replace(j,"").replace(U,"");var ye=Ke(se),We=le(_);if(!We)return null;V=null;var Fe=Be(_,ye)?ye:["div"],Ve=$e(ge,We,k(),"ul",null,Fe,-1,se),Ue=Ve.chunk,ct=Ve.entityMap;return Ue.text.indexOf("\r")===0&&(Ue={text:Ue.text.slice(1),inlines:Ue.inlines.slice(1),entities:Ue.entities.slice(1),blocks:Ue.blocks}),Ue.text.slice(-1)==="\r"&&(Ue.text=Ue.text.slice(0,-1),Ue.inlines=Ue.inlines.slice(0,-1),Ue.entities=Ue.entities.slice(0,-1),Ue.blocks.pop()),Ue.blocks.length===0&&Ue.blocks.push(x({},we,{type:"unstyled",depth:0})),Ue.text.split("\r").length===Ue.blocks.length+1&&Ue.blocks.unshift({type:"unstyled",depth:0}),{chunk:Ue,entityMap:ct}},Et=function(_){if(!_||!_.text||!Array.isArray(_.blocks))return null;var le={cacheRef:{},contentBlocks:[]},se=0,ge=_.blocks,ye=_.inlines,We=_.entities,Fe=h?E:I;return _.text.split("\r").reduce(function(Ve,Ue,ct){Ue=l(Ue);var Ze=ge[ct],ut=se+Ue.length,lt=ye.slice(se,ut),gt=We.slice(se,ut),pt=D(lt.map(function(Z,q){var de={style:Z,entity:null};return gt[q]&&(de.entity=gt[q]),p.create(de)}));se=ut+1;var ft=Ze.depth,Dt=Ze.type,wt=Ze.parent,Rt=Ze.key||u(),Mt=null;if(wt){var jt=Ve.cacheRef[wt],It=Ve.contentBlocks[jt];if(It.getChildKeys().isEmpty()&&It.getText()){var Qt=It.getCharacterList(),J=It.getText();Mt=u();var ae=new E({key:Mt,text:J,characterList:Qt,parent:wt,nextSibling:Rt});Ve.contentBlocks.push(ae),It=It.withMutations(function(Z){Z.set("characterList",D()).set("text","").set("children",It.children.push(ae.getKey()))})}Ve.contentBlocks[jt]=It.set("children",It.children.push(Rt))}var N=new Fe({key:Rt,parent:wt,type:Dt,depth:ft,text:Ue,characterList:pt,prevSibling:Mt||(ct===0||ge[ct-1].parent!==wt?null:ge[ct-1].key),nextSibling:ct===ge.length-1||ge[ct+1].parent!==wt?null:ge[ct+1].key});return Ve.contentBlocks.push(N),Ve.cacheRef[N.key]=ct,Ve},le).contentBlocks},De=function(_){var le=arguments.length>1&&arguments[1]!==void 0?arguments[1]:S,se=arguments.length>2&&arguments[2]!==void 0?arguments[2]:d,ge=Ft(_,le,se,f);if(ge==null)return null;var ye=ge.chunk,We=ge.entityMap,Fe=Et(ye);return{contentBlocks:Fe,entityMap:We}};H.exports=De},99607:function(H,P,i){"use strict";var x=i(27418),b=x||function(j){for(var U=1;U<arguments.length;U++){var O=arguments[U];for(var ie in O)Object.prototype.hasOwnProperty.call(O,ie)&&(j[ie]=O[ie])}return j},v=i(2641),A=i(67953),p=i(66912),I=i(82222),E=i(1065),d=i(68957),f=i(43393),g=i(25110),s=i(86019),a=i(67134),c=i(59672),o=i(25027),r=i(73759),u=E.draft_tree_data_support,S=f.List,C=f.Map,l=f.OrderedMap,h=function(U,O){var ie=U.key,Q=U.type,ue=U.data,Ee=U.text,me=U.depth,V={text:Ee,depth:me||0,type:Q||"unstyled",key:ie||o(),data:C(ue),characterList:D(U,O)};return V},D=function(U,O){var ie=U.text,Q=U.entityRanges,ue=U.inlineStyleRanges,Ee=Q||[],me=ue||[];return s(c(ie,me),a(ie,Ee.filter(function(V){return O.hasOwnProperty(V.key)}).map(function(V){return b({},V,{key:O[V.key]})})))},k=function(U){return b({},U,{key:U.key||o()})},y=function(U,O,ie){var Q=O.map(function(ue){return b({},ue,{parentRef:ie})});return U.concat(Q.reverse())},L=function(U,O){return U.map(k).reduce(function(ie,Q,ue){Array.isArray(Q.children)||r(!1);var Ee=Q.children.map(k),me=new A(b({},h(Q,O),{prevSibling:ue===0?null:U[ue-1].key,nextSibling:ue===U.length-1?null:U[ue+1].key,children:S(Ee.map(function(Je){return Je.key}))}));ie=ie.set(me.getKey(),me);for(var V=y([],Ee,me);V.length>0;){var we=V.pop(),Ne=we.parentRef,Me=Ne.getChildKeys(),Ke=Me.indexOf(we.key),qe=Array.isArray(we.children);if(!qe){qe||r(!1);break}var it=we.children.map(k),et=new A(b({},h(we,O),{parent:Ne.getKey(),children:S(it.map(function(Je){return Je.key})),prevSibling:Ke===0?null:Me.get(Ke-1),nextSibling:Ke===Me.size-1?null:Me.get(Ke+1)}));ie=ie.set(et.getKey(),et),V=y(V,it,et)}return ie},l())},T=function(U,O){return l(U.map(function(ie){var Q=new v(h(ie,O));return[Q.getKey(),Q]}))},B=function(U,O){var ie=Array.isArray(U.blocks[0].children),Q=u&&!ie?d.fromRawStateToRawTreeState(U).blocks:U.blocks;return u?L(Q,O):T(ie?d.fromRawTreeStateToRawState(U).blocks:Q,O)},w=function(U){var O=U.entityMap,ie={};return Object.keys(O).forEach(function(Q){var ue=O[Q],Ee=ue.type,me=ue.mutability,V=ue.data;ie[Q]=I.__create(Ee,me,V||{})}),ie},z=function(U){Array.isArray(U.blocks)||r(!1);var O=w(U),ie=B(U,O),Q=ie.isEmpty()?new g:g.createEmpty(ie.first().getKey());return new p({blockMap:ie,entityMap:O,selectionBefore:Q,selectionAfter:Q})};H.exports=z},86019:function(H,P,i){"use strict";var x=i(4516),b=i(43393),v=b.List;function A(p,I){var E=p.map(function(d,f){var g=I[f];return x.create({style:d,entity:g})});return v(E)}H.exports=A},67134:function(H,P,i){"use strict";var x=i(38935),b=x.substr;function v(A,p){var I=Array(A.length).fill(null);return p&&p.forEach(function(E){for(var d=b(A,0,E.offset).length,f=d+b(A,E.offset,E.length).length,g=d;g<f;g++)I[g]=E.key}),I}H.exports=v},59672:function(H,P,i){"use strict";var x=i(43393),b=x.OrderedSet,v=i(38935),A=v.substr,p=b();function I(E,d){var f=Array(E.length).fill(p);return d&&d.forEach(function(g){for(var s=A(E,0,g.offset).length,a=s+A(E,g.offset,g.length).length;s<a;)f[s]=f[s].add(g.style),s++}),f}H.exports=I},26396:function(H,P,i){"use strict";var x=i(34365),b=i(42307),v=i(14289),A=i(4856),p=i(42128),I=i(42177),E=i(40258),d=i(22045),f=i(56926),g="'",s="/",a=A.isBrowser("Firefox");function c(u){return a&&(u==g||u==s)}function o(u,S,C,l){var h=b.replaceText(u.getCurrentContent(),u.getSelection(),S,C,l);return v.push(u,h,"insert-characters")}function r(u,S){u._pendingStateFromBeforeInput!==void 0&&(u.update(u._pendingStateFromBeforeInput),u._pendingStateFromBeforeInput=void 0);var C=u._latestEditorState,l=S.data;if(!!l){if(u.props.handleBeforeInput&&I(u.props.handleBeforeInput(l,C))){S.preventDefault();return}var h=C.getSelection(),D=h.getStartOffset(),k=h.getEndOffset(),y=h.getAnchorKey();if(!h.isCollapsed()){S.preventDefault();var L=C.getCurrentContent().getPlainText().slice(D,k);l===L?u.update(v.forceSelection(C,h.merge({focusOffset:k}))):u.update(o(C,l,C.getCurrentInlineStyle(),p(C.getCurrentContent(),C.getSelection())));return}var T=o(C,l,C.getCurrentInlineStyle(),p(C.getCurrentContent(),C.getSelection())),B=!1;if(B||(B=E(u._latestCommittedEditorState)),!B){var w=i.g.getSelection();if(w.anchorNode&&w.anchorNode.nodeType===Node.TEXT_NODE){var z=w.anchorNode.parentNode;B=z.nodeName==="SPAN"&&z.firstChild.nodeType===Node.TEXT_NODE&&z.firstChild.nodeValue.indexOf("	")!==-1}}if(!B){var j=x.getFingerprint(C.getBlockTree(y)),U=x.getFingerprint(T.getBlockTree(y));B=j!==U}if(B||(B=c(l)),B||(B=d(T.getDirectionMap()).get(y)!==d(C.getDirectionMap()).get(y)),B){S.preventDefault(),u.update(T);return}T=v.set(T,{nativelyRenderedContent:T.getCurrentContent()}),u._pendingStateFromBeforeInput=T,f(function(){u._pendingStateFromBeforeInput!==void 0&&(u.update(u._pendingStateFromBeforeInput),u._pendingStateFromBeforeInput=void 0)})}}H.exports=r},43421:function(H,P,i){"use strict";var x=i(14289),b=i(67476),v=i(31003);function A(p,I){if(v()===document.body){var E=i.g.getSelection(),d=p.editor;E.rangeCount===1&&b(d,E.anchorNode)&&b(d,E.focusNode)&&E.removeAllRanges()}var f=p._latestEditorState,g=f.getSelection();if(!!g.getHasFocus()){var s=g.set("hasFocus",!1);p.props.onBlur&&p.props.onBlur(I),p.update(x.acceptSelection(f,s))}}H.exports=A},6155:function(H,P,i){"use strict";var x=i(14289);function b(v,A){v.setMode("composite"),v.update(x.set(v._latestEditorState,{inCompositionMode:!0})),v._onCompositionStart(A)}H.exports=b},69328:function(H,P,i){"use strict";var x=i(94882);function b(v,A){var p=v._latestEditorState,I=p.getSelection();if(I.isCollapsed()){A.preventDefault();return}v.setClipboard(x(v._latestEditorState))}H.exports=b},88922:function(H,P,i){"use strict";var x=i(42307),b=i(14289),v=i(19051),A=i(94882),p=i(79749);function I(d,f){var g=d._latestEditorState,s=g.getSelection(),a=f.target,c=void 0;if(s.isCollapsed()){f.preventDefault();return}a instanceof Node&&(c=p(v.getScrollParent(a)));var o=A(g);d.setClipboard(o),d.setMode("cut"),setTimeout(function(){d.restoreEditorDOM(c),d.exitCurrentMode(),d.update(E(g))},0)}function E(d){var f=x.removeRange(d.getCurrentContent(),d.getSelection(),"forward");return b.push(d,f,"remove-range")}H.exports=I},39499:function(H){"use strict";function P(i,x){i._internalDrag=!1,i.setMode("drag"),x.preventDefault()}H.exports=P},80981:function(H){"use strict";function P(i){i._internalDrag=!0,i.setMode("drag")}H.exports=P},62186:function(H,P,i){"use strict";var x=i(14289),b=i(4856);function v(A,p){var I=A._latestEditorState,E=I.getSelection();if(!E.getHasFocus()){var d=E.set("hasFocus",!0);A.props.onFocus&&A.props.onFocus(p),b.isBrowser("Chrome < 60.0.3081.0")?A.update(x.forceSelection(I,d)):A.update(x.acceptSelection(I,d))}}H.exports=v},29971:function(H,P,i){"use strict";var x=i(1065),b=i(42307),v=i(22146),A=i(14289),p=i(4856),I=i(69270),E=i(22045),d=p.isEngine("Gecko"),f=`

`;function g(s){s._pendingStateFromBeforeInput!==void 0&&(s.update(s._pendingStateFromBeforeInput),s._pendingStateFromBeforeInput=void 0);var a=i.g.getSelection(),c=a.anchorNode,o=a.isCollapsed,r=c.nodeType!==Node.TEXT_NODE,u=c.nodeType!==Node.TEXT_NODE&&c.nodeType!==Node.ELEMENT_NODE;if(x.draft_killswitch_allow_nontextnodes){if(r)return}else if(u)return;if(c.nodeType===Node.TEXT_NODE&&(c.previousSibling!==null||c.nextSibling!==null)){var S=c.parentNode;c.nodeValue=S.textContent;for(var C=S.firstChild;C!==null;C=C.nextSibling)C!==c&&S.removeChild(C)}var l=c.textContent,h=s._latestEditorState,D=E(I(c)),k=v.decode(D),y=k.blockKey,L=k.decoratorKey,T=k.leafKey,B=h.getBlockTree(y).getIn([L,"leaves",T]),w=B.start,z=B.end,j=h.getCurrentContent(),U=j.getBlockForKey(y),O=U.getText().slice(w,z);if(l.endsWith(f)&&(l=l.slice(0,-1)),l!==O){var ie=h.getSelection(),Q=ie.merge({anchorOffset:w,focusOffset:z,isBackward:!1}),ue=U.getEntityAt(w),Ee=ue&&j.getEntity(ue),me=Ee&&Ee.getMutability(),V=me==="MUTABLE",we=V?"spellcheck-change":"apply-entity",Ne=b.replaceText(j,Q,l,U.getInlineStyleAt(w),V?U.getEntityAt(w):null),Me,Ke,qe,it;if(d)Me=a.anchorOffset,Ke=a.focusOffset,qe=w+Math.min(Me,Ke),it=qe+Math.abs(Me-Ke),Me=qe,Ke=it;else{var et=l.length-O.length;qe=ie.getStartOffset(),it=ie.getEndOffset(),Me=o?it+et:qe,Ke=it+et}var Je=Ne.merge({selectionBefore:j.getSelectionAfter(),selectionAfter:ie.merge({anchorOffset:Me,focusOffset:Ke})});s.update(A.push(h,Je,we))}}H.exports=g},46397:function(H,P,i){"use strict";var x=i(42307),b=i(14289),v=i(47387),A=i(25399),p=i(83751),I=i(4856),E=i(42177),d=i(49779),f=i(51050),g=i(13767),s=i(77978),a=i(67217),c=i(8425),o=i(62800),r=i(13998),u=i(80138),S=i(87051),C=v.isOptionKeyCommand,l=I.isBrowser("Chrome");function h(k,y){switch(k){case"redo":return b.redo(y);case"delete":return r(y);case"delete-word":return g(y);case"backspace":return o(y);case"backspace-word":return f(y);case"backspace-to-start-of-line":return d(y);case"split-block":return s(y);case"transpose-characters":return u(y);case"move-selection-to-start-of-block":return c(y);case"move-selection-to-end-of-block":return a(y);case"secondary-cut":return p.cut(y);case"secondary-paste":return p.paste(y);default:return y}}function D(k,y){var L=y.which,T=k._latestEditorState;switch(L){case A.RETURN:if(y.preventDefault(),k.props.handleReturn&&E(k.props.handleReturn(y,T)))return;break;case A.ESC:y.preventDefault(),k.props.onEscape&&k.props.onEscape(y);return;case A.TAB:k.props.onTab&&k.props.onTab(y);return;case A.UP:k.props.onUpArrow&&k.props.onUpArrow(y);return;case A.RIGHT:k.props.onRightArrow&&k.props.onRightArrow(y);return;case A.DOWN:k.props.onDownArrow&&k.props.onDownArrow(y);return;case A.LEFT:k.props.onLeftArrow&&k.props.onLeftArrow(y);return;case A.SPACE:if(l&&C(y)){y.preventDefault();var B=x.replaceText(T.getCurrentContent(),T.getSelection(),"\xA0");k.update(b.push(T,B,"insert-characters"));return}}var w=k.props.keyBindingFn(y);if(!!w){if(w==="undo"){S(y,T,k.update);return}if(y.preventDefault(),!(k.props.handleKeyCommand&&E(k.props.handleKeyCommand(w,T)))){var z=h(w,T);z!==T&&k.update(z)}}}H.exports=D},6089:function(H,P,i){"use strict";var x=i(10329),b=i(4516),v=i(44891),A=i(42307),p=i(45712),I=i(14289),E=i(41947),d=i(42128),f=i(21738),g=i(42177),s=i(44300);function a(r,u){u.preventDefault();var S=new v(u.clipboardData);if(!S.isRichText()){var C=S.getFiles(),l=S.getText();if(C.length>0){if(r.props.handlePastedFiles&&g(r.props.handlePastedFiles(C)))return;f(C,function(Q){if(Q=Q||l,!!Q){var ue=r._latestEditorState,Ee=s(Q),me=b.create({style:ue.getCurrentInlineStyle(),entity:d(ue.getCurrentContent(),ue.getSelection())}),V=E.getCurrentBlockType(ue),we=p.processText(Ee,me,V),Ne=x.createFromArray(we),Me=A.replaceWithFragment(ue.getCurrentContent(),ue.getSelection(),Ne);r.update(I.push(ue,Me,"insert-fragment"))}});return}}var h=[],D=S.getText(),k=S.getHTML(),y=r._latestEditorState;if(!(r.props.handlePastedText&&g(r.props.handlePastedText(D,k,y)))){if(D&&(h=s(D)),!r.props.stripPastedStyles){var L=r.getClipboard();if(S.isRichText()&&L){if(k.indexOf(r.getEditorKey())!==-1||h.length===1&&L.size===1&&L.first().getText()===D){r.update(c(r._latestEditorState,L));return}}else if(L&&S.types.includes("com.apple.webarchive")&&!S.types.includes("text/html")&&o(h,L)){r.update(c(r._latestEditorState,L));return}if(k){var T=p.processHTML(k,r.props.blockRenderMap);if(T){var B=T.contentBlocks,w=T.entityMap;if(B){var z=x.createFromArray(B);r.update(c(r._latestEditorState,z,w));return}}}r.setClipboard(null)}if(h.length){var j=b.create({style:y.getCurrentInlineStyle(),entity:d(y.getCurrentContent(),y.getSelection())}),U=E.getCurrentBlockType(y),O=p.processText(h,j,U),ie=x.createFromArray(O);r.update(c(r._latestEditorState,ie))}}}function c(r,u,S){var C=A.replaceWithFragment(r.getCurrentContent(),r.getSelection(),u);return I.push(r,C.set("entityMap",S),"insert-fragment")}function o(r,u){return r.length===u.size&&u.valueSeq().every(function(S,C){return S.getText()===r[C]})}H.exports=a},14507:function(H,P,i){"use strict";var x=i(14289),b=i(73935),v=i(1244),A=i(73759);function p(I){if(!(I._blockSelectEvents||I._latestEditorState!==I.props.editorState)){var E=I.props.editorState,d=b.findDOMNode(I.editorContainer);d||A(!1),d.firstChild instanceof HTMLElement||A(!1);var f=v(E,d.firstChild),g=f.selectionState;g!==E.getSelection()&&(f.needsRecovery?E=x.forceSelection(E,g):E=x.acceptSelection(E,g),I.update(E))}}H.exports=p},56265:function(H,P,i){"use strict";var x=i(86155),b=i(38935),v=b.strlen;function A(p,I){var E=[];return p.findEntityRanges(function(d){return!!d.getEntity()},function(d,f){var g=p.getText(),s=p.getEntityAt(d);E.push({offset:v(g.slice(0,d)),length:v(g.slice(d,f)),key:Number(I[x.stringify(s)])})}),E}H.exports=A},31487:function(H,P,i){"use strict";var x=i(38935),b=i(29407),v=function(f,g){return f===g},A=function(f){return!!f},p=[];function I(d,f,g){var s=[],a=f.map(function(c){return c.has(g)}).toList();return b(a,v,A,function(c,o){var r=d.getText();s.push({offset:x.strlen(r.slice(0,c)),length:x.strlen(r.slice(c,o)),style:g})}),s}function E(d){var f=d.getCharacterList().map(function(s){return s.getStyle()}).toList(),g=f.flatten().toSet().map(function(s){return I(d,f,s)});return Array.prototype.concat.apply(p,g.toJS())}H.exports=E},88182:function(H,P,i){"use strict";var x=i(38935),b=i(6092),v=i(73759);function A(d){var f=getComputedStyle(d),g=document.createElement("div");g.style.fontFamily=f.fontFamily,g.style.fontSize=f.fontSize,g.style.fontStyle=f.fontStyle,g.style.fontWeight=f.fontWeight,g.style.lineHeight=f.lineHeight,g.style.position="absolute",g.textContent="M";var s=document.body;s||v(!1),s.appendChild(g);var a=g.getBoundingClientRect();return s.removeChild(g),a.height}function p(d,f){for(var g=Infinity,s=Infinity,a=-Infinity,c=-Infinity,o=0;o<d.length;o++){var r=d[o];r.width===0||r.width===1||(g=Math.min(g,r.top),s=Math.min(s,r.bottom),a=Math.max(a,r.top),c=Math.max(c,r.bottom))}return a<=s&&a-g<f&&c-s<f}function I(d){switch(d.nodeType){case Node.DOCUMENT_TYPE_NODE:return 0;case Node.TEXT_NODE:case Node.PROCESSING_INSTRUCTION_NODE:case Node.COMMENT_NODE:return d.length;default:return d.childNodes.length}}function E(d){d.collapsed||v(!1),d=d.cloneRange();var f=d.startContainer;f.nodeType!==1&&(f=f.parentNode);var g=A(f),s=d.endContainer,a=d.endOffset;for(d.setStart(d.startContainer,0);p(b(d),g)&&(s=d.startContainer,a=d.startOffset,s.parentNode||v(!1),d.setStartBefore(s),!(s.nodeType===1&&getComputedStyle(s).display!=="inline")););var c=s,o=a-1;do{for(var r=c.nodeValue,u=o;u>=0;u--)if(!(r!=null&&u>0&&x.isSurrogatePair(r,u-1)))if(d.setStart(c,u),p(b(d),g))s=c,a=u;else break;if(u===-1||c.childNodes.length===0)break;c=c.childNodes[u],o=I(c)}while(!0);return d.setStart(s,a),d}H.exports=E},69270:function(H,P,i){"use strict";var x=i(93578);function b(v){for(var A=v;A&&A!==document.documentElement;){var p=x(A);if(p!=null)return p;A=A.parentNode}return null}H.exports=b},29407:function(H){"use strict";function P(i,x,b,v){if(!!i.size){var A=0;i.reduce(function(p,I,E){return x(p,I)||(b(p)&&v(A,E),A=E),I}),b(i.last())&&v(A,i.count())}}H.exports=P},25027:function(H){"use strict";var P={},i=Math.pow(2,24);function x(){for(var b=void 0;b===void 0||P.hasOwnProperty(b)||!isNaN(+b);)b=Math.floor(Math.random()*i).toString(32);return P[b]=!0,b}H.exports=x},81446:function(H,P,i){"use strict";var x=i(5195),b=i(64994),v=i(73759);function A(I,E,d,f,g){var s=f.getStartOffset(),a=f.getEndOffset(),c=E.getEntityAt(s),o=d.getEntityAt(a-1);if(!c&&!o)return f;var r=f;if(c&&c===o)r=p(I,E,r,g,c,!0,!0);else if(c&&o){var u=p(I,E,r,g,c,!1,!0),S=p(I,d,r,g,o,!1,!1);r=r.merge({anchorOffset:u.getAnchorOffset(),focusOffset:S.getFocusOffset(),isBackward:!1})}else if(c){var C=p(I,E,r,g,c,!1,!0);r=r.merge({anchorOffset:C.getStartOffset(),isBackward:!1})}else if(o){var l=p(I,d,r,g,o,!1,!1);r=r.merge({focusOffset:l.getEndOffset(),isBackward:!1})}return r}function p(I,E,d,f,g,s,a){var c=d.getStartOffset(),o=d.getEndOffset(),r=I.__get(g),u=r.getMutability(),S=a?c:o;if(u==="MUTABLE")return d;var C=b(E,g).filter(function(D){return S<=D.end&&S>=D.start});C.length!=1&&v(!1);var l=C[0];if(u==="IMMUTABLE")return d.merge({anchorOffset:l.start,focusOffset:l.end,isBackward:!1});s||(a?o=l.end:c=l.start);var h=x.getRemovalRange(c,o,E.getText().slice(l.start,l.end),l.start,f);return d.merge({anchorOffset:h.start,focusOffset:h.end,isBackward:!1})}H.exports=A},88687:function(H,P,i){"use strict";var x=i(98555),b=i(14017),v=function(p,I){var E=I.getStartKey(),d=I.getStartOffset(),f=I.getEndKey(),g=I.getEndOffset(),s=b(p,I),a=s.getBlockMap(),c=a.keySeq(),o=c.indexOf(E),r=c.indexOf(f)+1;return x(a.slice(o,r).map(function(u,S){var C=u.getText(),l=u.getCharacterList();return E===f?u.merge({text:C.slice(d,g),characterList:l.slice(d,g)}):S===E?u.merge({text:C.slice(d),characterList:l.slice(d)}):S===f?u.merge({text:C.slice(0,g),characterList:l.slice(0,g)}):u}))};H.exports=v},41714:function(H,P,i){"use strict";var x=i(47387),b=i(25399),v=i(4856),A=v.isPlatform("Mac OS X"),p=v.isPlatform("Windows"),I=A&&v.isBrowser("Firefox < 29"),E=x.hasCommandModifier,d=x.isCtrlKeyCommand;function f(o){return A&&o.altKey||d(o)}function g(o){return E(o)?o.shiftKey?"redo":"undo":null}function s(o){return p&&o.shiftKey?null:f(o)?"delete-word":"delete"}function a(o){return E(o)&&A?"backspace-to-start-of-line":f(o)?"backspace-word":"backspace"}function c(o){switch(o.keyCode){case 66:return E(o)?"bold":null;case 68:return d(o)?"delete":null;case 72:return d(o)?"backspace":null;case 73:return E(o)?"italic":null;case 74:return E(o)?"code":null;case 75:return!p&&d(o)?"secondary-cut":null;case 77:return d(o)?"split-block":null;case 79:return d(o)?"split-block":null;case 84:return A&&d(o)?"transpose-characters":null;case 85:return E(o)?"underline":null;case 87:return A&&d(o)?"backspace-word":null;case 89:return d(o)?p?"redo":"secondary-paste":null;case 90:return g(o)||null;case b.RETURN:return"split-block";case b.DELETE:return s(o);case b.BACKSPACE:return a(o);case b.LEFT:return I&&E(o)?"move-selection-to-start-of-block":null;case b.RIGHT:return I&&E(o)?"move-selection-to-end-of-block":null;default:return null}}H.exports=c},1244:function(H,P,i){"use strict";var x=i(8101);function b(v,A){var p=i.g.getSelection();return p.rangeCount===0?{selectionState:v.getSelection().set("hasFocus",!1),needsRecovery:!1}:x(v,A,p.anchorNode,p.anchorOffset,p.focusNode,p.focusOffset)}H.exports=b},8101:function(H,P,i){"use strict";var x=i(69270),b=i(93578),v=i(94486),A=i(73759),p=i(22045);function I(s,a,c,o,r,u){var S=c.nodeType===Node.TEXT_NODE,C=r.nodeType===Node.TEXT_NODE;if(S&&C)return{selectionState:v(s,p(x(c)),o,p(x(r)),u),needsRecovery:!1};var l=null,h=null,D=!0;return S?(l={key:p(x(c)),offset:o},h=f(a,r,u)):C?(h={key:p(x(r)),offset:u},l=f(a,c,o)):(l=f(a,c,o),h=f(a,r,u),c===r&&o===u&&(D=!!c.firstChild&&c.firstChild.nodeName!=="BR")),{selectionState:v(s,l.key,l.offset,h.key,h.offset),needsRecovery:D}}function E(s){for(;s.firstChild&&(s.firstChild instanceof Element&&s.firstChild.getAttribute("data-blocks")==="true"||b(s.firstChild));)s=s.firstChild;return s}function d(s){for(;s.lastChild&&(s.lastChild instanceof Element&&s.lastChild.getAttribute("data-blocks")==="true"||b(s.lastChild));)s=s.lastChild;return s}function f(s,a,c){var o=a,r=x(o);if(r!=null||s&&(s===o||s.firstChild===o)||A(!1),s===o&&(o=o.firstChild,o instanceof Element&&o.getAttribute("data-contents")==="true"||A(!1),c>0&&(c=o.childNodes.length)),c===0){var u=null;if(r!=null)u=r;else{var S=E(o);u=p(b(S))}return{key:u,offset:0}}var C=o.childNodes[c-1],l=null,h=null;if(!b(C))l=p(r),h=g(C);else{var D=d(C);l=p(b(D)),h=g(D)}return{key:l,offset:h}}function g(s){var a=s.textContent;return a===`
`?0:a.length}H.exports=I},42128:function(H){"use strict";function P(x,b){var v;if(b.isCollapsed()){var A=b.getAnchorKey(),p=b.getAnchorOffset();return p>0?(v=x.getBlockForKey(A).getEntityAt(p-1),v!==x.getBlockForKey(A).getEntityAt(p)?null:i(x.getEntityMap(),v)):null}var I=b.getStartKey(),E=b.getStartOffset(),d=x.getBlockForKey(I);return v=E===d.getLength()?null:d.getEntityAt(E),i(x.getEntityMap(),v)}function i(x,b){if(b){var v=x.__get(b);return v.getMutability()==="MUTABLE"?b:null}return null}H.exports=P},94882:function(H,P,i){"use strict";var x=i(88687);function b(v){var A=v.getSelection();return A.isCollapsed()?null:x(v.getCurrentContent(),A)}H.exports=b},39506:function(H,P,i){"use strict";var x=i(67953),b=function(A,p){var I=A instanceof x;if(!I)return null;var E=A.getNextSiblingKey();if(E)return E;var d=A.getParentKey();if(!d)return null;for(var f=p.get(d);f&&!f.getNextSiblingKey();){var g=f.getParentKey();f=g?p.get(g):null}return f?f.getNextSiblingKey():null};H.exports=b},98056:function(H,P,i){"use strict";var x=i(6092);function b(v){var A=x(v),p=0,I=0,E=0,d=0;if(A.length){if(A.length>1&&A[0].width===0){var f=A[1];p=f.top,I=f.right,E=f.bottom,d=f.left}else{var g=A[0];p=g.top,I=g.right,E=g.bottom,d=g.left}for(var s=1;s<A.length;s++){var a=A[s];a.height!==0&&a.width!==0&&(p=Math.min(p,a.top),I=Math.max(I,a.right),E=Math.max(E,a.bottom),d=Math.min(d,a.left))}}return{top:p,right:I,bottom:E,left:d,width:I-d,height:E-p}}H.exports=b},6092:function(H,P,i){"use strict";var x=i(4856),b=i(73759),v=x.isBrowser("Chrome");function A(I){for(var E=I.cloneRange(),d=[],f=I.endContainer;f!=null;f=f.parentNode){var g=f===I.commonAncestorContainer;g?E.setStart(I.startContainer,I.startOffset):E.setStart(E.endContainer,0);var s=Array.from(E.getClientRects());if(d.push(s),g){var a;return d.reverse(),(a=[]).concat.apply(a,d)}E.setEndBefore(f)}b(!1)}var p=v?A:function(I){return Array.from(I.getClientRects())};H.exports=p},64994:function(H,P,i){"use strict";var x=i(73759);function b(v,A){var p=[];return v.findEntityRanges(function(I){return I.getEntity()===A},function(I,E){p.push({start:I,end:E})}),p.length||x(!1),p}H.exports=b},69769:function(H,P,i){"use strict";var x=i(4856),b=i(73759),v=x.isBrowser("IE <= 9");function A(p){var I,E=null;return!v&&document.implementation&&document.implementation.createHTMLDocument&&(I=document.implementation.createHTMLDocument("foo"),I.documentElement||b(!1),I.documentElement.innerHTML=p,E=I.getElementsByTagName("body")[0]),E}H.exports=A},93578:function(H){"use strict";function P(i){if(i instanceof Element){var x=i.getAttribute("data-offset-key");if(x)return x;for(var b=0;b<i.childNodes.length;b++){var v=P(i.childNodes[b]);if(v)return v}}return null}H.exports=P},21738:function(H,P,i){"use strict";var x=i(73759),b=/\.textClipping$/,v={"text/plain":!0,"text/html":!0,"text/rtf":!0},A=5e3;function p(E,d){var f=0,g=[];E.forEach(function(s){I(s,function(a){f++,a&&g.push(a.slice(0,A)),f==E.length&&d(g.join("\r"))})})}function I(E,d){if(!i.g.FileReader||E.type&&!(E.type in v)){d("");return}if(E.type===""){var f="";b.test(E.name)&&(f=E.name.replace(b,"")),d(f);return}var g=new FileReader;g.onload=function(){var s=g.result;typeof s!="string"&&x(!1),d(s)},g.onerror=function(){d("")},g.readAsText(E)}H.exports=p},94486:function(H,P,i){"use strict";var x=i(22146),b=i(22045);function v(A,p,I,E,d){var f=b(A.getSelection()),g=x.decode(p),s=g.blockKey,a=A.getBlockTree(s).getIn([g.decoratorKey,"leaves",g.leafKey]),c=x.decode(E),o=c.blockKey,r=A.getBlockTree(o).getIn([c.decoratorKey,"leaves",c.leafKey]),u=a.get("start"),S=r.get("start"),C=a?u+I:null,l=r?S+d:null,h=f.getAnchorKey()===s&&f.getAnchorOffset()===C&&f.getFocusKey()===o&&f.getFocusOffset()===l;if(h)return f;var D=!1;if(s===o){var k=a.get("end"),y=r.get("end");S===u&&y===k?D=d<I:D=S<u}else{var L=A.getCurrentContent().getBlockMap().keySeq().skipUntil(function(T){return T===s||T===o}).first();D=L===o}return f.merge({anchorKey:s,anchorOffset:C,focusKey:o,focusOffset:l,isBackward:D})}H.exports=v},96629:function(H,P,i){"use strict";var x=i(98056);function b(v){var A=v.getSelection();if(!A.rangeCount)return null;var p=A.getRangeAt(0),I=x(p),E=I.top,d=I.right,f=I.bottom,g=I.left;return E===0&&d===0&&f===0&&g===0?null:I}H.exports=b},54542:function(H,P,i){"use strict";var x=i(10329),b=i(67953),v=i(43393),A=i(40779),p=i(73759),I=i(98555),E=v.List,d=function(u,S,C,l,h,D){var k=C.get(h),y=k.getText(),L=k.getCharacterList(),T=h,B=D+l.getText().length,w=k.merge({text:y.slice(0,D)+l.getText()+y.slice(D),characterList:A(L,l.getCharacterList(),D),data:l.getData()});return u.merge({blockMap:C.set(h,w),selectionBefore:S,selectionAfter:S.merge({anchorKey:T,anchorOffset:B,focusKey:T,focusOffset:B,isBackward:!1})})},f=function(u,S,C){var l=u.getText(),h=u.getCharacterList(),D=l.slice(0,S),k=h.slice(0,S),y=C.first();return u.merge({text:D+y.getText(),characterList:k.concat(y.getCharacterList()),type:D?u.getType():y.getType(),data:y.getData()})},g=function(u,S,C){var l=u.getText(),h=u.getCharacterList(),D=l.length,k=l.slice(S,D),y=h.slice(S,D),L=C.last();return L.merge({text:L.getText()+k,characterList:L.getCharacterList().concat(y),data:L.getData()})},s=function(u,S){var C=u.getKey(),l=u,h=[];for(S.get(C)&&h.push(C);l&&l.getNextSiblingKey();){var D=l.getNextSiblingKey();if(!D)break;h.push(D),l=S.get(D)}return h},a=function(u,S,C,l){return u.withMutations(function(h){var D=C.getKey(),k=l.getKey(),y=C.getNextSiblingKey(),L=C.getParentKey(),T=s(l,u),B=T[T.length-1];if(h.get(k)?(h.setIn([D,"nextSibling"],k),h.setIn([k,"prevSibling"],D)):(h.setIn([D,"nextSibling"],l.getNextSiblingKey()),h.setIn([l.getNextSiblingKey(),"prevSibling"],D)),h.setIn([B,"nextSibling"],y),y&&h.setIn([y,"prevSibling"],B),T.forEach(function(ie){return h.setIn([ie,"parent"],L)}),L){var w=u.get(L),z=w.getChildKeys(),j=z.indexOf(D),U=j+1,O=z.toArray();O.splice.apply(O,[U,0].concat(T)),h.setIn([L,"children"],E(O))}})},c=function(u,S,C,l,h,D){var k=C.first()instanceof b,y=[],L=l.size,T=C.get(h),B=l.first(),w=l.last(),z=w.getLength(),j=w.getKey(),U=k&&(!T.getChildKeys().isEmpty()||!B.getChildKeys().isEmpty());C.forEach(function(ie,Q){if(Q!==h){y.push(ie);return}U?y.push(ie):y.push(f(ie,D,l)),l.slice(U?0:1,L-1).forEach(function(ue){return y.push(ue)}),y.push(g(ie,D,l))});var O=x.createFromArray(y);return k&&(O=a(O,C,T,B)),u.merge({blockMap:O,selectionBefore:S,selectionAfter:S.merge({anchorKey:j,anchorOffset:z,focusKey:j,focusOffset:z,isBackward:!1})})},o=function(u,S,C){S.isCollapsed()||p(!1);var l=u.getBlockMap(),h=I(C),D=S.getStartKey(),k=S.getStartOffset(),y=l.get(D);return y instanceof b&&(y.getChildKeys().isEmpty()||p(!1)),h.size===1?d(u,S,l,h.first(),D,k):c(u,S,l,h,D,k)};H.exports=o},40779:function(H){"use strict";function P(i,x,b){if(b===i.count())x.forEach(function(p){i=i.push(p)});else if(b===0)x.reverse().forEach(function(p){i=i.unshift(p)});else{var v=i.slice(0,b),A=i.slice(b);i=v.concat(x,A).toList()}return i}H.exports=P},18467:function(H,P,i){"use strict";var x=i(43393),b=i(40779),v=i(73759),A=x.Repeat;function p(I,E,d,f){E.isCollapsed()||v(!1);var g=d.length;if(!g)return I;var s=I.getBlockMap(),a=E.getStartKey(),c=E.getStartOffset(),o=s.get(a),r=o.getText(),u=o.merge({text:r.slice(0,c)+d+r.slice(c,o.getLength()),characterList:b(o.getCharacterList(),A(f,g).toList(),c)}),S=c+g;return I.merge({blockMap:s.set(a,u),selectionAfter:E.merge({anchorOffset:S,focusOffset:S})})}H.exports=p},42177:function(H){"use strict";function P(i){return i==="handled"||i===!0}H.exports=P},40258:function(H){"use strict";function P(i){var x=i.getSelection(),b=x.getAnchorKey(),v=i.getBlockTree(b),A=x.getStartOffset(),p=!1;return v.some(function(I){return A===I.get("start")?(p=!0,!0):A<I.get("end")?I.get("leaves").some(function(E){var d=E.get("start");return A===d?(p=!0,!0):!1}):!1}),p}H.exports=P},49779:function(H,P,i){"use strict";var x=i(14289),b=i(88182),v=i(8101),A=i(53268),p=i(14730);function I(E){var d=p(E,function(f){var g=f.getSelection();if(g.isCollapsed()&&g.getAnchorOffset()===0)return A(f,1);var s=i.g.getSelection(),a=s.getRangeAt(0);return a=b(a),v(f,null,a.endContainer,a.endOffset,a.startContainer,a.startOffset).selectionState},"backward");return d===E.getCurrentContent()?E:x.push(E,d,"remove-range")}H.exports=I},51050:function(H,P,i){"use strict";var x=i(73932),b=i(14289),v=i(53268),A=i(14730);function p(I){var E=A(I,function(d){var f=d.getSelection(),g=f.getStartOffset();if(g===0)return v(d,1);var s=f.getStartKey(),a=d.getCurrentContent(),c=a.getBlockForKey(s).getText().slice(0,g),o=x.getBackward(c);return v(d,o.length||1)},"backward");return E===I.getCurrentContent()?I:b.push(I,E,"remove-range")}H.exports=p},13767:function(H,P,i){"use strict";var x=i(73932),b=i(14289),v=i(19417),A=i(14730);function p(I){var E=A(I,function(d){var f=d.getSelection(),g=f.getStartOffset(),s=f.getStartKey(),a=d.getCurrentContent(),c=a.getBlockForKey(s).getText().slice(g),o=x.getForward(c);return v(d,o.length||1)},"forward");return E===I.getCurrentContent()?I:b.push(I,E,"remove-range")}H.exports=p},77978:function(H,P,i){"use strict";var x=i(42307),b=i(14289);function v(A){var p=x.splitBlock(A.getCurrentContent(),A.getSelection());return b.push(A,p,"split-block")}H.exports=v},67217:function(H,P,i){"use strict";var x=i(14289);function b(v){var A=v.getSelection(),p=A.getEndKey(),I=v.getCurrentContent(),E=I.getBlockForKey(p).getLength();return x.set(v,{selection:A.merge({anchorKey:p,anchorOffset:E,focusKey:p,focusOffset:E,isBackward:!1}),forceSelection:!0})}H.exports=b},8425:function(H,P,i){"use strict";var x=i(14289);function b(v){var A=v.getSelection(),p=A.getStartKey();return x.set(v,{selection:A.merge({anchorKey:p,anchorOffset:0,focusKey:p,focusOffset:0,isBackward:!1}),forceSelection:!0})}H.exports=b},62800:function(H,P,i){"use strict";var x=i(14289),b=i(38935),v=i(53268),A=i(14730);function p(I){var E=A(I,function(f){var g=f.getSelection(),s=f.getCurrentContent(),a=g.getAnchorKey(),c=g.getAnchorOffset(),o=s.getBlockForKey(a).getText()[c-1];return v(f,o?b.getUTF16Length(o,0):1)},"backward");if(E===I.getCurrentContent())return I;var d=I.getSelection();return x.push(I,E.set("selectionBefore",d),d.isCollapsed()?"backspace-character":"remove-range")}H.exports=p},13998:function(H,P,i){"use strict";var x=i(14289),b=i(38935),v=i(19417),A=i(14730);function p(I){var E=A(I,function(f){var g=f.getSelection(),s=f.getCurrentContent(),a=g.getAnchorKey(),c=g.getAnchorOffset(),o=s.getBlockForKey(a).getText()[c];return v(f,o?b.getUTF16Length(o,0):1)},"forward");if(E===I.getCurrentContent())return I;var d=I.getSelection();return x.push(I,E.set("selectionBefore",d),d.isCollapsed()?"delete-character":"remove-range")}H.exports=p},80138:function(H,P,i){"use strict";var x=i(42307),b=i(14289),v=i(88687);function A(p){var I=p.getSelection();if(!I.isCollapsed())return p;var E=I.getAnchorOffset();if(E===0)return p;var d=I.getAnchorKey(),f=p.getCurrentContent(),g=f.getBlockForKey(d),s=g.getLength();if(s<=1)return p;var a,c;E===s?(a=I.set("anchorOffset",E-1),c=I):(a=I.set("focusOffset",E+1),c=a.set("anchorOffset",E+1));var o=v(f,a),r=x.removeRange(f,a,"backward"),u=r.getSelectionAfter(),S=u.getAnchorOffset()-1,C=u.merge({anchorOffset:S,focusOffset:S}),l=x.replaceWithFragment(r,C,o),h=b.push(p,l,"insert-fragment");return b.acceptSelection(h,c)}H.exports=A},87051:function(H,P,i){"use strict";var x=i(14289);function b(v,A,p){var I=x.undo(A);if(A.getLastChangeType()==="spellcheck-change"){var E=I.getCurrentContent();p(x.set(I,{nativelyRenderedContent:E}));return}if(v.preventDefault(),!A.getNativelyRenderedContent()){p(I);return}p(x.set(A,{nativelyRenderedContent:null})),setTimeout(function(){p(I)},0)}H.exports=b},57429:function(H,P,i){"use strict";var x=i(43393),b=x.Map;function v(A,p,I){var E=p.getStartKey(),d=p.getEndKey(),f=A.getBlockMap(),g=f.toSeq().skipUntil(function(s,a){return a===E}).takeUntil(function(s,a){return a===d}).concat(b([[d,f.get(d)]])).map(I);return A.merge({blockMap:f.merge(g),selectionBefore:p,selectionAfter:p})}H.exports=v},61173:function(H,P,i){"use strict";var x=i(67953),b=i(43393),v=i(39506),A=i(73759),p=b.OrderedMap,I=b.List,E=function(s,a,c){if(!!s){var o=a.get(s);!o||a.set(s,c(o))}},d=function(s,a,c,o,r){if(!r)return s;var u=o==="after",S=a.getKey(),C=c.getKey(),l=a.getParentKey(),h=a.getNextSiblingKey(),D=a.getPrevSiblingKey(),k=c.getParentKey(),y=u?c.getNextSiblingKey():C,L=u?C:c.getPrevSiblingKey();return s.withMutations(function(T){E(l,T,function(B){var w=B.getChildKeys();return B.merge({children:w.delete(w.indexOf(S))})}),E(D,T,function(B){return B.merge({nextSibling:h})}),E(h,T,function(B){return B.merge({prevSibling:D})}),E(y,T,function(B){return B.merge({prevSibling:S})}),E(L,T,function(B){return B.merge({nextSibling:S})}),E(k,T,function(B){var w=B.getChildKeys(),z=w.indexOf(C),j=u?z+1:z!==0?z-1:0,U=w.toArray();return U.splice(j,0,S),B.merge({children:I(U)})}),E(S,T,function(B){return B.merge({nextSibling:y,prevSibling:L,parent:k})})})},f=function(s,a,c,o){o==="replace"&&A(!1);var r=c.getKey(),u=a.getKey();u===r&&A(!1);var S=s.getBlockMap(),C=a instanceof x,l=[a],h=S.delete(u);C&&(l=[],h=S.withMutations(function(w){var z=a.getNextSiblingKey(),j=v(a,w);w.toSeq().skipUntil(function(U){return U.getKey()===u}).takeWhile(function(U){var O=U.getKey(),ie=O===u,Q=z&&O!==z,ue=!z&&U.getParentKey()&&(!j||O!==j);return!!(ie||Q||ue)}).forEach(function(U){l.push(U),w.delete(U.getKey())})}));var D=h.toSeq().takeUntil(function(w){return w===c}),k=h.toSeq().skipUntil(function(w){return w===c}).skip(1),y=l.map(function(w){return[w.getKey(),w]}),L=p();if(o==="before"){var T=s.getBlockBefore(r);!T||T.getKey()!==a.getKey()||A(!1),L=D.concat([].concat(y,[[r,c]]),k).toOrderedMap()}else if(o==="after"){var B=s.getBlockAfter(r);!B||B.getKey()!==u||A(!1),L=D.concat([[r,c]].concat(y),k).toOrderedMap()}return s.merge({blockMap:d(L,a,c,o,C),selectionBefore:s.getSelectionAfter(),selectionAfter:s.getSelectionAfter().merge({anchorKey:u,focusKey:u})})};H.exports=f},53268:function(H){"use strict";function P(i,x){var b=i.getSelection(),v=i.getCurrentContent(),A=b.getStartKey(),p=b.getStartOffset(),I=A,E=0;if(x>p){var d=v.getKeyBefore(A);if(d==null)I=A;else{I=d;var f=v.getBlockForKey(d);E=f.getText().length}}else E=p-x;return b.merge({focusKey:I,focusOffset:E,isBackward:!0})}H.exports=P},19417:function(H){"use strict";function P(i,x){var b=i.getSelection(),v=b.getStartKey(),A=b.getStartOffset(),p=i.getCurrentContent(),I=v,E,d=p.getBlockForKey(v);return x>d.getText().length-A?(I=p.getKeyAfter(v),E=0):E=A+x,b.merge({focusKey:I,focusOffset:E})}H.exports=P},98555:function(H,P,i){"use strict";var x=i(67953),b=i(43393),v=i(25027),A=b.OrderedMap,p=function(f){var g={},s=void 0;return A(f.withMutations(function(a){a.forEach(function(c,o){var r=c.getKey(),u=c.getNextSiblingKey(),S=c.getPrevSiblingKey(),C=c.getChildKeys(),l=c.getParentKey(),h=v();if(g[r]=h,u){var D=a.get(u);D?a.setIn([u,"prevSibling"],h):a.setIn([r,"nextSibling"],null)}if(S){var k=a.get(S);k?a.setIn([S,"nextSibling"],h):a.setIn([r,"prevSibling"],null)}if(l&&a.get(l)){var y=a.get(l),L=y.getChildKeys();a.setIn([l,"children"],L.set(L.indexOf(c.getKey()),h))}else a.setIn([r,"parent"],null),s&&(a.setIn([s.getKey(),"nextSibling"],h),a.setIn([r,"prevSibling"],g[s.getKey()])),s=a.get(r);C.forEach(function(T){var B=a.get(T);B?a.setIn([T,"parent"],h):a.setIn([r,"children"],c.getChildKeys().filter(function(w){return w!==T}))})})}).toArray().map(function(a){return[g[a.getKey()],a.set("key",g[a.getKey()])]}))},I=function(f){return A(f.toArray().map(function(g){var s=v();return[s,g.set("key",s)]}))},E=function(f){var g=f.first()instanceof x;return g?p(f):I(f)};H.exports=E},14017:function(H,P,i){"use strict";var x=i(4516),b=i(29407),v=i(73759);function A(E,d){var f=E.getBlockMap(),g=E.getEntityMap(),s={},a=d.getStartKey(),c=d.getStartOffset(),o=f.get(a),r=I(g,o,c);r!==o&&(s[a]=r);var u=d.getEndKey(),S=d.getEndOffset(),C=f.get(u);a===u&&(C=r);var l=I(g,C,S);return l!==C&&(s[u]=l),Object.keys(s).length?E.merge({blockMap:f.merge(s),selectionAfter:d}):E.set("selectionAfter",d)}function p(E,d,f){var g;return b(E,function(s,a){return s.getEntity()===a.getEntity()},function(s){return s.getEntity()===d},function(s,a){s<=f&&a>=f&&(g={start:s,end:a})}),typeof g!="object"&&v(!1),g}function I(E,d,f){var g=d.getCharacterList(),s=f>0?g.get(f-1):void 0,a=f<g.count()?g.get(f):void 0,c=s?s.getEntity():void 0,o=a?a.getEntity():void 0;if(o&&o===c){var r=E.__get(o);if(r.getMutability()!=="MUTABLE"){for(var u=p(g,o,f),S=u.start,C=u.end,l;S<C;)l=g.get(S),g=g.set(S,x.applyEntity(l,null)),S++;return d.set("characterList",g)}}return d}H.exports=A},54879:function(H,P,i){"use strict";var x=i(67953),b=i(43393),v=i(39506),A=b.List,p=b.Map,I=function(r,u,S){if(!!r){var C=u.get(r);!C||u.set(r,S(C))}},E=function(r,u){var S=[];if(!r)return S;for(var C=u.get(r);C&&C.getParentKey();){var l=C.getParentKey();l&&S.push(l),C=l?u.get(l):null}return S},d=function(r,u){var S=[];if(!r)return S;for(var C=v(r,u);C&&u.get(C);){var l=u.get(C);S.push(C),C=l.getParentKey()?v(l,u):null}return S},f=function(r,u,S){if(!r)return null;for(var C=S.get(r.getKey()).getNextSiblingKey();C&&!u.get(C);)C=S.get(C).getNextSiblingKey()||null;return C},g=function(r,u,S){if(!r)return null;for(var C=S.get(r.getKey()).getPrevSiblingKey();C&&!u.get(C);)C=S.get(C).getPrevSiblingKey()||null;return C},s=function(r,u,S,C){return r.withMutations(function(l){I(u.getKey(),l,function(h){return h.merge({nextSibling:f(u,l,C),prevSibling:g(u,l,C)})}),I(S.getKey(),l,function(h){return h.merge({nextSibling:f(S,l,C),prevSibling:g(S,l,C)})}),E(u.getKey(),C).forEach(function(h){return I(h,l,function(D){return D.merge({children:D.getChildKeys().filter(function(k){return l.get(k)}),nextSibling:f(D,l,C),prevSibling:g(D,l,C)})})}),I(u.getNextSiblingKey(),l,function(h){return h.merge({prevSibling:u.getPrevSiblingKey()})}),I(u.getPrevSiblingKey(),l,function(h){return h.merge({nextSibling:f(u,l,C)})}),I(S.getNextSiblingKey(),l,function(h){return h.merge({prevSibling:g(S,l,C)})}),I(S.getPrevSiblingKey(),l,function(h){return h.merge({nextSibling:S.getNextSiblingKey()})}),E(S.getKey(),C).forEach(function(h){I(h,l,function(D){return D.merge({children:D.getChildKeys().filter(function(k){return l.get(k)}),nextSibling:f(D,l,C),prevSibling:g(D,l,C)})})}),d(S,C).forEach(function(h){return I(h,l,function(D){return D.merge({nextSibling:f(D,l,C),prevSibling:g(D,l,C)})})})})},a=function(r,u){if(u.isCollapsed())return r;var S=r.getBlockMap(),C=u.getStartKey(),l=u.getStartOffset(),h=u.getEndKey(),D=u.getEndOffset(),k=S.get(C),y=S.get(h),L=k instanceof x,T=[];if(L){var B=y.getChildKeys(),w=E(h,S);y.getNextSiblingKey()&&(T=T.concat(w)),B.isEmpty()||(T=T.concat(w.concat([h]))),T=T.concat(E(v(y,S),S))}var z=void 0;k===y?z=c(k.getCharacterList(),l,D):z=k.getCharacterList().slice(0,l).concat(y.getCharacterList().slice(D));var j=k.merge({text:k.getText().slice(0,l)+y.getText().slice(D),characterList:z}),U=S.toSeq().skipUntil(function(ie,Q){return Q===C}).takeUntil(function(ie,Q){return Q===h}).filter(function(ie,Q){return T.indexOf(Q)===-1}).concat(p([[h,null]])).map(function(ie,Q){return Q===C?j:null}),O=S.merge(U).filter(function(ie){return!!ie});return L&&(O=s(O,k,y,S)),r.merge({blockMap:O,selectionBefore:u,selectionAfter:u.merge({anchorKey:C,anchorOffset:l,focusKey:C,focusOffset:l,isBackward:!1})})},c=function(r,u,S){if(u===0)for(;u<S;)r=r.shift(),u++;else if(S===r.count())for(;S>u;)r=r.pop(),S--;else{var C=r.slice(0,u),l=r.slice(S);r=C.concat(l).toList()}return r};H.exports=a},14730:function(H,P,i){"use strict";var x=i(42307);function b(v,A,p){var I=v.getSelection(),E=v.getCurrentContent(),d=I;if(I.isCollapsed()){if(p==="forward"){if(v.isSelectionAtEndOfContent())return E}else if(v.isSelectionAtStartOfContent())return E;if(d=A(v),d===I)return E}return x.removeRange(E,d,p)}H.exports=b},55283:function(H){"use strict";var P=new RegExp("\r","g");function i(x){return x.replace(P,"")}H.exports=i},45412:function(H,P,i){"use strict";var x=i(97432),b=i(67476),v=i(31003),A=i(73759);function p(a,c){if(!a)return"[empty]";var o=I(a,c);return o.nodeType===Node.TEXT_NODE?o.textContent:(o instanceof Element||A(!1),o.outerHTML)}function I(a,c){var o=c!==void 0?c(a):[];if(a.nodeType===Node.TEXT_NODE){var r=a.textContent.length;return document.createTextNode("[text "+r+(o.length?" | "+o.join(", "):"")+"]")}var u=a.cloneNode();u.nodeType===1&&o.length&&u.setAttribute("data-labels",o.join(", "));for(var S=a.childNodes,C=0;C<S.length;C++)u.appendChild(I(S[C],c));return u}function E(a,c){for(var o=a;o;){if(o instanceof Element&&o.hasAttribute("contenteditable"))return p(o,c);o=o.parentNode}return"Could not find contentEditable parent of node"}function d(a){return a.nodeValue===null?a.childNodes.length:a.nodeValue.length}function f(a,c,o,r,u){if(!!b(document.documentElement,c)){var S=i.g.getSelection(),C=a.getAnchorKey(),l=a.getAnchorOffset(),h=a.getFocusKey(),D=a.getFocusOffset(),k=a.getIsBackward();if(!S.extend&&k){var y=C,L=l;C=h,l=D,h=y,D=L,k=!1}var T=C===o&&r<=l&&u>=l,B=h===o&&r<=D&&u>=D;if(T&&B){S.removeAllRanges(),s(S,c,l-r,a),g(S,c,D-r,a);return}if(!k)T&&(S.removeAllRanges(),s(S,c,l-r,a)),B&&g(S,c,D-r,a);else if(B&&(S.removeAllRanges(),s(S,c,D-r,a)),T){var w=S.focusNode,z=S.focusOffset;S.removeAllRanges(),s(S,c,l-r,a),g(S,w,z,a)}}}function g(a,c,o,r){var u=v();if(a.extend&&b(u,c)){o>d(c)&&x.logSelectionStateFailure({anonymizedDom:E(c),extraParams:JSON.stringify({offset:o}),selectionState:JSON.stringify(r.toJS())});var S=c===a.focusNode;try{a.extend(c,o)}catch(l){throw x.logSelectionStateFailure({anonymizedDom:E(c,function(h){var D=[];return h===u&&D.push("active element"),h===a.anchorNode&&D.push("selection anchor node"),h===a.focusNode&&D.push("selection focus node"),D}),extraParams:JSON.stringify({activeElementName:u?u.nodeName:null,nodeIsFocus:c===a.focusNode,nodeWasFocus:S,selectionRangeCount:a.rangeCount,selectionAnchorNodeName:a.anchorNode?a.anchorNode.nodeName:null,selectionAnchorOffset:a.anchorOffset,selectionFocusNodeName:a.focusNode?a.focusNode.nodeName:null,selectionFocusOffset:a.focusOffset,message:l?""+l:null,offset:o},null,2),selectionState:JSON.stringify(r.toJS(),null,2)}),l}}else{var C=a.getRangeAt(0);C.setEnd(c,o),a.addRange(C.cloneRange())}}function s(a,c,o,r){var u=document.createRange();o>d(c)&&x.logSelectionStateFailure({anonymizedDom:E(c),extraParams:JSON.stringify({offset:o}),selectionState:JSON.stringify(r.toJS())}),u.setStart(c,o),a.addRange(u)}H.exports=f},36043:function(H,P,i){"use strict";var x=i(67953),b=i(43393),v=i(25027),A=i(73759),p=b.List,I=b.Map,E=function(s,a,c){if(!!s){var o=a.get(s);!o||a.set(s,c(o))}},d=function(s,a,c){return s.withMutations(function(o){var r=a.getKey(),u=c.getKey();E(a.getParentKey(),o,function(S){var C=S.getChildKeys(),l=C.indexOf(r)+1,h=C.toArray();return h.splice(l,0,u),S.merge({children:p(h)})}),E(a.getNextSiblingKey(),o,function(S){return S.merge({prevSibling:u})}),E(r,o,function(S){return S.merge({nextSibling:u})}),E(u,o,function(S){return S.merge({prevSibling:r})})})},f=function(s,a){a.isCollapsed()||A(!1);var c=a.getAnchorKey(),o=a.getAnchorOffset(),r=s.getBlockMap(),u=r.get(c),S=u.getText(),C=u.getCharacterList(),l=v(),h=u instanceof x,D=u.merge({text:S.slice(0,o),characterList:C.slice(0,o)}),k=D.merge({key:l,text:S.slice(o),characterList:C.slice(o),data:I()}),y=r.toSeq().takeUntil(function(B){return B===u}),L=r.toSeq().skipUntil(function(B){return B===u}).rest(),T=y.concat([[c,D],[l,k]],L).toOrderedMap();return h&&(u.getChildKeys().isEmpty()||A(!1),T=d(T,D,k)),s.merge({blockMap:T,selectionBefore:a,selectionAfter:a.merge({anchorKey:l,anchorOffset:0,focusKey:l,focusOffset:0,isBackward:!1})})};H.exports=f},44300:function(H){"use strict";var P=/\r\n?|\n/g;function i(x){return x.split(P)}H.exports=i},5252:function(H,P,i){(function(x,b){H.exports=b(i(9041),i(43393))})(typeof self!="undefined"?self:this,function(x,b){return function(v){function A(I){if(p[I])return p[I].exports;var E=p[I]={i:I,l:!1,exports:{}};return v[I].call(E.exports,E,E.exports,A),E.l=!0,E.exports}var p={};return A.m=v,A.c=p,A.d=function(I,E,d){A.o(I,E)||Object.defineProperty(I,E,{configurable:!1,enumerable:!0,get:d})},A.n=function(I){var E=I&&I.__esModule?function(){return I.default}:function(){return I};return A.d(E,"a",E),E},A.o=function(I,E){return Object.prototype.hasOwnProperty.call(I,E)},A.p="",A(A.s=3)}([function(v,A){v.exports=x},function(v,A,p){"use strict";function I(k){var y=k.getSelection(),L=k.getCurrentContent(),T=y.getStartKey(),B=y.getEndKey(),w=L.getBlockMap();return w.toSeq().skipUntil(function(z,j){return j===T}).takeUntil(function(z,j){return j===B}).concat([[B,w.get(B)]])}function E(k){return I(k).toList()}function d(k){if(k)return E(k).get(0)}function f(k){if(k){var y=d(k),L=k.getCurrentContent(),T=L.getBlockMap().toSeq().toList(),B=0;if(T.forEach(function(w,z){w.get("key")===y.get("key")&&(B=z-1)}),B>-1)return T.get(B)}}function g(k){return k?k.getCurrentContent().getBlockMap().toList():new h.List}function s(k){var y=E(k);if(!y.some(function(L){return L.type!==y.get(0).type}))return y.get(0).type}function a(k){var y=l.RichUtils.tryToRemoveBlockStyle(k);return y?l.EditorState.push(k,y,"change-block-type"):k}function c(k){var y="",L=k.getSelection(),T=L.getAnchorOffset(),B=L.getFocusOffset(),w=E(k);if(w.size>0){if(L.getIsBackward()){var z=T;T=B,B=z}for(var j=0;j<w.size;j+=1){var U=j===0?T:0,O=j===w.size-1?B:w.get(j).getText().length;y+=w.get(j).getText().slice(U,O)}}return y}function o(k){var y=k.getCurrentContent(),L=k.getSelection(),T=l.Modifier.removeRange(y,L,"forward"),B=T.getSelectionAfter(),w=T.getBlockForKey(B.getStartKey());return T=l.Modifier.insertText(T,B,`
`,w.getInlineStyleAt(B.getStartOffset()),null),l.EditorState.push(k,T,"insert-fragment")}function r(k){var y=l.Modifier.splitBlock(k.getCurrentContent(),k.getSelection());return a(l.EditorState.push(k,y,"split-block"))}function u(k){var y=k.getCurrentContent().getBlockMap().toList(),L=k.getSelection().merge({anchorKey:y.first().get("key"),anchorOffset:0,focusKey:y.last().get("key"),focusOffset:y.last().getLength()}),T=l.Modifier.removeRange(k.getCurrentContent(),L,"forward");return l.EditorState.push(k,T,"remove-range")}function S(k,y){var L=l.Modifier.setBlockData(k.getCurrentContent(),k.getSelection(),y);return l.EditorState.push(k,L,"change-block-data")}function C(k){var y=new h.Map({}),L=E(k);if(L&&L.size>0)for(var T=0;T<L.size;T+=1){var B=function(w){var z=L.get(w).getData();if(!z||z.size===0)return y=y.clear(),"break";if(w===0)y=z;else if(y.forEach(function(j,U){z.get(U)&&z.get(U)===j||(y=y.delete(U))}),y.size===0)return y=y.clear(),"break"}(T);if(B==="break")break}return y}Object.defineProperty(A,"__esModule",{value:!0}),A.blockRenderMap=void 0,A.getSelectedBlocksMap=I,A.getSelectedBlocksList=E,A.getSelectedBlock=d,A.getBlockBeforeSelectedBlock=f,A.getAllBlocks=g,A.getSelectedBlocksType=s,A.removeSelectedBlocksStyle=a,A.getSelectionText=c,A.addLineBreakRemovingSelection=o,A.insertNewUnstyledBlock=r,A.clearEditorContent=u,A.setBlockData=S,A.getSelectedBlocksMetadata=C;var l=p(0),h=p(6),D=(0,h.Map)({code:{element:"pre"}});A.blockRenderMap=l.DefaultDraftBlockRenderMap.merge(D)},function(v,A,p){"use strict";function I(s){if(s){var a=s.getType();return a==="unordered-list-item"||a==="ordered-list-item"}return!1}function E(s,a,c){var o=s.getSelection(),r=s.getCurrentContent(),u=r.getBlockMap(),S=(0,g.getSelectedBlocksMap)(s).map(function(C){var l=C.getDepth()+a;return l=Math.max(0,Math.min(l,c)),C.set("depth",l)});return u=u.merge(S),r.merge({blockMap:u,selectionBefore:o,selectionAfter:o})}function d(s,a,c){var o=s.getSelection(),r=void 0;r=o.getIsBackward()?o.getFocusKey():o.getAnchorKey();var u=s.getCurrentContent(),S=u.getBlockForKey(r),C=S.getType();if(C!=="unordered-list-item"&&C!=="ordered-list-item")return s;var l=u.getBlockBefore(r);if(!l||l.getType()!==C)return s;var h=S.getDepth();if(a===1&&h===c)return s;var D=Math.min(l.getDepth()+1,c),k=E(s,a,D);return f.EditorState.push(s,k,"adjust-depth")}Object.defineProperty(A,"__esModule",{value:!0}),A.isListBlock=I,A.changeDepth=d;var f=p(0),g=p(1)},function(v,A,p){v.exports=p(4)},function(v,A,p){"use strict";var I=p(5),E=p(1),d=p(7),f=function(s){return s&&s.__esModule?s:{default:s}}(d),g=p(2);v.exports={getSelectedBlocksMap:E.getSelectedBlocksMap,getSelectedBlocksList:E.getSelectedBlocksList,getSelectedBlock:E.getSelectedBlock,getBlockBeforeSelectedBlock:E.getBlockBeforeSelectedBlock,getAllBlocks:E.getAllBlocks,getSelectedBlocksType:E.getSelectedBlocksType,removeSelectedBlocksStyle:E.removeSelectedBlocksStyle,getSelectionText:E.getSelectionText,addLineBreakRemovingSelection:E.addLineBreakRemovingSelection,insertNewUnstyledBlock:E.insertNewUnstyledBlock,clearEditorContent:E.clearEditorContent,setBlockData:E.setBlockData,getSelectedBlocksMetadata:E.getSelectedBlocksMetadata,blockRenderMap:E.blockRenderMap,getEntityRange:I.getEntityRange,getCustomStyleMap:I.getCustomStyleMap,toggleCustomInlineStyle:I.toggleCustomInlineStyle,getSelectionEntity:I.getSelectionEntity,extractInlineStyle:I.extractInlineStyle,removeAllInlineStyles:I.removeAllInlineStyles,getSelectionInlineStyle:I.getSelectionInlineStyle,getSelectionCustomInlineStyle:I.getSelectionCustomInlineStyle,handleNewLine:f.default,isListBlock:g.isListBlock,changeDepth:g.changeDepth}},function(v,A,p){"use strict";function I(k,y,L){return y in k?Object.defineProperty(k,y,{value:L,enumerable:!0,configurable:!0,writable:!0}):k[y]=L,k}function E(k){var y=k.getSelection();if(y.isCollapsed()){var L={},T=k.getCurrentInlineStyle().toList().toJS();if(T)return["BOLD","ITALIC","UNDERLINE","STRIKETHROUGH","CODE","SUPERSCRIPT","SUBSCRIPT"].forEach(function(U){L[U]=T.indexOf(U)>=0}),L}var B=y.getStartOffset(),w=y.getEndOffset(),z=(0,l.getSelectedBlocksList)(k);if(z.size>0){var j=function(){for(var U={BOLD:!0,ITALIC:!0,UNDERLINE:!0,STRIKETHROUGH:!0,CODE:!0,SUPERSCRIPT:!0,SUBSCRIPT:!0},O=0;O<z.size;O+=1){var ie=O===0?B:0,Q=O===z.size-1?w:z.get(O).getText().length;ie===Q&&ie===0?(ie=1,Q=2):ie===Q&&(ie-=1);for(var ue=ie;ue<Q;ue+=1)(function(Ee){var me=z.get(O).getInlineStyleAt(Ee);["BOLD","ITALIC","UNDERLINE","STRIKETHROUGH","CODE","SUPERSCRIPT","SUBSCRIPT"].forEach(function(V){U[V]=U[V]&&me.get(V)===V})})(ue)}return{v:U}}();if((j===void 0?"undefined":S(j))==="object")return j.v}return{}}function d(k){var y=void 0,L=k.getSelection(),T=L.getStartOffset(),B=L.getEndOffset();T===B&&T===0?B=1:T===B&&(T-=1);for(var w=(0,l.getSelectedBlock)(k),z=T;z<B;z+=1){var j=w.getEntityAt(z);if(!j){y=void 0;break}if(z===T)y=j;else if(y!==j){y=void 0;break}}return y}function f(k,y){var L=(0,l.getSelectedBlock)(k),T=void 0;return L.findEntityRanges(function(B){return B.get("entity")===y},function(B,w){T={start:B,end:w,text:L.get("text").slice(B,w)}}),T}function g(k,y,L){var T=k.getSelection(),B=Object.keys(h[y]).reduce(function(U,O){return C.Modifier.removeInlineStyle(U,T,O)},k.getCurrentContent()),w=C.EditorState.push(k,B,"changeinline-style"),z=k.getCurrentInlineStyle();if(T.isCollapsed()&&(w=z.reduce(function(U,O){return C.RichUtils.toggleInlineStyle(U,O)},w)),y==="SUPERSCRIPT"||y=="SUBSCRIPT")z.has(L)||(w=C.RichUtils.toggleInlineStyle(w,L));else{var j=y==="bgcolor"?"backgroundColor":y;z.has(j+"-"+L)||(w=C.RichUtils.toggleInlineStyle(w,y.toLowerCase()+"-"+L),D(y,j,L))}return w}function s(k){k&&k.getCurrentContent().getBlockMap().map(function(y){return y.get("characterList")}).toList().flatten().forEach(function(y){y&&y.indexOf("color-")===0?D("color","color",y.substr(6)):y&&y.indexOf("bgcolor-")===0?D("bgcolor","backgroundColor",y.substr(8)):y&&y.indexOf("fontsize-")===0?D("fontSize","fontSize",+y.substr(9)):y&&y.indexOf("fontfamily-")===0&&D("fontFamily","fontFamily",y.substr(11))})}function a(k,y,L){var T=k.getInlineStyleAt(L).toList(),B=T.filter(function(w){return w.startsWith(y.toLowerCase())});if(B&&B.size>0)return B.get(0)}function c(k,y){var L=k.getCurrentInlineStyle().toList(),T=L.filter(function(B){return B.startsWith(y.toLowerCase())});if(T&&T.size>0)return T.get(0)}function o(k,y){if(k&&y&&y.length>0){var L=function(){var T=k.getSelection(),B={};if(T.isCollapsed())return y.forEach(function(O){B[O]=c(k,O)}),{v:B};var w=T.getStartOffset(),z=T.getEndOffset(),j=(0,l.getSelectedBlocksList)(k);if(j.size>0){for(var U=0;U<j.size;U+=1)(function(O){var ie=O===0?w:0,Q=O===j.size-1?z:j.get(O).getText().length;ie===Q&&ie===0?(ie=1,Q=2):ie===Q&&(ie-=1);for(var ue=ie;ue<Q;ue+=1)(function(Ee){Ee===ie?y.forEach(function(me){B[me]=a(j.get(O),me,Ee)}):y.forEach(function(me){B[me]&&B[me]!==a(j.get(O),me,Ee)&&(B[me]=void 0)})})(ue)})(U);return{v:B}}}();if((L===void 0?"undefined":S(L))==="object")return L.v}return{}}function r(k){var y=k.getCurrentInlineStyle(),L=k.getCurrentContent();return y.forEach(function(T){L=C.Modifier.removeInlineStyle(L,k.getSelection(),T)}),C.EditorState.push(k,L,"change-inline-style")}Object.defineProperty(A,"__esModule",{value:!0}),A.getCustomStyleMap=void 0;var u=Object.assign||function(k){for(var y=1;y<arguments.length;y++){var L=arguments[y];for(var T in L)Object.prototype.hasOwnProperty.call(L,T)&&(k[T]=L[T])}return k},S=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(k){return typeof k}:function(k){return k&&typeof Symbol=="function"&&k.constructor===Symbol&&k!==Symbol.prototype?"symbol":typeof k};A.getSelectionInlineStyle=E,A.getSelectionEntity=d,A.getEntityRange=f,A.toggleCustomInlineStyle=g,A.extractInlineStyle=s,A.getSelectionCustomInlineStyle=o,A.removeAllInlineStyles=r;var C=p(0),l=p(1),h={color:{},bgcolor:{},fontSize:{},fontFamily:{},CODE:{fontFamily:"monospace",wordWrap:"break-word",background:"#f1f1f1",borderRadius:3,padding:"1px 3px"},SUPERSCRIPT:{fontSize:11,position:"relative",top:-8,display:"inline-flex"},SUBSCRIPT:{fontSize:11,position:"relative",bottom:-8,display:"inline-flex"}},D=function(k,y,L){h[k][k.toLowerCase()+"-"+L]=I({},""+y,L)};A.getCustomStyleMap=function(){return u({},h.color,h.bgcolor,h.fontSize,h.fontFamily,{CODE:h.CODE,SUPERSCRIPT:h.SUPERSCRIPT,SUBSCRIPT:h.SUBSCRIPT})}},function(v,A){v.exports=b},function(v,A,p){"use strict";function I(a){var c=a.getSelection();if(c.isCollapsed()){var o=a.getCurrentContent(),r=c.getStartKey(),u=o.getBlockForKey(r);if(!(0,s.isListBlock)(u)&&u.getType()!=="unstyled"&&u.getLength()===c.getStartOffset())return(0,g.insertNewUnstyledBlock)(a);if((0,s.isListBlock)(u)&&u.getLength()===0){var S=u.getDepth();if(S===0)return(0,g.removeSelectedBlocksStyle)(a);if(S>0)return(0,s.changeDepth)(a,-1,S)}}}function E(a){return a.which===13&&(a.getModifierState("Shift")||a.getModifierState("Alt")||a.getModifierState("Control"))}function d(a,c){return E(c)?a.getSelection().isCollapsed()?f.RichUtils.insertSoftNewline(a):(0,g.addLineBreakRemovingSelection)(a):I(a)}Object.defineProperty(A,"__esModule",{value:!0}),A.default=d;var f=p(0),g=p(1),s=p(2)}])})},44891:function(H,P,i){"use strict";function x(g,s){if(!(g instanceof s))throw new TypeError("Cannot call a class as a function")}var b=i(51006),v=i(89825),A=i(60139),p=new RegExp(`\r
`,"g"),I=`
`,E={"text/rtf":1,"text/html":1};function d(g){if(g.kind=="file")return g.getAsFile()}var f=function(){function g(s){x(this,g),this.data=s,this.types=s.types?v(s.types):[]}return g.prototype.isRichText=function(){return this.getHTML()&&this.getText()?!0:this.isImage()?!1:this.types.some(function(a){return E[a]})},g.prototype.getText=function(){var a;return this.data.getData&&(this.types.length?this.types.indexOf("text/plain")!=-1&&(a=this.data.getData("text/plain")):a=this.data.getData("Text")),a?a.replace(p,I):null},g.prototype.getHTML=function(){if(this.data.getData)if(this.types.length){if(this.types.indexOf("text/html")!=-1)return this.data.getData("text/html")}else return this.data.getData("Text")},g.prototype.isLink=function(){return this.types.some(function(a){return a.indexOf("Url")!=-1||a.indexOf("text/uri-list")!=-1||a.indexOf("text/x-moz-url")})},g.prototype.getLink=function(){if(this.data.getData){if(this.types.indexOf("text/x-moz-url")!=-1){var a=this.data.getData("text/x-moz-url").split(`
`);return a[0]}return this.types.indexOf("text/uri-list")!=-1?this.data.getData("text/uri-list"):this.data.getData("url")}return null},g.prototype.isImage=function(){var a=this.types.some(function(u){return u.indexOf("application/x-moz-file")!=-1});if(a)return!0;for(var c=this.getFiles(),o=0;o<c.length;o++){var r=c[o].type;if(!b.isImage(r))return!1}return!0},g.prototype.getCount=function(){return this.data.hasOwnProperty("items")?this.data.items.length:this.data.hasOwnProperty("mozItemCount")?this.data.mozItemCount:this.data.files?this.data.files.length:null},g.prototype.getFiles=function(){return this.data.items?Array.prototype.slice.call(this.data.items).map(d).filter(A.thatReturnsArgument):this.data.files?Array.prototype.slice.call(this.data.files):[]},g.prototype.hasFiles=function(){return this.getFiles().length>0},g}();H.exports=f},25399:function(H){"use strict";H.exports={BACKSPACE:8,TAB:9,RETURN:13,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46,COMMA:188,PERIOD:190,A:65,Z:90,ZERO:48,NUMPAD_0:96,NUMPAD_9:105}},51006:function(H){"use strict";var P={isImage:function(b){return i(b)[0]==="image"},isJpeg:function(b){var v=i(b);return P.isImage(b)&&(v[1]==="jpeg"||v[1]==="pjpeg")}};function i(x){return x.split("/")}H.exports=P},65994:function(H){"use strict";function P(x,b){return!!b&&(x===b.documentElement||x===b.body)}var i={getTop:function(b){var v=b.ownerDocument;return P(b,v)?v.body.scrollTop||v.documentElement.scrollTop:b.scrollTop},setTop:function(b,v){var A=b.ownerDocument;P(b,A)?A.body.scrollTop=A.documentElement.scrollTop=v:b.scrollTop=v},getLeft:function(b){var v=b.ownerDocument;return P(b,v)?v.body.scrollLeft||v.documentElement.scrollLeft:b.scrollLeft},setLeft:function(b,v){var A=b.ownerDocument;P(b,A)?A.body.scrollLeft=A.documentElement.scrollLeft=v:b.scrollLeft=v}};H.exports=i},19051:function(H,P,i){"use strict";var x=i(85466);function b(A,p){var I=v.get(A,p);return I==="auto"||I==="scroll"}var v={get:x,getScrollParent:function(p){if(!p)return null;for(var I=p.ownerDocument;p&&p!==I.body;){if(b(p,"overflow")||b(p,"overflowY")||b(p,"overflowX"))return p;p=p.parentNode}return I.defaultView||I.parentWindow}};H.exports=v},65724:function(H){"use strict";var P=`[.,+*?$|#{}()'\\^\\-\\[\\]\\\\\\/!@%"~=<>_:;\u30FB\u3001\u3002\u3008-\u3011\u3014-\u301F\uFF1A-\uFF1F\uFF01-\uFF0F\uFF3B-\uFF40\uFF5B-\uFF65\u2E2E\u061F\u066A-\u066C\u061B\u060C\u060D\uFD3E\uFD3F\u1801\u0964\u104A\u104B\u2010-\u2027\u2030-\u205E\xA1-\xB1\xB4-\xB8\xBA\xBB\xBF]`;H.exports={getPunctuation:function(){return P}}},61425:function(H){"use strict";function P(x,b){if(!(x instanceof b))throw new TypeError("Cannot call a class as a function")}var i=function(){function x(b){P(this,x),this._uri=b}return x.prototype.toString=function(){return this._uri},x}();H.exports=i},54191:function(H,P,i){"use strict";var x=i(16633),b=i(73759),v={L:"A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u01BA\u01BB\u01BC-\u01BF\u01C0-\u01C3\u01C4-\u0293\u0294\u0295-\u02AF\u02B0-\u02B8\u02BB-\u02C1\u02D0-\u02D1\u02E0-\u02E4\u02EE\u0370-\u0373\u0376-\u0377\u037A\u037B-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0482\u048A-\u052F\u0531-\u0556\u0559\u055A-\u055F\u0561-\u0587\u0589\u0903\u0904-\u0939\u093B\u093D\u093E-\u0940\u0949-\u094C\u094E-\u094F\u0950\u0958-\u0961\u0964-\u0965\u0966-\u096F\u0970\u0971\u0972-\u0980\u0982-\u0983\u0985-\u098C\u098F-\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09BE-\u09C0\u09C7-\u09C8\u09CB-\u09CC\u09CE\u09D7\u09DC-\u09DD\u09DF-\u09E1\u09E6-\u09EF\u09F0-\u09F1\u09F4-\u09F9\u09FA\u0A03\u0A05-\u0A0A\u0A0F-\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32-\u0A33\u0A35-\u0A36\u0A38-\u0A39\u0A3E-\u0A40\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2-\u0AB3\u0AB5-\u0AB9\u0ABD\u0ABE-\u0AC0\u0AC9\u0ACB-\u0ACC\u0AD0\u0AE0-\u0AE1\u0AE6-\u0AEF\u0AF0\u0B02-\u0B03\u0B05-\u0B0C\u0B0F-\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32-\u0B33\u0B35-\u0B39\u0B3D\u0B3E\u0B40\u0B47-\u0B48\u0B4B-\u0B4C\u0B57\u0B5C-\u0B5D\u0B5F-\u0B61\u0B66-\u0B6F\u0B70\u0B71\u0B72-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99-\u0B9A\u0B9C\u0B9E-\u0B9F\u0BA3-\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BBF\u0BC1-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCC\u0BD0\u0BD7\u0BE6-\u0BEF\u0BF0-\u0BF2\u0C01-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C41-\u0C44\u0C58-\u0C59\u0C60-\u0C61\u0C66-\u0C6F\u0C7F\u0C82-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CBE\u0CBF\u0CC0-\u0CC4\u0CC6\u0CC7-\u0CC8\u0CCA-\u0CCB\u0CD5-\u0CD6\u0CDE\u0CE0-\u0CE1\u0CE6-\u0CEF\u0CF1-\u0CF2\u0D02-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D3E-\u0D40\u0D46-\u0D48\u0D4A-\u0D4C\u0D4E\u0D57\u0D60-\u0D61\u0D66-\u0D6F\u0D70-\u0D75\u0D79\u0D7A-\u0D7F\u0D82-\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCF-\u0DD1\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2-\u0DF3\u0DF4\u0E01-\u0E30\u0E32-\u0E33\u0E40-\u0E45\u0E46\u0E4F\u0E50-\u0E59\u0E5A-\u0E5B\u0E81-\u0E82\u0E84\u0E87-\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA-\u0EAB\u0EAD-\u0EB0\u0EB2-\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F01-\u0F03\u0F04-\u0F12\u0F13\u0F14\u0F15-\u0F17\u0F1A-\u0F1F\u0F20-\u0F29\u0F2A-\u0F33\u0F34\u0F36\u0F38\u0F3E-\u0F3F\u0F40-\u0F47\u0F49-\u0F6C\u0F7F\u0F85\u0F88-\u0F8C\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE-\u0FCF\u0FD0-\u0FD4\u0FD5-\u0FD8\u0FD9-\u0FDA\u1000-\u102A\u102B-\u102C\u1031\u1038\u103B-\u103C\u103F\u1040-\u1049\u104A-\u104F\u1050-\u1055\u1056-\u1057\u105A-\u105D\u1061\u1062-\u1064\u1065-\u1066\u1067-\u106D\u106E-\u1070\u1075-\u1081\u1083-\u1084\u1087-\u108C\u108E\u108F\u1090-\u1099\u109A-\u109C\u109E-\u109F\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FB\u10FC\u10FD-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1360-\u1368\u1369-\u137C\u1380-\u138F\u13A0-\u13F4\u1401-\u166C\u166D-\u166E\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EB-\u16ED\u16EE-\u16F0\u16F1-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1735-\u1736\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17B6\u17BE-\u17C5\u17C7-\u17C8\u17D4-\u17D6\u17D7\u17D8-\u17DA\u17DC\u17E0-\u17E9\u1810-\u1819\u1820-\u1842\u1843\u1844-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1923-\u1926\u1929-\u192B\u1930-\u1931\u1933-\u1938\u1946-\u194F\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C0\u19C1-\u19C7\u19C8-\u19C9\u19D0-\u19D9\u19DA\u1A00-\u1A16\u1A19-\u1A1A\u1A1E-\u1A1F\u1A20-\u1A54\u1A55\u1A57\u1A61\u1A63-\u1A64\u1A6D-\u1A72\u1A80-\u1A89\u1A90-\u1A99\u1AA0-\u1AA6\u1AA7\u1AA8-\u1AAD\u1B04\u1B05-\u1B33\u1B35\u1B3B\u1B3D-\u1B41\u1B43-\u1B44\u1B45-\u1B4B\u1B50-\u1B59\u1B5A-\u1B60\u1B61-\u1B6A\u1B74-\u1B7C\u1B82\u1B83-\u1BA0\u1BA1\u1BA6-\u1BA7\u1BAA\u1BAE-\u1BAF\u1BB0-\u1BB9\u1BBA-\u1BE5\u1BE7\u1BEA-\u1BEC\u1BEE\u1BF2-\u1BF3\u1BFC-\u1BFF\u1C00-\u1C23\u1C24-\u1C2B\u1C34-\u1C35\u1C3B-\u1C3F\u1C40-\u1C49\u1C4D-\u1C4F\u1C50-\u1C59\u1C5A-\u1C77\u1C78-\u1C7D\u1C7E-\u1C7F\u1CC0-\u1CC7\u1CD3\u1CE1\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF2-\u1CF3\u1CF5-\u1CF6\u1D00-\u1D2B\u1D2C-\u1D6A\u1D6B-\u1D77\u1D78\u1D79-\u1D9A\u1D9B-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200E\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2134\u2135-\u2138\u2139\u213C-\u213F\u2145-\u2149\u214E\u214F\u2160-\u2182\u2183-\u2184\u2185-\u2188\u2336-\u237A\u2395\u249C-\u24E9\u26AC\u2800-\u28FF\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2C7B\u2C7C-\u2C7D\u2C7E-\u2CE4\u2CEB-\u2CEE\u2CF2-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D70\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005\u3006\u3007\u3021-\u3029\u302E-\u302F\u3031-\u3035\u3038-\u303A\u303B\u303C\u3041-\u3096\u309D-\u309E\u309F\u30A1-\u30FA\u30FC-\u30FE\u30FF\u3105-\u312D\u3131-\u318E\u3190-\u3191\u3192-\u3195\u3196-\u319F\u31A0-\u31BA\u31F0-\u31FF\u3200-\u321C\u3220-\u3229\u322A-\u3247\u3248-\u324F\u3260-\u327B\u327F\u3280-\u3289\u328A-\u32B0\u32C0-\u32CB\u32D0-\u32FE\u3300-\u3376\u337B-\u33DD\u33E0-\u33FE\u3400-\u4DB5\u4E00-\u9FCC\uA000-\uA014\uA015\uA016-\uA48C\uA4D0-\uA4F7\uA4F8-\uA4FD\uA4FE-\uA4FF\uA500-\uA60B\uA60C\uA610-\uA61F\uA620-\uA629\uA62A-\uA62B\uA640-\uA66D\uA66E\uA680-\uA69B\uA69C-\uA69D\uA6A0-\uA6E5\uA6E6-\uA6EF\uA6F2-\uA6F7\uA722-\uA76F\uA770\uA771-\uA787\uA789-\uA78A\uA78B-\uA78E\uA790-\uA7AD\uA7B0-\uA7B1\uA7F7\uA7F8-\uA7F9\uA7FA\uA7FB-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA823-\uA824\uA827\uA830-\uA835\uA836-\uA837\uA840-\uA873\uA880-\uA881\uA882-\uA8B3\uA8B4-\uA8C3\uA8CE-\uA8CF\uA8D0-\uA8D9\uA8F2-\uA8F7\uA8F8-\uA8FA\uA8FB\uA900-\uA909\uA90A-\uA925\uA92E-\uA92F\uA930-\uA946\uA952-\uA953\uA95F\uA960-\uA97C\uA983\uA984-\uA9B2\uA9B4-\uA9B5\uA9BA-\uA9BB\uA9BD-\uA9C0\uA9C1-\uA9CD\uA9CF\uA9D0-\uA9D9\uA9DE-\uA9DF\uA9E0-\uA9E4\uA9E6\uA9E7-\uA9EF\uA9F0-\uA9F9\uA9FA-\uA9FE\uAA00-\uAA28\uAA2F-\uAA30\uAA33-\uAA34\uAA40-\uAA42\uAA44-\uAA4B\uAA4D\uAA50-\uAA59\uAA5C-\uAA5F\uAA60-\uAA6F\uAA70\uAA71-\uAA76\uAA77-\uAA79\uAA7A\uAA7B\uAA7D\uAA7E-\uAAAF\uAAB1\uAAB5-\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADC\uAADD\uAADE-\uAADF\uAAE0-\uAAEA\uAAEB\uAAEE-\uAAEF\uAAF0-\uAAF1\uAAF2\uAAF3-\uAAF4\uAAF5\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5B\uAB5C-\uAB5F\uAB64-\uAB65\uABC0-\uABE2\uABE3-\uABE4\uABE6-\uABE7\uABE9-\uABEA\uABEB\uABEC\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uE000-\uF8FF\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFF6F\uFF70\uFF71-\uFF9D\uFF9E-\uFF9F\uFFA0-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",R:"\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05D0-\u05EA\u05EB-\u05EF\u05F0-\u05F2\u05F3-\u05F4\u05F5-\u05FF\u07C0-\u07C9\u07CA-\u07EA\u07F4-\u07F5\u07FA\u07FB-\u07FF\u0800-\u0815\u081A\u0824\u0828\u082E-\u082F\u0830-\u083E\u083F\u0840-\u0858\u085C-\u085D\u085E\u085F-\u089F\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB37\uFB38-\uFB3C\uFB3D\uFB3E\uFB3F\uFB40-\uFB41\uFB42\uFB43-\uFB44\uFB45\uFB46-\uFB4F",AL:"\u0608\u060B\u060D\u061B\u061C\u061D\u061E-\u061F\u0620-\u063F\u0640\u0641-\u064A\u066D\u066E-\u066F\u0671-\u06D3\u06D4\u06D5\u06E5-\u06E6\u06EE-\u06EF\u06FA-\u06FC\u06FD-\u06FE\u06FF\u0700-\u070D\u070E\u070F\u0710\u0712-\u072F\u074B-\u074C\u074D-\u07A5\u07B1\u07B2-\u07BF\u08A0-\u08B2\u08B3-\u08E3\uFB50-\uFBB1\uFBB2-\uFBC1\uFBC2-\uFBD2\uFBD3-\uFD3D\uFD40-\uFD4F\uFD50-\uFD8F\uFD90-\uFD91\uFD92-\uFDC7\uFDC8-\uFDCF\uFDF0-\uFDFB\uFDFC\uFDFE-\uFDFF\uFE70-\uFE74\uFE75\uFE76-\uFEFC\uFEFD-\uFEFE"},A=new RegExp("["+v.L+v.R+v.AL+"]"),p=new RegExp("["+v.R+v.AL+"]");function I(c){var o=A.exec(c);return o==null?null:o[0]}function E(c){var o=I(c);return o==null?x.NEUTRAL:p.exec(o)?x.RTL:x.LTR}function d(c,o){if(o=o||x.NEUTRAL,!c.length)return o;var r=E(c);return r===x.NEUTRAL?o:r}function f(c,o){return o||(o=x.getGlobalDir()),x.isStrong(o)||b(!1),d(c,o)}function g(c,o){return f(c,o)===x.LTR}function s(c,o){return f(c,o)===x.RTL}var a={firstStrongChar:I,firstStrongCharDir:E,resolveBlockDir:d,getDirection:f,isDirectionLTR:g,isDirectionRTL:s};H.exports=a},16633:function(H,P,i){"use strict";var x=i(73759),b="NEUTRAL",v="LTR",A="RTL",p=null;function I(c){return c===v||c===A}function E(c){return I(c)||x(!1),c===v?"ltr":"rtl"}function d(c,o){return I(c)||x(!1),I(o)||x(!1),c===o?null:E(c)}function f(c){p=c}function g(){f(v)}function s(){return p||this.initGlobalDir(),p||x(!1),p}var a={NEUTRAL:b,LTR:v,RTL:A,isStrong:I,getHTMLDir:E,getHTMLDirIfDifferent:d,setGlobalDir:f,initGlobalDir:g,getGlobalDir:s};H.exports=a},7902:function(H,P,i){"use strict";function x(I,E){if(!(I instanceof E))throw new TypeError("Cannot call a class as a function")}var b=i(54191),v=i(16633),A=i(73759),p=function(){function I(E){x(this,I),E?v.isStrong(E)||A(!1):E=v.getGlobalDir(),this._defaultDir=E,this.reset()}return I.prototype.reset=function(){this._lastDir=this._defaultDir},I.prototype.getDirection=function(d){return this._lastDir=b.getDirection(d,this._lastDir),this._lastDir},I}();H.exports=p},38935:function(H,P,i){"use strict";var x=i(73759),b=55296,v=56319,A=56320,p=57343,I=/[\uD800-\uDFFF]/;function E(u){return b<=u&&u<=p}function d(u,S){if(0<=S&&S<u.length||x(!1),S+1===u.length)return!1;var C=u.charCodeAt(S),l=u.charCodeAt(S+1);return b<=C&&C<=v&&A<=l&&l<=p}function f(u){return I.test(u)}function g(u,S){return 1+E(u.charCodeAt(S))}function s(u){if(!f(u))return u.length;for(var S=0,C=0;C<u.length;C+=g(u,C))S++;return S}function a(u,S,C){if(S=S||0,C=C===void 0?Infinity:C||0,!f(u))return u.substr(S,C);var l=u.length;if(l<=0||S>l||C<=0)return"";var h=0;if(S>0){for(;S>0&&h<l;S--)h+=g(u,h);if(h>=l)return""}else if(S<0){for(h=l;S<0&&0<h;S++)h-=g(u,h-1);h<0&&(h=0)}var D=l;if(C<l)for(D=h;C>0&&D<l;C--)D+=g(u,D);return u.substring(h,D)}function c(u,S,C){S=S||0,C=C===void 0?Infinity:C||0,S<0&&(S=0),C<0&&(C=0);var l=Math.abs(C-S);return S=S<C?S:C,a(u,S,l)}function o(u){for(var S=[],C=0;C<u.length;C+=g(u,C))S.push(u.codePointAt(C));return S}var r={getCodePoints:o,getUTF16Length:g,hasSurrogateUnit:f,isCodeUnitInSurrogateRange:E,isSurrogatePair:d,strlen:s,substring:c,substr:a};H.exports=r},4856:function(H,P,i){"use strict";var x=i(95845),b=i(59859),v=i(79467),A=i(51767);function p(d,f,g,s){if(d===g)return!0;if(!g.startsWith(d))return!1;var a=g.slice(d.length);return f?(a=s?s(a):a,b.contains(a,f)):!1}function I(d){return x.platformName==="Windows"?d.replace(/^\s*NT/,""):d}var E={isBrowser:function(f){return p(x.browserName,x.browserFullVersion,f)},isBrowserArchitecture:function(f){return p(x.browserArchitecture,null,f)},isDevice:function(f){return p(x.deviceName,null,f)},isEngine:function(f){return p(x.engineName,x.engineVersion,f)},isPlatform:function(f){return p(x.platformName,x.platformFullVersion,f,I)},isPlatformArchitecture:function(f){return p(x.platformArchitecture,null,f)}};H.exports=v(E,A)},95845:function(H,P,i){"use strict";var x=i(42238),b="Unknown",v={"Mac OS":"Mac OS X"};function A(g){return v[g]||g}function p(g){if(!g)return{major:"",minor:""};var s=g.split(".");return{major:s[0],minor:s[1]}}var I=new x,E=I.getResult(),d=p(E.browser.version),f={browserArchitecture:E.cpu.architecture||b,browserFullVersion:E.browser.version||b,browserMinorVersion:d.minor||b,browserName:E.browser.name||b,browserVersion:E.browser.major||b,deviceName:E.device.model||b,engineName:E.engine.name||b,engineVersion:E.engine.version||b,platformArchitecture:E.cpu.architecture||b,platformName:A(E.os.name)||b,platformVersion:E.os.version||b,platformFullVersion:E.os.version||b};H.exports=f},59859:function(H,P,i){"use strict";var x=i(73759),b=/\./,v=/\|\|/,A=/\s+\-\s+/,p=/^(<=|<|=|>=|~>|~|>|)?\s*(.+)/,I=/^(\d*)(.*)/;function E(T,B){var w=T.split(v);return w.length>1?w.some(function(z){return L.contains(z,B)}):(T=w[0].trim(),d(T,B))}function d(T,B){var w=T.split(A);if(w.length>0&&w.length<=2||x(!1),w.length===1)return f(w[0],B);var z=w[0],j=w[1];return C(z)&&C(j)||x(!1),f(">="+z,B)&&f("<="+j,B)}function f(T,B){if(T=T.trim(),T==="")return!0;var w=B.split(b),z=u(T),j=z.modifier,U=z.rangeComponents;switch(j){case"<":return g(w,U);case"<=":return s(w,U);case">=":return c(w,U);case">":return o(w,U);case"~":case"~>":return r(w,U);default:return a(w,U)}}function g(T,B){return y(T,B)===-1}function s(T,B){var w=y(T,B);return w===-1||w===0}function a(T,B){return y(T,B)===0}function c(T,B){var w=y(T,B);return w===1||w===0}function o(T,B){return y(T,B)===1}function r(T,B){var w=B.slice(),z=B.slice();z.length>1&&z.pop();var j=z.length-1,U=parseInt(z[j],10);return S(U)&&(z[j]=U+1+""),c(T,w)&&g(T,z)}function u(T){var B=T.split(b),w=B[0].match(p);return w||x(!1),{modifier:w[1],rangeComponents:[w[2]].concat(B.slice(1))}}function S(T){return!isNaN(T)&&isFinite(T)}function C(T){return!u(T).modifier}function l(T,B){for(var w=T.length;w<B;w++)T[w]="0"}function h(T,B){T=T.slice(),B=B.slice(),l(T,B.length);for(var w=0;w<B.length;w++){var z=B[w].match(/^[x*]$/i);if(z&&(B[w]=T[w]="0",z[0]==="*"&&w===B.length-1))for(var j=w;j<T.length;j++)T[j]="0"}return l(B,T.length),[T,B]}function D(T,B){var w=T.match(I)[1],z=B.match(I)[1],j=parseInt(w,10),U=parseInt(z,10);return S(j)&&S(U)&&j!==U?k(j,U):k(T,B)}function k(T,B){return typeof T!=typeof B&&x(!1),T>B?1:T<B?-1:0}function y(T,B){for(var w=h(T,B),z=w[0],j=w[1],U=0;U<j.length;U++){var O=D(z[U],j[U]);if(O)return O}return 0}var L={contains:function(B,w){return E(B.trim(),w.trim())}};H.exports=L},52297:function(H){"use strict";var P=/-(.)/g;function i(x){return x.replace(P,function(b,v){return v.toUpperCase()})}H.exports=i},67476:function(H,P,i){"use strict";var x=i(52334);function b(v,A){return!v||!A?!1:v===A?!0:x(v)?!1:x(A)?b(v,A.parentNode):"contains"in v?v.contains(A):v.compareDocumentPosition?!!(v.compareDocumentPosition(A)&16):!1}H.exports=b},89825:function(H,P,i){"use strict";var x=i(73759);function b(p){var I=p.length;if(!Array.isArray(p)&&(typeof p=="object"||typeof p=="function")||x(!1),typeof I!="number"&&x(!1),I===0||I-1 in p||x(!1),typeof p.callee=="function"&&x(!1),p.hasOwnProperty)try{return Array.prototype.slice.call(p)}catch(f){}for(var E=Array(I),d=0;d<I;d++)E[d]=p[d];return E}function v(p){return!!p&&(typeof p=="object"||typeof p=="function")&&"length"in p&&!("setInterval"in p)&&typeof p.nodeType!="number"&&(Array.isArray(p)||"callee"in p||"item"in p)}function A(p){return v(p)?Array.isArray(p)?p.slice():b(p):[p]}H.exports=A},62620:function(H){"use strict";function P(x){return typeof x=="object"?Object.keys(x).filter(function(b){return x[b]}).map(i).join(" "):Array.prototype.map.call(arguments,i).join(" ")}function i(x){return x.replace(/\//g,"-")}H.exports=P},60139:function(H){"use strict";function P(x){return function(){return x}}var i=function(){};i.thatReturns=P,i.thatReturnsFalse=P(!1),i.thatReturnsTrue=P(!0),i.thatReturnsNull=P(null),i.thatReturnsThis=function(){return this},i.thatReturnsArgument=function(x){return x},H.exports=i},31003:function(H){"use strict";function P(i){if(i=i||(typeof document!="undefined"?document:void 0),typeof i=="undefined")return null;try{return i.activeElement||i.body}catch(x){return i.body}}H.exports=P},35179:function(H){"use strict";var P=typeof navigator!="undefined"&&navigator.userAgent.indexOf("AppleWebKit")>-1;function i(x){return x=x||document,x.scrollingElement?x.scrollingElement:!P&&x.compatMode==="CSS1Compat"?x.documentElement:x.body}H.exports=i},55258:function(H,P,i){"use strict";var x=i(67026);function b(v){var A=x(v);return{x:A.left,y:A.top,width:A.right-A.left,height:A.bottom-A.top}}H.exports=b},67026:function(H,P,i){"use strict";var x=i(67476);function b(v){var A=v.ownerDocument.documentElement;if(!("getBoundingClientRect"in v)||!x(A,v))return{left:0,right:0,top:0,bottom:0};var p=v.getBoundingClientRect();return{left:Math.round(p.left)-A.clientLeft,right:Math.round(p.right)-A.clientLeft,top:Math.round(p.top)-A.clientTop,bottom:Math.round(p.bottom)-A.clientTop}}H.exports=b},79749:function(H,P,i){"use strict";var x=i(35179),b=i(30787);function v(A){var p=x(A.ownerDocument||A.document);A.Window&&A instanceof A.Window&&(A=p);var I=b(A),E=A===p?A.ownerDocument.documentElement:A,d=A.scrollWidth-E.clientWidth,f=A.scrollHeight-E.clientHeight;return I.x=Math.max(0,Math.min(I.x,d)),I.y=Math.max(0,Math.min(I.y,f)),I}H.exports=v},85466:function(H,P,i){"use strict";var x=i(52297),b=i(89349);function v(p){return p==null?p:String(p)}function A(p,I){var E=void 0;if(window.getComputedStyle&&(E=window.getComputedStyle(p,null),E))return v(E.getPropertyValue(b(I)));if(document.defaultView&&document.defaultView.getComputedStyle){if(E=document.defaultView.getComputedStyle(p,null),E)return v(E.getPropertyValue(b(I)));if(I==="display")return"none"}return p.currentStyle?v(I==="float"?p.currentStyle.cssFloat||p.currentStyle.styleFloat:p.currentStyle[x(I)]):v(p.style&&p.style[x(I)])}H.exports=A},30787:function(H){"use strict";function P(i){return i.Window&&i instanceof i.Window?{x:i.pageXOffset||i.document.documentElement.scrollLeft,y:i.pageYOffset||i.document.documentElement.scrollTop}:{x:i.scrollLeft,y:i.scrollTop}}H.exports=P},70746:function(H){"use strict";function P(){var b=void 0;return document.documentElement&&(b=document.documentElement.clientWidth),!b&&document.body&&(b=document.body.clientWidth),b||0}function i(){var b=void 0;return document.documentElement&&(b=document.documentElement.clientHeight),!b&&document.body&&(b=document.body.clientHeight),b||0}function x(){return{width:window.innerWidth||P(),height:window.innerHeight||i()}}x.withoutScrollbars=function(){return{width:P(),height:i()}},H.exports=x},89349:function(H){"use strict";var P=/([A-Z])/g;function i(x){return x.replace(P,"-$1").toLowerCase()}H.exports=i},73759:function(H){"use strict";var P=function(b){};function i(x,b,v,A,p,I,E,d){if(P(b),!x){var f;if(b===void 0)f=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var g=[v,A,p,I,E,d],s=0;f=new Error(b.replace(/%s/g,function(){return g[s++]})),f.name="Invariant Violation"}throw f.framesToPop=1,f}}H.exports=i},20901:function(H){"use strict";function P(i){var x=i?i.ownerDocument||i:document,b=x.defaultView||window;return!!(i&&(typeof b.Node=="function"?i instanceof b.Node:typeof i=="object"&&typeof i.nodeType=="number"&&typeof i.nodeName=="string"))}H.exports=P},52334:function(H,P,i){"use strict";var x=i(20901);function b(v){return x(v)&&v.nodeType==3}H.exports=b},71108:function(H){"use strict";function P(i){i||(i="");var x=void 0,b=arguments.length;if(b>1)for(var v=1;v<b;v++)x=arguments[v],x&&(i=(i?i+" ":"")+x);return i}H.exports=P},79467:function(H){"use strict";var P=Object.prototype.hasOwnProperty;function i(x,b,v){if(!x)return null;var A={};for(var p in x)P.call(x,p)&&(A[p]=b.call(v,x[p],p,x));return A}H.exports=i},51767:function(H){"use strict";function P(i){var x={};return function(b){return x.hasOwnProperty(b)||(x[b]=i.call(this,b)),x[b]}}H.exports=P},22045:function(H){"use strict";var P=function(x){if(x!=null)return x;throw new Error("Got unexpected null or undefined")};H.exports=P},56926:function(H,P,i){"use strict";i(24889),H.exports=i.g.setImmediate},43393:function(H){(function(P,i){H.exports=i()})(this,function(){"use strict";var P=Array.prototype.slice;function i(e,t){t&&(e.prototype=Object.create(t.prototype)),e.prototype.constructor=e}function x(e){return p(e)?e:Be(e)}i(b,x);function b(e){return I(e)?e:He(e)}i(v,x);function v(e){return E(e)?e:Qe(e)}i(A,x);function A(e){return p(e)&&!d(e)?e:ot(e)}function p(e){return!!(e&&e[g])}function I(e){return!!(e&&e[s])}function E(e){return!!(e&&e[a])}function d(e){return I(e)||E(e)}function f(e){return!!(e&&e[c])}x.isIterable=p,x.isKeyed=I,x.isIndexed=E,x.isAssociative=d,x.isOrdered=f,x.Keyed=b,x.Indexed=v,x.Set=A;var g="@@__IMMUTABLE_ITERABLE__@@",s="@@__IMMUTABLE_KEYED__@@",a="@@__IMMUTABLE_INDEXED__@@",c="@@__IMMUTABLE_ORDERED__@@",o="delete",r=5,u=1<<r,S=u-1,C={},l={value:!1},h={value:!1};function D(e){return e.value=!1,e}function k(e){e&&(e.value=!0)}function y(){}function L(e,t){t=t||0;for(var m=Math.max(0,e.length-t),F=new Array(m),M=0;M<m;M++)F[M]=e[M+t];return F}function T(e){return e.size===void 0&&(e.size=e.__iterate(w)),e.size}function B(e,t){if(typeof t!="number"){var m=t>>>0;if(""+m!==t||m===4294967295)return NaN;t=m}return t<0?T(e)+t:t}function w(){return!0}function z(e,t,m){return(e===0||m!==void 0&&e<=-m)&&(t===void 0||m!==void 0&&t>=m)}function j(e,t){return O(e,t,0)}function U(e,t){return O(e,t,t)}function O(e,t,m){return e===void 0?m:e<0?Math.max(0,t+e):t===void 0?e:Math.min(t,e)}var ie=0,Q=1,ue=2,Ee=typeof Symbol=="function"&&Symbol.iterator,me="@@iterator",V=Ee||me;function we(e){this.next=e}we.prototype.toString=function(){return"[Iterator]"},we.KEYS=ie,we.VALUES=Q,we.ENTRIES=ue,we.prototype.inspect=we.prototype.toSource=function(){return this.toString()},we.prototype[V]=function(){return this};function Ne(e,t,m,F){var M=e===0?t:e===1?m:[t,m];return F?F.value=M:F={value:M,done:!1},F}function Me(){return{value:void 0,done:!0}}function Ke(e){return!!et(e)}function qe(e){return e&&typeof e.next=="function"}function it(e){var t=et(e);return t&&t.call(e)}function et(e){var t=e&&(Ee&&e[Ee]||e[me]);if(typeof t=="function")return t}function Je(e){return e&&typeof e.length=="number"}i(Be,x);function Be(e){return e==null?ne():p(e)?e.toSeq():se(e)}Be.of=function(){return Be(arguments)},Be.prototype.toSeq=function(){return this},Be.prototype.toString=function(){return this.__toString("Seq {","}")},Be.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},Be.prototype.__iterate=function(e,t){return ye(this,e,t,!0)},Be.prototype.__iterator=function(e,t){return We(this,e,t,!0)},i(He,Be);function He(e){return e==null?ne().toKeyedSeq():p(e)?I(e)?e.toSeq():e.fromEntrySeq():_(e)}He.prototype.toKeyedSeq=function(){return this},i(Qe,Be);function Qe(e){return e==null?ne():p(e)?I(e)?e.entrySeq():e.toIndexedSeq():le(e)}Qe.of=function(){return Qe(arguments)},Qe.prototype.toIndexedSeq=function(){return this},Qe.prototype.toString=function(){return this.__toString("Seq [","]")},Qe.prototype.__iterate=function(e,t){return ye(this,e,t,!1)},Qe.prototype.__iterator=function(e,t){return We(this,e,t,!1)},i(ot,Be);function ot(e){return(e==null?ne():p(e)?I(e)?e.entrySeq():e:le(e)).toSetSeq()}ot.of=function(){return ot(arguments)},ot.prototype.toSetSeq=function(){return this},Be.isSeq=Et,Be.Keyed=He,Be.Set=ot,Be.Indexed=Qe;var mt="@@__IMMUTABLE_SEQ__@@";Be.prototype[mt]=!0,i(rt,Qe);function rt(e){this._array=e,this.size=e.length}rt.prototype.get=function(e,t){return this.has(e)?this._array[B(this,e)]:t},rt.prototype.__iterate=function(e,t){for(var m=this._array,F=m.length-1,M=0;M<=F;M++)if(e(m[t?F-M:M],M,this)===!1)return M+1;return M},rt.prototype.__iterator=function(e,t){var m=this._array,F=m.length-1,M=0;return new we(function(){return M>F?Me():Ne(e,M,m[t?F-M++:M++])})},i(Ce,He);function Ce(e){var t=Object.keys(e);this._object=e,this._keys=t,this.size=t.length}Ce.prototype.get=function(e,t){return t!==void 0&&!this.has(e)?t:this._object[e]},Ce.prototype.has=function(e){return this._object.hasOwnProperty(e)},Ce.prototype.__iterate=function(e,t){for(var m=this._object,F=this._keys,M=F.length-1,G=0;G<=M;G++){var $=F[t?M-G:G];if(e(m[$],$,this)===!1)return G+1}return G},Ce.prototype.__iterator=function(e,t){var m=this._object,F=this._keys,M=F.length-1,G=0;return new we(function(){var $=F[t?M-G:G];return G++>M?Me():Ne(e,$,m[$])})},Ce.prototype[c]=!0,i($e,Qe);function $e(e){this._iterable=e,this.size=e.length||e.size}$e.prototype.__iterateUncached=function(e,t){if(t)return this.cacheResult().__iterate(e,t);var m=this._iterable,F=it(m),M=0;if(qe(F))for(var G;!(G=F.next()).done&&e(G.value,M++,this)!==!1;);return M},$e.prototype.__iteratorUncached=function(e,t){if(t)return this.cacheResult().__iterator(e,t);var m=this._iterable,F=it(m);if(!qe(F))return new we(Me);var M=0;return new we(function(){var G=F.next();return G.done?G:Ne(e,M++,G.value)})},i(Ft,Qe);function Ft(e){this._iterator=e,this._iteratorCache=[]}Ft.prototype.__iterateUncached=function(e,t){if(t)return this.cacheResult().__iterate(e,t);for(var m=this._iterator,F=this._iteratorCache,M=0;M<F.length;)if(e(F[M],M++,this)===!1)return M;for(var G;!(G=m.next()).done;){var $=G.value;if(F[M]=$,e($,M++,this)===!1)break}return M},Ft.prototype.__iteratorUncached=function(e,t){if(t)return this.cacheResult().__iterator(e,t);var m=this._iterator,F=this._iteratorCache,M=0;return new we(function(){if(M>=F.length){var G=m.next();if(G.done)return G;F[M]=G.value}return Ne(e,M,F[M++])})};function Et(e){return!!(e&&e[mt])}var De;function ne(){return De||(De=new rt([]))}function _(e){var t=Array.isArray(e)?new rt(e).fromEntrySeq():qe(e)?new Ft(e).fromEntrySeq():Ke(e)?new $e(e).fromEntrySeq():typeof e=="object"?new Ce(e):void 0;if(!t)throw new TypeError("Expected Array or iterable object of [k, v] entries, or keyed object: "+e);return t}function le(e){var t=ge(e);if(!t)throw new TypeError("Expected Array or iterable object of values: "+e);return t}function se(e){var t=ge(e)||typeof e=="object"&&new Ce(e);if(!t)throw new TypeError("Expected Array or iterable object of values, or keyed object: "+e);return t}function ge(e){return Je(e)?new rt(e):qe(e)?new Ft(e):Ke(e)?new $e(e):void 0}function ye(e,t,m,F){var M=e._cache;if(M){for(var G=M.length-1,$=0;$<=G;$++){var re=M[m?G-$:$];if(t(re[1],F?re[0]:$,e)===!1)return $+1}return $}return e.__iterateUncached(t,m)}function We(e,t,m,F){var M=e._cache;if(M){var G=M.length-1,$=0;return new we(function(){var re=M[m?G-$:$];return $++>G?Me():Ne(t,F?re[0]:$-1,re[1])})}return e.__iteratorUncached(t,m)}function Fe(e,t){return t?Ve(t,e,"",{"":e}):Ue(e)}function Ve(e,t,m,F){return Array.isArray(t)?e.call(F,m,Qe(t).map(function(M,G){return Ve(e,M,G,t)})):ct(t)?e.call(F,m,He(t).map(function(M,G){return Ve(e,M,G,t)})):t}function Ue(e){return Array.isArray(e)?Qe(e).map(Ue).toList():ct(e)?He(e).map(Ue).toMap():e}function ct(e){return e&&(e.constructor===Object||e.constructor===void 0)}function Ze(e,t){if(e===t||e!==e&&t!==t)return!0;if(!e||!t)return!1;if(typeof e.valueOf=="function"&&typeof t.valueOf=="function"){if(e=e.valueOf(),t=t.valueOf(),e===t||e!==e&&t!==t)return!0;if(!e||!t)return!1}return!!(typeof e.equals=="function"&&typeof t.equals=="function"&&e.equals(t))}function ut(e,t){if(e===t)return!0;if(!p(t)||e.size!==void 0&&t.size!==void 0&&e.size!==t.size||e.__hash!==void 0&&t.__hash!==void 0&&e.__hash!==t.__hash||I(e)!==I(t)||E(e)!==E(t)||f(e)!==f(t))return!1;if(e.size===0&&t.size===0)return!0;var m=!d(e);if(f(e)){var F=e.entries();return t.every(function(fe,te){var he=F.next().value;return he&&Ze(he[1],fe)&&(m||Ze(he[0],te))})&&F.next().done}var M=!1;if(e.size===void 0)if(t.size===void 0)typeof e.cacheResult=="function"&&e.cacheResult();else{M=!0;var G=e;e=t,t=G}var $=!0,re=t.__iterate(function(fe,te){if(m?!e.has(fe):M?!Ze(fe,e.get(te,C)):!Ze(e.get(te,C),fe))return $=!1,!1});return $&&e.size===re}i(lt,Qe);function lt(e,t){if(!(this instanceof lt))return new lt(e,t);if(this._value=e,this.size=t===void 0?Infinity:Math.max(0,t),this.size===0){if(gt)return gt;gt=this}}lt.prototype.toString=function(){return this.size===0?"Repeat []":"Repeat [ "+this._value+" "+this.size+" times ]"},lt.prototype.get=function(e,t){return this.has(e)?this._value:t},lt.prototype.includes=function(e){return Ze(this._value,e)},lt.prototype.slice=function(e,t){var m=this.size;return z(e,t,m)?this:new lt(this._value,U(t,m)-j(e,m))},lt.prototype.reverse=function(){return this},lt.prototype.indexOf=function(e){return Ze(this._value,e)?0:-1},lt.prototype.lastIndexOf=function(e){return Ze(this._value,e)?this.size:-1},lt.prototype.__iterate=function(e,t){for(var m=0;m<this.size;m++)if(e(this._value,m,this)===!1)return m+1;return m},lt.prototype.__iterator=function(e,t){var m=this,F=0;return new we(function(){return F<m.size?Ne(e,F++,m._value):Me()})},lt.prototype.equals=function(e){return e instanceof lt?Ze(this._value,e._value):ut(e)};var gt;function pt(e,t){if(!e)throw new Error(t)}i(ft,Qe);function ft(e,t,m){if(!(this instanceof ft))return new ft(e,t,m);if(pt(m!==0,"Cannot step a Range by 0"),e=e||0,t===void 0&&(t=Infinity),m=m===void 0?1:Math.abs(m),t<e&&(m=-m),this._start=e,this._end=t,this._step=m,this.size=Math.max(0,Math.ceil((t-e)/m-1)+1),this.size===0){if(Dt)return Dt;Dt=this}}ft.prototype.toString=function(){return this.size===0?"Range []":"Range [ "+this._start+"..."+this._end+(this._step>1?" by "+this._step:"")+" ]"},ft.prototype.get=function(e,t){return this.has(e)?this._start+B(this,e)*this._step:t},ft.prototype.includes=function(e){var t=(e-this._start)/this._step;return t>=0&&t<this.size&&t===Math.floor(t)},ft.prototype.slice=function(e,t){return z(e,t,this.size)?this:(e=j(e,this.size),t=U(t,this.size),t<=e?new ft(0,0):new ft(this.get(e,this._end),this.get(t,this._end),this._step))},ft.prototype.indexOf=function(e){var t=e-this._start;if(t%this._step==0){var m=t/this._step;if(m>=0&&m<this.size)return m}return-1},ft.prototype.lastIndexOf=function(e){return this.indexOf(e)},ft.prototype.__iterate=function(e,t){for(var m=this.size-1,F=this._step,M=t?this._start+m*F:this._start,G=0;G<=m;G++){if(e(M,G,this)===!1)return G+1;M+=t?-F:F}return G},ft.prototype.__iterator=function(e,t){var m=this.size-1,F=this._step,M=t?this._start+m*F:this._start,G=0;return new we(function(){var $=M;return M+=t?-F:F,G>m?Me():Ne(e,G++,$)})},ft.prototype.equals=function(e){return e instanceof ft?this._start===e._start&&this._end===e._end&&this._step===e._step:ut(this,e)};var Dt;i(wt,x);function wt(){throw TypeError("Abstract")}i(Rt,wt);function Rt(){}i(Mt,wt);function Mt(){}i(jt,wt);function jt(){}wt.Keyed=Rt,wt.Indexed=Mt,wt.Set=jt;var It=typeof Math.imul=="function"&&Math.imul(4294967295,2)===-2?Math.imul:function(t,m){t=t|0,m=m|0;var F=t&65535,M=m&65535;return F*M+((t>>>16)*M+F*(m>>>16)<<16>>>0)|0};function Qt(e){return e>>>1&1073741824|e&3221225471}function J(e){if(e===!1||e===null||e===void 0||typeof e.valueOf=="function"&&(e=e.valueOf(),e===!1||e===null||e===void 0))return 0;if(e===!0)return 1;var t=typeof e;if(t==="number"){var m=e|0;for(m!==e&&(m^=e*4294967295);e>4294967295;)e/=4294967295,m^=e;return Qt(m)}if(t==="string")return e.length>ze?ae(e):N(e);if(typeof e.hashCode=="function")return e.hashCode();if(t==="object")return Z(e);if(typeof e.toString=="function")return N(e.toString());throw new Error("Value type "+t+" cannot be hashed.")}function ae(e){var t=Pe[e];return t===void 0&&(t=N(e),Te===Ge&&(Te=0,Pe={}),Te++,Pe[e]=t),t}function N(e){for(var t=0,m=0;m<e.length;m++)t=31*t+e.charCodeAt(m)|0;return Qt(t)}function Z(e){var t;if(Se&&(t=Ae.get(e),t!==void 0)||(t=e[ve],t!==void 0)||!de&&(t=e.propertyIsEnumerable&&e.propertyIsEnumerable[ve],t!==void 0||(t=be(e),t!==void 0)))return t;if(t=++xe,xe&1073741824&&(xe=0),Se)Ae.set(e,t);else{if(q!==void 0&&q(e)===!1)throw new Error("Non-extensible objects are not allowed as keys.");if(de)Object.defineProperty(e,ve,{enumerable:!1,configurable:!1,writable:!1,value:t});else if(e.propertyIsEnumerable!==void 0&&e.propertyIsEnumerable===e.constructor.prototype.propertyIsEnumerable)e.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},e.propertyIsEnumerable[ve]=t;else if(e.nodeType!==void 0)e[ve]=t;else throw new Error("Unable to set a non-enumerable property on object.")}return t}var q=Object.isExtensible,de=function(){try{return Object.defineProperty({},"@",{}),!0}catch(e){return!1}}();function be(e){if(e&&e.nodeType>0)switch(e.nodeType){case 1:return e.uniqueID;case 9:return e.documentElement&&e.documentElement.uniqueID}}var Se=typeof WeakMap=="function",Ae;Se&&(Ae=new WeakMap);var xe=0,ve="__immutablehash__";typeof Symbol=="function"&&(ve=Symbol(ve));var ze=16,Ge=255,Te=0,Pe={};function Oe(e){pt(e!==Infinity,"Cannot perform this action with an infinite size.")}i(Ie,Rt);function Ie(e){return e==null?Lt():ht(e)&&!f(e)?e:Lt().withMutations(function(t){var m=b(e);Oe(m.size),m.forEach(function(F,M){return t.set(M,F)})})}Ie.prototype.toString=function(){return this.__toString("Map {","}")},Ie.prototype.get=function(e,t){return this._root?this._root.get(0,void 0,e,t):t},Ie.prototype.set=function(e,t){return Bn(this,e,t)},Ie.prototype.setIn=function(e,t){return this.updateIn(e,C,function(){return t})},Ie.prototype.remove=function(e){return Bn(this,e,C)},Ie.prototype.deleteIn=function(e){return this.updateIn(e,function(){return C})},Ie.prototype.update=function(e,t,m){return arguments.length===1?e(this):this.updateIn([e],t,m)},Ie.prototype.updateIn=function(e,t,m){m||(m=t,t=void 0);var F=bn(this,lr(e),t,m);return F===C?void 0:F},Ie.prototype.clear=function(){return this.size===0?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):Lt()},Ie.prototype.merge=function(){return fn(this,void 0,arguments)},Ie.prototype.mergeWith=function(e){var t=P.call(arguments,1);return fn(this,e,t)},Ie.prototype.mergeIn=function(e){var t=P.call(arguments,1);return this.updateIn(e,Lt(),function(m){return typeof m.merge=="function"?m.merge.apply(m,t):t[t.length-1]})},Ie.prototype.mergeDeep=function(){return fn(this,An,arguments)},Ie.prototype.mergeDeepWith=function(e){var t=P.call(arguments,1);return fn(this,dn(e),t)},Ie.prototype.mergeDeepIn=function(e){var t=P.call(arguments,1);return this.updateIn(e,Lt(),function(m){return typeof m.mergeDeep=="function"?m.mergeDeep.apply(m,t):t[t.length-1]})},Ie.prototype.sort=function(e){return Nt(In(this,e))},Ie.prototype.sortBy=function(e,t){return Nt(In(this,t,e))},Ie.prototype.withMutations=function(e){var t=this.asMutable();return e(t),t.wasAltered()?t.__ensureOwner(this.__ownerID):this},Ie.prototype.asMutable=function(){return this.__ownerID?this:this.__ensureOwner(new y)},Ie.prototype.asImmutable=function(){return this.__ensureOwner()},Ie.prototype.wasAltered=function(){return this.__altered},Ie.prototype.__iterator=function(e,t){return new Zt(this,e,t)},Ie.prototype.__iterate=function(e,t){var m=this,F=0;return this._root&&this._root.iterate(function(M){return F++,e(M[1],M[0],m)},t),F},Ie.prototype.__ensureOwner=function(e){return e===this.__ownerID?this:e?cn(this.size,this._root,e,this.__hash):(this.__ownerID=e,this.__altered=!1,this)};function ht(e){return!!(e&&e[yt])}Ie.isMap=ht;var yt="@@__IMMUTABLE_MAP__@@",Re=Ie.prototype;Re[yt]=!0,Re[o]=Re.remove,Re.removeIn=Re.deleteIn;function nt(e,t){this.ownerID=e,this.entries=t}nt.prototype.get=function(e,t,m,F){for(var M=this.entries,G=0,$=M.length;G<$;G++)if(Ze(m,M[G][0]))return M[G][1];return F},nt.prototype.update=function(e,t,m,F,M,G,$){for(var re=M===C,fe=this.entries,te=0,he=fe.length;te<he&&!Ze(F,fe[te][0]);te++);var ke=te<he;if(ke?fe[te][1]===M:re)return this;if(k($),(re||!ke)&&k(G),!(re&&fe.length===1)){if(!ke&&!re&&fe.length>=mr)return Nn(e,fe,F,M);var Le=e&&e===this.ownerID,tt=Le?fe:L(fe);return ke?re?te===he-1?tt.pop():tt[te]=tt.pop():tt[te]=[F,M]:tt.push([F,M]),Le?(this.entries=tt,this):new nt(e,tt)}};function Bt(e,t,m){this.ownerID=e,this.bitmap=t,this.nodes=m}Bt.prototype.get=function(e,t,m,F){t===void 0&&(t=J(m));var M=1<<((e===0?t:t>>>e)&S),G=this.bitmap;return(G&M)==0?F:this.nodes[Xn(G&M-1)].get(e+r,t,m,F)},Bt.prototype.update=function(e,t,m,F,M,G,$){m===void 0&&(m=J(F));var re=(t===0?m:m>>>t)&S,fe=1<<re,te=this.bitmap,he=(te&fe)!=0;if(!he&&M===C)return this;var ke=Xn(te&fe-1),Le=this.nodes,tt=he?Le[ke]:void 0,at=_t(tt,e,t+r,m,F,M,G,$);if(at===tt)return this;if(!he&&at&&Le.length>=yr)return Hn(e,Le,te,re,at);if(he&&!at&&Le.length===2&&Jt(Le[ke^1]))return Le[ke^1];if(he&&at&&Le.length===1&&Jt(at))return at;var vt=e&&e===this.ownerID,en=he?at?te:te^fe:te|fe,rn=he?at?gr(Le,ke,at,vt):vr(Le,ke,vt):hr(Le,ke,at,vt);return vt?(this.bitmap=en,this.nodes=rn,this):new Bt(e,en,rn)};function _e(e,t,m){this.ownerID=e,this.count=t,this.nodes=m}_e.prototype.get=function(e,t,m,F){t===void 0&&(t=J(m));var M=(e===0?t:t>>>e)&S,G=this.nodes[M];return G?G.get(e+r,t,m,F):F},_e.prototype.update=function(e,t,m,F,M,G,$){m===void 0&&(m=J(F));var re=(t===0?m:m>>>t)&S,fe=M===C,te=this.nodes,he=te[re];if(fe&&!he)return this;var ke=_t(he,e,t+r,m,F,M,G,$);if(ke===he)return this;var Le=this.count;if(!he)Le++;else if(!ke&&(Le--,Le<Ar))return Sn(e,te,Le,re);var tt=e&&e===this.ownerID,at=gr(te,re,ke,tt);return tt?(this.count=Le,this.nodes=at,this):new _e(e,Le,at)};function dt(e,t,m){this.ownerID=e,this.keyHash=t,this.entries=m}dt.prototype.get=function(e,t,m,F){for(var M=this.entries,G=0,$=M.length;G<$;G++)if(Ze(m,M[G][0]))return M[G][1];return F},dt.prototype.update=function(e,t,m,F,M,G,$){m===void 0&&(m=J(F));var re=M===C;if(m!==this.keyHash)return re?this:(k($),k(G),on(this,e,t,m,[F,M]));for(var fe=this.entries,te=0,he=fe.length;te<he&&!Ze(F,fe[te][0]);te++);var ke=te<he;if(ke?fe[te][1]===M:re)return this;if(k($),(re||!ke)&&k(G),re&&he===2)return new kt(e,this.keyHash,fe[te^1]);var Le=e&&e===this.ownerID,tt=Le?fe:L(fe);return ke?re?te===he-1?tt.pop():tt[te]=tt.pop():tt[te]=[F,M]:tt.push([F,M]),Le?(this.entries=tt,this):new dt(e,this.keyHash,tt)};function kt(e,t,m){this.ownerID=e,this.keyHash=t,this.entry=m}kt.prototype.get=function(e,t,m,F){return Ze(m,this.entry[0])?this.entry[1]:F},kt.prototype.update=function(e,t,m,F,M,G,$){var re=M===C,fe=Ze(F,this.entry[0]);if(fe?M===this.entry[1]:re)return this;if(k($),re){k(G);return}return fe?e&&e===this.ownerID?(this.entry[1]=M,this):new kt(e,this.keyHash,[F,M]):(k(G),on(this,e,t,J(F),[F,M]))},nt.prototype.iterate=dt.prototype.iterate=function(e,t){for(var m=this.entries,F=0,M=m.length-1;F<=M;F++)if(e(m[t?M-F:F])===!1)return!1},Bt.prototype.iterate=_e.prototype.iterate=function(e,t){for(var m=this.nodes,F=0,M=m.length-1;F<=M;F++){var G=m[t?M-F:F];if(G&&G.iterate(e,t)===!1)return!1}},kt.prototype.iterate=function(e,t){return e(this.entry)},i(Zt,we);function Zt(e,t,m){this._type=t,this._reverse=m,this._stack=e._root&&Vt(e._root)}Zt.prototype.next=function(){for(var e=this._type,t=this._stack;t;){var m=t.node,F=t.index++,M;if(m.entry){if(F===0)return Ut(e,m.entry)}else if(m.entries){if(M=m.entries.length-1,F<=M)return Ut(e,m.entries[this._reverse?M-F:F])}else if(M=m.nodes.length-1,F<=M){var G=m.nodes[this._reverse?M-F:F];if(G){if(G.entry)return Ut(e,G.entry);t=this._stack=Vt(G,t)}continue}t=this._stack=this._stack.__prev}return Me()};function Ut(e,t){return Ne(e,t[0],t[1])}function Vt(e,t){return{node:e,index:0,__prev:t}}function cn(e,t,m,F){var M=Object.create(Re);return M.size=e,M._root=t,M.__ownerID=m,M.__hash=F,M.__altered=!1,M}var Ln;function Lt(){return Ln||(Ln=cn(0))}function Bn(e,t,m){var F,M;if(e._root){var G=D(l),$=D(h);if(F=_t(e._root,e.__ownerID,0,void 0,t,m,G,$),!$.value)return e;M=e.size+(G.value?m===C?-1:1:0)}else{if(m===C)return e;M=1,F=new nt(e.__ownerID,[[t,m]])}return e.__ownerID?(e.size=M,e._root=F,e.__hash=void 0,e.__altered=!0,e):F?cn(M,F):Lt()}function _t(e,t,m,F,M,G,$,re){return e?e.update(t,m,F,M,G,$,re):G===C?e:(k(re),k($),new kt(t,F,[M,G]))}function Jt(e){return e.constructor===kt||e.constructor===dt}function on(e,t,m,F,M){if(e.keyHash===F)return new dt(t,F,[e.entry,M]);var G=(m===0?e.keyHash:e.keyHash>>>m)&S,$=(m===0?F:F>>>m)&S,re,fe=G===$?[on(e,t,m+r,F,M)]:(re=new kt(t,F,M),G<$?[e,re]:[re,e]);return new Bt(t,1<<G|1<<$,fe)}function Nn(e,t,m,F){e||(e=new y);for(var M=new kt(e,J(m),[m,F]),G=0;G<t.length;G++){var $=t[G];M=M.update(e,0,void 0,$[0],$[1])}return M}function Sn(e,t,m,F){for(var M=0,G=0,$=new Array(m),re=0,fe=1,te=t.length;re<te;re++,fe<<=1){var he=t[re];he!==void 0&&re!==F&&(M|=fe,$[G++]=he)}return new Bt(e,M,$)}function Hn(e,t,m,F,M){for(var G=0,$=new Array(u),re=0;m!==0;re++,m>>>=1)$[re]=m&1?t[G++]:void 0;return $[F]=M,new _e(e,G+1,$)}function fn(e,t,m){for(var F=[],M=0;M<m.length;M++){var G=m[M],$=b(G);p(G)||($=$.map(function(re){return Fe(re)})),F.push($)}return Cn(e,t,F)}function An(e,t,m){return e&&e.mergeDeep&&p(t)?e.mergeDeep(t):Ze(e,t)?e:t}function dn(e){return function(t,m,F){if(t&&t.mergeDeepWith&&p(m))return t.mergeDeepWith(e,m);var M=e(t,m,F);return Ze(t,M)?t:M}}function Cn(e,t,m){return m=m.filter(function(F){return F.size!==0}),m.length===0?e:e.size===0&&!e.__ownerID&&m.length===1?e.constructor(m[0]):e.withMutations(function(F){for(var M=t?function($,re){F.update(re,C,function(fe){return fe===C?$:t(fe,$,re)})}:function($,re){F.set(re,$)},G=0;G<m.length;G++)m[G].forEach(M)})}function bn(e,t,m,F){var M=e===C,G=t.next();if(G.done){var $=M?m:e,re=F($);return re===$?e:re}pt(M||e&&e.set,"invalid keyPath");var fe=G.value,te=M?C:e.get(fe,C),he=bn(te,t,m,F);return he===te?e:he===C?e.remove(fe):(M?Lt():e).set(fe,he)}function Xn(e){return e=e-(e>>1&1431655765),e=(e&858993459)+(e>>2&858993459),e=e+(e>>4)&252645135,e=e+(e>>8),e=e+(e>>16),e&127}function gr(e,t,m,F){var M=F?e:L(e);return M[t]=m,M}function hr(e,t,m,F){var M=e.length+1;if(F&&t+1===M)return e[t]=m,e;for(var G=new Array(M),$=0,re=0;re<M;re++)re===t?(G[re]=m,$=-1):G[re]=e[re+$];return G}function vr(e,t,m){var F=e.length-1;if(m&&t===F)return e.pop(),e;for(var M=new Array(F),G=0,$=0;$<F;$++)$===t&&(G=1),M[$]=e[$+G];return M}var mr=u/4,yr=u/2,Ar=u/4;i(xt,Mt);function xt(e){var t=jn();if(e==null)return t;if(pn(e))return e;var m=v(e),F=m.size;return F===0?t:(Oe(F),F>0&&F<u?kn(0,F,r,null,new an(m.toArray())):t.withMutations(function(M){M.setSize(F),m.forEach(function(G,$){return M.set($,G)})}))}xt.of=function(){return this(arguments)},xt.prototype.toString=function(){return this.__toString("List [","]")},xt.prototype.get=function(e,t){if(e=B(this,e),e>=0&&e<this.size){e+=this._origin;var m=_n(this,e);return m&&m.array[e&S]}return t},xt.prototype.set=function(e,t){return Er(this,e,t)},xt.prototype.remove=function(e){return this.has(e)?e===0?this.shift():e===this.size-1?this.pop():this.splice(e,1):this},xt.prototype.insert=function(e,t){return this.splice(e,0,t)},xt.prototype.clear=function(){return this.size===0?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=r,this._root=this._tail=null,this.__hash=void 0,this.__altered=!0,this):jn()},xt.prototype.push=function(){var e=arguments,t=this.size;return this.withMutations(function(m){gn(m,0,t+e.length);for(var F=0;F<e.length;F++)m.set(t+F,e[F])})},xt.prototype.pop=function(){return gn(this,0,-1)},xt.prototype.unshift=function(){var e=arguments;return this.withMutations(function(t){gn(t,-e.length);for(var m=0;m<e.length;m++)t.set(m,e[m])})},xt.prototype.shift=function(){return gn(this,1)},xt.prototype.merge=function(){return Pn(this,void 0,arguments)},xt.prototype.mergeWith=function(e){var t=P.call(arguments,1);return Pn(this,e,t)},xt.prototype.mergeDeep=function(){return Pn(this,An,arguments)},xt.prototype.mergeDeepWith=function(e){var t=P.call(arguments,1);return Pn(this,dn(e),t)},xt.prototype.setSize=function(e){return gn(this,0,e)},xt.prototype.slice=function(e,t){var m=this.size;return z(e,t,m)?this:gn(this,j(e,m),U(t,m))},xt.prototype.__iterator=function(e,t){var m=0,F=$n(this,t);return new we(function(){var M=F();return M===Fn?Me():Ne(e,m++,M)})},xt.prototype.__iterate=function(e,t){for(var m=0,F=$n(this,t),M;(M=F())!==Fn&&e(M,m++,this)!==!1;);return m},xt.prototype.__ensureOwner=function(e){return e===this.__ownerID?this:e?kn(this._origin,this._capacity,this._level,this._root,this._tail,e,this.__hash):(this.__ownerID=e,this)};function pn(e){return!!(e&&e[Jn])}xt.isList=pn;var Jn="@@__IMMUTABLE_LIST__@@",Wt=xt.prototype;Wt[Jn]=!0,Wt[o]=Wt.remove,Wt.setIn=Re.setIn,Wt.deleteIn=Wt.removeIn=Re.removeIn,Wt.update=Re.update,Wt.updateIn=Re.updateIn,Wt.mergeIn=Re.mergeIn,Wt.mergeDeepIn=Re.mergeDeepIn,Wt.withMutations=Re.withMutations,Wt.asMutable=Re.asMutable,Wt.asImmutable=Re.asImmutable,Wt.wasAltered=Re.wasAltered;function an(e,t){this.array=e,this.ownerID=t}an.prototype.removeBefore=function(e,t,m){if(m===t?1<<t:this.array.length===0)return this;var F=m>>>t&S;if(F>=this.array.length)return new an([],e);var M=F===0,G;if(t>0){var $=this.array[F];if(G=$&&$.removeBefore(e,t-r,m),G===$&&M)return this}if(M&&!G)return this;var re=xn(this,e);if(!M)for(var fe=0;fe<F;fe++)re.array[fe]=void 0;return G&&(re.array[F]=G),re},an.prototype.removeAfter=function(e,t,m){if(m===(t?1<<t:0)||this.array.length===0)return this;var F=m-1>>>t&S;if(F>=this.array.length)return this;var M;if(t>0){var G=this.array[F];if(M=G&&G.removeAfter(e,t-r,m),M===G&&F===this.array.length-1)return this}var $=xn(this,e);return $.array.splice(F+1),M&&($.array[F]=M),$};var Fn={};function $n(e,t){var m=e._origin,F=e._capacity,M=qt(F),G=e._tail;return $(e._root,e._level,0);function $(te,he,ke){return he===0?re(te,ke):fe(te,he,ke)}function re(te,he){var ke=he===M?G&&G.array:te&&te.array,Le=he>m?0:m-he,tt=F-he;return tt>u&&(tt=u),function(){if(Le===tt)return Fn;var at=t?--tt:Le++;return ke&&ke[at]}}function fe(te,he,ke){var Le,tt=te&&te.array,at=ke>m?0:m-ke>>he,vt=(F-ke>>he)+1;return vt>u&&(vt=u),function(){do{if(Le){var en=Le();if(en!==Fn)return en;Le=null}if(at===vt)return Fn;var rn=t?--vt:at++;Le=$(tt&&tt[rn],he-r,ke+(rn<<he))}while(!0)}}}function kn(e,t,m,F,M,G,$){var re=Object.create(Wt);return re.size=t-e,re._origin=e,re._capacity=t,re._level=m,re._root=F,re._tail=M,re.__ownerID=G,re.__hash=$,re.__altered=!1,re}var Zn;function jn(){return Zn||(Zn=kn(0,0,r))}function Er(e,t,m){if(t=B(e,t),t!==t)return e;if(t>=e.size||t<0)return e.withMutations(function($){t<0?gn($,t).set(0,m):gn($,0,t+1).set(t,m)});t+=e._origin;var F=e._tail,M=e._root,G=D(h);return t>=qt(e._capacity)?F=Wn(F,e.__ownerID,0,t,m,G):M=Wn(M,e.__ownerID,e._level,t,m,G),G.value?e.__ownerID?(e._root=M,e._tail=F,e.__hash=void 0,e.__altered=!0,e):kn(e._origin,e._capacity,e._level,M,F):e}function Wn(e,t,m,F,M,G){var $=F>>>m&S,re=e&&$<e.array.length;if(!re&&M===void 0)return e;var fe;if(m>0){var te=e&&e.array[$],he=Wn(te,t,m-r,F,M,G);return he===te?e:(fe=xn(e,t),fe.array[$]=he,fe)}return re&&e.array[$]===M?e:(k(G),fe=xn(e,t),M===void 0&&$===fe.array.length-1?fe.array.pop():fe.array[$]=M,fe)}function xn(e,t){return t&&e&&t===e.ownerID?e:new an(e?e.array.slice():[],t)}function _n(e,t){if(t>=qt(e._capacity))return e._tail;if(t<1<<e._level+r){for(var m=e._root,F=e._level;m&&F>0;)m=m.array[t>>>F&S],F-=r;return m}}function gn(e,t,m){t!==void 0&&(t=t|0),m!==void 0&&(m=m|0);var F=e.__ownerID||new y,M=e._origin,G=e._capacity,$=M+t,re=m===void 0?G:m<0?G+m:M+m;if($===M&&re===G)return e;if($>=re)return e.clear();for(var fe=e._level,te=e._root,he=0;$+he<0;)te=new an(te&&te.array.length?[void 0,te]:[],F),fe+=r,he+=1<<fe;he&&($+=he,M+=he,re+=he,G+=he);for(var ke=qt(G),Le=qt(re);Le>=1<<fe+r;)te=new an(te&&te.array.length?[te]:[],F),fe+=r;var tt=e._tail,at=Le<ke?_n(e,re-1):Le>ke?new an([],F):tt;if(tt&&Le>ke&&$<G&&tt.array.length){te=xn(te,F);for(var vt=te,en=fe;en>r;en-=r){var rn=ke>>>en&S;vt=vt.array[rn]=xn(vt.array[rn],F)}vt.array[ke>>>r&S]=tt}if(re<G&&(at=at&&at.removeAfter(F,0,re)),$>=Le)$-=Le,re-=Le,fe=r,te=null,at=at&&at.removeBefore(F,0,$);else if($>M||Le<ke){for(he=0;te;){var zn=$>>>fe&S;if(zn!==Le>>>fe&S)break;zn&&(he+=(1<<fe)*zn),fe-=r,te=te.array[zn]}te&&$>M&&(te=te.removeBefore(F,fe,$-he)),te&&Le<ke&&(te=te.removeAfter(F,fe,Le-he)),he&&($-=he,re-=he)}return e.__ownerID?(e.size=re-$,e._origin=$,e._capacity=re,e._level=fe,e._root=te,e._tail=at,e.__hash=void 0,e.__altered=!0,e):kn($,re,fe,te,at)}function Pn(e,t,m){for(var F=[],M=0,G=0;G<m.length;G++){var $=m[G],re=v($);re.size>M&&(M=re.size),p($)||(re=re.map(function(fe){return Fe(fe)})),F.push(re)}return M>e.size&&(e=e.setSize(M)),Cn(e,t,F)}function qt(e){return e<u?0:e-1>>>r<<r}i(Nt,Ie);function Nt(e){return e==null?Dn():Sr(e)?e:Dn().withMutations(function(t){var m=b(e);Oe(m.size),m.forEach(function(F,M){return t.set(M,F)})})}Nt.of=function(){return this(arguments)},Nt.prototype.toString=function(){return this.__toString("OrderedMap {","}")},Nt.prototype.get=function(e,t){var m=this._map.get(e);return m!==void 0?this._list.get(m)[1]:t},Nt.prototype.clear=function(){return this.size===0?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this):Dn()},Nt.prototype.set=function(e,t){return Kn(this,e,t)},Nt.prototype.remove=function(e){return Kn(this,e,C)},Nt.prototype.wasAltered=function(){return this._map.wasAltered()||this._list.wasAltered()},Nt.prototype.__iterate=function(e,t){var m=this;return this._list.__iterate(function(F){return F&&e(F[1],F[0],m)},t)},Nt.prototype.__iterator=function(e,t){return this._list.fromEntrySeq().__iterator(e,t)},Nt.prototype.__ensureOwner=function(e){if(e===this.__ownerID)return this;var t=this._map.__ensureOwner(e),m=this._list.__ensureOwner(e);return e?qn(t,m,e,this.__hash):(this.__ownerID=e,this._map=t,this._list=m,this)};function Sr(e){return ht(e)&&f(e)}Nt.isOrderedMap=Sr,Nt.prototype[c]=!0,Nt.prototype[o]=Nt.prototype.remove;function qn(e,t,m,F){var M=Object.create(Nt.prototype);return M.size=e?e.size:0,M._map=e,M._list=t,M.__ownerID=m,M.__hash=F,M}var Tn;function Dn(){return Tn||(Tn=qn(Lt(),jn()))}function Kn(e,t,m){var F=e._map,M=e._list,G=F.get(t),$=G!==void 0,re,fe;if(m===C){if(!$)return e;M.size>=u&&M.size>=F.size*2?(fe=M.filter(function(te,he){return te!==void 0&&G!==he}),re=fe.toKeyedSeq().map(function(te){return te[0]}).flip().toMap(),e.__ownerID&&(re.__ownerID=fe.__ownerID=e.__ownerID)):(re=F.remove(t),fe=G===M.size-1?M.pop():M.set(G,void 0))}else if($){if(m===M.get(G)[1])return e;re=F,fe=M.set(G,[t,m])}else re=F.set(t,M.size),fe=M.set(M.size,[t,m]);return e.__ownerID?(e.size=re.size,e._map=re,e._list=fe,e.__hash=void 0,e):qn(re,fe)}i(Yt,He);function Yt(e,t){this._iter=e,this._useKeys=t,this.size=e.size}Yt.prototype.get=function(e,t){return this._iter.get(e,t)},Yt.prototype.has=function(e){return this._iter.has(e)},Yt.prototype.valueSeq=function(){return this._iter.valueSeq()},Yt.prototype.reverse=function(){var e=this,t=tr(this,!0);return this._useKeys||(t.valueSeq=function(){return e._iter.toSeq().reverse()}),t},Yt.prototype.map=function(e,t){var m=this,F=er(this,e,t);return this._useKeys||(F.valueSeq=function(){return m._iter.toSeq().map(e,t)}),F},Yt.prototype.__iterate=function(e,t){var m=this,F;return this._iter.__iterate(this._useKeys?function(M,G){return e(M,G,m)}:(F=t?or(this):0,function(M){return e(M,t?--F:F++,m)}),t)},Yt.prototype.__iterator=function(e,t){if(this._useKeys)return this._iter.__iterator(e,t);var m=this._iter.__iterator(Q,t),F=t?or(this):0;return new we(function(){var M=m.next();return M.done?M:Ne(e,t?--F:F++,M.value,M)})},Yt.prototype[c]=!0,i(Gt,Qe);function Gt(e){this._iter=e,this.size=e.size}Gt.prototype.includes=function(e){return this._iter.includes(e)},Gt.prototype.__iterate=function(e,t){var m=this,F=0;return this._iter.__iterate(function(M){return e(M,F++,m)},t)},Gt.prototype.__iterator=function(e,t){var m=this._iter.__iterator(Q,t),F=0;return new we(function(){var M=m.next();return M.done?M:Ne(e,F++,M.value,M)})},i(hn,ot);function hn(e){this._iter=e,this.size=e.size}hn.prototype.has=function(e){return this._iter.includes(e)},hn.prototype.__iterate=function(e,t){var m=this;return this._iter.__iterate(function(F){return e(F,F,m)},t)},hn.prototype.__iterator=function(e,t){var m=this._iter.__iterator(Q,t);return new we(function(){var F=m.next();return F.done?F:Ne(e,F.value,F.value,F)})},i(wn,He);function wn(e){this._iter=e,this.size=e.size}wn.prototype.entrySeq=function(){return this._iter.toSeq()},wn.prototype.__iterate=function(e,t){var m=this;return this._iter.__iterate(function(F){if(F){Br(F);var M=p(F);return e(M?F.get(1):F[1],M?F.get(0):F[0],m)}},t)},wn.prototype.__iterator=function(e,t){var m=this._iter.__iterator(Q,t);return new we(function(){for(;;){var F=m.next();if(F.done)return F;var M=F.value;if(M){Br(M);var G=p(M);return Ne(e,G?M.get(0):M[0],G?M.get(1):M[1],F)}}})},Gt.prototype.cacheResult=Yt.prototype.cacheResult=hn.prototype.cacheResult=wn.prototype.cacheResult=Yn;function Cr(e){var t=un(e);return t._iter=e,t.size=e.size,t.flip=function(){return e},t.reverse=function(){var m=e.reverse.apply(this);return m.flip=function(){return e.reverse()},m},t.has=function(m){return e.includes(m)},t.includes=function(m){return e.has(m)},t.cacheResult=Yn,t.__iterateUncached=function(m,F){var M=this;return e.__iterate(function(G,$){return m($,G,M)!==!1},F)},t.__iteratorUncached=function(m,F){if(m===ue){var M=e.__iterator(m,F);return new we(function(){var G=M.next();if(!G.done){var $=G.value[0];G.value[0]=G.value[1],G.value[1]=$}return G})}return e.__iterator(m===Q?ie:Q,F)},t}function er(e,t,m){var F=un(e);return F.size=e.size,F.has=function(M){return e.has(M)},F.get=function(M,G){var $=e.get(M,C);return $===C?G:t.call(m,$,M,e)},F.__iterateUncached=function(M,G){var $=this;return e.__iterate(function(re,fe,te){return M(t.call(m,re,fe,te),fe,$)!==!1},G)},F.__iteratorUncached=function(M,G){var $=e.__iterator(ue,G);return new we(function(){var re=$.next();if(re.done)return re;var fe=re.value,te=fe[0];return Ne(M,te,t.call(m,fe[1],te,e),re)})},F}function tr(e,t){var m=un(e);return m._iter=e,m.size=e.size,m.reverse=function(){return e},e.flip&&(m.flip=function(){var F=Cr(e);return F.reverse=function(){return e.flip()},F}),m.get=function(F,M){return e.get(t?F:-1-F,M)},m.has=function(F){return e.has(t?F:-1-F)},m.includes=function(F){return e.includes(F)},m.cacheResult=Yn,m.__iterate=function(F,M){var G=this;return e.__iterate(function($,re){return F($,re,G)},!M)},m.__iterator=function(F,M){return e.__iterator(F,!M)},m}function nr(e,t,m,F){var M=un(e);return F&&(M.has=function(G){var $=e.get(G,C);return $!==C&&!!t.call(m,$,G,e)},M.get=function(G,$){var re=e.get(G,C);return re!==C&&t.call(m,re,G,e)?re:$}),M.__iterateUncached=function(G,$){var re=this,fe=0;return e.__iterate(function(te,he,ke){if(t.call(m,te,he,ke))return fe++,G(te,F?he:fe-1,re)},$),fe},M.__iteratorUncached=function(G,$){var re=e.__iterator(ue,$),fe=0;return new we(function(){for(;;){var te=re.next();if(te.done)return te;var he=te.value,ke=he[0],Le=he[1];if(t.call(m,Le,ke,e))return Ne(G,F?ke:fe++,Le,te)}})},M}function Tr(e,t,m){var F=Ie().asMutable();return e.__iterate(function(M,G){F.update(t.call(m,M,G,e),0,function($){return $+1})}),F.asImmutable()}function Dr(e,t,m){var F=I(e),M=(f(e)?Nt():Ie()).asMutable();e.__iterate(function($,re){M.update(t.call(m,$,re,e),function(fe){return fe=fe||[],fe.push(F?[re,$]:$),fe})});var G=ar(e);return M.map(function($){return St(e,G($))})}function Vn(e,t,m,F){var M=e.size;if(t!==void 0&&(t=t|0),m!==void 0&&(m=m|0),z(t,m,M))return e;var G=j(t,M),$=U(m,M);if(G!==G||$!==$)return Vn(e.toSeq().cacheResult(),t,m,F);var re=$-G,fe;re===re&&(fe=re<0?0:re);var te=un(e);return te.size=fe===0?fe:e.size&&fe||void 0,!F&&Et(e)&&fe>=0&&(te.get=function(he,ke){return he=B(this,he),he>=0&&he<fe?e.get(he+G,ke):ke}),te.__iterateUncached=function(he,ke){var Le=this;if(fe===0)return 0;if(ke)return this.cacheResult().__iterate(he,ke);var tt=0,at=!0,vt=0;return e.__iterate(function(en,rn){if(!(at&&(at=tt++<G)))return vt++,he(en,F?rn:vt-1,Le)!==!1&&vt!==fe}),vt},te.__iteratorUncached=function(he,ke){if(fe!==0&&ke)return this.cacheResult().__iterator(he,ke);var Le=fe!==0&&e.__iterator(he,ke),tt=0,at=0;return new we(function(){for(;tt++<G;)Le.next();if(++at>fe)return Me();var vt=Le.next();return F||he===Q?vt:he===ie?Ne(he,at-1,void 0,vt):Ne(he,at-1,vt.value[1],vt)})},te}function br(e,t,m){var F=un(e);return F.__iterateUncached=function(M,G){var $=this;if(G)return this.cacheResult().__iterate(M,G);var re=0;return e.__iterate(function(fe,te,he){return t.call(m,fe,te,he)&&++re&&M(fe,te,$)}),re},F.__iteratorUncached=function(M,G){var $=this;if(G)return this.cacheResult().__iterator(M,G);var re=e.__iterator(ue,G),fe=!0;return new we(function(){if(!fe)return Me();var te=re.next();if(te.done)return te;var he=te.value,ke=he[0],Le=he[1];return t.call(m,Le,ke,$)?M===ue?te:Ne(M,ke,Le,te):(fe=!1,Me())})},F}function xr(e,t,m,F){var M=un(e);return M.__iterateUncached=function(G,$){var re=this;if($)return this.cacheResult().__iterate(G,$);var fe=!0,te=0;return e.__iterate(function(he,ke,Le){if(!(fe&&(fe=t.call(m,he,ke,Le))))return te++,G(he,F?ke:te-1,re)}),te},M.__iteratorUncached=function(G,$){var re=this;if($)return this.cacheResult().__iterator(G,$);var fe=e.__iterator(ue,$),te=!0,he=0;return new we(function(){var ke,Le,tt;do{if(ke=fe.next(),ke.done)return F||G===Q?ke:G===ie?Ne(G,he++,void 0,ke):Ne(G,he++,ke.value[1],ke);var at=ke.value;Le=at[0],tt=at[1],te&&(te=t.call(m,tt,Le,re))}while(te);return G===ue?ke:Ne(G,Le,tt,ke)})},M}function wr(e,t){var m=I(e),F=[e].concat(t).map(function($){return p($)?m&&($=b($)):$=m?_($):le(Array.isArray($)?$:[$]),$}).filter(function($){return $.size!==0});if(F.length===0)return e;if(F.length===1){var M=F[0];if(M===e||m&&I(M)||E(e)&&E(M))return M}var G=new rt(F);return m?G=G.toKeyedSeq():E(e)||(G=G.toSetSeq()),G=G.flatten(!0),G.size=F.reduce(function($,re){if($!==void 0){var fe=re.size;if(fe!==void 0)return $+fe}},0),G}function rr(e,t,m){var F=un(e);return F.__iterateUncached=function(M,G){var $=0,re=!1;function fe(te,he){var ke=this;te.__iterate(function(Le,tt){return(!t||he<t)&&p(Le)?fe(Le,he+1):M(Le,m?tt:$++,ke)===!1&&(re=!0),!re},G)}return fe(e,0),$},F.__iteratorUncached=function(M,G){var $=e.__iterator(M,G),re=[],fe=0;return new we(function(){for(;$;){var te=$.next();if(te.done!==!1){$=re.pop();continue}var he=te.value;if(M===ue&&(he=he[1]),(!t||re.length<t)&&p(he))re.push($),$=he.__iterator(M,G);else return m?te:Ne(M,fe++,he,te)}return Me()})},F}function Mr(e,t,m){var F=ar(e);return e.toSeq().map(function(M,G){return F(t.call(m,M,G,e))}).flatten(!0)}function Ir(e,t){var m=un(e);return m.size=e.size&&e.size*2-1,m.__iterateUncached=function(F,M){var G=this,$=0;return e.__iterate(function(re,fe){return(!$||F(t,$++,G)!==!1)&&F(re,$++,G)!==!1},M),$},m.__iteratorUncached=function(F,M){var G=e.__iterator(Q,M),$=0,re;return new we(function(){return(!re||$%2)&&(re=G.next(),re.done)?re:$%2?Ne(F,$++,t):Ne(F,$++,re.value,re)})},m}function In(e,t,m){t||(t=ur);var F=I(e),M=0,G=e.toSeq().map(function($,re){return[re,$,M++,m?m($,re,e):$]}).toArray();return G.sort(function($,re){return t($[3],re[3])||$[2]-re[2]}).forEach(F?function($,re){G[re].length=2}:function($,re){G[re]=$[1]}),F?He(G):E(e)?Qe(G):ot(G)}function Gn(e,t,m){if(t||(t=ur),m){var F=e.toSeq().map(function(M,G){return[M,m(M,G,e)]}).reduce(function(M,G){return ir(t,M[1],G[1])?G:M});return F&&F[0]}else return e.reduce(function(M,G){return ir(t,M,G)?G:M})}function ir(e,t,m){var F=e(m,t);return F===0&&m!==t&&(m==null||m!==m)||F>0}function Qn(e,t,m){var F=un(e);return F.size=new rt(m).map(function(M){return M.size}).min(),F.__iterate=function(M,G){for(var $=this.__iterator(Q,G),re,fe=0;!(re=$.next()).done&&M(re.value,fe++,this)!==!1;);return fe},F.__iteratorUncached=function(M,G){var $=m.map(function(te){return te=x(te),it(G?te.reverse():te)}),re=0,fe=!1;return new we(function(){var te;return fe||(te=$.map(function(he){return he.next()}),fe=te.some(function(he){return he.done})),fe?Me():Ne(M,re++,t.apply(null,te.map(function(he){return he.value})))})},F}function St(e,t){return Et(e)?t:e.constructor(t)}function Br(e){if(e!==Object(e))throw new TypeError("Expected [K, V] tuple: "+e)}function or(e){return Oe(e.size),T(e)}function ar(e){return I(e)?b:E(e)?v:A}function un(e){return Object.create((I(e)?He:E(e)?Qe:ot).prototype)}function Yn(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):Be.prototype.cacheResult.call(this)}function ur(e,t){return e>t?1:e<t?-1:0}function lr(e){var t=it(e);if(!t){if(!Je(e))throw new TypeError("Expected iterable or array-like: "+e);t=it(x(e))}return t}i(Xt,Rt);function Xt(e,t){var m,F=function($){if($ instanceof F)return $;if(!(this instanceof F))return new F($);if(!m){m=!0;var re=Object.keys(e);Fr(M,re),M.size=re.length,M._name=t,M._keys=re,M._defaultValues=e}this._map=Ie($)},M=F.prototype=Object.create(zt);return M.constructor=F,F}Xt.prototype.toString=function(){return this.__toString(sr(this)+" {","}")},Xt.prototype.has=function(e){return this._defaultValues.hasOwnProperty(e)},Xt.prototype.get=function(e,t){if(!this.has(e))return t;var m=this._defaultValues[e];return this._map?this._map.get(e,m):m},Xt.prototype.clear=function(){if(this.__ownerID)return this._map&&this._map.clear(),this;var e=this.constructor;return e._empty||(e._empty=Un(this,Lt()))},Xt.prototype.set=function(e,t){if(!this.has(e))throw new Error('Cannot set unknown key "'+e+'" on '+sr(this));var m=this._map&&this._map.set(e,t);return this.__ownerID||m===this._map?this:Un(this,m)},Xt.prototype.remove=function(e){if(!this.has(e))return this;var t=this._map&&this._map.remove(e);return this.__ownerID||t===this._map?this:Un(this,t)},Xt.prototype.wasAltered=function(){return this._map.wasAltered()},Xt.prototype.__iterator=function(e,t){var m=this;return b(this._defaultValues).map(function(F,M){return m.get(M)}).__iterator(e,t)},Xt.prototype.__iterate=function(e,t){var m=this;return b(this._defaultValues).map(function(F,M){return m.get(M)}).__iterate(e,t)},Xt.prototype.__ensureOwner=function(e){if(e===this.__ownerID)return this;var t=this._map&&this._map.__ensureOwner(e);return e?Un(this,t,e):(this.__ownerID=e,this._map=t,this)};var zt=Xt.prototype;zt[o]=zt.remove,zt.deleteIn=zt.removeIn=Re.removeIn,zt.merge=Re.merge,zt.mergeWith=Re.mergeWith,zt.mergeIn=Re.mergeIn,zt.mergeDeep=Re.mergeDeep,zt.mergeDeepWith=Re.mergeDeepWith,zt.mergeDeepIn=Re.mergeDeepIn,zt.setIn=Re.setIn,zt.update=Re.update,zt.updateIn=Re.updateIn,zt.withMutations=Re.withMutations,zt.asMutable=Re.asMutable,zt.asImmutable=Re.asImmutable;function Un(e,t,m){var F=Object.create(Object.getPrototypeOf(e));return F._map=t,F.__ownerID=m,F}function sr(e){return e._name||e.constructor.name||"Record"}function Fr(e,t){try{t.forEach(cr.bind(void 0,e))}catch(m){}}function cr(e,t){Object.defineProperty(e,t,{get:function(){return this.get(t)},set:function(m){pt(this.__ownerID,"Cannot set on an immutable record."),this.set(t,m)}})}i(Ct,jt);function Ct(e){return e==null?W():En(e)&&!f(e)?e:W().withMutations(function(t){var m=A(e);Oe(m.size),m.forEach(function(F){return t.add(F)})})}Ct.of=function(){return this(arguments)},Ct.fromKeys=function(e){return this(b(e).keySeq())},Ct.prototype.toString=function(){return this.__toString("Set {","}")},Ct.prototype.has=function(e){return this._map.has(e)},Ct.prototype.add=function(e){return fr(this,this._map.set(e,!0))},Ct.prototype.remove=function(e){return fr(this,this._map.remove(e))},Ct.prototype.clear=function(){return fr(this,this._map.clear())},Ct.prototype.union=function(){var e=P.call(arguments,0);return e=e.filter(function(t){return t.size!==0}),e.length===0?this:this.size===0&&!this.__ownerID&&e.length===1?this.constructor(e[0]):this.withMutations(function(t){for(var m=0;m<e.length;m++)A(e[m]).forEach(function(F){return t.add(F)})})},Ct.prototype.intersect=function(){var e=P.call(arguments,0);if(e.length===0)return this;e=e.map(function(m){return A(m)});var t=this;return this.withMutations(function(m){t.forEach(function(F){e.every(function(M){return M.includes(F)})||m.remove(F)})})},Ct.prototype.subtract=function(){var e=P.call(arguments,0);if(e.length===0)return this;e=e.map(function(m){return A(m)});var t=this;return this.withMutations(function(m){t.forEach(function(F){e.some(function(M){return M.includes(F)})&&m.remove(F)})})},Ct.prototype.merge=function(){return this.union.apply(this,arguments)},Ct.prototype.mergeWith=function(e){var t=P.call(arguments,1);return this.union.apply(this,t)},Ct.prototype.sort=function(e){return n(In(this,e))},Ct.prototype.sortBy=function(e,t){return n(In(this,t,e))},Ct.prototype.wasAltered=function(){return this._map.wasAltered()},Ct.prototype.__iterate=function(e,t){var m=this;return this._map.__iterate(function(F,M){return e(M,M,m)},t)},Ct.prototype.__iterator=function(e,t){return this._map.map(function(m,F){return F}).__iterator(e,t)},Ct.prototype.__ensureOwner=function(e){if(e===this.__ownerID)return this;var t=this._map.__ensureOwner(e);return e?this.__make(t,e):(this.__ownerID=e,this._map=t,this)};function En(e){return!!(e&&e[Mn])}Ct.isSet=En;var Mn="@@__IMMUTABLE_SET__@@",Pt=Ct.prototype;Pt[Mn]=!0,Pt[o]=Pt.remove,Pt.mergeDeep=Pt.merge,Pt.mergeDeepWith=Pt.mergeWith,Pt.withMutations=Re.withMutations,Pt.asMutable=Re.asMutable,Pt.asImmutable=Re.asImmutable,Pt.__empty=W,Pt.__make=ee;function fr(e,t){return e.__ownerID?(e.size=t.size,e._map=t,e):t===e._map?e:t.size===0?e.__empty():e.__make(t)}function ee(e,t){var m=Object.create(Pt);return m.size=e?e.size:0,m._map=e,m.__ownerID=t,m}var R;function W(){return R||(R=ee(Lt()))}i(n,Ct);function n(e){return e==null?ce():K(e)?e:ce().withMutations(function(t){var m=A(e);Oe(m.size),m.forEach(function(F){return t.add(F)})})}n.of=function(){return this(arguments)},n.fromKeys=function(e){return this(b(e).keySeq())},n.prototype.toString=function(){return this.__toString("OrderedSet {","}")};function K(e){return En(e)&&f(e)}n.isOrderedSet=K;var X=n.prototype;X[c]=!0,X.__empty=ce,X.__make=oe;function oe(e,t){var m=Object.create(X);return m.size=e?e.size:0,m._map=e,m.__ownerID=t,m}var Y;function ce(){return Y||(Y=oe(Dn()))}i(pe,Mt);function pe(e){return e==null?Ht():je(e)?e:Ht().unshiftAll(e)}pe.of=function(){return this(arguments)},pe.prototype.toString=function(){return this.__toString("Stack [","]")},pe.prototype.get=function(e,t){var m=this._head;for(e=B(this,e);m&&e--;)m=m.next;return m?m.value:t},pe.prototype.peek=function(){return this._head&&this._head.value},pe.prototype.push=function(){if(arguments.length===0)return this;for(var e=this.size+arguments.length,t=this._head,m=arguments.length-1;m>=0;m--)t={value:arguments[m],next:t};return this.__ownerID?(this.size=e,this._head=t,this.__hash=void 0,this.__altered=!0,this):st(e,t)},pe.prototype.pushAll=function(e){if(e=v(e),e.size===0)return this;Oe(e.size);var t=this.size,m=this._head;return e.reverse().forEach(function(F){t++,m={value:F,next:m}}),this.__ownerID?(this.size=t,this._head=m,this.__hash=void 0,this.__altered=!0,this):st(t,m)},pe.prototype.pop=function(){return this.slice(1)},pe.prototype.unshift=function(){return this.push.apply(this,arguments)},pe.prototype.unshiftAll=function(e){return this.pushAll(e)},pe.prototype.shift=function(){return this.pop.apply(this,arguments)},pe.prototype.clear=function(){return this.size===0?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):Ht()},pe.prototype.slice=function(e,t){if(z(e,t,this.size))return this;var m=j(e,this.size),F=U(t,this.size);if(F!==this.size)return Mt.prototype.slice.call(this,e,t);for(var M=this.size-m,G=this._head;m--;)G=G.next;return this.__ownerID?(this.size=M,this._head=G,this.__hash=void 0,this.__altered=!0,this):st(M,G)},pe.prototype.__ensureOwner=function(e){return e===this.__ownerID?this:e?st(this.size,this._head,e,this.__hash):(this.__ownerID=e,this.__altered=!1,this)},pe.prototype.__iterate=function(e,t){if(t)return this.reverse().__iterate(e);for(var m=0,F=this._head;F&&e(F.value,m++,this)!==!1;)F=F.next;return m},pe.prototype.__iterator=function(e,t){if(t)return this.reverse().__iterator(e);var m=0,F=this._head;return new we(function(){if(F){var M=F.value;return F=F.next,Ne(e,m++,M)}return Me()})};function je(e){return!!(e&&e[Ye])}pe.isStack=je;var Ye="@@__IMMUTABLE_STACK__@@",Xe=pe.prototype;Xe[Ye]=!0,Xe.withMutations=Re.withMutations,Xe.asMutable=Re.asMutable,Xe.asImmutable=Re.asImmutable,Xe.wasAltered=Re.wasAltered;function st(e,t,m,F){var M=Object.create(Xe);return M.size=e,M._head=t,M.__ownerID=m,M.__hash=F,M.__altered=!1,M}var At;function Ht(){return At||(At=st(0))}function Kt(e,t){var m=function(F){e.prototype[F]=t[F]};return Object.keys(t).forEach(m),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(t).forEach(m),e}x.Iterator=we,Kt(x,{toArray:function(){Oe(this.size);var e=new Array(this.size||0);return this.valueSeq().__iterate(function(t,m){e[m]=t}),e},toIndexedSeq:function(){return new Gt(this)},toJS:function(){return this.toSeq().map(function(e){return e&&typeof e.toJS=="function"?e.toJS():e}).__toJS()},toJSON:function(){return this.toSeq().map(function(e){return e&&typeof e.toJSON=="function"?e.toJSON():e}).__toJS()},toKeyedSeq:function(){return new Yt(this,!0)},toMap:function(){return Ie(this.toKeyedSeq())},toObject:function(){Oe(this.size);var e={};return this.__iterate(function(t,m){e[m]=t}),e},toOrderedMap:function(){return Nt(this.toKeyedSeq())},toOrderedSet:function(){return n(I(this)?this.valueSeq():this)},toSet:function(){return Ct(I(this)?this.valueSeq():this)},toSetSeq:function(){return new hn(this)},toSeq:function(){return E(this)?this.toIndexedSeq():I(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return pe(I(this)?this.valueSeq():this)},toList:function(){return xt(I(this)?this.valueSeq():this)},toString:function(){return"[Iterable]"},__toString:function(e,t){return this.size===0?e+t:e+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+t},concat:function(){var e=P.call(arguments,0);return St(this,wr(this,e))},includes:function(e){return this.some(function(t){return Ze(t,e)})},entries:function(){return this.__iterator(ue)},every:function(e,t){Oe(this.size);var m=!0;return this.__iterate(function(F,M,G){if(!e.call(t,F,M,G))return m=!1,!1}),m},filter:function(e,t){return St(this,nr(this,e,t,!0))},find:function(e,t,m){var F=this.findEntry(e,t);return F?F[1]:m},findEntry:function(e,t){var m;return this.__iterate(function(F,M,G){if(e.call(t,F,M,G))return m=[M,F],!1}),m},findLastEntry:function(e,t){return this.toSeq().reverse().findEntry(e,t)},forEach:function(e,t){return Oe(this.size),this.__iterate(t?e.bind(t):e)},join:function(e){Oe(this.size),e=e!==void 0?""+e:",";var t="",m=!0;return this.__iterate(function(F){m?m=!1:t+=e,t+=F!=null?F.toString():""}),t},keys:function(){return this.__iterator(ie)},map:function(e,t){return St(this,er(this,e,t))},reduce:function(e,t,m){Oe(this.size);var F,M;return arguments.length<2?M=!0:F=t,this.__iterate(function(G,$,re){M?(M=!1,F=G):F=e.call(m,F,G,$,re)}),F},reduceRight:function(e,t,m){var F=this.toKeyedSeq().reverse();return F.reduce.apply(F,arguments)},reverse:function(){return St(this,tr(this,!0))},slice:function(e,t){return St(this,Vn(this,e,t,!0))},some:function(e,t){return!this.every(sn(e),t)},sort:function(e){return St(this,In(this,e))},values:function(){return this.__iterator(Q)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return this.size!==void 0?this.size===0:!this.some(function(){return!0})},count:function(e,t){return T(e?this.toSeq().filter(e,t):this)},countBy:function(e,t){return Tr(this,e,t)},equals:function(e){return ut(this,e)},entrySeq:function(){var e=this;if(e._cache)return new rt(e._cache);var t=e.toSeq().map(ln).toIndexedSeq();return t.fromEntrySeq=function(){return e.toSeq()},t},filterNot:function(e,t){return this.filter(sn(e),t)},findLast:function(e,t,m){return this.toKeyedSeq().reverse().find(e,t,m)},first:function(){return this.find(w)},flatMap:function(e,t){return St(this,Mr(this,e,t))},flatten:function(e){return St(this,rr(this,e,!0))},fromEntrySeq:function(){return new wn(this)},get:function(e,t){return this.find(function(m,F){return Ze(F,e)},void 0,t)},getIn:function(e,t){for(var m=this,F=lr(e),M;!(M=F.next()).done;){var G=M.value;if(m=m&&m.get?m.get(G,C):C,m===C)return t}return m},groupBy:function(e,t){return Dr(this,e,t)},has:function(e){return this.get(e,C)!==C},hasIn:function(e){return this.getIn(e,C)!==C},isSubset:function(e){return e=typeof e.includes=="function"?e:x(e),this.every(function(t){return e.includes(t)})},isSuperset:function(e){return e=typeof e.isSubset=="function"?e:x(e),e.isSubset(this)},keySeq:function(){return this.toSeq().map(vn).toIndexedSeq()},last:function(){return this.toSeq().reverse().first()},max:function(e){return Gn(this,e)},maxBy:function(e,t){return Gn(this,t,e)},min:function(e){return Gn(this,e?tn(e):nn)},minBy:function(e,t){return Gn(this,t?tn(t):nn,e)},rest:function(){return this.slice(1)},skip:function(e){return this.slice(Math.max(0,e))},skipLast:function(e){return St(this,this.toSeq().reverse().skip(e).reverse())},skipWhile:function(e,t){return St(this,xr(this,e,t,!0))},skipUntil:function(e,t){return this.skipWhile(sn(e),t)},sortBy:function(e,t){return St(this,In(this,t,e))},take:function(e){return this.slice(0,Math.max(0,e))},takeLast:function(e){return St(this,this.toSeq().reverse().take(e).reverse())},takeWhile:function(e,t){return St(this,br(this,e,t))},takeUntil:function(e,t){return this.takeWhile(sn(e),t)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=yn(this))}});var bt=x.prototype;bt[g]=!0,bt[V]=bt.values,bt.__toJS=bt.toArray,bt.__toStringMapper=mn,bt.inspect=bt.toSource=function(){return this.toString()},bt.chain=bt.flatMap,bt.contains=bt.includes,function(){try{Object.defineProperty(bt,"length",{get:function(){if(!x.noLengthWarning){var e;try{throw new Error}catch(t){e=t.stack}if(e.indexOf("_wrapObject")===-1)return console&&console.warn&&console.warn("iterable.length has been deprecated, use iterable.size or iterable.count(). This warning will become a silent error in a future version. "+e),this.size}}})}catch(e){}}(),Kt(b,{flip:function(){return St(this,Cr(this))},findKey:function(e,t){var m=this.findEntry(e,t);return m&&m[0]},findLastKey:function(e,t){return this.toSeq().reverse().findKey(e,t)},keyOf:function(e){return this.findKey(function(t){return Ze(t,e)})},lastKeyOf:function(e){return this.findLastKey(function(t){return Ze(t,e)})},mapEntries:function(e,t){var m=this,F=0;return St(this,this.toSeq().map(function(M,G){return e.call(t,[G,M],F++,m)}).fromEntrySeq())},mapKeys:function(e,t){var m=this;return St(this,this.toSeq().flip().map(function(F,M){return e.call(t,F,M,m)}).flip())}});var Tt=b.prototype;Tt[s]=!0,Tt[V]=bt.entries,Tt.__toJS=bt.toObject,Tt.__toStringMapper=function(e,t){return JSON.stringify(t)+": "+mn(e)},Kt(v,{toKeyedSeq:function(){return new Yt(this,!1)},filter:function(e,t){return St(this,nr(this,e,t,!1))},findIndex:function(e,t){var m=this.findEntry(e,t);return m?m[0]:-1},indexOf:function(e){var t=this.toKeyedSeq().keyOf(e);return t===void 0?-1:t},lastIndexOf:function(e){var t=this.toKeyedSeq().reverse().keyOf(e);return t===void 0?-1:t},reverse:function(){return St(this,tr(this,!1))},slice:function(e,t){return St(this,Vn(this,e,t,!1))},splice:function(e,t){var m=arguments.length;if(t=Math.max(t|0,0),m===0||m===2&&!t)return this;e=j(e,e<0?this.count():this.size);var F=this.slice(0,e);return St(this,m===1?F:F.concat(L(arguments,2),this.slice(e+t)))},findLastIndex:function(e,t){var m=this.toKeyedSeq().findLastKey(e,t);return m===void 0?-1:m},first:function(){return this.get(0)},flatten:function(e){return St(this,rr(this,e,!1))},get:function(e,t){return e=B(this,e),e<0||this.size===Infinity||this.size!==void 0&&e>this.size?t:this.find(function(m,F){return F===e},void 0,t)},has:function(e){return e=B(this,e),e>=0&&(this.size!==void 0?this.size===Infinity||e<this.size:this.indexOf(e)!==-1)},interpose:function(e){return St(this,Ir(this,e))},interleave:function(){var e=[this].concat(L(arguments)),t=Qn(this.toSeq(),Qe.of,e),m=t.flatten(!0);return t.size&&(m.size=t.size*e.length),St(this,m)},last:function(){return this.get(-1)},skipWhile:function(e,t){return St(this,xr(this,e,t,!1))},zip:function(){var e=[this].concat(L(arguments));return St(this,Qn(this,On,e))},zipWith:function(e){var t=L(arguments);return t[0]=this,St(this,Qn(this,e,t))}}),v.prototype[a]=!0,v.prototype[c]=!0,Kt(A,{get:function(e,t){return this.has(e)?e:t},includes:function(e){return this.has(e)},keySeq:function(){return this.valueSeq()}}),A.prototype.has=bt.includes,Kt(He,b.prototype),Kt(Qe,v.prototype),Kt(ot,A.prototype),Kt(Rt,b.prototype),Kt(Mt,v.prototype),Kt(jt,A.prototype);function vn(e,t){return t}function ln(e,t){return[t,e]}function sn(e){return function(){return!e.apply(this,arguments)}}function tn(e){return function(){return-e.apply(this,arguments)}}function mn(e){return typeof e=="string"?JSON.stringify(e):e}function On(){return L(arguments)}function nn(e,t){return e<t?1:e>t?-1:0}function yn(e){if(e.size===Infinity)return 0;var t=f(e),m=I(e),F=t?1:0,M=e.__iterate(m?t?function(G,$){F=31*F+Ot(J(G),J($))|0}:function(G,$){F=F+Ot(J(G),J($))|0}:t?function(G){F=31*F+J(G)|0}:function(G){F=F+J(G)|0});return Rn(M,F)}function Rn(e,t){return t=It(t,**********),t=It(t<<15|t>>>-15,461845907),t=It(t<<13|t>>>-13,5),t=(t+**********|0)^e,t=It(t^t>>>16,**********),t=It(t^t>>>13,**********),t=Qt(t^t>>>16),t}function Ot(e,t){return e^t+**********+(e<<6)+(e>>2)|0}var $t={Iterable:x,Seq:Be,Collection:wt,Map:Ie,OrderedMap:Nt,List:xt,Stack:pe,Set:Ct,OrderedSet:n,Record:Xt,Range:ft,Repeat:lt,is:Ze,fromJS:Fe};return $t})},38698:function(H,P,i){"use strict";/** @license React v17.0.2
 * react-dom-server.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var x=i(27418),b=i(67294);function v(N){for(var Z="https://reactjs.org/docs/error-decoder.html?invariant="+N,q=1;q<arguments.length;q++)Z+="&args[]="+encodeURIComponent(arguments[q]);return"Minified React error #"+N+"; visit "+Z+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var A=60106,p=60107,I=60108,E=60114,d=60109,f=60110,g=60112,s=60113,a=60120,c=60115,o=60116,r=60121,u=60117,S=60119,C=60129,l=60131;if(typeof Symbol=="function"&&Symbol.for){var h=Symbol.for;A=h("react.portal"),p=h("react.fragment"),I=h("react.strict_mode"),E=h("react.profiler"),d=h("react.provider"),f=h("react.context"),g=h("react.forward_ref"),s=h("react.suspense"),a=h("react.suspense_list"),c=h("react.memo"),o=h("react.lazy"),r=h("react.block"),u=h("react.fundamental"),S=h("react.scope"),C=h("react.debug_trace_mode"),l=h("react.legacy_hidden")}function D(N){if(N==null)return null;if(typeof N=="function")return N.displayName||N.name||null;if(typeof N=="string")return N;switch(N){case p:return"Fragment";case A:return"Portal";case E:return"Profiler";case I:return"StrictMode";case s:return"Suspense";case a:return"SuspenseList"}if(typeof N=="object")switch(N.$$typeof){case f:return(N.displayName||"Context")+".Consumer";case d:return(N._context.displayName||"Context")+".Provider";case g:var Z=N.render;return Z=Z.displayName||Z.name||"",N.displayName||(Z!==""?"ForwardRef("+Z+")":"ForwardRef");case c:return D(N.type);case r:return D(N._render);case o:Z=N._payload,N=N._init;try{return D(N(Z))}catch(q){}}return null}var k=b.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,y={};function L(N,Z){for(var q=N._threadCount|0;q<=Z;q++)N[q]=N._currentValue2,N._threadCount=q+1}function T(N,Z,q,de){if(de&&(de=N.contextType,typeof de=="object"&&de!==null))return L(de,q),de[q];if(N=N.contextTypes){q={};for(var be in N)q[be]=Z[be];Z=q}else Z=y;return Z}for(var B=new Uint16Array(16),w=0;15>w;w++)B[w]=w+1;B[15]=0;var z=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,j=Object.prototype.hasOwnProperty,U={},O={};function ie(N){return j.call(O,N)?!0:j.call(U,N)?!1:z.test(N)?O[N]=!0:(U[N]=!0,!1)}function Q(N,Z,q,de){if(q!==null&&q.type===0)return!1;switch(typeof Z){case"function":case"symbol":return!0;case"boolean":return de?!1:q!==null?!q.acceptsBooleans:(N=N.toLowerCase().slice(0,5),N!=="data-"&&N!=="aria-");default:return!1}}function ue(N,Z,q,de){if(Z===null||typeof Z=="undefined"||Q(N,Z,q,de))return!0;if(de)return!1;if(q!==null)switch(q.type){case 3:return!Z;case 4:return Z===!1;case 5:return isNaN(Z);case 6:return isNaN(Z)||1>Z}return!1}function Ee(N,Z,q,de,be,Se,Ae){this.acceptsBooleans=Z===2||Z===3||Z===4,this.attributeName=de,this.attributeNamespace=be,this.mustUseProperty=q,this.propertyName=N,this.type=Z,this.sanitizeURL=Se,this.removeEmptyString=Ae}var me={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(N){me[N]=new Ee(N,0,!1,N,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(N){var Z=N[0];me[Z]=new Ee(Z,1,!1,N[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(N){me[N]=new Ee(N,2,!1,N.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(N){me[N]=new Ee(N,2,!1,N,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(N){me[N]=new Ee(N,3,!1,N.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(N){me[N]=new Ee(N,3,!0,N,null,!1,!1)}),["capture","download"].forEach(function(N){me[N]=new Ee(N,4,!1,N,null,!1,!1)}),["cols","rows","size","span"].forEach(function(N){me[N]=new Ee(N,6,!1,N,null,!1,!1)}),["rowSpan","start"].forEach(function(N){me[N]=new Ee(N,5,!1,N.toLowerCase(),null,!1,!1)});var V=/[\-:]([a-z])/g;function we(N){return N[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(N){var Z=N.replace(V,we);me[Z]=new Ee(Z,1,!1,N,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(N){var Z=N.replace(V,we);me[Z]=new Ee(Z,1,!1,N,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(N){var Z=N.replace(V,we);me[Z]=new Ee(Z,1,!1,N,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(N){me[N]=new Ee(N,1,!1,N.toLowerCase(),null,!1,!1)}),me.xlinkHref=new Ee("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(N){me[N]=new Ee(N,1,!1,N.toLowerCase(),null,!0,!0)});var Ne=/["'&<>]/;function Me(N){if(typeof N=="boolean"||typeof N=="number")return""+N;N=""+N;var Z=Ne.exec(N);if(Z){var q="",de,be=0;for(de=Z.index;de<N.length;de++){switch(N.charCodeAt(de)){case 34:Z="&quot;";break;case 38:Z="&amp;";break;case 39:Z="&#x27;";break;case 60:Z="&lt;";break;case 62:Z="&gt;";break;default:continue}be!==de&&(q+=N.substring(be,de)),be=de+1,q+=Z}N=be!==de?q+N.substring(be,de):q}return N}function Ke(N,Z){var q=me.hasOwnProperty(N)?me[N]:null,de;return(de=N!=="style")&&(de=q!==null?q.type===0:!(!(2<N.length)||N[0]!=="o"&&N[0]!=="O"||N[1]!=="n"&&N[1]!=="N")),de||ue(N,Z,q,!1)?"":q!==null?(N=q.attributeName,de=q.type,de===3||de===4&&Z===!0?N+'=""':(q.sanitizeURL&&(Z=""+Z),N+'="'+(Me(Z)+'"'))):ie(N)?N+'="'+(Me(Z)+'"'):""}function qe(N,Z){return N===Z&&(N!==0||1/N==1/Z)||N!==N&&Z!==Z}var it=typeof Object.is=="function"?Object.is:qe,et=null,Je=null,Be=null,He=!1,Qe=!1,ot=null,mt=0;function rt(){if(et===null)throw Error(v(321));return et}function Ce(){if(0<mt)throw Error(v(312));return{memoizedState:null,queue:null,next:null}}function $e(){return Be===null?Je===null?(He=!1,Je=Be=Ce()):(He=!0,Be=Je):Be.next===null?(He=!1,Be=Be.next=Ce()):(He=!0,Be=Be.next),Be}function Ft(N,Z,q,de){for(;Qe;)Qe=!1,mt+=1,Be=null,q=N(Z,de);return Et(),q}function Et(){et=null,Qe=!1,Je=null,mt=0,Be=ot=null}function De(N,Z){return typeof Z=="function"?Z(N):Z}function ne(N,Z,q){if(et=rt(),Be=$e(),He){var de=Be.queue;if(Z=de.dispatch,ot!==null&&(q=ot.get(de),q!==void 0)){ot.delete(de),de=Be.memoizedState;do de=N(de,q.action),q=q.next;while(q!==null);return Be.memoizedState=de,[de,Z]}return[Be.memoizedState,Z]}return N=N===De?typeof Z=="function"?Z():Z:q!==void 0?q(Z):Z,Be.memoizedState=N,N=Be.queue={last:null,dispatch:null},N=N.dispatch=le.bind(null,et,N),[Be.memoizedState,N]}function _(N,Z){if(et=rt(),Be=$e(),Z=Z===void 0?null:Z,Be!==null){var q=Be.memoizedState;if(q!==null&&Z!==null){var de=q[1];e:if(de===null)de=!1;else{for(var be=0;be<de.length&&be<Z.length;be++)if(!it(Z[be],de[be])){de=!1;break e}de=!0}if(de)return q[0]}}return N=N(),Be.memoizedState=[N,Z],N}function le(N,Z,q){if(!(25>mt))throw Error(v(301));if(N===et)if(Qe=!0,N={action:q,next:null},ot===null&&(ot=new Map),q=ot.get(Z),q===void 0)ot.set(Z,N);else{for(Z=q;Z.next!==null;)Z=Z.next;Z.next=N}}function se(){}var ge=null,ye={readContext:function(N){var Z=ge.threadID;return L(N,Z),N[Z]},useContext:function(N){rt();var Z=ge.threadID;return L(N,Z),N[Z]},useMemo:_,useReducer:ne,useRef:function(N){et=rt(),Be=$e();var Z=Be.memoizedState;return Z===null?(N={current:N},Be.memoizedState=N):Z},useState:function(N){return ne(De,N)},useLayoutEffect:function(){},useCallback:function(N,Z){return _(function(){return N},Z)},useImperativeHandle:se,useEffect:se,useDebugValue:se,useDeferredValue:function(N){return rt(),N},useTransition:function(){return rt(),[function(N){N()},!1]},useOpaqueIdentifier:function(){return(ge.identifierPrefix||"")+"R:"+(ge.uniqueID++).toString(36)},useMutableSource:function(N,Z){return rt(),Z(N._source)}},We={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg"};function Fe(N){switch(N){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}var Ve={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},Ue=x({menuitem:!0},Ve),ct={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ze=["Webkit","ms","Moz","O"];Object.keys(ct).forEach(function(N){Ze.forEach(function(Z){Z=Z+N.charAt(0).toUpperCase()+N.substring(1),ct[Z]=ct[N]})});var ut=/([A-Z])/g,lt=/^ms-/,gt=b.Children.toArray,pt=k.ReactCurrentDispatcher,ft={listing:!0,pre:!0,textarea:!0},Dt=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,wt={},Rt={};function Mt(N){if(N==null)return N;var Z="";return b.Children.forEach(N,function(q){q!=null&&(Z+=q)}),Z}var jt=Object.prototype.hasOwnProperty,It={children:null,dangerouslySetInnerHTML:null,suppressContentEditableWarning:null,suppressHydrationWarning:null};function Qt(N,Z){if(N===void 0)throw Error(v(152,D(Z)||"Component"))}function J(N,Z,q){function de(Ae,xe){var ve=xe.prototype&&xe.prototype.isReactComponent,ze=T(xe,Z,q,ve),Ge=[],Te=!1,Pe={isMounted:function(){return!1},enqueueForceUpdate:function(){if(Ge===null)return null},enqueueReplaceState:function(Bt,_e){Te=!0,Ge=[_e]},enqueueSetState:function(Bt,_e){if(Ge===null)return null;Ge.push(_e)}};if(ve){if(ve=new xe(Ae.props,ze,Pe),typeof xe.getDerivedStateFromProps=="function"){var Oe=xe.getDerivedStateFromProps.call(null,Ae.props,ve.state);Oe!=null&&(ve.state=x({},ve.state,Oe))}}else if(et={},ve=xe(Ae.props,ze,Pe),ve=Ft(xe,Ae.props,ve,ze),ve==null||ve.render==null){N=ve,Qt(N,xe);return}if(ve.props=Ae.props,ve.context=ze,ve.updater=Pe,Pe=ve.state,Pe===void 0&&(ve.state=Pe=null),typeof ve.UNSAFE_componentWillMount=="function"||typeof ve.componentWillMount=="function")if(typeof ve.componentWillMount=="function"&&typeof xe.getDerivedStateFromProps!="function"&&ve.componentWillMount(),typeof ve.UNSAFE_componentWillMount=="function"&&typeof xe.getDerivedStateFromProps!="function"&&ve.UNSAFE_componentWillMount(),Ge.length){Pe=Ge;var Ie=Te;if(Ge=null,Te=!1,Ie&&Pe.length===1)ve.state=Pe[0];else{Oe=Ie?Pe[0]:ve.state;var ht=!0;for(Ie=Ie?1:0;Ie<Pe.length;Ie++){var yt=Pe[Ie];yt=typeof yt=="function"?yt.call(ve,Oe,Ae.props,ze):yt,yt!=null&&(ht?(ht=!1,Oe=x({},Oe,yt)):x(Oe,yt))}ve.state=Oe}}else Ge=null;if(N=ve.render(),Qt(N,xe),typeof ve.getChildContext=="function"&&(Ae=xe.childContextTypes,typeof Ae=="object")){var Re=ve.getChildContext();for(var nt in Re)if(!(nt in Ae))throw Error(v(108,D(xe)||"Unknown",nt))}Re&&(Z=x({},Z,Re))}for(;b.isValidElement(N);){var be=N,Se=be.type;if(typeof Se!="function")break;de(be,Se)}return{child:N,context:Z}}var ae=function(){function N(q,de,be){b.isValidElement(q)?q.type!==p?q=[q]:(q=q.props.children,q=b.isValidElement(q)?[q]:gt(q)):q=gt(q),q={type:null,domNamespace:We.html,children:q,childIndex:0,context:y,footer:""};var Se=B[0];if(Se===0){var Ae=B;Se=Ae.length;var xe=2*Se;if(!(65536>=xe))throw Error(v(304));var ve=new Uint16Array(xe);for(ve.set(Ae),B=ve,B[0]=Se+1,Ae=Se;Ae<xe-1;Ae++)B[Ae]=Ae+1;B[xe-1]=0}else B[0]=B[Se];this.threadID=Se,this.stack=[q],this.exhausted=!1,this.currentSelectValue=null,this.previousWasTextNode=!1,this.makeStaticMarkup=de,this.suspenseDepth=0,this.contextIndex=-1,this.contextStack=[],this.contextValueStack=[],this.uniqueID=0,this.identifierPrefix=be&&be.identifierPrefix||""}var Z=N.prototype;return Z.destroy=function(){if(!this.exhausted){this.exhausted=!0,this.clearProviders();var q=this.threadID;B[q]=B[0],B[0]=q}},Z.pushProvider=function(q){var de=++this.contextIndex,be=q.type._context,Se=this.threadID;L(be,Se);var Ae=be[Se];this.contextStack[de]=be,this.contextValueStack[de]=Ae,be[Se]=q.props.value},Z.popProvider=function(){var q=this.contextIndex,de=this.contextStack[q],be=this.contextValueStack[q];this.contextStack[q]=null,this.contextValueStack[q]=null,this.contextIndex--,de[this.threadID]=be},Z.clearProviders=function(){for(var q=this.contextIndex;0<=q;q--)this.contextStack[q][this.threadID]=this.contextValueStack[q]},Z.read=function(q){if(this.exhausted)return null;var de=ge;ge=this;var be=pt.current;pt.current=ye;try{for(var Se=[""],Ae=!1;Se[0].length<q;){if(this.stack.length===0){this.exhausted=!0;var xe=this.threadID;B[xe]=B[0],B[0]=xe;break}var ve=this.stack[this.stack.length-1];if(Ae||ve.childIndex>=ve.children.length){var ze=ve.footer;if(ze!==""&&(this.previousWasTextNode=!1),this.stack.pop(),ve.type==="select")this.currentSelectValue=null;else if(ve.type!=null&&ve.type.type!=null&&ve.type.type.$$typeof===d)this.popProvider(ve.type);else if(ve.type===s){this.suspenseDepth--;var Ge=Se.pop();if(Ae){Ae=!1;var Te=ve.fallbackFrame;if(!Te)throw Error(v(303));this.stack.push(Te),Se[this.suspenseDepth]+="<!--$!-->";continue}else Se[this.suspenseDepth]+=Ge}Se[this.suspenseDepth]+=ze}else{var Pe=ve.children[ve.childIndex++],Oe="";try{Oe+=this.render(Pe,ve.context,ve.domNamespace)}catch(Ie){throw Ie!=null&&typeof Ie.then=="function"?Error(v(294)):Ie}finally{}Se.length<=this.suspenseDepth&&Se.push(""),Se[this.suspenseDepth]+=Oe}}return Se[0]}finally{pt.current=be,ge=de,Et()}},Z.render=function(q,de,be){if(typeof q=="string"||typeof q=="number")return be=""+q,be===""?"":this.makeStaticMarkup?Me(be):this.previousWasTextNode?"<!-- -->"+Me(be):(this.previousWasTextNode=!0,Me(be));if(de=J(q,de,this.threadID),q=de.child,de=de.context,q===null||q===!1)return"";if(!b.isValidElement(q)){if(q!=null&&q.$$typeof!=null)throw be=q.$$typeof,Error(be===A?v(257):v(258,be.toString()));return q=gt(q),this.stack.push({type:null,domNamespace:be,children:q,childIndex:0,context:de,footer:""}),""}var Se=q.type;if(typeof Se=="string")return this.renderDOM(q,de,be);switch(Se){case l:case C:case I:case E:case a:case p:return q=gt(q.props.children),this.stack.push({type:null,domNamespace:be,children:q,childIndex:0,context:de,footer:""}),"";case s:throw Error(v(294));case S:throw Error(v(343))}if(typeof Se=="object"&&Se!==null)switch(Se.$$typeof){case g:et={};var Ae=Se.render(q.props,q.ref);return Ae=Ft(Se.render,q.props,Ae,q.ref),Ae=gt(Ae),this.stack.push({type:null,domNamespace:be,children:Ae,childIndex:0,context:de,footer:""}),"";case c:return q=[b.createElement(Se.type,x({ref:q.ref},q.props))],this.stack.push({type:null,domNamespace:be,children:q,childIndex:0,context:de,footer:""}),"";case d:return Se=gt(q.props.children),be={type:q,domNamespace:be,children:Se,childIndex:0,context:de,footer:""},this.pushProvider(q),this.stack.push(be),"";case f:Se=q.type,Ae=q.props;var xe=this.threadID;return L(Se,xe),Se=gt(Ae.children(Se[xe])),this.stack.push({type:q,domNamespace:be,children:Se,childIndex:0,context:de,footer:""}),"";case u:throw Error(v(338));case o:return Se=q.type,Ae=Se._init,Se=Ae(Se._payload),q=[b.createElement(Se,x({ref:q.ref},q.props))],this.stack.push({type:null,domNamespace:be,children:q,childIndex:0,context:de,footer:""}),""}throw Error(v(130,Se==null?Se:typeof Se,""))},Z.renderDOM=function(q,de,be){var Se=q.type.toLowerCase();if(be===We.html&&Fe(Se),!wt.hasOwnProperty(Se)){if(!Dt.test(Se))throw Error(v(65,Se));wt[Se]=!0}var Ae=q.props;if(Se==="input")Ae=x({type:void 0},Ae,{defaultChecked:void 0,defaultValue:void 0,value:Ae.value!=null?Ae.value:Ae.defaultValue,checked:Ae.checked!=null?Ae.checked:Ae.defaultChecked});else if(Se==="textarea"){var xe=Ae.value;if(xe==null){xe=Ae.defaultValue;var ve=Ae.children;if(ve!=null){if(xe!=null)throw Error(v(92));if(Array.isArray(ve)){if(!(1>=ve.length))throw Error(v(93));ve=ve[0]}xe=""+ve}xe==null&&(xe="")}Ae=x({},Ae,{value:void 0,children:""+xe})}else if(Se==="select")this.currentSelectValue=Ae.value!=null?Ae.value:Ae.defaultValue,Ae=x({},Ae,{value:void 0});else if(Se==="option"){ve=this.currentSelectValue;var ze=Mt(Ae.children);if(ve!=null){var Ge=Ae.value!=null?Ae.value+"":ze;if(xe=!1,Array.isArray(ve)){for(var Te=0;Te<ve.length;Te++)if(""+ve[Te]===Ge){xe=!0;break}}else xe=""+ve===Ge;Ae=x({selected:void 0,children:void 0},Ae,{selected:xe,children:ze})}}if(xe=Ae){if(Ue[Se]&&(xe.children!=null||xe.dangerouslySetInnerHTML!=null))throw Error(v(137,Se));if(xe.dangerouslySetInnerHTML!=null){if(xe.children!=null)throw Error(v(60));if(!(typeof xe.dangerouslySetInnerHTML=="object"&&"__html"in xe.dangerouslySetInnerHTML))throw Error(v(61))}if(xe.style!=null&&typeof xe.style!="object")throw Error(v(62))}xe=Ae,ve=this.makeStaticMarkup,ze=this.stack.length===1,Ge="<"+q.type;e:if(Se.indexOf("-")===-1)Te=typeof xe.is=="string";else switch(Se){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":Te=!1;break e;default:Te=!0}for(_e in xe)if(jt.call(xe,_e)){var Pe=xe[_e];if(Pe!=null){if(_e==="style"){var Oe=void 0,Ie="",ht="";for(Oe in Pe)if(Pe.hasOwnProperty(Oe)){var yt=Oe.indexOf("--")===0,Re=Pe[Oe];if(Re!=null){if(yt)var nt=Oe;else if(nt=Oe,Rt.hasOwnProperty(nt))nt=Rt[nt];else{var Bt=nt.replace(ut,"-$1").toLowerCase().replace(lt,"-ms-");nt=Rt[nt]=Bt}Ie+=ht+nt+":",ht=Oe,yt=Re==null||typeof Re=="boolean"||Re===""?"":yt||typeof Re!="number"||Re===0||ct.hasOwnProperty(ht)&&ct[ht]?(""+Re).trim():Re+"px",Ie+=yt,ht=";"}}Pe=Ie||null}Oe=null,Te?It.hasOwnProperty(_e)||(Oe=_e,Oe=ie(Oe)&&Pe!=null?Oe+'="'+(Me(Pe)+'"'):""):Oe=Ke(_e,Pe),Oe&&(Ge+=" "+Oe)}}ve||ze&&(Ge+=' data-reactroot=""');var _e=Ge;xe="",Ve.hasOwnProperty(Se)?_e+="/>":(_e+=">",xe="</"+q.type+">");e:{if(ve=Ae.dangerouslySetInnerHTML,ve!=null){if(ve.__html!=null){ve=ve.__html;break e}}else if(ve=Ae.children,typeof ve=="string"||typeof ve=="number"){ve=Me(ve);break e}ve=null}return ve!=null?(Ae=[],ft.hasOwnProperty(Se)&&ve.charAt(0)===`
`&&(_e+=`
`),_e+=ve):Ae=gt(Ae.children),q=q.type,be=be==null||be==="http://www.w3.org/1999/xhtml"?Fe(q):be==="http://www.w3.org/2000/svg"&&q==="foreignObject"?"http://www.w3.org/1999/xhtml":be,this.stack.push({domNamespace:be,type:Se,children:Ae,childIndex:0,context:de,footer:xe}),this.previousWasTextNode=!1,_e},N}();P.renderToNodeStream=function(){throw Error(v(207))},P.renderToStaticMarkup=function(N,Z){N=new ae(N,!0,Z);try{return N.read(Infinity)}finally{N.destroy()}},P.renderToStaticNodeStream=function(){throw Error(v(208))},P.renderToString=function(N,Z){N=new ae(N,!1,Z);try{return N.read(Infinity)}finally{N.destroy()}},P.version="17.0.2"},97762:function(H,P,i){"use strict";H.exports=i(38698)},24889:function(H,P,i){var x=i(34155);(function(b,v){"use strict";if(b.setImmediate)return;var A=1,p={},I=!1,E=b.document,d;function f(h){typeof h!="function"&&(h=new Function(""+h));for(var D=new Array(arguments.length-1),k=0;k<D.length;k++)D[k]=arguments[k+1];var y={callback:h,args:D};return p[A]=y,d(A),A++}function g(h){delete p[h]}function s(h){var D=h.callback,k=h.args;switch(k.length){case 0:D();break;case 1:D(k[0]);break;case 2:D(k[0],k[1]);break;case 3:D(k[0],k[1],k[2]);break;default:D.apply(v,k);break}}function a(h){if(I)setTimeout(a,0,h);else{var D=p[h];if(D){I=!0;try{s(D)}finally{g(h),I=!1}}}}function c(){d=function(h){x.nextTick(function(){a(h)})}}function o(){if(b.postMessage&&!b.importScripts){var h=!0,D=b.onmessage;return b.onmessage=function(){h=!1},b.postMessage("","*"),b.onmessage=D,h}}function r(){var h="setImmediate$"+Math.random()+"$",D=function(k){k.source===b&&typeof k.data=="string"&&k.data.indexOf(h)===0&&a(+k.data.slice(h.length))};b.addEventListener?b.addEventListener("message",D,!1):b.attachEvent("onmessage",D),d=function(k){b.postMessage(h+k,"*")}}function u(){var h=new MessageChannel;h.port1.onmessage=function(D){var k=D.data;a(k)},d=function(D){h.port2.postMessage(D)}}function S(){var h=E.documentElement;d=function(D){var k=E.createElement("script");k.onreadystatechange=function(){a(D),k.onreadystatechange=null,h.removeChild(k),k=null},h.appendChild(k)}}function C(){d=function(h){setTimeout(a,0,h)}}var l=Object.getPrototypeOf&&Object.getPrototypeOf(b);l=l&&l.setTimeout?l:b,{}.toString.call(b.process)==="[object process]"?c():o()?r():b.MessageChannel?u():E&&"onreadystatechange"in E.createElement("script")?S():C(),l.setImmediate=f,l.clearImmediate=g})(typeof self=="undefined"?typeof i.g=="undefined"?this:i.g:self)},42238:function(H,P,i){var x;(function(b,v){"use strict";var A="0.7.31",p="",I="?",E="function",d="undefined",f="object",g="string",s="major",a="model",c="name",o="type",r="vendor",u="version",S="architecture",C="console",l="mobile",h="tablet",D="smarttv",k="wearable",y="embedded",L=255,T="Amazon",B="Apple",w="ASUS",z="BlackBerry",j="Browser",U="Chrome",O="Edge",ie="Firefox",Q="Google",ue="Huawei",Ee="LG",me="Microsoft",V="Motorola",we="Opera",Ne="Samsung",Me="Sony",Ke="Xiaomi",qe="Zebra",it="Facebook",et=function(_,le){var se={};for(var ge in _)le[ge]&&le[ge].length%2==0?se[ge]=le[ge].concat(_[ge]):se[ge]=_[ge];return se},Je=function(_){for(var le={},se=0;se<_.length;se++)le[_[se].toUpperCase()]=_[se];return le},Be=function(_,le){return typeof _===g?He(le).indexOf(He(_))!==-1:!1},He=function(_){return _.toLowerCase()},Qe=function(_){return typeof _===g?_.replace(/[^\d\.]/g,p).split(".")[0]:v},ot=function(_,le){if(typeof _===g)return _=_.replace(/^\s\s*/,p).replace(/\s\s*$/,p),typeof le===d?_:_.substring(0,L)},mt=function(_,le){for(var se=0,ge,ye,We,Fe,Ve,Ue;se<le.length&&!Ve;){var ct=le[se],Ze=le[se+1];for(ge=ye=0;ge<ct.length&&!Ve;)if(Ve=ct[ge++].exec(_),Ve)for(We=0;We<Ze.length;We++)Ue=Ve[++ye],Fe=Ze[We],typeof Fe===f&&Fe.length>0?Fe.length===2?typeof Fe[1]==E?this[Fe[0]]=Fe[1].call(this,Ue):this[Fe[0]]=Fe[1]:Fe.length===3?typeof Fe[1]===E&&!(Fe[1].exec&&Fe[1].test)?this[Fe[0]]=Ue?Fe[1].call(this,Ue,Fe[2]):v:this[Fe[0]]=Ue?Ue.replace(Fe[1],Fe[2]):v:Fe.length===4&&(this[Fe[0]]=Ue?Fe[3].call(this,Ue.replace(Fe[1],Fe[2])):v):this[Fe]=Ue||v;se+=2}},rt=function(_,le){for(var se in le)if(typeof le[se]===f&&le[se].length>0){for(var ge=0;ge<le[se].length;ge++)if(Be(le[se][ge],_))return se===I?v:se}else if(Be(le[se],_))return se===I?v:se;return _},Ce={"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},$e={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0","2000":"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0","7":"NT 6.1","8":"NT 6.2","8.1":"NT 6.3","10":["NT 6.4","NT 10.0"],RT:"ARM"},Ft={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[u,[c,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[u,[c,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[c,u],[/opios[\/ ]+([\w\.]+)/i],[u,[c,we+" Mini"]],[/\bopr\/([\w\.]+)/i],[u,[c,we]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([-\w\.]+)/i,/(weibo)__([\d\.]+)/i],[c,u],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[u,[c,"UC"+j]],[/\bqbcore\/([\w\.]+)/i],[u,[c,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[u,[c,"WeChat"]],[/konqueror\/([\w\.]+)/i],[u,[c,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[u,[c,"IE"]],[/yabrowser\/([\w\.]+)/i],[u,[c,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[c,/(.+)/,"$1 Secure "+j],u],[/\bfocus\/([\w\.]+)/i],[u,[c,ie+" Focus"]],[/\bopt\/([\w\.]+)/i],[u,[c,we+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[u,[c,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[u,[c,"Dolphin"]],[/coast\/([\w\.]+)/i],[u,[c,we+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[u,[c,"MIUI "+j]],[/fxios\/([-\w\.]+)/i],[u,[c,ie]],[/\bqihu|(qi?ho?o?|360)browser/i],[[c,"360 "+j]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[c,/(.+)/,"$1 "+j],u],[/(comodo_dragon)\/([\w\.]+)/i],[[c,/_/g," "],u],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[c,u],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i],[c],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[c,it],u],[/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[c,u],[/\bgsa\/([\w\.]+) .*safari\//i],[u,[c,"GSA"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[u,[c,U+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[c,U+" WebView"],u],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[u,[c,"Android "+j]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[c,u],[/version\/([\w\.]+) .*mobile\/\w+ (safari)/i],[u,[c,"Mobile Safari"]],[/version\/([\w\.]+) .*(mobile ?safari|safari)/i],[u,c],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[c,[u,rt,Ce]],[/(webkit|khtml)\/([\w\.]+)/i],[c,u],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[c,"Netscape"],u],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[u,[c,ie+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[c,u]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[S,"amd64"]],[/(ia32(?=;))/i],[[S,He]],[/((?:i[346]|x)86)[;\)]/i],[[S,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[S,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[S,"armhf"]],[/windows (ce|mobile); ppc;/i],[[S,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[S,/ower/,p,He]],[/(sun4\w)[;\)]/i],[[S,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[S,He]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[a,[r,Ne],[o,h]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[a,[r,Ne],[o,l]],[/\((ip(?:hone|od)[\w ]*);/i],[a,[r,B],[o,l]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[a,[r,B],[o,h]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[a,[r,ue],[o,h]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}-[atu]?[ln][01259x][012359][an]?)\b(?!.+d\/s)/i],[a,[r,ue],[o,l]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[a,/_/g," "],[r,Ke],[o,l]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[a,/_/g," "],[r,Ke],[o,h]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[a,[r,"OPPO"],[o,l]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[a,[r,"Vivo"],[o,l]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[a,[r,"Realme"],[o,l]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[a,[r,V],[o,l]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[a,[r,V],[o,h]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[a,[r,Ee],[o,h]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[a,[r,Ee],[o,l]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[a,[r,"Lenovo"],[o,h]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[a,/_/g," "],[r,"Nokia"],[o,l]],[/(pixel c)\b/i],[a,[r,Q],[o,h]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[a,[r,Q],[o,l]],[/droid.+ ([c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[a,[r,Me],[o,l]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[a,"Xperia Tablet"],[r,Me],[o,h]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[a,[r,"OnePlus"],[o,l]],[/(alexa)webm/i,/(kf[a-z]{2}wi)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[a,[r,T],[o,h]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[a,/(.+)/g,"Fire Phone $1"],[r,T],[o,l]],[/(playbook);[-\w\),; ]+(rim)/i],[a,r,[o,h]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[a,[r,z],[o,l]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[a,[r,w],[o,h]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[a,[r,w],[o,l]],[/(nexus 9)/i],[a,[r,"HTC"],[o,h]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic|sony)[-_ ]?([-\w]*)/i],[r,[a,/_/g," "],[o,l]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[a,[r,"Acer"],[o,h]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[a,[r,"Meizu"],[o,l]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[a,[r,"Sharp"],[o,l]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[r,a,[o,l]],[/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[r,a,[o,h]],[/(surface duo)/i],[a,[r,me],[o,h]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[a,[r,"Fairphone"],[o,l]],[/(u304aa)/i],[a,[r,"AT&T"],[o,l]],[/\bsie-(\w*)/i],[a,[r,"Siemens"],[o,l]],[/\b(rct\w+) b/i],[a,[r,"RCA"],[o,h]],[/\b(venue[\d ]{2,7}) b/i],[a,[r,"Dell"],[o,h]],[/\b(q(?:mv|ta)\w+) b/i],[a,[r,"Verizon"],[o,h]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[a,[r,"Barnes & Noble"],[o,h]],[/\b(tm\d{3}\w+) b/i],[a,[r,"NuVision"],[o,h]],[/\b(k88) b/i],[a,[r,"ZTE"],[o,h]],[/\b(nx\d{3}j) b/i],[a,[r,"ZTE"],[o,l]],[/\b(gen\d{3}) b.+49h/i],[a,[r,"Swiss"],[o,l]],[/\b(zur\d{3}) b/i],[a,[r,"Swiss"],[o,h]],[/\b((zeki)?tb.*\b) b/i],[a,[r,"Zeki"],[o,h]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[r,"Dragon Touch"],a,[o,h]],[/\b(ns-?\w{0,9}) b/i],[a,[r,"Insignia"],[o,h]],[/\b((nxa|next)-?\w{0,9}) b/i],[a,[r,"NextBook"],[o,h]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[r,"Voice"],a,[o,l]],[/\b(lvtel\-)?(v1[12]) b/i],[[r,"LvTel"],a,[o,l]],[/\b(ph-1) /i],[a,[r,"Essential"],[o,l]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[a,[r,"Envizen"],[o,h]],[/\b(trio[-\w\. ]+) b/i],[a,[r,"MachSpeed"],[o,h]],[/\btu_(1491) b/i],[a,[r,"Rotor"],[o,h]],[/(shield[\w ]+) b/i],[a,[r,"Nvidia"],[o,h]],[/(sprint) (\w+)/i],[r,a,[o,l]],[/(kin\.[onetw]{3})/i],[[a,/\./g," "],[r,me],[o,l]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[a,[r,qe],[o,h]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[a,[r,qe],[o,l]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[r,a,[o,C]],[/droid.+; (shield) bui/i],[a,[r,"Nvidia"],[o,C]],[/(playstation [345portablevi]+)/i],[a,[r,Me],[o,C]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[a,[r,me],[o,C]],[/smart-tv.+(samsung)/i],[r,[o,D]],[/hbbtv.+maple;(\d+)/i],[[a,/^/,"SmartTV"],[r,Ne],[o,D]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[r,Ee],[o,D]],[/(apple) ?tv/i],[r,[a,B+" TV"],[o,D]],[/crkey/i],[[a,U+"cast"],[r,Q],[o,D]],[/droid.+aft(\w)( bui|\))/i],[a,[r,T],[o,D]],[/\(dtv[\);].+(aquos)/i],[a,[r,"Sharp"],[o,D]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w ]*; *(\w[^;]*);([^;]*)/i],[[r,ot],[a,ot],[o,D]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[o,D]],[/((pebble))app/i],[r,a,[o,k]],[/droid.+; (glass) \d/i],[a,[r,Q],[o,k]],[/droid.+; (wt63?0{2,3})\)/i],[a,[r,qe],[o,k]],[/(quest( 2)?)/i],[a,[r,it],[o,k]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[r,[o,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[a,[o,l]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[a,[o,h]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[o,h]],[/(phone|mobile(?:[;\/]| safari)|pda(?=.+windows ce))/i],[[o,l]],[/(android[-\w\. ]{0,9});.+buil/i],[a,[r,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[u,[c,O+"HTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[u,[c,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i],[c,u],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[u,c]],os:[[/microsoft (windows) (vista|xp)/i],[c,u],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[c,[u,rt,$e]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[c,"Windows"],[u,rt,$e]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/cfnetwork\/.+darwin/i],[[u,/_/g,"."],[c,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[c,"Mac OS"],[u,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86)/i],[u,c],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[c,u],[/\(bb(10);/i],[u,[c,z]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[u,[c,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[u,[c,ie+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[u,[c,"webOS"]],[/crkey\/([\d\.]+)/i],[u,[c,U+"cast"]],[/(cros) [\w]+ ([\w\.]+\w)/i],[[c,"Chromium OS"],u],[/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[c,u],[/(sunos) ?([\w\.\d]*)/i],[[c,"Solaris"],u],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux)/i,/(unix) ?([\w\.]*)/i],[c,u]]},Et=function(_,le){if(typeof _===f&&(le=_,_=v),!(this instanceof Et))return new Et(_,le).getResult();var se=_||(typeof b!==d&&b.navigator&&b.navigator.userAgent?b.navigator.userAgent:p),ge=le?et(Ft,le):Ft;return this.getBrowser=function(){var ye={};return ye[c]=v,ye[u]=v,mt.call(ye,se,ge.browser),ye.major=Qe(ye.version),ye},this.getCPU=function(){var ye={};return ye[S]=v,mt.call(ye,se,ge.cpu),ye},this.getDevice=function(){var ye={};return ye[r]=v,ye[a]=v,ye[o]=v,mt.call(ye,se,ge.device),ye},this.getEngine=function(){var ye={};return ye[c]=v,ye[u]=v,mt.call(ye,se,ge.engine),ye},this.getOS=function(){var ye={};return ye[c]=v,ye[u]=v,mt.call(ye,se,ge.os),ye},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return se},this.setUA=function(ye){return se=typeof ye===g&&ye.length>L?ot(ye,L):ye,this},this.setUA(se),this};Et.VERSION=A,Et.BROWSER=Je([c,u,s]),Et.CPU=Je([S]),Et.DEVICE=Je([a,r,o,C,l,D,h,k,y]),Et.ENGINE=Et.OS=Je([c,u]),typeof P!==d?(d!=="object"&&H.exports&&(P=H.exports=Et),P.UAParser=Et):E==="function"&&i.amdO?(x=function(){return Et}.call(P,i,P,H),x!==v&&(H.exports=x)):typeof b!==d&&(b.UAParser=Et);var De=typeof b!==d&&(b.jQuery||b.Zepto);if(De&&!De.ua){var ne=new Et;De.ua=ne.getResult(),De.ua.get=function(){return ne.getUA()},De.ua.set=function(_){ne.setUA(_);var le=ne.getResult();for(var se in le)De.ua[se]=le[se]}}})(typeof window=="object"?window:this)}}]);
