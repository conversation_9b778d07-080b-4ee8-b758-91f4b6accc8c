.markdown {
  color: #454d64;
  font-size: 15px;
  line-height: 1.60625;
}
[data-prefers-color=dark] .markdown {
  color: rgba(255, 255, 255, 0.65);
}
.markdown:not(:first-child):empty {
  min-height: 32px;
}
.markdown h1,
.markdown h2,
.markdown h3,
.markdown h4,
.markdown h5,
.markdown h6 {
  margin: 42px 0 18px;
  color: #454d64;
  font-weight: 500;
  line-height: 1.40625;
}
[data-prefers-color=dark] .markdown h1,
[data-prefers-color=dark] .markdown h2,
[data-prefers-color=dark] .markdown h3,
[data-prefers-color=dark] .markdown h4,
[data-prefers-color=dark] .markdown h5,
[data-prefers-color=dark] .markdown h6 {
  color: rgba(255, 255, 255, 0.85);
}
.markdown h1:hover > a[aria-hidden],
.markdown h2:hover > a[aria-hidden],
.markdown h3:hover > a[aria-hidden],
.markdown h4:hover > a[aria-hidden],
.markdown h5:hover > a[aria-hidden],
.markdown h6:hover > a[aria-hidden] {
  float: left;
  margin-top: 0.06em;
  margin-left: -20px;
  width: 20px;
  padding-right: 4px;
  line-height: 1;
  box-sizing: border-box;
}
@media only screen and (max-width: 767px) {
  .markdown h1:hover > a[aria-hidden],
  .markdown h2:hover > a[aria-hidden],
  .markdown h3:hover > a[aria-hidden],
  .markdown h4:hover > a[aria-hidden],
  .markdown h5:hover > a[aria-hidden],
  .markdown h6:hover > a[aria-hidden] {
    width: 14px;
    margin-left: -14px;
  }
}
.markdown h1:hover > a[aria-hidden]::after,
.markdown h2:hover > a[aria-hidden]::after,
.markdown h3:hover > a[aria-hidden]::after,
.markdown h4:hover > a[aria-hidden]::after,
.markdown h5:hover > a[aria-hidden]::after,
.markdown h6:hover > a[aria-hidden]::after {
  content: '#';
  display: inline-block;
  vertical-align: middle;
  font-size: 20px;
}
.markdown h1:hover > a[aria-hidden] span,
.markdown h2:hover > a[aria-hidden] span,
.markdown h3:hover > a[aria-hidden] span,
.markdown h4:hover > a[aria-hidden] span,
.markdown h5:hover > a[aria-hidden] span,
.markdown h6:hover > a[aria-hidden] span {
  display: none;
}
.markdown h1 + h1,
.markdown h2 + h1,
.markdown h3 + h1,
.markdown h4 + h1,
.markdown h5 + h1,
.markdown h6 + h1,
.markdown h1 + h2,
.markdown h2 + h2,
.markdown h3 + h2,
.markdown h4 + h2,
.markdown h5 + h2,
.markdown h6 + h2,
.markdown h1 + h3,
.markdown h2 + h3,
.markdown h3 + h3,
.markdown h4 + h3,
.markdown h5 + h3,
.markdown h6 + h3,
.markdown h1 + h4,
.markdown h2 + h4,
.markdown h3 + h4,
.markdown h4 + h4,
.markdown h5 + h4,
.markdown h6 + h4,
.markdown h1 + h5,
.markdown h2 + h5,
.markdown h3 + h5,
.markdown h4 + h5,
.markdown h5 + h5,
.markdown h6 + h5,
.markdown h1 + h6,
.markdown h2 + h6,
.markdown h3 + h6,
.markdown h4 + h6,
.markdown h5 + h6,
.markdown h6 + h6 {
  margin-top: 16px;
}
.markdown h1 {
  margin-top: 48px;
  margin-bottom: 32px;
  font-size: 32px;
}
.markdown h2 {
  font-size: 24px;
}
.markdown h3 {
  font-size: 20px;
}
.markdown h4 {
  font-size: 18px;
}
.markdown h5 {
  font-size: 16px;
}
.markdown h6 {
  font-size: 14px;
}
.markdown p {
  margin: 16px 0;
}
.markdown *:not(pre) code {
  padding: 2px 5px;
  color: #d56161;
  background: #f6f7f9;
}
[data-prefers-color=dark] .markdown *:not(pre) code {
  color: #ff7875;
  background: rgba(255, 255, 255, 0.08);
}
.markdown code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
}
.markdown pre {
  font-size: 14px;
  background: #f6f7f9;
}
.markdown pre:not([class^='language-']) {
  padding: 1em;
}
.markdown hr {
  margin: 16px 0;
  border: 0;
  border-top: 1px solid #ebedf1;
}
.markdown blockquote {
  margin: 16px 0;
  padding: 0 24px;
  color: rgba(69, 77, 100, 0.7);
  border-left: 4px solid #ebedf1;
  overflow: hidden;
}
[data-prefers-color=dark] .markdown blockquote {
  color: rgba(255, 255, 255, 0.55);
  border-color: #6b6c6d;
}
.markdown ul,
.markdown ol {
  margin: 8px 0 8px 32px;
  padding: 0;
}
.markdown ul li,
.markdown ol li {
  margin-bottom: 4px;
}
.markdown table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ebedf1;
}
[data-prefers-color=dark] .markdown table {
  border: 1px solid #3b434b;
}
.markdown table th,
.markdown table td {
  padding: 10px 24px;
  border: 1px solid #ebedf1;
}
[data-prefers-color=dark] .markdown table th,
[data-prefers-color=dark] .markdown table td {
  border: 1px solid #3b434b;
}
.markdown table th {
  font-weight: 600;
  background: #f9fafb;
}
[data-prefers-color=dark] .markdown table th {
  background: rgba(255, 255, 255, 0.08);
}
.markdown table td:first-child {
  font-weight: 500;
}
.markdown table a svg {
  display: none;
}
.markdown a {
  color: #4569d4;
  text-decoration: none;
  transition: opacity 0.2s;
  outline: none;
}
[data-prefers-color=dark] .markdown a {
  color: #7395f7;
}
.markdown a:hover {
  opacity: 0.7;
  text-decoration: underline;
}
.markdown a:active {
  opacity: 0.9;
}
.markdown img {
  max-width: 100%;
}
.__dumi-default-external-link-icon {
  vertical-align: -0.155em;
  margin-left: 2px;
}
[data-prefers-color=dark] h1,
[data-prefers-color=dark] h2,
[data-prefers-color=dark] h3,
[data-prefers-color=dark] h4 {
  color: rgba(255, 255, 255, 0.85);
}
/* 颜色表 */
/* 尺寸表 */
.__dumi-default-icon {
  background: url('data:image/png;base64,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') no-repeat 0 0/230px auto;
}
/* 颜色表 - dark */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  font-variant: tabular-nums;
  font-feature-settings: 'tnum', "tnum";
  transition: background 0.2s cubic-bezier(0.075, 0.82, 0.165, 1), color 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);
}
[data-prefers-color=dark] body {
  color: rgba(255, 255, 255, 0.85);
  background-color: #141414;
}
.__dumi-default-layout {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 16px 194px 50px 318px;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-layout {
    padding-top: 66px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
}
.__dumi-default-layout[data-gapless='true'] {
  padding-top: 64px !important;
  padding-right: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-layout[data-gapless='true'] {
    padding-top: 50px !important;
  }
}
.__dumi-default-layout[data-show-sidemenu='false'] {
  padding-left: 58px;
}
.__dumi-default-layout[data-show-slugs='false'] {
  padding-right: 58px;
}
.__dumi-default-layout[data-site-mode='true'] {
  padding-top: 114px;
}
.__dumi-default-layout[data-site-mode='true'][data-show-sidemenu='true'] {
  padding-left: 350px;
}
.__dumi-default-layout[data-site-mode='true'][data-show-slugs='true'] {
  padding-right: 208px;
}
.__dumi-default-layout[data-site-mode='true'] .__dumi-default-layout-content > .markdown:first-child > *:first-child {
  margin-top: 0;
}
.__dumi-default-layout[data-site-mode='true'] .__dumi-default-layout-toc {
  top: 114px;
  max-height: calc(90vh - 144px);
}
.__dumi-default-layout-hero {
  margin: -50px -58px 0;
  padding: 100px 0;
  text-align: center;
  background-color: #f5f6f8;
}
[data-prefers-color=dark] .__dumi-default-layout-hero {
  background-color: rgba(255, 255, 255, 0.08);
}
@media only screen and (max-width: 767px) {
  .__dumi-default-layout-hero {
    margin: -16px -16px 0;
    padding: 48px 0;
  }
}
.__dumi-default-layout-hero img {
  max-width: 100%;
  max-height: 200px;
  margin-bottom: 1rem;
}
.__dumi-default-layout-hero h1 {
  margin: 0 0 16px;
  font-size: 48px;
  font-weight: 600;
  line-height: 56px;
  color: #080e29;
}
[data-prefers-color=dark] .__dumi-default-layout-hero h1 {
  color: rgba(255, 255, 255, 0.85);
}
.__dumi-default-layout-hero h1 + div {
  margin: 16px 0 32px;
  opacity: 0.78;
}
.__dumi-default-layout-hero h1 + div .markdown {
  font-size: 16px;
}
.__dumi-default-layout-hero button {
  margin-right: 16px;
  padding: 0 32px;
  height: 44px;
  color: #4569d4;
  font-size: 16px;
  background: transparent;
  border: 1px solid #4569d4;
  border-radius: 22px;
  box-sizing: border-box;
  cursor: pointer;
  outline: none;
  transition: all 0.3s;
}
.__dumi-default-layout-hero button:hover {
  opacity: 0.8;
}
.__dumi-default-layout-hero button:active {
  opacity: 0.9;
}
.__dumi-default-layout-hero a:last-child button {
  margin-right: 0;
  color: #fff;
  background: #4569d4;
}
.__dumi-default-layout-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 96px;
  grid-row-gap: 56px;
  padding: 72px 0;
}
.__dumi-default-layout-features > dl {
  flex: 1 1;
  margin: 0;
  text-align: center;
  background: no-repeat center top / auto 48px;
}
.__dumi-default-layout-features > dl[style*='background-image'] {
  padding-top: 64px;
}
.__dumi-default-layout-features > dl dt {
  margin-bottom: 12px;
  font-size: 20px;
  line-height: 1;
  color: #454d64;
}
[data-prefers-color=dark] .__dumi-default-layout-features > dl dt {
  color: rgba(255, 255, 255, 0.85);
}
.__dumi-default-layout-features > dl a {
  transition-duration: none;
}
.__dumi-default-layout-features > dl a dt {
  color: #4569d4;
  transition: opacity 0.2s;
}
.__dumi-default-layout-features > dl a dt:hover {
  opacity: 0.7;
  text-decoration: underline;
}
.__dumi-default-layout-features > dl a dt:active {
  opacity: 0.9;
}
.__dumi-default-layout-features > dl dd {
  margin: 0;
}
.__dumi-default-layout-features > dl dd .markdown {
  color: #717484;
  font-size: 14px;
  line-height: 22px;
}
[data-prefers-color=dark] .__dumi-default-layout-features > dl dd .markdown {
  color: rgba(255, 255, 255, 0.65);
}
.__dumi-default-layout-features > dl dd .markdown > p:first-child {
  margin-top: 0;
}
.__dumi-default-layout-features > dl dd .markdown > p:last-child {
  margin-bottom: 0;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-layout-features {
    display: block;
    padding: 40px 0;
  }
  .__dumi-default-layout-features > dl {
    text-align: left;
    background-position: left top;
  }
  .__dumi-default-layout-features > dl[style*='background-image'] {
    padding: 0 0 0 60px;
  }
  .__dumi-default-layout-features > dl + dl {
    margin-top: 32px;
  }
}
.__dumi-default-layout-features,
.__dumi-default-layout-features + .__dumi-default-layout-content,
.__dumi-default-layout-hero + .__dumi-default-layout-content {
  margin-left: auto;
  margin-right: auto;
  max-width: 960px;
}
.__dumi-default-layout-hero + .__dumi-default-layout-content {
  margin-top: 60px;
}
.__dumi-default-layout-toc {
  list-style: none;
  position: fixed;
  z-index: 10;
  top: 50px;
  right: 0;
  width: 136px;
  max-height: calc(90vh - 80px);
  margin: 0;
  padding: 0 24px 0 0;
  background-color: #fff;
  box-sizing: content-box;
  overflow: auto;
}
[data-prefers-color=dark] .__dumi-default-layout-toc {
  background-color: #141414;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-layout-toc {
    display: none;
  }
}
.__dumi-default-layout-toc li {
  position: relative;
  margin: 0;
  padding: 4px 0 4px 6px;
  text-indent: 12px;
  font-size: 13px;
  line-height: 1.40625;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.__dumi-default-layout-toc li a {
  color: #454d64;
  text-decoration: none;
}
[data-prefers-color=dark] .__dumi-default-layout-toc li a {
  color: rgba(255, 255, 255, 0.85);
}
.__dumi-default-layout-toc li a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  display: inline-block;
  width: 2px;
  background: #ebedf1;
}
.__dumi-default-layout-toc li a:hover {
  color: #5a7ad9;
}
[data-prefers-color=dark] .__dumi-default-layout-toc li a:hover {
  color: #8ba7f8;
}
.__dumi-default-layout-toc li a:active {
  color: #5173d7;
}
[data-prefers-color=dark] .__dumi-default-layout-toc li a:active {
  color: #81a0f8;
}
.__dumi-default-layout-toc li a.active {
  color: #4569d4;
}
[data-prefers-color=dark] .__dumi-default-layout-toc li a.active {
  color: #7395f7;
}
.__dumi-default-layout-toc li a.active::before {
  background: #4569d4;
}
[data-prefers-color=dark] .__dumi-default-layout-toc li a.active::before {
  background: #7395f7;
}
.__dumi-default-layout-footer-meta {
  margin-top: 40px;
  padding-top: 24px;
  display: flex;
  color: #717484;
  font-size: 14px;
  justify-content: space-between;
  border-top: 1px solid #ebedf1;
}
[data-prefers-color=dark] .__dumi-default-layout-footer-meta {
  color: rgba(255, 255, 255, 0.65);
  border-color: #6b6c6d;
}
@media only screen and (max-width: 960px) {
  .__dumi-default-layout-footer-meta {
    display: block;
  }
}
.__dumi-default-layout-footer-meta > a {
  margin-bottom: 4px;
  display: block;
  color: #4569d4;
  transition: opacity 0.2s;
  text-decoration: none;
}
[data-prefers-color=dark] .__dumi-default-layout-footer-meta > a {
  color: #7395f7;
}
.__dumi-default-layout-footer-meta > a:hover {
  opacity: 0.7;
  text-decoration: underline;
}
.__dumi-default-layout-footer-meta > a:active {
  opacity: 0.9;
}
.__dumi-default-layout-footer-meta > span:last-child::before {
  content: attr(data-updated-text);
  color: #4569d4;
}
[data-prefers-color=dark] .__dumi-default-layout-footer-meta > span:last-child::before {
  color: #7395f7;
}
.__dumi-default-layout-footer {
  margin: 72px 0 -32px;
  padding-top: 24px;
  border-top: 1px solid #ebedf1;
  text-align: center;
}
[data-prefers-color=dark] .__dumi-default-layout-footer {
  border-color: #6b6c6d;
}
.__dumi-default-layout-footer .markdown {
  color: #b0b1ba;
}
[data-prefers-color=dark] .__dumi-default-layout-footer .markdown {
  color: rgba(255, 255, 255, 0.45);
}

.__dumi-default-navbar {
  position: fixed;
  z-index: 101;
  top: 0;
  left: 0;
  right: 0;
  display: none;
  align-items: center;
  justify-content: space-between;
  padding: 0 58px;
  height: 64px;
  white-space: nowrap;
  background: #fff;
  box-shadow: 0 8px 24px -2px rgba(0, 0, 0, 0.05);
}
[data-prefers-color=dark] .__dumi-default-navbar {
  background: #141414;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.65);
}
@media only screen and (max-width: 767px) {
  .__dumi-default-navbar {
    display: flex;
    justify-content: center;
    height: 50px;
  }
}
.__dumi-default-navbar-toggle {
  position: absolute;
  top: 14px;
  left: 16px;
  display: none;
  width: 22px;
  height: 22px;
  border: 0;
  outline: none;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAAA4CAYAAAB5YT9uAAAAAXNSR0IArs4c6QAAASVJREFUeAHt3DuOwjAUBVAHscWZjaSkAIqU2QjskV80QwpsF3ArdGiw3hW3OGnQM2Iof6/dfr7+n71/LjAdx+HRsvm8SkNPAHBPJ5ABDiD2KgD3dAIZ4ABirwJwTyeQAQ4g9ioA93QC2fbZsSm/z7MDAQIECBAgQIAAAQIECBAgQIAAAQKvAsvV8mO8O8yn19jkXYHpMC7byXVdeS0/75b5XFvAwr1tE0kARxjbJYDbNpEEcISxXQK4bRNJAEcY2yWA2zaRZP0ePJRzpFEJAQIECBAgQIAAAQIECBAgQIAAgW8VWK/tj7Nb5eBTnvbjsp1c15WX4ncRQeB7lb8zyHrW29xo1F1iU8AxynoR4LpLbAo4RlkvAlx3iU0BxyjrRYDrLrHpDVSAEEPXScHTAAAAAElFTkSuQmCC') no-repeat center / contain;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-navbar-toggle {
    display: block;
  }
}
.__dumi-default-navbar-logo {
  display: inline-block;
  height: 40px;
  color: #080e29;
  font-weight: 500;
  text-decoration: none;
  font-size: 24px;
  line-height: 40px;
}
[data-prefers-color=dark] .__dumi-default-navbar-logo {
  color: rgba(255, 255, 255, 0.85);
}
.__dumi-default-navbar-logo:not([data-plaintext]) {
  padding-left: 56px;
  background: url('data:image/png;base64,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') no-repeat 0 / contain;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-navbar-logo {
    height: 28px;
    line-height: 28px;
  }
  .__dumi-default-navbar-logo:not([data-plaintext]) {
    padding-left: 36px;
  }
}
.__dumi-default-navbar-tool {
  display: inline-block;
}
.__dumi-default-navbar nav > span {
  position: relative;
  margin-left: 40px;
  display: inline-block;
  color: #454d64;
  height: 64px;
  cursor: pointer;
  font-size: 14px;
  line-height: 64px;
  text-decoration: none;
  letter-spacing: 0;
}
[data-prefers-color=dark] .__dumi-default-navbar nav > span {
  color: rgba(255, 255, 255, 0.85);
}
.__dumi-default-navbar nav > span > a {
  color: #4d5164;
  text-decoration: none;
}
[data-prefers-color=dark] .__dumi-default-navbar nav > span > a {
  color: rgba(255, 255, 255, 0.85);
}
.__dumi-default-navbar nav > span > a:hover,
.__dumi-default-navbar nav > span > a.active {
  color: #4569d4;
}
[data-prefers-color=dark] .__dumi-default-navbar nav > span > a:hover,
[data-prefers-color=dark] .__dumi-default-navbar nav > span > a.active {
  color: #7395f7;
}
.__dumi-default-navbar nav > span > a::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: -18px;
  left: -18px;
}
.__dumi-default-navbar nav > span > a.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -2px;
  right: -2px;
  height: 2px;
  background-color: #4569d4;
  border-radius: 1px;
}
.__dumi-default-navbar nav > span + *:not(a) {
  margin-left: 40px;
}
.__dumi-default-navbar nav > span > ul {
  list-style: none;
  position: absolute;
  top: 100%;
  left: 50%;
  margin: 0;
  min-width: 100px;
  padding: 8px 18px;
  line-height: 2;
  background-color: #fff;
  box-shadow: 0 8px 24px -2px rgba(0, 0, 0, 0.08);
  transform: translate(-50%);
  transform-origin: top;
  border-radius: 1px;
  transition: all 0.2s;
}
[data-prefers-color=dark] .__dumi-default-navbar nav > span > ul {
  background-color: #141414;
}
.__dumi-default-navbar nav > span > ul a {
  position: relative;
  display: block;
  color: #454d64;
  text-decoration: none;
}
[data-prefers-color=dark] .__dumi-default-navbar nav > span > ul a {
  color: rgba(255, 255, 255, 0.85);
}
.__dumi-default-navbar nav > span > ul a:hover,
.__dumi-default-navbar nav > span > ul a.active {
  color: #4569d4;
}
[data-prefers-color=dark] .__dumi-default-navbar nav > span > ul a:hover,
[data-prefers-color=dark] .__dumi-default-navbar nav > span > ul a.active {
  color: #7395f7;
}
.__dumi-default-navbar nav > span:not(:hover) > ul {
  visibility: hidden;
  pointer-events: none;
  transform: translate(-50%) scaleY(0.9);
  opacity: 0;
}
.__dumi-default-navbar nav .__dumi-default-search + .__dumi-default-locale-select {
  margin-left: 40px;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-navbar nav > a,
  .__dumi-default-navbar nav > span,
  .__dumi-default-navbar nav > div {
    display: none;
  }
}
.__dumi-default-navbar[data-mode='site'] {
  display: flex;
}

.__dumi-default-locale-select {
  position: relative;
  display: inline-block;
  border: 1px solid #dadadf;
  border-radius: 14px;
  transition: background 0.2s;
}
[data-prefers-color=dark] .__dumi-default-locale-select {
  border: 1px solid #464646;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-locale-select {
    margin-top: 6px;
  }
}
.__dumi-default-locale-select:hover {
  background-color: #fafafa;
}
[data-prefers-color=dark] .__dumi-default-locale-select:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.__dumi-default-locale-select:not([data-locale-count='1']):not([data-locale-count='2'])::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 10px;
  margin-top: -3px;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-top: 6px solid #7b7f8d;
  pointer-events: none;
}
.__dumi-default-locale-select a,
.__dumi-default-locale-select span,
.__dumi-default-locale-select select {
  padding: 0 24px 0 16px;
  height: 28px;
  text-align: center;
  text-decoration: none;
  line-height: 28px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: 0;
  font-size: 16px;
  color: #7b7f8d;
  background: transparent;
  outline: none;
  cursor: pointer;
}
.__dumi-default-locale-select a,
.__dumi-default-locale-select span {
  padding-right: 16px;
}

/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
/* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
.ant-message {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: 'tnum', "tnum";
  position: fixed;
  top: 8px;
  left: 0;
  z-index: 1010;
  width: 100%;
  pointer-events: none;
}
.ant-message-notice {
  padding: 8px;
  text-align: center;
}
.ant-message-notice-content {
  display: inline-block;
  padding: 10px 16px;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: all;
}
.ant-message-success .anticon {
  color: #52c41a;
}
.ant-message-error .anticon {
  color: #ff4d4f;
}
.ant-message-warning .anticon {
  color: #faad14;
}
.ant-message-info .anticon,
.ant-message-loading .anticon {
  color: #1890ff;
}
.ant-message .anticon {
  position: relative;
  top: 1px;
  margin-right: 8px;
  font-size: 16px;
}
.ant-message-notice.ant-move-up-leave.ant-move-up-leave-active {
  animation-name: MessageMoveOut;
  animation-duration: 0.3s;
}
@keyframes MessageMoveOut {
  0% {
    max-height: 150px;
    padding: 8px;
    opacity: 1;
  }
  100% {
    max-height: 0;
    padding: 0;
    opacity: 0;
  }
}
.ant-message-rtl {
  direction: rtl;
}
.ant-message-rtl span {
  direction: rtl;
}
.ant-message-rtl .anticon {
  margin-right: 0;
  margin-left: 8px;
}

/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
/* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
.ant-notification {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: 'tnum', "tnum";
  position: fixed;
  z-index: 1010;
  margin-right: 24px;
}
.ant-notification-topLeft,
.ant-notification-bottomLeft {
  margin-right: 0;
  margin-left: 24px;
}
.ant-notification-topLeft .ant-notification-fade-enter.ant-notification-fade-enter-active,
.ant-notification-bottomLeft .ant-notification-fade-enter.ant-notification-fade-enter-active,
.ant-notification-topLeft .ant-notification-fade-appear.ant-notification-fade-appear-active,
.ant-notification-bottomLeft .ant-notification-fade-appear.ant-notification-fade-appear-active {
  animation-name: NotificationLeftFadeIn;
}
.ant-notification-close-icon {
  font-size: 14px;
  cursor: pointer;
}
.ant-notification-hook-holder {
  position: relative;
}
.ant-notification-notice {
  position: relative;
  width: 384px;
  max-width: calc(100vw - 24px * 2);
  margin-bottom: 16px;
  margin-left: auto;
  padding: 16px 24px;
  overflow: hidden;
  line-height: 1.5715;
  word-wrap: break-word;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}
.ant-notification-topLeft .ant-notification-notice,
.ant-notification-bottomLeft .ant-notification-notice {
  margin-right: auto;
  margin-left: 0;
}
.ant-notification-notice-message {
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  line-height: 24px;
}
.ant-notification-notice-message-single-line-auto-margin {
  display: block;
  width: calc(384px - 24px * 2 - 24px - 48px - 100%);
  max-width: 4px;
  background-color: transparent;
  pointer-events: none;
}
.ant-notification-notice-message-single-line-auto-margin::before {
  display: block;
  content: '';
}
.ant-notification-notice-description {
  font-size: 14px;
}
.ant-notification-notice-closable .ant-notification-notice-message {
  padding-right: 24px;
}
.ant-notification-notice-with-icon .ant-notification-notice-message {
  margin-bottom: 4px;
  margin-left: 48px;
  font-size: 16px;
}
.ant-notification-notice-with-icon .ant-notification-notice-description {
  margin-left: 48px;
  font-size: 14px;
}
.ant-notification-notice-icon {
  position: absolute;
  margin-left: 4px;
  font-size: 24px;
  line-height: 24px;
}
.anticon.ant-notification-notice-icon-success {
  color: #52c41a;
}
.anticon.ant-notification-notice-icon-info {
  color: #1890ff;
}
.anticon.ant-notification-notice-icon-warning {
  color: #faad14;
}
.anticon.ant-notification-notice-icon-error {
  color: #ff4d4f;
}
.ant-notification-notice-close {
  position: absolute;
  top: 16px;
  right: 22px;
  color: rgba(0, 0, 0, 0.45);
  outline: none;
}
.ant-notification-notice-close:hover {
  color: rgba(0, 0, 0, 0.67);
}
.ant-notification-notice-btn {
  float: right;
  margin-top: 16px;
}
.ant-notification .notification-fade-effect {
  animation-duration: 0.24s;
  animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  animation-fill-mode: both;
}
.ant-notification-fade-enter,
.ant-notification-fade-appear {
  animation-duration: 0.24s;
  animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  animation-fill-mode: both;
  opacity: 0;
  animation-play-state: paused;
}
.ant-notification-fade-leave {
  animation-duration: 0.24s;
  animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  animation-fill-mode: both;
  animation-duration: 0.2s;
  animation-play-state: paused;
}
.ant-notification-fade-enter.ant-notification-fade-enter-active,
.ant-notification-fade-appear.ant-notification-fade-appear-active {
  animation-name: NotificationFadeIn;
  animation-play-state: running;
}
.ant-notification-fade-leave.ant-notification-fade-leave-active {
  animation-name: NotificationFadeOut;
  animation-play-state: running;
}
@keyframes NotificationFadeIn {
  0% {
    left: 384px;
    opacity: 0;
  }
  100% {
    left: 0;
    opacity: 1;
  }
}
@keyframes NotificationLeftFadeIn {
  0% {
    right: 384px;
    opacity: 0;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}
@keyframes NotificationFadeOut {
  0% {
    max-height: 150px;
    margin-bottom: 16px;
    opacity: 1;
  }
  100% {
    max-height: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
  }
}
.ant-notification-rtl {
  direction: rtl;
}
.ant-notification-rtl .ant-notification-notice-closable .ant-notification-notice-message {
  padding-right: 0;
  padding-left: 24px;
}
.ant-notification-rtl .ant-notification-notice-with-icon .ant-notification-notice-message {
  margin-right: 48px;
  margin-left: 0;
}
.ant-notification-rtl .ant-notification-notice-with-icon .ant-notification-notice-description {
  margin-right: 48px;
  margin-left: 0;
}
.ant-notification-rtl .ant-notification-notice-icon {
  margin-right: 4px;
  margin-left: 0;
}
.ant-notification-rtl .ant-notification-notice-close {
  right: auto;
  left: 22px;
}
.ant-notification-rtl .ant-notification-notice-btn {
  float: left;
}

.__dumi-default-search {
  margin-left: 20px;
  position: relative;
  display: inline-block;
}
.__dumi-default-search-input {
  width: 200px;
  height: 32px;
  padding: 0 38px 0 14px;
  color: #454d64;
  font-size: 14px;
  border: 0;
  outline: none;
  transition: all 0.2s;
  border-radius: 16px;
  background: url('data:image/png;base64,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') #f5f6f7 no-repeat right 14px center / 16px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
[data-prefers-color=dark] .__dumi-default-search-input {
  color: rgba(255, 255, 255, 0.65);
  background: url('data:image/png;base64,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') #ffffff1f no-repeat right 14px center / 16px;
}
.__dumi-default-search > ul {
  list-style: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  margin: 8px 0 0;
  min-width: 280px;
  max-width: 400px;
  max-height: 230px;
  padding: 6px 0;
  background-color: #fff;
  border: 1px solid #ebedf1;
  border-radius: 1px;
  box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  overflow-y: auto;
}
[data-prefers-color=dark] .__dumi-default-search > ul {
  border: none;
  background-color: #141414;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
}
.__dumi-default-search > ul:empty {
  display: none;
}
.__dumi-default-search > ul li {
  font-size: 15px;
}
.__dumi-default-search > ul li a {
  display: block;
  padding: 6px 20px;
  color: #717484;
  text-decoration: none;
  line-height: 1.5715;
  transition: background-color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
[data-prefers-color=dark] .__dumi-default-search > ul li a {
  color: rgba(255, 255, 255, 0.65);
}
.__dumi-default-search > ul li a:hover {
  color: #4569d4;
  background-color: #f9fafb;
}
[data-prefers-color=dark] .__dumi-default-search > ul li a:hover {
  color: #7395f7;
  background-color: rgba(255, 255, 255, 0.08);
}
.__dumi-default-search > ul li span:first-child:not(.__dumi-default-search-highlight) {
  position: relative;
  display: inline-block;
  max-width: 50%;
  padding-right: 26px;
  vertical-align: bottom;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 0.8;
}
.__dumi-default-search > ul li span:first-child:not(.__dumi-default-search-highlight)::after {
  content: '>';
  position: absolute;
  top: 50%;
  right: 6px;
  opacity: 0.6;
  transform: translateY(-54%);
}
.__dumi-default-search-empty {
  margin: 6px 0;
  opacity: 0.6;
  fill: #454d64;
}
[data-prefers-color=dark] .__dumi-default-search-empty {
  fill: rgba(255, 255, 255, 0.85);
}
.__dumi-default-search-highlight {
  padding: 2px 0;
  color: #736b2d;
  vertical-align: bottom;
  background: #fff9cb;
}
[data-prefers-color=dark] .__dumi-default-search-highlight {
  color: #454d64;
}
@media only screen and (max-width: 1024px) {
  .__dumi-default-search {
    margin-right: -14px;
  }
  .__dumi-default-search > input:not(:focus) {
    color: transparent;
    width: 32px;
    padding-right: 0;
    box-shadow: none;
    cursor: pointer;
    background-position: right 8px center;
  }
  .__dumi-default-search > input:not(:focus) + ul {
    transition: 0.1s visibility;
    visibility: hidden;
  }
}
@media only screen and (max-width: 767px) {
  .__dumi-default-search {
    position: absolute;
    top: 9px;
    right: 24px;
    display: block !important;
  }
  .__dumi-default-search > ul {
    right: 0;
    left: auto;
  }
}

.__dumi-default-dark {
  position: fixed;
  right: 20px;
  top: 16px;
}
[data-mode="doc"] .__dumi-default-dark {
  position: relative;
  top: 0;
  right: 0;
  display: inline-block;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-dark {
    position: relative;
    top: 6px;
    right: auto;
    display: flex;
    justify-content: center;
  }
  .__dumi-default-navbar .__dumi-default-dark {
    display: none;
  }
}
.__dumi-default-dark-sun,
.__dumi-default-dark-moon,
.__dumi-default-dark-auto {
  position: relative;
  border-radius: 50%;
  outline: none;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.__dumi-default-dark-sun svg,
.__dumi-default-dark-moon svg,
.__dumi-default-dark-auto svg {
  transition: all 0.3s linear;
}
.__dumi-default-dark-sun svg:hover,
.__dumi-default-dark-moon svg:hover,
.__dumi-default-dark-auto svg:hover {
  transform: scale(1.2);
}
.__dumi-default-dark-sun,
.__dumi-default-dark-moon,
.__dumi-default-dark-auto {
  border: 1px solid #dadadf;
  background-color: #f9fafb;
}
.__dumi-default-dark-sun svg,
.__dumi-default-dark-moon svg,
.__dumi-default-dark-auto svg {
  fill: #454d64;
}
[data-prefers-color=dark] .__dumi-default-dark-sun svg,
[data-prefers-color=dark] .__dumi-default-dark-moon svg,
[data-prefers-color=dark] .__dumi-default-dark-auto svg {
  fill: rgba(255, 255, 255, 0.85);
}
[data-prefers-color=dark] .__dumi-default-dark-sun,
[data-prefers-color=dark] .__dumi-default-dark-moon,
[data-prefers-color=dark] .__dumi-default-dark-auto {
  border-color: #464646;
  background-color: #141414;
}
[data-mode=doc] .__dumi-default-dark-switch {
  display: flex;
}
[data-mode=doc] .__dumi-default-dark-switch button + button {
  margin-left: 4px;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-dark-switch {
    display: flex;
  }
  .__dumi-default-dark-switch button + button {
    margin-left: 10px;
  }
}
.__dumi-default-menu .__dumi-default-dark-switch-active {
  border-color: #4569d4;
}
[data-prefers-color=dark] .__dumi-default-menu .__dumi-default-dark-switch-active {
  border-color: #7395f7;
}
[data-mode=doc][data-mobile-show=true] .__dumi-default-dark-switch {
  margin-bottom: 10px;
}
.__dumi-default-dark-switch button {
  z-index: 102;
}
.__dumi-default-dark-switch-list {
  animation: dropDown 0.2s linear 0s 1;
}
.__dumi-default-dark-switch-list button {
  z-index: 101;
  margin-top: 4px;
}
[data-mode="doc"] .__dumi-default-dark-switch-list {
  position: absolute;
}
@keyframes dropDown {
  0% {
    margin-top: -100%;
  }
  100% {
    margin-top: 0;
  }
}

.__dumi-default-menu {
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  bottom: 0;
  width: 260px;
  background-color: #f2f5fa;
  box-sizing: border-box;
  transition: left 0.3s;
}
.__dumi-default-menu[data-hidden] {
  display: none;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-menu {
    left: -240px;
    top: 50px;
    display: block !important;
    width: 240px;
    background-color: #fff;
  }
  .__dumi-default-menu[data-mobile-show] {
    left: 0;
  }
  [data-prefers-color=dark] .__dumi-default-menu {
    background-color: #141414;
  }
}
.__dumi-default-menu::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  display: block;
  width: 20px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.03));
  pointer-events: none;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-menu::after {
    width: 1px;
    background: #ebedf1;
  }
}
.__dumi-default-menu-header {
  position: relative;
  padding-top: 40px;
  text-align: center;
  border-bottom: 1px solid #ebedf1;
}
[data-prefers-color=dark] .__dumi-default-menu-header {
  border-color: #6b6c6d;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-menu-header {
    display: none;
  }
}
.__dumi-default-menu-header .__dumi-default-menu-logo {
  display: inline-block;
  width: 66px;
  height: 65px;
  background: url('data:image/png;base64,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') no-repeat 0 / contain;
}
.__dumi-default-menu-header h1 {
  margin: 10px 0 0;
  color: #454d64;
  font-weight: 500;
  line-height: 1.40625;
}
.__dumi-default-menu-header p {
  margin: 0 0 5px;
  color: #8c8e9c;
}
.__dumi-default-menu-header p > object[data^='https://img.shields.io'] {
  max-height: 20px;
}
.__dumi-default-menu-header p + p {
  margin-bottom: 10px;
}
.__dumi-default-menu-doc-locale {
  padding: 16px 0;
  text-align: center;
  border-bottom: 1px solid #ebedf1;
  display: flex;
  justify-content: space-evenly;
}
[data-prefers-color=dark] .__dumi-default-menu-doc-locale {
  border-color: #6b6c6d;
}
[data-mode=doc][data-mobile-show=true] .__dumi-default-menu-doc-locale {
  display: grid;
}
.__dumi-default-menu-doc-locale:empty {
  display: none;
}
.__dumi-default-menu-inner {
  width: 100%;
  height: 100%;
  overflow: auto;
  -ms-scroll-chaining: none;
      overscroll-behavior: contain;
}
[data-prefers-color=dark] .__dumi-default-menu-inner {
  background-color: #262626;
}
.__dumi-default-menu-inner ul {
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: 16px;
}
.__dumi-default-menu-inner ul li {
  color: #454d64;
}
.__dumi-default-menu-inner ul li a,
.__dumi-default-menu-inner ul li > span {
  position: relative;
  display: block;
  padding-right: 24px;
  color: #454d64;
  line-height: 2.4;
  text-decoration: none;
  outline: none;
  transition: color 0.3s, background 0.3s;
}
[data-prefers-color=dark] .__dumi-default-menu-inner ul li a,
[data-prefers-color=dark] .__dumi-default-menu-inner ul li > span {
  color: rgba(255, 255, 255, 0.85);
}
.__dumi-default-menu-inner ul li a span,
.__dumi-default-menu-inner ul li > span span {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.__dumi-default-menu-inner ul li a:hover,
.__dumi-default-menu-inner ul li > span:hover,
.__dumi-default-menu-inner ul li a.active,
.__dumi-default-menu-inner ul li > span.active {
  color: #4569d4;
}
[data-prefers-color=dark] .__dumi-default-menu-inner ul li a:hover,
[data-prefers-color=dark] .__dumi-default-menu-inner ul li > span:hover,
[data-prefers-color=dark] .__dumi-default-menu-inner ul li a.active,
[data-prefers-color=dark] .__dumi-default-menu-inner ul li > span.active {
  color: #7395f7;
}
.__dumi-default-menu-inner ul li a::before,
.__dumi-default-menu-inner ul li > span::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -10px;
  margin-top: -2.5px;
  display: inline-block;
  width: 5px;
  height: 5px;
  background-color: #4569d4;
  border-radius: 50%;
  opacity: 0;
  transition: transform 0.2s, opacity 0.2s;
  transform: scale(0) translateX(-10px);
}
.__dumi-default-menu-inner ul li.active a::before,
.__dumi-default-menu-inner ul li a.active::before {
  opacity: 1;
  transform: scale(1) translateX(0);
}
.__dumi-default-menu-inner ul li ul {
  font-size: 0.9em;
  padding-left: 1em;
}
.__dumi-default-menu-inner > ul > li > a {
  line-height: 2.875;
}
.__dumi-default-menu-inner > ul > li > a:not([href]) {
  padding-top: 24px;
  line-height: 1;
  font-weight: 500;
  color: #454d64 !important;
  background: transparent !important;
  cursor: default;
}
[data-prefers-color=dark] .__dumi-default-menu-inner > ul > li > a:not([href]) {
  color: rgba(255, 255, 255, 0.85) !important;
}
.__dumi-default-menu-inner > ul > li:first-child > a:not([href]) {
  padding-top: 0;
}
.__dumi-default-menu-inner > ul ul a {
  color: #717484;
}
[data-prefers-color=dark] .__dumi-default-menu-inner > ul ul a {
  color: rgba(255, 255, 255, 0.65);
}
.__dumi-default-menu-inner > ul ul a.active {
  color: #4569d4;
}
[data-prefers-color=dark] .__dumi-default-menu-inner > ul ul a.active {
  color: #7395f7;
}
.__dumi-default-menu-inner .__dumi-default-menu-mobile-area {
  display: none;
  padding-bottom: 16px;
  margin-bottom: 16px;
  text-align: center;
  border-bottom: 1px solid #ebedf1;
}
[data-prefers-color=dark] .__dumi-default-menu-inner .__dumi-default-menu-mobile-area {
  border-color: #6b6c6d;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-menu-inner .__dumi-default-menu-mobile-area {
    display: block;
  }
}
.__dumi-default-menu-inner .__dumi-default-menu-nav-list {
  padding: 16px 0 0 0;
}
.__dumi-default-menu-inner .__dumi-default-menu-nav-list > li,
.__dumi-default-menu-inner .__dumi-default-menu-nav-list > li > a {
  padding-right: 0;
  line-height: 2.4;
}
.__dumi-default-menu-inner .__dumi-default-menu-nav-list > li ul,
.__dumi-default-menu-inner .__dumi-default-menu-nav-list > li > a ul {
  padding-left: 0;
}
.__dumi-default-menu-inner .__dumi-default-menu-nav-list > li ul a,
.__dumi-default-menu-inner .__dumi-default-menu-nav-list > li > a ul a {
  padding-right: 0;
  font-size: 90%;
}
.__dumi-default-menu-inner .__dumi-default-menu-list {
  padding: 8px 0;
  margin-bottom: 40px;
}
.__dumi-default-menu-inner .__dumi-default-menu-list > li > a {
  padding-left: 28px;
}
.__dumi-default-menu-inner .__dumi-default-menu-list > li > a.active {
  background: linear-gradient(to left, #e8ecf4, rgba(232, 236, 244, 0));
}
[data-prefers-color=dark] .__dumi-default-menu-inner .__dumi-default-menu-list > li > a.active {
  background: linear-gradient(to left, #3d3d3e, rgba(255, 255, 255, 0.06));
}
.__dumi-default-menu-inner .__dumi-default-menu-list > li > a ~ ul {
  margin-top: 8px;
  margin-left: 28px;
}
@media only screen and (max-width: 767px) {
  .__dumi-default-menu-inner .__dumi-default-menu-list > li > a {
    padding-left: 16px;
  }
  .__dumi-default-menu-inner .__dumi-default-menu-list > li > a ~ ul {
    margin-left: 16px;
  }
}
.__dumi-default-menu[data-mode='site']::after {
  width: 1px;
  background: #ebedf1;
}
[data-prefers-color=dark] .__dumi-default-menu[data-mode='site']::after {
  background: #6b6c6d;
}
.__dumi-default-menu[data-mode='site'] .__dumi-default-menu-list {
  padding: 0;
}
.__dumi-default-menu[data-mode='site'] .__dumi-default-menu-list > li > a {
  position: relative;
}
.__dumi-default-menu[data-mode='site'] .__dumi-default-menu-list > li > a::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  display: block;
  width: 3px;
  background-color: #4569d4;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
  border-radius: 1px;
}
.__dumi-default-menu[data-mode='site'] .__dumi-default-menu-list > li > a.active {
  z-index: 1;
  background: linear-gradient(to left, #f8faff, rgba(248, 250, 255, 0));
}
[data-prefers-color=dark] .__dumi-default-menu[data-mode='site'] .__dumi-default-menu-list > li > a.active {
  background: linear-gradient(to left, #3d3d3e, rgba(255, 255, 255, 0.06));
}
.__dumi-default-menu[data-mode='site'] .__dumi-default-menu-list > li > a.active::after {
  opacity: 1;
  visibility: visible;
}
@media only screen and (min-width: 768px) {
  .__dumi-default-menu[data-mode='site'] {
    top: 64px;
    width: 300px;
    padding-top: 50px;
    background: transparent;
  }
  [data-prefers-color=dark] .__dumi-default-menu[data-mode='site'] {
    background: rgba(255, 255, 255, 0.08);
  }
  .__dumi-default-menu[data-mode='site'] .__dumi-default-menu-nav,
  .__dumi-default-menu[data-mode='site'] .__dumi-default-menu-header {
    display: none;
  }
  .__dumi-default-menu[data-mode='site'] .__dumi-default-menu-list > li > a {
    padding-left: 58px;
  }
  .__dumi-default-menu[data-mode='site'] .__dumi-default-menu-list > li > a ~ ul {
    margin-left: 58px;
  }
}

ul[role='slug-list']:empty {
  margin: 0 !important;
  padding: 0 !important;
}
ul[role='slug-list'] li > a.active {
  color: #3d62d2;
}
ul[role='slug-list'] li[data-depth='3'] {
  padding-left: 12px;
}

