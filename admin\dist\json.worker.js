var xl=Object.defineProperty,er=(rt,Te,ce)=>(typeof Te!="symbol"&&(Te+=""),Te in rt?xl(rt,Te,{enumerable:!0,configurable:!0,writable:!0,value:ce}):rt[Te]=ce),ke=(rt,Te,ce)=>new Promise((jr,Le)=>{var H=de=>{try{le(ce.next(de))}catch(Se){Le(Se)}},ne=de=>{try{le(ce.throw(de))}catch(Se){Le(Se)}},le=de=>de.done?jr(de.value):Promise.resolve(de.value).then(H,ne);le((ce=ce.apply(rt,Te)).next())});(function(){var rt={155:function(Le){var H=Le.exports={},ne,le;function de(){throw new Error("setTimeout has not been defined")}function Se(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?ne=setTimeout:ne=de}catch(Z){ne=de}try{typeof clearTimeout=="function"?le=clearTimeout:le=Se}catch(Z){le=Se}})();function Et(Z){if(ne===setTimeout)return setTimeout(Z,0);if((ne===de||!ne)&&setTimeout)return ne=setTimeout,setTimeout(Z,0);try{return ne(Z,0)}catch(ae){try{return ne.call(null,Z,0)}catch(Ae){return ne.call(this,Z,0)}}}function On(Z){if(le===clearTimeout)return clearTimeout(Z);if((le===Se||!le)&&clearTimeout)return le=clearTimeout,clearTimeout(Z);try{return le(Z)}catch(ae){try{return le.call(null,Z)}catch(Ae){return le.call(this,Z)}}}var Ie=[],it=!1,We,Lt=-1;function Mn(){!it||!We||(it=!1,We.length?Ie=We.concat(Ie):Lt=-1,Ie.length&&ft())}function ft(){if(!it){var Z=Et(Mn);it=!0;for(var ae=Ie.length;ae;){for(We=Ie,Ie=[];++Lt<ae;)We&&We[Lt].run();Lt=-1,ae=Ie.length}We=null,it=!1,On(Z)}}H.nextTick=function(Z){var ae=new Array(arguments.length-1);if(arguments.length>1)for(var Ae=1;Ae<arguments.length;Ae++)ae[Ae-1]=arguments[Ae];Ie.push(new en(Z,ae)),Ie.length===1&&!it&&Et(ft)};function en(Z,ae){this.fun=Z,this.array=ae}en.prototype.run=function(){this.fun.apply(null,this.array)},H.title="browser",H.browser=!0,H.env={},H.argv=[],H.version="",H.versions={};function Y(){}H.on=Y,H.addListener=Y,H.once=Y,H.off=Y,H.removeListener=Y,H.removeAllListeners=Y,H.emit=Y,H.prependListener=Y,H.prependOnceListener=Y,H.listeners=function(Z){return[]},H.binding=function(Z){throw new Error("process.binding is not supported")},H.cwd=function(){return"/"},H.chdir=function(Z){throw new Error("process.chdir is not supported")},H.umask=function(){return 0}}},Te={};function ce(Le){var H=Te[Le];if(H!==void 0)return H.exports;var ne=Te[Le]={exports:{}};return rt[Le](ne,ne.exports,ce),ne.exports}(function(){ce.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch(Le){if(typeof window=="object")return window}}()})();var jr={};(function(){"use strict";class Le{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(t){setTimeout(()=>{throw t.stack?new Error(t.message+`

`+t.stack):t},0)}}emit(t){this.listeners.forEach(n=>{n(t)})}onUnexpectedError(t){this.unexpectedErrorHandler(t),this.emit(t)}onUnexpectedExternalError(t){this.unexpectedErrorHandler(t)}}const H=new Le;function ne(e){Et(e)||H.onUnexpectedError(e)}function le(e){Et(e)||H.onUnexpectedExternalError(e)}function de(e){if(e instanceof Error){let{name:t,message:n}=e;const r=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:r}}return e}const Se="Canceled";function Et(e){return e instanceof On?!0:e instanceof Error&&e.name===Se&&e.message===Se}class On extends Error{constructor(){super(Se);this.name=this.message}}function Ie(){const e=new Error(Se);return e.name=e.message,e}function it(e){return e?new Error(`Illegal argument: ${e}`):new Error("Illegal argument")}function We(e){return e?new Error(`Illegal state: ${e}`):new Error("Illegal state")}class Lt extends Error{constructor(t){super("NotSupported");t&&(this.message=t)}}function Mn(e){const t=this;let n=!1,r;return function(){return n||(n=!0,r=e.apply(t,arguments)),r}}var ft;(function(e){function t(b){return b&&typeof b=="object"&&typeof b[Symbol.iterator]=="function"}e.is=t;const n=Object.freeze([]);function r(){return n}e.empty=r;function*i(b){yield b}e.single=i;function a(b){return b||n}e.from=a;function s(b){return!b||b[Symbol.iterator]().next().done===!0}e.isEmpty=s;function u(b){return b[Symbol.iterator]().next().value}e.first=u;function c(b,v){for(const L of b)if(v(L))return!0;return!1}e.some=c;function l(b,v){for(const L of b)if(v(L))return L}e.find=l;function*f(b,v){for(const L of b)v(L)&&(yield L)}e.filter=f;function*h(b,v){let L=0;for(const x of b)yield v(x,L++)}e.map=h;function*d(...b){for(const v of b)for(const L of v)yield L}e.concat=d;function*m(b){for(const v of b)for(const L of v)yield L}e.concatNested=m;function p(b,v,L){let x=L;for(const A of b)x=v(x,A);return x}e.reduce=p;function*y(b,v,L=b.length){for(v<0&&(v+=b.length),L<0?L+=b.length:L>b.length&&(L=b.length);v<L;v++)yield b[v]}e.slice=y;function g(b,v=Number.POSITIVE_INFINITY){const L=[];if(v===0)return[L,b];const x=b[Symbol.iterator]();for(let A=0;A<v;A++){const C=x.next();if(C.done)return[L,e.empty()];L.push(C.value)}return[L,{[Symbol.iterator](){return x}}]}e.consume=g;function w(b,v,L=(x,A)=>x===A){const x=b[Symbol.iterator](),A=v[Symbol.iterator]();for(;;){const C=x.next(),_=A.next();if(C.done!==_.done)return!1;if(C.done)return!0;if(!L(C.value,_.value))return!1}}e.equals=w})(ft||(ft={}));const en=!1;let Y=null;function Z(e){Y=e}if(en){const e="__is_disposable_tracked__";Z(new class{trackDisposable(t){const n=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(n)},3e3)}setParent(t,n){if(t&&t!==Nt.None)try{t[e]=!0}catch(r){}}markAsDisposed(t){if(t&&t!==Nt.None)try{t[e]=!0}catch(n){}}markAsSingleton(t){}})}function ae(e){return Y==null||Y.trackDisposable(e),e}function Ae(e){Y==null||Y.markAsDisposed(e)}function tn(e,t){Y==null||Y.setParent(e,t)}function L1(e,t){if(Y)for(const n of e)Y.setParent(n,t)}function Nu(e){return Y==null||Y.markAsSingleton(e),e}class N1 extends Error{constructor(t){super(`Encountered errors while disposing of store. Errors: [${t.join(", ")}]`);this.errors=t}}function ku(e){return typeof e.dispose=="function"&&e.dispose.length===0}function Br(e){if(ft.is(e)){let t=[];for(const n of e)if(n)try{n.dispose()}catch(r){t.push(r)}if(t.length===1)throw t[0];if(t.length>1)throw new N1(t);return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function k1(...e){const t=nn(()=>Br(e));return L1(e,t),t}function nn(e){const t=ae({dispose:Mn(()=>{Ae(t),e()})});return t}class dt{constructor(){this._toDispose=new Set,this._isDisposed=!1,ae(this)}dispose(){this._isDisposed||(Ae(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){try{Br(this._toDispose.values())}finally{this._toDispose.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return tn(t,this),this._isDisposed?dt.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(t),t}}dt.DISABLE_DISPOSED_WARNING=!1;class Nt{constructor(){this._store=new dt,ae(this),tn(this._store,this)}dispose(){Ae(this),this._store.dispose()}_register(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(t)}}Nt.None=Object.freeze({dispose(){}});class Ou{constructor(){this._isDisposed=!1,ae(this)}get value(){return this._isDisposed?void 0:this._value}set value(t){var n;this._isDisposed||t===this._value||((n=this._value)===null||n===void 0||n.dispose(),t&&tn(t,this),this._value=t)}clear(){this.value=void 0}dispose(){var t;this._isDisposed=!0,Ae(this),(t=this._value)===null||t===void 0||t.dispose(),this._value=void 0}clearAndLeak(){const t=this._value;return this._value=void 0,t&&tn(t,null),t}}class O1{constructor(){this.dispose=()=>{},this.unset=()=>{},this.isset=()=>!1,ae(this)}set(t){let n=t;return this.unset=()=>n=void 0,this.isset=()=>n!==void 0,this.dispose=()=>{n&&(n(),n=void 0,Ae(this))},this}}class Mu{constructor(t){this.object=t}dispose(){}}class z{constructor(t){this.element=t,this.next=z.Undefined,this.prev=z.Undefined}}z.Undefined=new z(void 0);class Ur{constructor(){this._first=z.Undefined,this._last=z.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===z.Undefined}clear(){let t=this._first;for(;t!==z.Undefined;){const n=t.next;t.prev=z.Undefined,t.next=z.Undefined,t=n}this._first=z.Undefined,this._last=z.Undefined,this._size=0}unshift(t){return this._insert(t,!1)}push(t){return this._insert(t,!0)}_insert(t,n){const r=new z(t);if(this._first===z.Undefined)this._first=r,this._last=r;else if(n){const a=this._last;this._last=r,r.prev=a,a.next=r}else{const a=this._first;this._first=r,r.next=a,a.prev=r}this._size+=1;let i=!1;return()=>{i||(i=!0,this._remove(r))}}shift(){if(this._first!==z.Undefined){const t=this._first.element;return this._remove(this._first),t}}pop(){if(this._last!==z.Undefined){const t=this._last.element;return this._remove(this._last),t}}_remove(t){if(t.prev!==z.Undefined&&t.next!==z.Undefined){const n=t.prev;n.next=t.next,t.next.prev=n}else t.prev===z.Undefined&&t.next===z.Undefined?(this._first=z.Undefined,this._last=z.Undefined):t.next===z.Undefined?(this._last=this._last.prev,this._last.next=z.Undefined):t.prev===z.Undefined&&(this._first=this._first.next,this._first.prev=z.Undefined);this._size-=1}*[Symbol.iterator](){let t=this._first;for(;t!==z.Undefined;)yield t.element,t=t.next}}var $r=ce(155),Tn;const In="en";let kt=!1,Ot=!1,rn=!1,M1=!1,T1=!1,qr=!1,I1=!1,Wr=!1,P1=!1,on,Pn=null,F1,He;const ie=typeof self=="object"?self:typeof ce.g=="object"?ce.g:{};let he;typeof ie.vscode!="undefined"&&typeof ie.vscode.process!="undefined"?he=ie.vscode.process:typeof $r!="undefined"&&(he=$r);const Hr=typeof((Tn=he==null?void 0:he.versions)===null||Tn===void 0?void 0:Tn.electron)=="string",R1=Hr&&(he==null?void 0:he.type)==="renderer";if(typeof navigator=="object"&&!R1)He=navigator.userAgent,kt=He.indexOf("Windows")>=0,Ot=He.indexOf("Macintosh")>=0,Wr=(He.indexOf("Macintosh")>=0||He.indexOf("iPad")>=0||He.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,rn=He.indexOf("Linux")>=0,qr=!0,on=navigator.language,Pn=on;else if(typeof he=="object"){kt=he.platform==="win32",Ot=he.platform==="darwin",rn=he.platform==="linux",M1=rn&&!!he.env.SNAP&&!!he.env.SNAP_REVISION,I1=Hr,P1=!!he.env.CI||!!he.env.BUILD_ARTIFACTSTAGINGDIRECTORY,on=In,Pn=In;const e=he.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e),n=t.availableLanguages["*"];on=t.locale,Pn=n||In,F1=t._translationsConfigFile}catch(t){}T1=!0}else console.error("Unable to resolve platform.");let Fn=0;Ot?Fn=1:kt?Fn=3:rn&&(Fn=2);const Mt=kt,V1=Ot,Tu=null,Iu=null,Pu=null,Fu=qr&&typeof ie.importScripts=="function",Ru=null,Pe=He,Vu=null,Du=(()=>{if(typeof ie.postMessage=="function"&&!ie.importScripts){let e=[];ie.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let r=0,i=e.length;r<i;r++){const a=e[r];if(a.id===n.data.vscodeScheduleAsyncWork){e.splice(r,1),a.callback();return}}});let t=0;return n=>{const r=++t;e.push({id:r,callback:n}),ie.postMessage({vscodeScheduleAsyncWork:r},"*")}}return e=>setTimeout(e)})(),Ku=Ot||Wr?2:kt?1:3;let zr=!0,Gr=!1;function ju(){if(!Gr){Gr=!0;const e=new Uint8Array(2);e[0]=1,e[1]=2,zr=new Uint16Array(e.buffer)[0]===(2<<8)+1}return zr}const D1=!!(Pe&&Pe.indexOf("Chrome")>=0),Bu=!!(Pe&&Pe.indexOf("Firefox")>=0),Uu=!!(!D1&&Pe&&Pe.indexOf("Safari")>=0),$u=!!(Pe&&Pe.indexOf("Edg/")>=0),qu=!!(Pe&&Pe.indexOf("Android")>=0),K1=ie.performance&&typeof ie.performance.now=="function";class an{constructor(t){this._highResolution=K1&&t,this._startTime=this._now(),this._stopTime=-1}static create(t=!0){return new an(t)}stop(){this._stopTime=this._now()}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}_now(){return this._highResolution?ie.performance.now():Date.now()}}let Jr=!1,j1=!1;var sn;(function(e){e.None=()=>Nt.None;function t(x){if(j1){const{onListenerDidAdd:A}=x,C=Tt.create();let _=0;x.onListenerDidAdd=()=>{++_==2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),C.print()),A==null||A()}}}function n(x){return(A,C=null,_)=>{let S=!1,k;return k=x(E=>{if(!S)return k?k.dispose():S=!0,A.call(C,E)},null,_),S&&k.dispose(),k}}e.once=n;function r(x,A,C){return l((_,S=null,k)=>x(E=>_.call(S,A(E)),null,k),C)}e.map=r;function i(x,A,C){return l((_,S=null,k)=>x(E=>{A(E),_.call(S,E)},null,k),C)}e.forEach=i;function a(x,A,C){return l((_,S=null,k)=>x(E=>A(E)&&_.call(S,E),null,k),C)}e.filter=a;function s(x){return x}e.signal=s;function u(...x){return(A,C=null,_)=>k1(...x.map(S=>S(k=>A.call(C,k),null,_)))}e.any=u;function c(x,A,C,_){let S=C;return r(x,k=>(S=A(S,k),S),_)}e.reduce=c;function l(x,A){let C;const _={onFirstListenerAdd(){C=x(S.fire,S)},onLastListenerRemove(){C.dispose()}};A||t(_);const S=new Fe(_);return A&&A.add(S),S.event}function f(x,A,C=100,_=!1,S,k){let E,T,R,j=0;const te={leakWarningThreshold:S,onFirstListenerAdd(){E=x(M=>{j++,T=A(T,M),_&&!R&&(q.fire(T),T=void 0),clearTimeout(R),R=setTimeout(()=>{const O=T;T=void 0,R=void 0,(!_||j>1)&&q.fire(O),j=0},C)})},onLastListenerRemove(){E.dispose()}};k||t(te);const q=new Fe(te);return k&&k.add(q),q.event}e.debounce=f;function h(x,A=(_,S)=>_===S,C){let _=!0,S;return a(x,k=>{const E=_||!A(k,S);return _=!1,S=k,E},C)}e.latch=h;function d(x,A,C){return[e.filter(x,A,C),e.filter(x,_=>!A(_),C)]}e.split=d;function m(x,A=!1,C=[]){let _=C.slice(),S=x(T=>{_?_.push(T):E.fire(T)});const k=()=>{_&&_.forEach(T=>E.fire(T)),_=null},E=new Fe({onFirstListenerAdd(){S||(S=x(T=>E.fire(T)))},onFirstListenerDidAdd(){_&&(A?setTimeout(k):k())},onLastListenerRemove(){S&&S.dispose(),S=null}});return E.event}e.buffer=m;class p{constructor(A){this.event=A}map(A){return new p(r(this.event,A))}forEach(A){return new p(i(this.event,A))}filter(A){return new p(a(this.event,A))}reduce(A,C){return new p(c(this.event,A,C))}latch(){return new p(h(this.event))}debounce(A,C=100,_=!1,S){return new p(f(this.event,A,C,_,S))}on(A,C,_){return this.event(A,C,_)}once(A,C,_){return n(this.event)(A,C,_)}}function y(x){return new p(x)}e.chain=y;function g(x,A,C=_=>_){const _=(...T)=>E.fire(C(...T)),S=()=>x.on(A,_),k=()=>x.removeListener(A,_),E=new Fe({onFirstListenerAdd:S,onLastListenerRemove:k});return E.event}e.fromNodeEventEmitter=g;function w(x,A,C=_=>_){const _=(...T)=>E.fire(C(...T)),S=()=>x.addEventListener(A,_),k=()=>x.removeEventListener(A,_),E=new Fe({onFirstListenerAdd:S,onLastListenerRemove:k});return E.event}e.fromDOMEventEmitter=w;function b(x){return new Promise(A=>n(x)(A))}e.toPromise=b;function v(x,A){return A(void 0),x(C=>A(C))}e.runAndSubscribe=v;function L(x,A){let C=null;function _(k){C==null||C.dispose(),C=new dt,A(k,C)}_(void 0);const S=x(k=>_(k));return nn(()=>{S.dispose(),C==null||C.dispose()})}e.runAndSubscribeWithStore=L})(sn||(sn={}));class un{constructor(t){this._listenerCount=0,this._invocationCount=0,this._elapsedOverall=0,this._name=`${t}_${un._idPool++}`}start(t){this._stopWatch=new an(!0),this._listenerCount=t}stop(){if(this._stopWatch){const t=this._stopWatch.elapsed();this._elapsedOverall+=t,this._invocationCount+=1,console.info(`did FIRE ${this._name}: elapsed_ms: ${t.toFixed(5)}, listener: ${this._listenerCount} (elapsed_overall: ${this._elapsedOverall.toFixed(2)}, invocations: ${this._invocationCount})`),this._stopWatch=void 0}}}un._idPool=0;let Zr=-1;class B1{constructor(t,n=Math.random().toString(18).slice(2,5)){this.customThreshold=t,this.name=n,this._warnCountdown=0}dispose(){this._stacks&&this._stacks.clear()}check(t,n){let r=Zr;if(typeof this.customThreshold=="number"&&(r=this.customThreshold),r<=0||n<r)return;this._stacks||(this._stacks=new Map);const i=this._stacks.get(t.value)||0;if(this._stacks.set(t.value,i+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=r*.5;let a,s=0;for(const[u,c]of this._stacks)(!a||s<c)&&(a=u,s=c);console.warn(`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${s}):`),console.warn(a)}return()=>{const a=this._stacks.get(t.value)||0;this._stacks.set(t.value,a-1)}}}class Tt{constructor(t){this.value=t}static create(){var t;return new Tt((t=new Error().stack)!==null&&t!==void 0?t:"")}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}}class U1{constructor(t,n,r){this.callback=t,this.callbackThis=n,this.stack=r,this.subscription=new O1}invoke(t){this.callback.call(this.callbackThis,t)}}class Fe{constructor(t){var n;this._disposed=!1,this._options=t,this._leakageMon=Zr>0?new B1(this._options&&this._options.leakWarningThreshold):void 0,this._perfMon=((n=this._options)===null||n===void 0?void 0:n._profName)?new un(this._options._profName):void 0}dispose(){var t,n,r,i;if(!this._disposed){if(this._disposed=!0,this._listeners){if(Jr){const a=Array.from(this._listeners);queueMicrotask(()=>{var s;for(const u of a)u.subscription.isset()&&(u.subscription.unset(),(s=u.stack)===null||s===void 0||s.print())})}this._listeners.clear()}(t=this._deliveryQueue)===null||t===void 0||t.clear(),(r=(n=this._options)===null||n===void 0?void 0:n.onLastListenerRemove)===null||r===void 0||r.call(n),(i=this._leakageMon)===null||i===void 0||i.dispose()}}get event(){return this._event||(this._event=(t,n,r)=>{var i,a,s;this._listeners||(this._listeners=new Ur);const u=this._listeners.isEmpty();u&&((i=this._options)===null||i===void 0?void 0:i.onFirstListenerAdd)&&this._options.onFirstListenerAdd(this);let c,l;this._leakageMon&&this._listeners.size>=30&&(l=Tt.create(),c=this._leakageMon.check(l,this._listeners.size+1)),Jr&&(l=l!=null?l:Tt.create());const f=new U1(t,n,l),h=this._listeners.push(f);u&&((a=this._options)===null||a===void 0?void 0:a.onFirstListenerDidAdd)&&this._options.onFirstListenerDidAdd(this),((s=this._options)===null||s===void 0?void 0:s.onListenerDidAdd)&&this._options.onListenerDidAdd(this,t,n);const d=f.subscription.set(()=>{c&&c(),this._disposed||(h(),this._options&&this._options.onLastListenerRemove&&(this._listeners&&!this._listeners.isEmpty()||this._options.onLastListenerRemove(this)))});return r instanceof dt?r.add(d):Array.isArray(r)&&r.push(d),d}),this._event}fire(t){var n,r;if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new Ur);for(let i of this._listeners)this._deliveryQueue.push([i,t]);for((n=this._perfMon)===null||n===void 0||n.start(this._deliveryQueue.size);this._deliveryQueue.size>0;){const[i,a]=this._deliveryQueue.shift();try{i.invoke(a)}catch(s){ne(s)}}(r=this._perfMon)===null||r===void 0||r.stop()}}}class Wu extends null{constructor(t){super(t);this._isPaused=0,this._eventQueue=new LinkedList,this._mergeFn=t==null?void 0:t.merge}pause(){this._isPaused++}resume(){if(this._isPaused!==0&&--this._isPaused==0)if(this._mergeFn){const t=Array.from(this._eventQueue);this._eventQueue.clear(),super.fire(this._mergeFn(t))}else for(;!this._isPaused&&this._eventQueue.size!==0;)super.fire(this._eventQueue.shift())}fire(t){this._listeners&&(this._isPaused!==0?this._eventQueue.push(t):super.fire(t))}}class Hu extends null{constructor(t){var n;super(t);this._delay=(n=t.delay)!==null&&n!==void 0?n:100}fire(t){this._handle||(this.pause(),this._handle=setTimeout(()=>{this._handle=void 0,this.resume()},this._delay)),super.fire(t)}}class zu{constructor(){this.buffers=[]}wrapEvent(t){return(n,r,i)=>t(a=>{const s=this.buffers[this.buffers.length-1];s?s.push(()=>n.call(r,a)):n.call(r,a)},void 0,i)}bufferEvents(t){const n=[];this.buffers.push(n);const r=t();return this.buffers.pop(),n.forEach(i=>i()),r}}class Gu{constructor(){this.listening=!1,this.inputEvent=sn.None,this.inputEventListener=Disposable.None,this.emitter=new Fe({onFirstListenerDidAdd:()=>{this.listening=!0,this.inputEventListener=this.inputEvent(this.emitter.fire,this.emitter)},onLastListenerRemove:()=>{this.listening=!1,this.inputEventListener.dispose()}}),this.event=this.emitter.event}set input(t){this.inputEvent=t,this.listening&&(this.inputEventListener.dispose(),this.inputEventListener=t(this.emitter.fire,this.emitter))}dispose(){this.inputEventListener.dispose(),this.emitter.dispose()}}function Ju(e){return Array.isArray(e)}function $1(e){return typeof e=="string"}function Zu(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)&&!(e instanceof RegExp)&&!(e instanceof Date)}function Qu(e){return typeof e=="number"&&!isNaN(e)}function Yu(e){return!!e&&typeof e[Symbol.iterator]=="function"}function Xu(e){return e===!0||e===!1}function q1(e){return typeof e=="undefined"}function ec(e){return!Rn(e)}function Rn(e){return q1(e)||e===null}function tc(e,t){if(!e)throw new Error(t?`Unexpected type, expected '${t}'`:"Unexpected type")}function nc(e){if(Rn(e))throw new Error("Assertion Failed: argument is undefined or null");return e}function W1(e){return typeof e=="function"}function rc(e,t){const n=Math.min(e.length,t.length);for(let r=0;r<n;r++)H1(e[r],t[r])}function H1(e,t){if($1(t)){if(typeof e!==t)throw new Error(`argument does not match constraint: typeof ${t}`)}else if(W1(t)){try{if(e instanceof t)return}catch(n){}if(!Rn(e)&&e.constructor===t||t.length===1&&t.call(void 0,e)===!0)return;throw new Error("argument does not match one of these constraints: arg instanceof constraint, arg.constructor === constraint, nor constraint(arg) === true")}}function z1(e){let t=[],n=Object.getPrototypeOf(e);for(;Object.prototype!==n;)t=t.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return t}function Vn(e){const t=[];for(const n of z1(e))typeof e[n]=="function"&&t.push(n);return t}function G1(e,t){const n=i=>function(){const a=Array.prototype.slice.call(arguments,0);return t(i,a)};let r={};for(const i of e)r[i]=n(i);return r}function ic(e){return e===null?void 0:e}function J1(e,t="Unreachable"){throw new Error(t)}class Z1{constructor(t){this.computeFn=t,this.lastCache=void 0,this.lastArgKey=void 0}get(t){const n=JSON.stringify(t);return this.lastArgKey!==n&&(this.lastArgKey=n,this.lastCache=this.computeFn(t)),this.lastCache}}class Qr{constructor(t){this.executor=t,this._didRun=!1}getValue(){if(!this._didRun)try{this._value=this.executor()}catch(t){this._error=t}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}var Yr;function oc(e){return!e||typeof e!="string"?!0:e.trim().length===0}const Q1=/{(\d+)}/g;function ac(e,...t){return t.length===0?e:e.replace(Q1,function(n,r){const i=parseInt(r,10);return isNaN(i)||i<0||i>=t.length?n:t[i]})}function sc(e){return e.replace(/[<>&]/g,function(t){switch(t){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return t}})}function Xr(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function uc(e,t=" "){const n=Y1(e,t);return X1(n,t)}function Y1(e,t){if(!e||!t)return e;const n=t.length;if(n===0||e.length===0)return e;let r=0;for(;e.indexOf(t,r)===r;)r=r+n;return e.substring(r)}function X1(e,t){if(!e||!t)return e;const n=t.length,r=e.length;if(n===0||r===0)return e;let i=r,a=-1;for(;a=e.lastIndexOf(t,i-1),!(a===-1||a+n!==i);){if(a===0)return"";i=a}return e.substring(0,i)}function cc(e){return e.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")}function lc(e){return e.replace(/\*/g,"")}function hc(e,t,n={}){if(!e)throw new Error("Cannot create regex from empty string");t||(e=Xr(e)),n.wholeWord&&(/\B/.test(e.charAt(0))||(e="\\b"+e),/\B/.test(e.charAt(e.length-1))||(e=e+"\\b"));let r="";return n.global&&(r+="g"),n.matchCase||(r+="i"),n.multiline&&(r+="m"),n.unicode&&(r+="u"),new RegExp(e,r)}function fc(e){return e.source==="^"||e.source==="^$"||e.source==="$"||e.source==="^\\s*$"?!1:!!(e.exec("")&&e.lastIndex===0)}function dc(e){return(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")}function ea(e){return e.split(/\r\n|\r|\n/)}function ta(e){for(let t=0,n=e.length;t<n;t++){const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}return-1}function mc(e,t=0,n=e.length){for(let r=t;r<n;r++){const i=e.charCodeAt(r);if(i!==32&&i!==9)return e.substring(t,r)}return e.substring(t,n)}function na(e,t=e.length-1){for(let n=t;n>=0;n--){const r=e.charCodeAt(n);if(r!==32&&r!==9)return n}return-1}function gc(e,t){return e<t?-1:e>t?1:0}function ra(e,t,n=0,r=e.length,i=0,a=t.length){for(;n<r&&i<a;n++,i++){let c=e.charCodeAt(n),l=t.charCodeAt(i);if(c<l)return-1;if(c>l)return 1}const s=r-n,u=a-i;return s<u?-1:s>u?1:0}function pc(e,t){return Dn(e,t,0,e.length,0,t.length)}function Dn(e,t,n=0,r=e.length,i=0,a=t.length){for(;n<r&&i<a;n++,i++){let c=e.charCodeAt(n),l=t.charCodeAt(i);if(c===l)continue;if(c>=128||l>=128)return ra(e.toLowerCase(),t.toLowerCase(),n,r,i,a);ei(c)&&(c-=32),ei(l)&&(l-=32);const f=c-l;if(f!==0)return f}const s=r-n,u=a-i;return s<u?-1:s>u?1:0}function ei(e){return e>=97&&e<=122}function ti(e){return e>=65&&e<=90}function bc(e,t){return e.length===t.length&&Dn(e,t)===0}function vc(e,t){const n=t.length;return t.length>e.length?!1:Dn(e,t,0,n)===0}function yc(e,t){let n,r=Math.min(e.length,t.length);for(n=0;n<r;n++)if(e.charCodeAt(n)!==t.charCodeAt(n))return n;return r}function Cc(e,t){let n,r=Math.min(e.length,t.length);const i=e.length-1,a=t.length-1;for(n=0;n<r;n++)if(e.charCodeAt(i-n)!==t.charCodeAt(a-n))return n;return r}function It(e){return 55296<=e&&e<=56319}function Pt(e){return 56320<=e&&e<=57343}function Kn(e,t){return(e-55296<<10)+(t-56320)+65536}function ni(e,t,n){const r=e.charCodeAt(n);if(It(r)&&n+1<t){const i=e.charCodeAt(n+1);if(Pt(i))return Kn(r,i)}return r}function ia(e,t){const n=e.charCodeAt(t-1);if(Pt(n)&&t>1){const r=e.charCodeAt(t-2);if(It(r))return Kn(r,n)}return n}class jn{constructor(t,n=0){this._str=t,this._len=t.length,this._offset=n}get offset(){return this._offset}setOffset(t){this._offset=t}prevCodePoint(){const t=ia(this._str,this._offset);return this._offset-=t>=65536?2:1,t}nextCodePoint(){const t=ni(this._str,this._len,this._offset);return this._offset+=t>=65536?2:1,t}eol(){return this._offset>=this._len}}class ri{constructor(t,n=0){this._iterator=new jn(t,n)}get offset(){return this._iterator.offset}nextGraphemeLength(){const t=ze.getInstance(),n=this._iterator,r=n.offset;let i=t.getGraphemeBreakType(n.nextCodePoint());for(;!n.eol();){const a=n.offset,s=t.getGraphemeBreakType(n.nextCodePoint());if(ii(i,s)){n.setOffset(a);break}i=s}return n.offset-r}prevGraphemeLength(){const t=ze.getInstance(),n=this._iterator,r=n.offset;let i=t.getGraphemeBreakType(n.prevCodePoint());for(;n.offset>0;){const a=n.offset,s=t.getGraphemeBreakType(n.prevCodePoint());if(ii(s,i)){n.setOffset(a);break}i=s}return r-n.offset}eol(){return this._iterator.eol()}}function oa(e,t){return new ri(e,t).nextGraphemeLength()}function aa(e,t){return new ri(e,t).prevGraphemeLength()}function wc(e,t){t>0&&Pt(e.charCodeAt(t))&&t--;const n=t+oa(e,t);return[n-aa(e,n),n]}const sa=/(?:[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u0710\u0712-\u072F\u074D-\u07A5\u07B1-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u0858\u085E-\u088E\u08A0-\u08C9\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFD3D\uFD50-\uFDC7\uFDF0-\uFDFC\uFE70-\uFEFC]|\uD802[\uDC00-\uDD1B\uDD20-\uDE00\uDE10-\uDE35\uDE40-\uDEE4\uDEEB-\uDF35\uDF40-\uDFFF]|\uD803[\uDC00-\uDD23\uDE80-\uDEA9\uDEAD-\uDF45\uDF51-\uDF81\uDF86-\uDFF6]|\uD83A[\uDC00-\uDCCF\uDD00-\uDD43\uDD4B-\uDFFF]|\uD83B[\uDC00-\uDEBB])/;function _c(e){return sa.test(e)}const ua=/^[\t\n\r\x20-\x7E]*$/;function ca(e){return ua.test(e)}const la=/[\u2028\u2029]/;function Sc(e){return la.test(e)}function Ac(e){return e>=11904&&e<=55215||e>=63744&&e<=64255||e>=65281&&e<=65374}function ha(e){return e>=127462&&e<=127487||e===8986||e===8987||e===9200||e===9203||e>=9728&&e<=10175||e===11088||e===11093||e>=127744&&e<=128591||e>=128640&&e<=128764||e>=128992&&e<=129008||e>=129280&&e<=129535||e>=129648&&e<=129782}const xc=String.fromCharCode(65279);function Ec(e){return!!(e&&e.length>0&&e.charCodeAt(0)===65279)}function Lc(e,t=!1){return e?(t&&(e=e.replace(/\\./g,"")),e.toLowerCase()!==e):!1}function Nc(e){const t=90-65+1;return e=e%(2*t),e<t?String.fromCharCode(97+e):String.fromCharCode(65+e-t)}function ii(e,t){return e===0?t!==5&&t!==7:e===2&&t===3?!1:e===4||e===2||e===3||t===4||t===2||t===3?!0:!(e===8&&(t===8||t===9||t===11||t===12)||(e===11||e===9)&&(t===9||t===10)||(e===12||e===10)&&t===10||t===5||t===13||t===7||e===1||e===13&&t===14||e===6&&t===6)}class ze{constructor(){this._data=fa()}static getInstance(){return ze._INSTANCE||(ze._INSTANCE=new ze),ze._INSTANCE}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const n=this._data,r=n.length/3;let i=1;for(;i<=r;)if(t<n[3*i])i=2*i;else if(t>n[3*i+1])i=2*i+1;else return n[3*i+2];return 0}}ze._INSTANCE=null;function fa(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}function kc(e,t){if(e===0)return 0;const n=da(e,t);if(n!==void 0)return n;const r=new jn(t,e);return r.prevCodePoint(),r.offset}function da(e,t){const n=new jn(t,e);let r=n.prevCodePoint();for(;ma(r)||r===65039||r===8419;){if(n.offset===0)return;r=n.prevCodePoint()}if(!ha(r))return;let i=n.offset;return i>0&&n.prevCodePoint()===8205&&(i=n.offset),i}function ma(e){return 127995<=e&&e<=127999}const Oc="\xA0";class xe{constructor(t){this.confusableDictionary=t}static getInstance(t){return xe.cache.get(Array.from(t))}static getLocales(){return xe._locales.getValue()}isAmbiguous(t){return this.confusableDictionary.has(t)}getPrimaryConfusable(t){return this.confusableDictionary.get(t)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}Yr=xe,xe.ambiguousCharacterData=new Qr(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}')),xe.cache=new Z1(e=>{function t(l){const f=new Map;for(let h=0;h<l.length;h+=2)f.set(l[h],l[h+1]);return f}function n(l,f){const h=new Map(l);for(const[d,m]of f)h.set(d,m);return h}function r(l,f){if(!l)return f;const h=new Map;for(const[d,m]of l)f.has(d)&&h.set(d,m);return h}const i=Yr.ambiguousCharacterData.getValue();let a=e.filter(l=>!l.startsWith("_")&&l in i);a.length===0&&(a=["_default"]);let s;for(const l of a){const f=t(i[l]);s=r(s,f)}const u=t(i._common),c=n(u,s);return new xe(c)}),xe._locales=new Qr(()=>Object.keys(xe.ambiguousCharacterData.getValue()).filter(e=>!e.startsWith("_")));class Ge{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(Ge.getRawData())),this._data}static isInvisibleCharacter(t){return Ge.getData().has(t)}static get codePoints(){return Ge.getData()}}Ge._data=void 0;const oi="$initialize";let ai=!1;function Mc(e){!isWeb||(ai||(ai=!0,console.warn("Could not create web worker(s). Falling back to loading web worker code in main thread, which might cause UI freezes. Please see https://github.com/microsoft/monaco-editor#faq")),console.warn(e.message))}class ga{constructor(t,n,r,i){this.vsWorker=t,this.req=n,this.method=r,this.args=i,this.type=0}}class si{constructor(t,n,r,i){this.vsWorker=t,this.seq=n,this.res=r,this.err=i,this.type=1}}class pa{constructor(t,n,r,i){this.vsWorker=t,this.req=n,this.eventName=r,this.arg=i,this.type=2}}class ba{constructor(t,n,r){this.vsWorker=t,this.req=n,this.event=r,this.type=3}}class va{constructor(t,n){this.vsWorker=t,this.req=n,this.type=4}}class ui{constructor(t){this._workerId=-1,this._handler=t,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(t){this._workerId=t}sendMessage(t,n){const r=String(++this._lastSentReq);return new Promise((i,a)=>{this._pendingReplies[r]={resolve:i,reject:a},this._send(new ga(this._workerId,r,t,n))})}listen(t,n){let r=null;const i=new Fe({onFirstListenerAdd:()=>{r=String(++this._lastSentReq),this._pendingEmitters.set(r,i),this._send(new pa(this._workerId,r,t,n))},onLastListenerRemove:()=>{this._pendingEmitters.delete(r),this._send(new va(this._workerId,r)),r=null}});return i.event}handleMessage(t){!t||!t.vsWorker||this._workerId!==-1&&t.vsWorker!==this._workerId||this._handleMessage(t)}_handleMessage(t){switch(t.type){case 1:return this._handleReplyMessage(t);case 0:return this._handleRequestMessage(t);case 2:return this._handleSubscribeEventMessage(t);case 3:return this._handleEventMessage(t);case 4:return this._handleUnsubscribeEventMessage(t)}}_handleReplyMessage(t){if(!this._pendingReplies[t.seq]){console.warn("Got reply to unknown seq");return}let n=this._pendingReplies[t.seq];if(delete this._pendingReplies[t.seq],t.err){let r=t.err;t.err.$isError&&(r=new Error,r.name=t.err.name,r.message=t.err.message,r.stack=t.err.stack),n.reject(r);return}n.resolve(t.res)}_handleRequestMessage(t){let n=t.req;this._handler.handleMessage(t.method,t.args).then(r=>{this._send(new si(this._workerId,n,r,void 0))},r=>{r.detail instanceof Error&&(r.detail=de(r.detail)),this._send(new si(this._workerId,n,void 0,de(r)))})}_handleSubscribeEventMessage(t){const n=t.req,r=this._handler.handleEvent(t.eventName,t.arg)(i=>{this._send(new ba(this._workerId,n,i))});this._pendingEvents.set(n,r)}_handleEventMessage(t){if(!this._pendingEmitters.has(t.req)){console.warn("Got event for unknown req");return}this._pendingEmitters.get(t.req).fire(t.event)}_handleUnsubscribeEventMessage(t){if(!this._pendingEvents.has(t.req)){console.warn("Got unsubscribe for unknown req");return}this._pendingEvents.get(t.req).dispose(),this._pendingEvents.delete(t.req)}_send(t){let n=[];if(t.type===0)for(let r=0;r<t.args.length;r++)t.args[r]instanceof ArrayBuffer&&n.push(t.args[r]);else t.type===1&&t.res instanceof ArrayBuffer&&n.push(t.res);this._handler.sendMessage(t,n)}}class Tc extends null{constructor(t,n,r){super();let i=null;this._worker=this._register(t.create("vs/base/common/worker/simpleWorker",l=>{this._protocol.handleMessage(l)},l=>{i&&i(l)})),this._protocol=new ui({sendMessage:(l,f)=>{this._worker.postMessage(l,f)},handleMessage:(l,f)=>{if(typeof r[l]!="function")return Promise.reject(new Error("Missing method "+l+" on main thread host."));try{return Promise.resolve(r[l].apply(r,f))}catch(h){return Promise.reject(h)}},handleEvent:(l,f)=>{if(Un(l)){const h=r[l].call(r,f);if(typeof h!="function")throw new Error(`Missing dynamic event ${l} on main thread host.`);return h}if(Bn(l)){const h=r[l];if(typeof h!="function")throw new Error(`Missing event ${l} on main thread host.`);return h}throw new Error(`Malformed event name ${l}`)}}),this._protocol.setWorkerId(this._worker.getId());let a=null;typeof globals.require!="undefined"&&typeof globals.require.getConfig=="function"?a=globals.require.getConfig():typeof globals.requirejs!="undefined"&&(a=globals.requirejs.s.contexts._.config);const s=types.getAllMethodNames(r);this._onModuleLoaded=this._protocol.sendMessage(oi,[this._worker.getId(),JSON.parse(JSON.stringify(a)),n,s]);const u=(l,f)=>this._request(l,f),c=(l,f)=>this._protocol.listen(l,f);this._lazyProxy=new Promise((l,f)=>{i=f,this._onModuleLoaded.then(h=>{l(ci(h,u,c))},h=>{f(h),this._onError("Worker failed to load "+n,h)})})}getProxyObject(){return this._lazyProxy}_request(t,n){return new Promise((r,i)=>{this._onModuleLoaded.then(()=>{this._protocol.sendMessage(t,n).then(r,i)},i)})}_onError(t,n){console.error(t),console.info(n)}}function Bn(e){return e[0]==="o"&&e[1]==="n"&&ti(e.charCodeAt(2))}function Un(e){return/^onDynamic/.test(e)&&ti(e.charCodeAt(9))}function ci(e,t,n){const r=s=>function(){const u=Array.prototype.slice.call(arguments,0);return t(s,u)},i=s=>function(u){return n(s,u)};let a={};for(const s of e){if(Un(s)){a[s]=i(s);continue}if(Bn(s)){a[s]=n(s,void 0);continue}a[s]=r(s)}return a}class li{constructor(t,n){this._requestHandlerFactory=n,this._requestHandler=null,this._protocol=new ui({sendMessage:(r,i)=>{t(r,i)},handleMessage:(r,i)=>this._handleMessage(r,i),handleEvent:(r,i)=>this._handleEvent(r,i)})}onmessage(t){this._protocol.handleMessage(t)}_handleMessage(t,n){if(t===oi)return this.initialize(n[0],n[1],n[2],n[3]);if(!this._requestHandler||typeof this._requestHandler[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._requestHandler[t].apply(this._requestHandler,n))}catch(r){return Promise.reject(r)}}_handleEvent(t,n){if(!this._requestHandler)throw new Error("Missing requestHandler");if(Un(t)){const r=this._requestHandler[t].call(this._requestHandler,n);if(typeof r!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return r}if(Bn(t)){const r=this._requestHandler[t];if(typeof r!="function")throw new Error(`Missing event ${t} on request handler.`);return r}throw new Error(`Malformed event name ${t}`)}initialize(t,n,r,i){this._protocol.setWorkerId(t);const a=ci(i,(s,u)=>this._protocol.sendMessage(s,u),(s,u)=>this._protocol.listen(s,u));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(a),Promise.resolve(Vn(this._requestHandler))):(n&&(typeof n.baseUrl!="undefined"&&delete n.baseUrl,typeof n.paths!="undefined"&&typeof n.paths.vs!="undefined"&&delete n.paths.vs,typeof n.trustedTypesPolicy!==void 0&&delete n.trustedTypesPolicy,n.catchError=!0,ie.require.config(n)),new Promise((s,u)=>{ie.require([r],c=>{if(this._requestHandler=c.create(a),!this._requestHandler){u(new Error("No RequestHandler!"));return}s(Vn(this._requestHandler))},u)}))}}function Ic(e){return new li(e,null)}class Je{constructor(t,n,r,i){this.originalStart=t,this.originalLength=n,this.modifiedStart=r,this.modifiedLength=i}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function Pc(e){return $n(e,0)}function $n(e,t){switch(typeof e){case"object":return e===null?Be(349,t):Array.isArray(e)?Ca(e,t):wa(e,t);case"string":return qn(e,t);case"boolean":return ya(e,t);case"number":return Be(e,t);case"undefined":return Be(937,t);default:return Be(617,t)}}function Be(e,t){return(t<<5)-t+e|0}function ya(e,t){return Be(e?433:863,t)}function qn(e,t){t=Be(149417,t);for(let n=0,r=e.length;n<r;n++)t=Be(e.charCodeAt(n),t);return t}function Ca(e,t){return t=Be(104579,t),e.reduce((n,r)=>$n(r,n),t)}function wa(e,t){return t=Be(181387,t),Object.keys(e).sort().reduce((n,r)=>(n=qn(r,n),$n(e[r],n)),t)}function Wn(e,t,n=32){const r=n-t,i=~((1<<r)-1);return(e<<t|(i&e)>>>r)>>>0}function hi(e,t=0,n=e.byteLength,r=0){for(let i=0;i<n;i++)e[t+i]=r}function _a(e,t,n="0"){for(;e.length<t;)e=n+e;return e}function Ft(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(n=>n.toString(16).padStart(2,"0")).join(""):_a((e>>>0).toString(16),t/4)}class Hn{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(64+3),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(t){const n=t.length;if(n===0)return;const r=this._buff;let i=this._buffLen,a=this._leftoverHighSurrogate,s,u;for(a!==0?(s=a,u=-1,a=0):(s=t.charCodeAt(0),u=0);;){let c=s;if(It(s))if(u+1<n){const l=t.charCodeAt(u+1);Pt(l)?(u++,c=Kn(s,l)):c=65533}else{a=s;break}else Pt(s)&&(c=65533);if(i=this._push(r,i,c),u++,u<n)s=t.charCodeAt(u);else break}this._buffLen=i,this._leftoverHighSurrogate=a}_push(t,n,r){return r<128?t[n++]=r:r<2048?(t[n++]=192|(r&1984)>>>6,t[n++]=128|(r&63)>>>0):r<65536?(t[n++]=224|(r&61440)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0):(t[n++]=240|(r&1835008)>>>18,t[n++]=128|(r&258048)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0),n>=64&&(this._step(),n-=64,this._totalLen+=64,t[0]=t[64+0],t[1]=t[64+1],t[2]=t[64+2]),n}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),Ft(this._h0)+Ft(this._h1)+Ft(this._h2)+Ft(this._h3)+Ft(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,hi(this._buff,this._buffLen),this._buffLen>56&&(this._step(),hi(this._buff));const t=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(t/4294967296),!1),this._buffDV.setUint32(60,t%4294967296,!1),this._step()}_step(){const t=Hn._bigBlock32,n=this._buffDV;for(let h=0;h<64;h+=4)t.setUint32(h,n.getUint32(h,!1),!1);for(let h=64;h<320;h+=4)t.setUint32(h,Wn(t.getUint32(h-12,!1)^t.getUint32(h-32,!1)^t.getUint32(h-56,!1)^t.getUint32(h-64,!1),1),!1);let r=this._h0,i=this._h1,a=this._h2,s=this._h3,u=this._h4,c,l,f;for(let h=0;h<80;h++)h<20?(c=i&a|~i&s,l=1518500249):h<40?(c=i^a^s,l=1859775393):h<60?(c=i&a|i&s|a&s,l=2400959708):(c=i^a^s,l=3395469782),f=Wn(r,5)+c+u+l+t.getUint32(h*4,!1)&4294967295,u=s,s=a,a=Wn(i,30),i=r,r=f;this._h0=this._h0+r&4294967295,this._h1=this._h1+i&4294967295,this._h2=this._h2+a&4294967295,this._h3=this._h3+s&4294967295,this._h4=this._h4+u&4294967295}}Hn._bigBlock32=new DataView(new ArrayBuffer(320));class fi{constructor(t){this.source=t}getElements(){const t=this.source,n=new Int32Array(t.length);for(let r=0,i=t.length;r<i;r++)n[r]=t.charCodeAt(r);return n}}function Sa(e,t,n){return new Ze(new fi(e),new fi(t)).ComputeDiff(n).changes}class mt{static Assert(t,n){if(!t)throw new Error(n)}}class gt{static Copy(t,n,r,i,a){for(let s=0;s<a;s++)r[i+s]=t[n+s]}static Copy2(t,n,r,i,a){for(let s=0;s<a;s++)r[i+s]=t[n+s]}}class di{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new Je(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_originalCount++}AddModifiedElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class Ze{constructor(t,n,r=null){this.ContinueProcessingPredicate=r,this._originalSequence=t,this._modifiedSequence=n;const[i,a,s]=Ze._getElements(t),[u,c,l]=Ze._getElements(n);this._hasStrings=s&&l,this._originalStringElements=i,this._originalElementsOrHash=a,this._modifiedStringElements=u,this._modifiedElementsOrHash=c,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(t){return t.length>0&&typeof t[0]=="string"}static _getElements(t){const n=t.getElements();if(Ze._isStringArray(n)){const r=new Int32Array(n.length);for(let i=0,a=n.length;i<a;i++)r[i]=qn(n[i],0);return[n,r,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}ElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._modifiedStringElements[n]:!0}ElementsAreStrictEqual(t,n){if(!this.ElementsAreEqual(t,n))return!1;const r=Ze._getStrictElement(this._originalSequence,t),i=Ze._getStrictElement(this._modifiedSequence,n);return r===i}static _getStrictElement(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}OriginalElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._originalElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._originalStringElements[n]:!0}ModifiedElementsAreEqual(t,n){return this._modifiedElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._modifiedStringElements[t]===this._modifiedStringElements[n]:!0}ComputeDiff(t){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,t)}_ComputeDiff(t,n,r,i,a){const s=[!1];let u=this.ComputeDiffRecursive(t,n,r,i,s);return a&&(u=this.PrettifyChanges(u)),{quitEarly:s[0],changes:u}}ComputeDiffRecursive(t,n,r,i,a){for(a[0]=!1;t<=n&&r<=i&&this.ElementsAreEqual(t,r);)t++,r++;for(;n>=t&&i>=r&&this.ElementsAreEqual(n,i);)n--,i--;if(t>n||r>i){let h;return r<=i?(mt.Assert(t===n+1,"originalStart should only be one more than originalEnd"),h=[new Je(t,0,r,i-r+1)]):t<=n?(mt.Assert(r===i+1,"modifiedStart should only be one more than modifiedEnd"),h=[new Je(t,n-t+1,r,0)]):(mt.Assert(t===n+1,"originalStart should only be one more than originalEnd"),mt.Assert(r===i+1,"modifiedStart should only be one more than modifiedEnd"),h=[]),h}const s=[0],u=[0],c=this.ComputeRecursionPoint(t,n,r,i,s,u,a),l=s[0],f=u[0];if(c!==null)return c;if(!a[0]){const h=this.ComputeDiffRecursive(t,l,r,f,a);let d=[];return a[0]?d=[new Je(l+1,n-(l+1)+1,f+1,i-(f+1)+1)]:d=this.ComputeDiffRecursive(l+1,n,f+1,i,a),this.ConcatenateChanges(h,d)}return[new Je(t,n-t+1,r,i-r+1)]}WALKTRACE(t,n,r,i,a,s,u,c,l,f,h,d,m,p,y,g,w,b){let v=null,L=null,x=new di,A=n,C=r,_=m[0]-g[0]-i,S=-1073741824,k=this.m_forwardHistory.length-1;do{const E=_+t;E===A||E<C&&l[E-1]<l[E+1]?(h=l[E+1],p=h-_-i,h<S&&x.MarkNextChange(),S=h,x.AddModifiedElement(h+1,p),_=E+1-t):(h=l[E-1]+1,p=h-_-i,h<S&&x.MarkNextChange(),S=h-1,x.AddOriginalElement(h,p+1),_=E-1-t),k>=0&&(l=this.m_forwardHistory[k],t=l[0],A=1,C=l.length-1)}while(--k>=-1);if(v=x.getReverseChanges(),b[0]){let E=m[0]+1,T=g[0]+1;if(v!==null&&v.length>0){const R=v[v.length-1];E=Math.max(E,R.getOriginalEnd()),T=Math.max(T,R.getModifiedEnd())}L=[new Je(E,d-E+1,T,y-T+1)]}else{x=new di,A=s,C=u,_=m[0]-g[0]-c,S=1073741824,k=w?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const E=_+a;E===A||E<C&&f[E-1]>=f[E+1]?(h=f[E+1]-1,p=h-_-c,h>S&&x.MarkNextChange(),S=h+1,x.AddOriginalElement(h+1,p+1),_=E+1-a):(h=f[E-1],p=h-_-c,h>S&&x.MarkNextChange(),S=h,x.AddModifiedElement(h+1,p+1),_=E-1-a),k>=0&&(f=this.m_reverseHistory[k],a=f[0],A=1,C=f.length-1)}while(--k>=-1);L=x.getChanges()}return this.ConcatenateChanges(v,L)}ComputeRecursionPoint(t,n,r,i,a,s,u){let c=0,l=0,f=0,h=0,d=0,m=0;t--,r--,a[0]=0,s[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const p=n-t+(i-r),y=p+1,g=new Int32Array(y),w=new Int32Array(y),b=i-r,v=n-t,L=t-r,x=n-i,A=(v-b)%2==0;g[b]=t,w[v]=n,u[0]=!1;for(let C=1;C<=p/2+1;C++){let _=0,S=0;f=this.ClipDiagonalBound(b-C,C,b,y),h=this.ClipDiagonalBound(b+C,C,b,y);for(let E=f;E<=h;E+=2){E===f||E<h&&g[E-1]<g[E+1]?c=g[E+1]:c=g[E-1]+1,l=c-(E-b)-L;const T=c;for(;c<n&&l<i&&this.ElementsAreEqual(c+1,l+1);)c++,l++;if(g[E]=c,c+l>_+S&&(_=c,S=l),!A&&Math.abs(E-v)<=C-1&&c>=w[E])return a[0]=c,s[0]=l,T<=w[E]&&1447>0&&C<=1447+1?this.WALKTRACE(b,f,h,L,v,d,m,x,g,w,c,n,a,l,i,s,A,u):null}const k=(_-t+(S-r)-C)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(_,k))return u[0]=!0,a[0]=_,s[0]=S,k>0&&1447>0&&C<=1447+1?this.WALKTRACE(b,f,h,L,v,d,m,x,g,w,c,n,a,l,i,s,A,u):(t++,r++,[new Je(t,n-t+1,r,i-r+1)]);d=this.ClipDiagonalBound(v-C,C,v,y),m=this.ClipDiagonalBound(v+C,C,v,y);for(let E=d;E<=m;E+=2){E===d||E<m&&w[E-1]>=w[E+1]?c=w[E+1]-1:c=w[E-1],l=c-(E-v)-x;const T=c;for(;c>t&&l>r&&this.ElementsAreEqual(c,l);)c--,l--;if(w[E]=c,A&&Math.abs(E-b)<=C&&c<=g[E])return a[0]=c,s[0]=l,T>=g[E]&&1447>0&&C<=1447+1?this.WALKTRACE(b,f,h,L,v,d,m,x,g,w,c,n,a,l,i,s,A,u):null}if(C<=1447){let E=new Int32Array(h-f+2);E[0]=b-f+1,gt.Copy2(g,f,E,1,h-f+1),this.m_forwardHistory.push(E),E=new Int32Array(m-d+2),E[0]=v-d+1,gt.Copy2(w,d,E,1,m-d+1),this.m_reverseHistory.push(E)}}return this.WALKTRACE(b,f,h,L,v,d,m,x,g,w,c,n,a,l,i,s,A,u)}PrettifyChanges(t){for(let n=0;n<t.length;n++){const r=t[n],i=n<t.length-1?t[n+1].originalStart:this._originalElementsOrHash.length,a=n<t.length-1?t[n+1].modifiedStart:this._modifiedElementsOrHash.length,s=r.originalLength>0,u=r.modifiedLength>0;for(;r.originalStart+r.originalLength<i&&r.modifiedStart+r.modifiedLength<a&&(!s||this.OriginalElementsAreEqual(r.originalStart,r.originalStart+r.originalLength))&&(!u||this.ModifiedElementsAreEqual(r.modifiedStart,r.modifiedStart+r.modifiedLength));){const l=this.ElementsAreStrictEqual(r.originalStart,r.modifiedStart);if(this.ElementsAreStrictEqual(r.originalStart+r.originalLength,r.modifiedStart+r.modifiedLength)&&!l)break;r.originalStart++,r.modifiedStart++}let c=[null];if(n<t.length-1&&this.ChangesOverlap(t[n],t[n+1],c)){t[n]=c[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){const r=t[n];let i=0,a=0;if(n>0){const h=t[n-1];i=h.originalStart+h.originalLength,a=h.modifiedStart+h.modifiedLength}const s=r.originalLength>0,u=r.modifiedLength>0;let c=0,l=this._boundaryScore(r.originalStart,r.originalLength,r.modifiedStart,r.modifiedLength);for(let h=1;;h++){const d=r.originalStart-h,m=r.modifiedStart-h;if(d<i||m<a||s&&!this.OriginalElementsAreEqual(d,d+r.originalLength)||u&&!this.ModifiedElementsAreEqual(m,m+r.modifiedLength))break;const p=(d===i&&m===a?5:0)+this._boundaryScore(d,r.originalLength,m,r.modifiedLength);p>l&&(l=p,c=h)}r.originalStart-=c,r.modifiedStart-=c;const f=[null];if(n>0&&this.ChangesOverlap(t[n-1],t[n],f)){t[n-1]=f[0],t.splice(n,1),n++;continue}}if(this._hasStrings)for(let n=1,r=t.length;n<r;n++){const i=t[n-1],a=t[n],s=a.originalStart-i.originalStart-i.originalLength,u=i.originalStart,c=a.originalStart+a.originalLength,l=c-u,f=i.modifiedStart,h=a.modifiedStart+a.modifiedLength,d=h-f;if(s<5&&l<20&&d<20){const m=this._findBetterContiguousSequence(u,l,f,d,s);if(m){const[p,y]=m;(p!==i.originalStart+i.originalLength||y!==i.modifiedStart+i.modifiedLength)&&(i.originalLength=p-i.originalStart,i.modifiedLength=y-i.modifiedStart,a.originalStart=p+s,a.modifiedStart=y+s,a.originalLength=c-a.originalStart,a.modifiedLength=h-a.modifiedStart)}}}return t}_findBetterContiguousSequence(t,n,r,i,a){if(n<a||i<a)return null;const s=t+n-a+1,u=r+i-a+1;let c=0,l=0,f=0;for(let h=t;h<s;h++)for(let d=r;d<u;d++){const m=this._contiguousSequenceScore(h,d,a);m>0&&m>c&&(c=m,l=h,f=d)}return c>0?[l,f]:null}_contiguousSequenceScore(t,n,r){let i=0;for(let a=0;a<r;a++){if(!this.ElementsAreEqual(t+a,n+a))return 0;i+=this._originalStringElements[t+a].length}return i}_OriginalIsBoundary(t){return t<=0||t>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._originalStringElements[t])}_OriginalRegionIsBoundary(t,n){if(this._OriginalIsBoundary(t)||this._OriginalIsBoundary(t-1))return!0;if(n>0){const r=t+n;if(this._OriginalIsBoundary(r-1)||this._OriginalIsBoundary(r))return!0}return!1}_ModifiedIsBoundary(t){return t<=0||t>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[t])}_ModifiedRegionIsBoundary(t,n){if(this._ModifiedIsBoundary(t)||this._ModifiedIsBoundary(t-1))return!0;if(n>0){const r=t+n;if(this._ModifiedIsBoundary(r-1)||this._ModifiedIsBoundary(r))return!0}return!1}_boundaryScore(t,n,r,i){const a=this._OriginalRegionIsBoundary(t,n)?1:0,s=this._ModifiedRegionIsBoundary(r,i)?1:0;return a+s}ConcatenateChanges(t,n){let r=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.ChangesOverlap(t[t.length-1],n[0],r)){const i=new Array(t.length+n.length-1);return gt.Copy(t,0,i,0,t.length-1),i[t.length-1]=r[0],gt.Copy(n,1,i,t.length,n.length-1),i}else{const i=new Array(t.length+n.length);return gt.Copy(t,0,i,0,t.length),gt.Copy(n,0,i,t.length,n.length),i}}ChangesOverlap(t,n,r){if(mt.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),mt.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){const i=t.originalStart;let a=t.originalLength;const s=t.modifiedStart;let u=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(a=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(u=n.modifiedStart+n.modifiedLength-t.modifiedStart),r[0]=new Je(i,a,s,u),!0}else return r[0]=null,!1}ClipDiagonalBound(t,n,r,i){if(t>=0&&t<i)return t;const a=r,s=i-r-1,u=n%2==0;if(t<0){const c=a%2==0;return u===c?0:1}else{const c=s%2==0;return u===c?i-1:i-2}}}var cn=ce(155);let pt;if(typeof ie.vscode!="undefined"&&typeof ie.vscode.process!="undefined"){const e=ie.vscode.process;pt={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof cn!="undefined"?pt={get platform(){return cn.platform},get arch(){return cn.arch},get env(){return{NODE_ENV:"production"}},cwd(){return{NODE_ENV:"production"}.VSCODE_CWD||cn.cwd()}}:pt={get platform(){return Mt?"win32":V1?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};const zn=pt.cwd,Fc=pt.env,ot=pt.platform,Aa=65,xa=97,Ea=90,La=122,Qe=46,re=47,me=92,Ye=58,Na=63;class mi extends Error{constructor(t,n,r){let i;typeof n=="string"&&n.indexOf("not ")===0?(i="must not be",n=n.replace(/^not /,"")):i="must be";const a=t.indexOf(".")!==-1?"property":"argument";let s=`The "${t}" ${a} ${i} of type ${n}`;s+=`. Received type ${typeof r}`,super(s),this.code="ERR_INVALID_ARG_TYPE"}}function Q(e,t){if(typeof e!="string")throw new mi(t,"string",e)}function B(e){return e===re||e===me}function Gn(e){return e===re}function Xe(e){return e>=Aa&&e<=Ea||e>=xa&&e<=La}function ln(e,t,n,r){let i="",a=0,s=-1,u=0,c=0;for(let l=0;l<=e.length;++l){if(l<e.length)c=e.charCodeAt(l);else{if(r(c))break;c=re}if(r(c)){if(!(s===l-1||u===1))if(u===2){if(i.length<2||a!==2||i.charCodeAt(i.length-1)!==Qe||i.charCodeAt(i.length-2)!==Qe){if(i.length>2){const f=i.lastIndexOf(n);f===-1?(i="",a=0):(i=i.slice(0,f),a=i.length-1-i.lastIndexOf(n)),s=l,u=0;continue}else if(i.length!==0){i="",a=0,s=l,u=0;continue}}t&&(i+=i.length>0?`${n}..`:"..",a=2)}else i.length>0?i+=`${n}${e.slice(s+1,l)}`:i=e.slice(s+1,l),a=l-s-1;s=l,u=0}else c===Qe&&u!==-1?++u:u=-1}return i}function gi(e,t){if(t===null||typeof t!="object")throw new mi("pathObject","Object",t);const n=t.dir||t.root,r=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}const fe={resolve(...e){let t="",n="",r=!1;for(let i=e.length-1;i>=-1;i--){let a;if(i>=0){if(a=e[i],Q(a,"path"),a.length===0)continue}else t.length===0?a=zn():(a={NODE_ENV:"production"}[`=${t}`]||zn(),(a===void 0||a.slice(0,2).toLowerCase()!==t.toLowerCase()&&a.charCodeAt(2)===me)&&(a=`${t}\\`));const s=a.length;let u=0,c="",l=!1;const f=a.charCodeAt(0);if(s===1)B(f)&&(u=1,l=!0);else if(B(f))if(l=!0,B(a.charCodeAt(1))){let h=2,d=h;for(;h<s&&!B(a.charCodeAt(h));)h++;if(h<s&&h!==d){const m=a.slice(d,h);for(d=h;h<s&&B(a.charCodeAt(h));)h++;if(h<s&&h!==d){for(d=h;h<s&&!B(a.charCodeAt(h));)h++;(h===s||h!==d)&&(c=`\\\\${m}\\${a.slice(d,h)}`,u=h)}}}else u=1;else Xe(f)&&a.charCodeAt(1)===Ye&&(c=a.slice(0,2),u=2,s>2&&B(a.charCodeAt(2))&&(l=!0,u=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(r){if(t.length>0)break}else if(n=`${a.slice(u)}\\${n}`,r=l,l&&t.length>0)break}return n=ln(n,!r,"\\",B),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){Q(e,"path");const t=e.length;if(t===0)return".";let n=0,r,i=!1;const a=e.charCodeAt(0);if(t===1)return Gn(a)?"\\":e;if(B(a))if(i=!0,B(e.charCodeAt(1))){let u=2,c=u;for(;u<t&&!B(e.charCodeAt(u));)u++;if(u<t&&u!==c){const l=e.slice(c,u);for(c=u;u<t&&B(e.charCodeAt(u));)u++;if(u<t&&u!==c){for(c=u;u<t&&!B(e.charCodeAt(u));)u++;if(u===t)return`\\\\${l}\\${e.slice(c)}\\`;u!==c&&(r=`\\\\${l}\\${e.slice(c,u)}`,n=u)}}}else n=1;else Xe(a)&&e.charCodeAt(1)===Ye&&(r=e.slice(0,2),n=2,t>2&&B(e.charCodeAt(2))&&(i=!0,n=3));let s=n<t?ln(e.slice(n),!i,"\\",B):"";return s.length===0&&!i&&(s="."),s.length>0&&B(e.charCodeAt(t-1))&&(s+="\\"),r===void 0?i?`\\${s}`:s:i?`${r}\\${s}`:`${r}${s}`},isAbsolute(e){Q(e,"path");const t=e.length;if(t===0)return!1;const n=e.charCodeAt(0);return B(n)||t>2&&Xe(n)&&e.charCodeAt(1)===Ye&&B(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let a=0;a<e.length;++a){const s=e[a];Q(s,"path"),s.length>0&&(t===void 0?t=n=s:t+=`\\${s}`)}if(t===void 0)return".";let r=!0,i=0;if(typeof n=="string"&&B(n.charCodeAt(0))){++i;const a=n.length;a>1&&B(n.charCodeAt(1))&&(++i,a>2&&(B(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&B(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return fe.normalize(t)},relative(e,t){if(Q(e,"from"),Q(t,"to"),e===t)return"";const n=fe.resolve(e),r=fe.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return"";let i=0;for(;i<e.length&&e.charCodeAt(i)===me;)i++;let a=e.length;for(;a-1>i&&e.charCodeAt(a-1)===me;)a--;const s=a-i;let u=0;for(;u<t.length&&t.charCodeAt(u)===me;)u++;let c=t.length;for(;c-1>u&&t.charCodeAt(c-1)===me;)c--;const l=c-u,f=s<l?s:l;let h=-1,d=0;for(;d<f;d++){const p=e.charCodeAt(i+d);if(p!==t.charCodeAt(u+d))break;p===me&&(h=d)}if(d!==f){if(h===-1)return r}else{if(l>f){if(t.charCodeAt(u+d)===me)return r.slice(u+d+1);if(d===2)return r.slice(u+d)}s>f&&(e.charCodeAt(i+d)===me?h=d:d===2&&(h=3)),h===-1&&(h=0)}let m="";for(d=i+h+1;d<=a;++d)(d===a||e.charCodeAt(d)===me)&&(m+=m.length===0?"..":"\\..");return u+=h,m.length>0?`${m}${r.slice(u,c)}`:(r.charCodeAt(u)===me&&++u,r.slice(u,c))},toNamespacedPath(e){if(typeof e!="string")return e;if(e.length===0)return"";const t=fe.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===me){if(t.charCodeAt(1)===me){const n=t.charCodeAt(2);if(n!==Na&&n!==Qe)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(Xe(t.charCodeAt(0))&&t.charCodeAt(1)===Ye&&t.charCodeAt(2)===me)return`\\\\?\\${t}`;return e},dirname(e){Q(e,"path");const t=e.length;if(t===0)return".";let n=-1,r=0;const i=e.charCodeAt(0);if(t===1)return B(i)?e:".";if(B(i)){if(n=r=1,B(e.charCodeAt(1))){let u=2,c=u;for(;u<t&&!B(e.charCodeAt(u));)u++;if(u<t&&u!==c){for(c=u;u<t&&B(e.charCodeAt(u));)u++;if(u<t&&u!==c){for(c=u;u<t&&!B(e.charCodeAt(u));)u++;if(u===t)return e;u!==c&&(n=r=u+1)}}}}else Xe(i)&&e.charCodeAt(1)===Ye&&(n=t>2&&B(e.charCodeAt(2))?3:2,r=n);let a=-1,s=!0;for(let u=t-1;u>=r;--u)if(B(e.charCodeAt(u))){if(!s){a=u;break}}else s=!1;if(a===-1){if(n===-1)return".";a=n}return e.slice(0,a)},basename(e,t){t!==void 0&&Q(t,"ext"),Q(e,"path");let n=0,r=-1,i=!0,a;if(e.length>=2&&Xe(e.charCodeAt(0))&&e.charCodeAt(1)===Ye&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let s=t.length-1,u=-1;for(a=e.length-1;a>=n;--a){const c=e.charCodeAt(a);if(B(c)){if(!i){n=a+1;break}}else u===-1&&(i=!1,u=a+1),s>=0&&(c===t.charCodeAt(s)?--s==-1&&(r=a):(s=-1,r=u))}return n===r?r=u:r===-1&&(r=e.length),e.slice(n,r)}for(a=e.length-1;a>=n;--a)if(B(e.charCodeAt(a))){if(!i){n=a+1;break}}else r===-1&&(i=!1,r=a+1);return r===-1?"":e.slice(n,r)},extname(e){Q(e,"path");let t=0,n=-1,r=0,i=-1,a=!0,s=0;e.length>=2&&e.charCodeAt(1)===Ye&&Xe(e.charCodeAt(0))&&(t=r=2);for(let u=e.length-1;u>=t;--u){const c=e.charCodeAt(u);if(B(c)){if(!a){r=u+1;break}continue}i===-1&&(a=!1,i=u+1),c===Qe?n===-1?n=u:s!==1&&(s=1):n!==-1&&(s=-1)}return n===-1||i===-1||s===0||s===1&&n===i-1&&n===r+1?"":e.slice(n,i)},format:gi.bind(null,"\\"),parse(e){Q(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.length;let r=0,i=e.charCodeAt(0);if(n===1)return B(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(B(i)){if(r=1,B(e.charCodeAt(1))){let h=2,d=h;for(;h<n&&!B(e.charCodeAt(h));)h++;if(h<n&&h!==d){for(d=h;h<n&&B(e.charCodeAt(h));)h++;if(h<n&&h!==d){for(d=h;h<n&&!B(e.charCodeAt(h));)h++;h===n?r=h:h!==d&&(r=h+1)}}}}else if(Xe(i)&&e.charCodeAt(1)===Ye){if(n<=2)return t.root=t.dir=e,t;if(r=2,B(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let a=-1,s=r,u=-1,c=!0,l=e.length-1,f=0;for(;l>=r;--l){if(i=e.charCodeAt(l),B(i)){if(!c){s=l+1;break}continue}u===-1&&(c=!1,u=l+1),i===Qe?a===-1?a=l:f!==1&&(f=1):a!==-1&&(f=-1)}return u!==-1&&(a===-1||f===0||f===1&&a===u-1&&a===s+1?t.base=t.name=e.slice(s,u):(t.name=e.slice(s,a),t.base=e.slice(s,u),t.ext=e.slice(a,u))),s>0&&s!==r?t.dir=e.slice(0,s-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},ge={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){const i=r>=0?e[r]:zn();Q(i,"path"),i.length!==0&&(t=`${i}/${t}`,n=i.charCodeAt(0)===re)}return t=ln(t,!n,"/",Gn),n?`/${t}`:t.length>0?t:"."},normalize(e){if(Q(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===re,n=e.charCodeAt(e.length-1)===re;return e=ln(e,!t,"/",Gn),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return Q(e,"path"),e.length>0&&e.charCodeAt(0)===re},join(...e){if(e.length===0)return".";let t;for(let n=0;n<e.length;++n){const r=e[n];Q(r,"path"),r.length>0&&(t===void 0?t=r:t+=`/${r}`)}return t===void 0?".":ge.normalize(t)},relative(e,t){if(Q(e,"from"),Q(t,"to"),e===t||(e=ge.resolve(e),t=ge.resolve(t),e===t))return"";const n=1,r=e.length,i=r-n,a=1,s=t.length-a,u=i<s?i:s;let c=-1,l=0;for(;l<u;l++){const h=e.charCodeAt(n+l);if(h!==t.charCodeAt(a+l))break;h===re&&(c=l)}if(l===u)if(s>u){if(t.charCodeAt(a+l)===re)return t.slice(a+l+1);if(l===0)return t.slice(a+l)}else i>u&&(e.charCodeAt(n+l)===re?c=l:l===0&&(c=0));let f="";for(l=n+c+1;l<=r;++l)(l===r||e.charCodeAt(l)===re)&&(f+=f.length===0?"..":"/..");return`${f}${t.slice(a+c)}`},toNamespacedPath(e){return e},dirname(e){if(Q(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===re;let n=-1,r=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===re){if(!r){n=i;break}}else r=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&Q(t,"ext"),Q(e,"path");let n=0,r=-1,i=!0,a;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let s=t.length-1,u=-1;for(a=e.length-1;a>=0;--a){const c=e.charCodeAt(a);if(c===re){if(!i){n=a+1;break}}else u===-1&&(i=!1,u=a+1),s>=0&&(c===t.charCodeAt(s)?--s==-1&&(r=a):(s=-1,r=u))}return n===r?r=u:r===-1&&(r=e.length),e.slice(n,r)}for(a=e.length-1;a>=0;--a)if(e.charCodeAt(a)===re){if(!i){n=a+1;break}}else r===-1&&(i=!1,r=a+1);return r===-1?"":e.slice(n,r)},extname(e){Q(e,"path");let t=-1,n=0,r=-1,i=!0,a=0;for(let s=e.length-1;s>=0;--s){const u=e.charCodeAt(s);if(u===re){if(!i){n=s+1;break}continue}r===-1&&(i=!1,r=s+1),u===Qe?t===-1?t=s:a!==1&&(a=1):t!==-1&&(a=-1)}return t===-1||r===-1||a===0||a===1&&t===r-1&&t===n+1?"":e.slice(t,r)},format:gi.bind(null,"/"),parse(e){Q(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.charCodeAt(0)===re;let r;n?(t.root="/",r=1):r=0;let i=-1,a=0,s=-1,u=!0,c=e.length-1,l=0;for(;c>=r;--c){const f=e.charCodeAt(c);if(f===re){if(!u){a=c+1;break}continue}s===-1&&(u=!1,s=c+1),f===Qe?i===-1?i=c:l!==1&&(l=1):i!==-1&&(l=-1)}if(s!==-1){const f=a===0&&n?1:a;i===-1||l===0||l===1&&i===s-1&&i===a+1?t.base=t.name=e.slice(f,s):(t.name=e.slice(f,i),t.base=e.slice(f,s),t.ext=e.slice(i,s))}return a>0?t.dir=e.slice(0,a-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};ge.win32=fe.win32=fe,ge.posix=fe.posix=ge;const Rc=ot==="win32"?fe.normalize:ge.normalize,Vc=ot==="win32"?fe.resolve:ge.resolve,Dc=ot==="win32"?fe.relative:ge.relative,Kc=ot==="win32"?fe.dirname:ge.dirname,jc=ot==="win32"?fe.basename:ge.basename,Bc=ot==="win32"?fe.extname:ge.extname,Uc=ot==="win32"?fe.sep:ge.sep,ka=/^\w[\w\d+.-]*$/,Oa=/^\//,Ma=/^\/\//;function pi(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!ka.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!Oa.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Ma.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function Ta(e,t){return!e&&!t?"file":e}function Ia(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==Ne&&(t=Ne+t):t=Ne;break}return t}const G="",Ne="/",Pa=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class at{constructor(t,n,r,i,a,s=!1){typeof t=="object"?(this.scheme=t.scheme||G,this.authority=t.authority||G,this.path=t.path||G,this.query=t.query||G,this.fragment=t.fragment||G):(this.scheme=Ta(t,s),this.authority=n||G,this.path=Ia(this.scheme,r||G),this.query=i||G,this.fragment=a||G,pi(this,s))}static isUri(t){return t instanceof at?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}get fsPath(){return Jn(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:r,path:i,query:a,fragment:s}=t;return n===void 0?n=this.scheme:n===null&&(n=G),r===void 0?r=this.authority:r===null&&(r=G),i===void 0?i=this.path:i===null&&(i=G),a===void 0?a=this.query:a===null&&(a=G),s===void 0?s=this.fragment:s===null&&(s=G),n===this.scheme&&r===this.authority&&i===this.path&&a===this.query&&s===this.fragment?this:new bt(n,r,i,a,s)}static parse(t,n=!1){const r=Pa.exec(t);return r?new bt(r[2]||G,hn(r[4]||G),hn(r[5]||G),hn(r[7]||G),hn(r[9]||G),n):new bt(G,G,G,G,G)}static file(t){let n=G;if(Mt&&(t=t.replace(/\\/g,Ne)),t[0]===Ne&&t[1]===Ne){const r=t.indexOf(Ne,2);r===-1?(n=t.substring(2),t=Ne):(n=t.substring(2,r),t=t.substring(r)||Ne)}return new bt("file",n,t,G,G)}static from(t){const n=new bt(t.scheme,t.authority,t.path,t.query,t.fragment);return pi(n,!0),n}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return Mt&&t.scheme==="file"?r=at.file(fe.join(Jn(t,!0),...n)).path:r=ge.join(t.path,...n),t.with({path:r})}toString(t=!1){return Zn(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof at)return t;{const n=new bt(t);return n._formatted=t.external,n._fsPath=t._sep===bi?t.fsPath:null,n}}else return t}}const bi=Mt?1:void 0;class bt extends at{constructor(){super(...arguments);this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Jn(this,!1)),this._fsPath}toString(t=!1){return t?Zn(this,!0):(this._formatted||(this._formatted=Zn(this,!1)),this._formatted)}toJSON(){const t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=bi),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t}}const vi={[58]:"%3A",[47]:"%2F",[63]:"%3F",[35]:"%23",[91]:"%5B",[93]:"%5D",[64]:"%40",[33]:"%21",[36]:"%24",[38]:"%26",[39]:"%27",[40]:"%28",[41]:"%29",[42]:"%2A",[43]:"%2B",[44]:"%2C",[59]:"%3B",[61]:"%3D",[32]:"%20"};function yi(e,t){let n,r=-1;for(let i=0;i<e.length;i++){const a=e.charCodeAt(i);if(a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===45||a===46||a===95||a===126||t&&a===47)r!==-1&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n!==void 0&&(n+=e.charAt(i));else{n===void 0&&(n=e.substr(0,i));const s=vi[a];s!==void 0?(r!==-1&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=s):r===-1&&(r=i)}}return r!==-1&&(n+=encodeURIComponent(e.substring(r))),n!==void 0?n:e}function Fa(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=vi[r]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function Jn(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,Mt&&(n=n.replace(/\//g,"\\")),n}function Zn(e,t){const n=t?Fa:yi;let r="",{scheme:i,authority:a,path:s,query:u,fragment:c}=e;if(i&&(r+=i,r+=":"),(a||i==="file")&&(r+=Ne,r+=Ne),a){let l=a.indexOf("@");if(l!==-1){const f=a.substr(0,l);a=a.substr(l+1),l=f.indexOf(":"),l===-1?r+=n(f,!1):(r+=n(f.substr(0,l),!1),r+=":",r+=n(f.substr(l+1),!1)),r+="@"}a=a.toLowerCase(),l=a.indexOf(":"),l===-1?r+=n(a,!1):(r+=n(a.substr(0,l),!1),r+=a.substr(l))}if(s){if(s.length>=3&&s.charCodeAt(0)===47&&s.charCodeAt(2)===58){const l=s.charCodeAt(1);l>=65&&l<=90&&(s=`/${String.fromCharCode(l+32)}:${s.substr(3)}`)}else if(s.length>=2&&s.charCodeAt(1)===58){const l=s.charCodeAt(0);l>=65&&l<=90&&(s=`${String.fromCharCode(l+32)}:${s.substr(2)}`)}r+=n(s,!0)}return u&&(r+="?",r+=n(u,!1)),c&&(r+="#",r+=t?c:yi(c,!1)),r}function Ci(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+Ci(e.substr(3)):e}}const wi=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function hn(e){return e.match(wi)?e.replace(wi,t=>Ci(t)):e}class pe{constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new pe(t,n)}delta(t=0,n=0){return this.with(this.lineNumber+t,this.column+n)}equals(t){return pe.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return pe.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return pe.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){const r=t.lineNumber|0,i=n.lineNumber|0;if(r===i){const a=t.column|0,s=n.column|0;return a-s}return r-i}clone(){return new pe(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new pe(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}}class J{constructor(t,n,r,i){t>r||t===r&&n>i?(this.startLineNumber=r,this.startColumn=i,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=r,this.endColumn=i)}isEmpty(){return J.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return J.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return J.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return J.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return J.plusRange(this,t)}static plusRange(t,n){let r,i,a,s;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(a=n.endLineNumber,s=n.endColumn):n.endLineNumber===t.endLineNumber?(a=n.endLineNumber,s=Math.max(n.endColumn,t.endColumn)):(a=t.endLineNumber,s=t.endColumn),new J(r,i,a,s)}intersectRanges(t){return J.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,i=t.startColumn,a=t.endLineNumber,s=t.endColumn,u=n.startLineNumber,c=n.startColumn,l=n.endLineNumber,f=n.endColumn;return r<u?(r=u,i=c):r===u&&(i=Math.max(i,c)),a>l?(a=l,s=f):a===l&&(s=Math.min(s,f)),r>a||r===a&&i>s?null:new J(r,i,a,s)}equalsRange(t){return J.equalsRange(this,t)}static equalsRange(t,n){return!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return J.getEndPosition(this)}static getEndPosition(t){return new pe(t.endLineNumber,t.endColumn)}getStartPosition(){return J.getStartPosition(this)}static getStartPosition(t){return new pe(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new J(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new J(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return J.collapseToStart(this)}static collapseToStart(t){return new J(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}static fromPositions(t,n=t){return new J(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new J(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static compareRangesUsingStarts(t,n){if(t&&n){const r=t.startLineNumber|0,i=n.startLineNumber|0;if(r===i){const a=t.startColumn|0,s=n.startColumn|0;if(a===s){const u=t.endLineNumber|0,c=n.endLineNumber|0;if(u===c){const l=t.endColumn|0,f=n.endColumn|0;return l-f}return u-c}return a-s}return r-i}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}}const Ra=3;function _i(e,t,n,r){return new Ze(e,t,n).ComputeDiff(r)}class Si{constructor(t){const n=[],r=[];for(let i=0,a=t.length;i<a;i++)n[i]=Qn(t[i],1),r[i]=Yn(t[i],1);this.lines=t,this._startColumns=n,this._endColumns=r}getElements(){const t=[];for(let n=0,r=this.lines.length;n<r;n++)t[n]=this.lines[n].substring(this._startColumns[n]-1,this._endColumns[n]-1);return t}getStrictElement(t){return this.lines[t]}getStartLineNumber(t){return t+1}getEndLineNumber(t){return t+1}createCharSequence(t,n,r){const i=[],a=[],s=[];let u=0;for(let c=n;c<=r;c++){const l=this.lines[c],f=t?this._startColumns[c]:1,h=t?this._endColumns[c]:l.length+1;for(let d=f;d<h;d++)i[u]=l.charCodeAt(d-1),a[u]=c+1,s[u]=d,u++}return new Va(i,a,s)}}class Va{constructor(t,n,r){this._charCodes=t,this._lineNumbers=n,this._columns=r}getElements(){return this._charCodes}getStartLineNumber(t){return this._lineNumbers[t]}getStartColumn(t){return this._columns[t]}getEndLineNumber(t){return this._lineNumbers[t]}getEndColumn(t){return this._columns[t]+1}}class Rt{constructor(t,n,r,i,a,s,u,c){this.originalStartLineNumber=t,this.originalStartColumn=n,this.originalEndLineNumber=r,this.originalEndColumn=i,this.modifiedStartLineNumber=a,this.modifiedStartColumn=s,this.modifiedEndLineNumber=u,this.modifiedEndColumn=c}static createFromDiffChange(t,n,r){let i,a,s,u,c,l,f,h;return t.originalLength===0?(i=0,a=0,s=0,u=0):(i=n.getStartLineNumber(t.originalStart),a=n.getStartColumn(t.originalStart),s=n.getEndLineNumber(t.originalStart+t.originalLength-1),u=n.getEndColumn(t.originalStart+t.originalLength-1)),t.modifiedLength===0?(c=0,l=0,f=0,h=0):(c=r.getStartLineNumber(t.modifiedStart),l=r.getStartColumn(t.modifiedStart),f=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),h=r.getEndColumn(t.modifiedStart+t.modifiedLength-1)),new Rt(i,a,s,u,c,l,f,h)}}function Da(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let r=1,i=e.length;r<i;r++){const a=e[r],s=a.originalStart-(n.originalStart+n.originalLength),u=a.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(s,u)<Ra?(n.originalLength=a.originalStart+a.originalLength-n.originalStart,n.modifiedLength=a.modifiedStart+a.modifiedLength-n.modifiedStart):(t.push(a),n=a)}return t}class Vt{constructor(t,n,r,i,a){this.originalStartLineNumber=t,this.originalEndLineNumber=n,this.modifiedStartLineNumber=r,this.modifiedEndLineNumber=i,this.charChanges=a}static createFromDiffResult(t,n,r,i,a,s,u){let c,l,f,h,d;if(n.originalLength===0?(c=r.getStartLineNumber(n.originalStart)-1,l=0):(c=r.getStartLineNumber(n.originalStart),l=r.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(f=i.getStartLineNumber(n.modifiedStart)-1,h=0):(f=i.getStartLineNumber(n.modifiedStart),h=i.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),s&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&a()){const m=r.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),p=i.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);let y=_i(m,p,a,!0).changes;u&&(y=Da(y)),d=[];for(let g=0,w=y.length;g<w;g++)d.push(Rt.createFromDiffChange(y[g],m,p))}return new Vt(c,l,f,h,d)}}class Ka{constructor(t,n,r){this.shouldComputeCharChanges=r.shouldComputeCharChanges,this.shouldPostProcessCharChanges=r.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=r.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=r.shouldMakePrettyDiff,this.originalLines=t,this.modifiedLines=n,this.original=new Si(t),this.modified=new Si(n),this.continueLineDiff=Ai(r.maxComputationTime),this.continueCharDiff=Ai(r.maxComputationTime===0?0:Math.min(r.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};const t=_i(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),n=t.changes,r=t.quitEarly;if(this.shouldIgnoreTrimWhitespace){const u=[];for(let c=0,l=n.length;c<l;c++)u.push(Vt.createFromDiffResult(this.shouldIgnoreTrimWhitespace,n[c],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:r,changes:u}}const i=[];let a=0,s=0;for(let u=-1,c=n.length;u<c;u++){const l=u+1<c?n[u+1]:null,f=l?l.originalStart:this.originalLines.length,h=l?l.modifiedStart:this.modifiedLines.length;for(;a<f&&s<h;){const d=this.originalLines[a],m=this.modifiedLines[s];if(d!==m){{let p=Qn(d,1),y=Qn(m,1);for(;p>1&&y>1;){const g=d.charCodeAt(p-2),w=m.charCodeAt(y-2);if(g!==w)break;p--,y--}(p>1||y>1)&&this._pushTrimWhitespaceCharChange(i,a+1,1,p,s+1,1,y)}{let p=Yn(d,1),y=Yn(m,1);const g=d.length+1,w=m.length+1;for(;p<g&&y<w;){const b=d.charCodeAt(p-1),v=d.charCodeAt(y-1);if(b!==v)break;p++,y++}(p<g||y<w)&&this._pushTrimWhitespaceCharChange(i,a+1,p,g,s+1,y,w)}}a++,s++}l&&(i.push(Vt.createFromDiffResult(this.shouldIgnoreTrimWhitespace,l,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),a+=l.originalLength,s+=l.modifiedLength)}return{quitEarly:r,changes:i}}_pushTrimWhitespaceCharChange(t,n,r,i,a,s,u){if(this._mergeTrimWhitespaceCharChange(t,n,r,i,a,s,u))return;let c;this.shouldComputeCharChanges&&(c=[new Rt(n,r,n,i,a,s,a,u)]),t.push(new Vt(n,n,a,a,c))}_mergeTrimWhitespaceCharChange(t,n,r,i,a,s,u){const c=t.length;if(c===0)return!1;const l=t[c-1];return l.originalEndLineNumber===0||l.modifiedEndLineNumber===0?!1:l.originalEndLineNumber+1===n&&l.modifiedEndLineNumber+1===a?(l.originalEndLineNumber=n,l.modifiedEndLineNumber=a,this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new Rt(n,r,n,i,a,s,a,u)),!0):!1}}function Qn(e,t){const n=ta(e);return n===-1?t:n+1}function Yn(e,t){const n=na(e);return n===-1?t:n+2}function Ai(e){if(e===0)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}function xi(e){return e<0?0:e>255?255:e|0}function vt(e){return e<0?0:e>4294967295?4294967295:e|0}class ja{constructor(t){this.values=t,this.prefixSum=new Uint32Array(t.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(t,n){t=vt(t);const r=this.values,i=this.prefixSum,a=n.length;return a===0?!1:(this.values=new Uint32Array(r.length+a),this.values.set(r.subarray(0,t),0),this.values.set(r.subarray(t),t+a),this.values.set(n,t),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(t,n){return t=vt(t),n=vt(n),this.values[t]===n?!1:(this.values[t]=n,t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),!0)}removeValues(t,n){t=vt(t),n=vt(n);const r=this.values,i=this.prefixSum;if(t>=r.length)return!1;const a=r.length-t;return n>=a&&(n=a),n===0?!1:(this.values=new Uint32Array(r.length-n),this.values.set(r.subarray(0,t),0),this.values.set(r.subarray(t+n),t),this.prefixSum=new Uint32Array(this.values.length),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(t){return t<0?0:(t=vt(t),this._getPrefixSum(t))}_getPrefixSum(t){if(t<=this.prefixSumValidIndex[0])return this.prefixSum[t];let n=this.prefixSumValidIndex[0]+1;n===0&&(this.prefixSum[0]=this.values[0],n++),t>=this.values.length&&(t=this.values.length-1);for(let r=n;r<=t;r++)this.prefixSum[r]=this.prefixSum[r-1]+this.values[r];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],t),this.prefixSum[t]}getIndexOf(t){t=Math.floor(t),this.getTotalSum();let n=0,r=this.values.length-1,i=0,a=0,s=0;for(;n<=r;)if(i=n+(r-n)/2|0,a=this.prefixSum[i],s=a-this.values[i],t<s)r=i-1;else if(t>=a)n=i+1;else break;return new Ei(i,t-s)}}class $c{constructor(t){this._values=t,this._isValid=!1,this._validEndIndex=-1,this._prefixSum=[],this._indexBySum=[]}getTotalSum(){return this._ensureValid(),this._indexBySum.length}getPrefixSum(t){return this._ensureValid(),t===0?0:this._prefixSum[t-1]}getIndexOf(t){this._ensureValid();const n=this._indexBySum[t],r=n>0?this._prefixSum[n-1]:0;return new Ei(n,t-r)}removeValues(t,n){this._values.splice(t,n),this._invalidate(t)}insertValues(t,n){this._values=arrayInsert(this._values,t,n),this._invalidate(t)}_invalidate(t){this._isValid=!1,this._validEndIndex=Math.min(this._validEndIndex,t-1)}_ensureValid(){if(!this._isValid){for(let t=this._validEndIndex+1,n=this._values.length;t<n;t++){const r=this._values[t],i=t>0?this._prefixSum[t-1]:0;this._prefixSum[t]=i+r;for(let a=0;a<r;a++)this._indexBySum[i+a]=t}this._prefixSum.length=this._values.length,this._indexBySum.length=this._prefixSum[this._prefixSum.length-1],this._isValid=!0,this._validEndIndex=this._values.length-1}}setValue(t,n){this._values[t]!==n&&(this._values[t]=n,this._invalidate(t))}}class Ei{constructor(t,n){this.index=t,this.remainder=n,this._prefixSumIndexOfResultBrand=void 0,this.index=t,this.remainder=n}}class Ba{constructor(t,n,r,i){this._uri=t,this._lines=n,this._eol=r,this._versionId=i,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(t){t.eol&&t.eol!==this._eol&&(this._eol=t.eol,this._lineStarts=null);const n=t.changes;for(const r of n)this._acceptDeleteRange(r.range),this._acceptInsertText(new pe(r.range.startLineNumber,r.range.startColumn),r.text);this._versionId=t.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const t=this._eol.length,n=this._lines.length,r=new Uint32Array(n);for(let i=0;i<n;i++)r[i]=this._lines[i].length+t;this._lineStarts=new ja(r)}}_setLineText(t,n){this._lines[t]=n,this._lineStarts&&this._lineStarts.setValue(t,this._lines[t].length+this._eol.length)}_acceptDeleteRange(t){if(t.startLineNumber===t.endLineNumber){if(t.startColumn===t.endColumn)return;this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.startLineNumber-1].substring(t.endColumn-1));return}this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.endLineNumber-1].substring(t.endColumn-1)),this._lines.splice(t.startLineNumber,t.endLineNumber-t.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(t.startLineNumber,t.endLineNumber-t.startLineNumber)}_acceptInsertText(t,n){if(n.length===0)return;const r=ea(n);if(r.length===1){this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+r[0]+this._lines[t.lineNumber-1].substring(t.column-1));return}r[r.length-1]+=this._lines[t.lineNumber-1].substring(t.column-1),this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+r[0]);const i=new Uint32Array(r.length-1);for(let a=1;a<r.length;a++)this._lines.splice(t.lineNumber+a-1,0,r[a]),i[a-1]=r[a].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(t.lineNumber,i)}}const Ua="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function $a(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of Ua)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}const Li=$a();function qa(e){let t=Li;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}const Wa={maxLen:1e3,windowSize:15,timeBudget:150};function Xn(e,t,n,r,i=Wa){if(n.length>i.maxLen){let l=e-i.maxLen/2;return l<0?l=0:r+=l,n=n.substring(l,e+i.maxLen/2),Xn(e,t,n,r,i)}const a=Date.now(),s=e-1-r;let u=-1,c=null;for(let l=1;!(Date.now()-a>=i.timeBudget);l++){const f=s-i.windowSize*l;t.lastIndex=Math.max(0,f);const h=Ha(t,n,s,u);if(!h&&c||(c=h,f<=0))break;u=f}if(c){const l={word:c[0],startColumn:r+1+c.index,endColumn:r+1+c.index+c[0].length};return t.lastIndex=0,l}return null}function Ha(e,t,n,r){let i;for(;i=e.exec(t);){const a=i.index||0;if(a<=n&&e.lastIndex>=n)return i;if(r>0&&a>r)return null}return null}class Dt{constructor(t){const n=xi(t);this._defaultValue=n,this._asciiMap=Dt._createAsciiMap(n),this._map=new Map}static _createAsciiMap(t){const n=new Uint8Array(256);for(let r=0;r<256;r++)n[r]=t;return n}set(t,n){const r=xi(n);t>=0&&t<256?this._asciiMap[t]=r:this._map.set(t,r)}get(t){return t>=0&&t<256?this._asciiMap[t]:this._map.get(t)||this._defaultValue}}class qc{constructor(){this._actual=new Dt(0)}add(t){this._actual.set(t,1)}has(t){return this._actual.get(t)===1}}class za{constructor(t,n,r){const i=new Uint8Array(t*n);for(let a=0,s=t*n;a<s;a++)i[a]=r;this._data=i,this.rows=t,this.cols=n}get(t,n){return this._data[t*this.cols+n]}set(t,n,r){this._data[t*this.cols+n]=r}}class Ga{constructor(t){let n=0,r=0;for(let a=0,s=t.length;a<s;a++){const[u,c,l]=t[a];c>n&&(n=c),u>r&&(r=u),l>r&&(r=l)}n++,r++;const i=new za(r,n,0);for(let a=0,s=t.length;a<s;a++){const[u,c,l]=t[a];i.set(u,c,l)}this._states=i,this._maxCharCode=n}nextState(t,n){return n<0||n>=this._maxCharCode?0:this._states.get(t,n)}}let tr=null;function Ja(){return tr===null&&(tr=new Ga([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),tr}let Kt=null;function Za(){if(Kt===null){Kt=new Dt(0);const e=` 	<>'"\u3001\u3002\uFF61\uFF64\uFF0C\uFF0E\uFF1A\uFF1B\u2018\u3008\u300C\u300E\u3014\uFF08\uFF3B\uFF5B\uFF62\uFF63\uFF5D\uFF3D\uFF09\u3015\u300F\u300D\u3009\u2019\uFF40\uFF5E\u2026`;for(let n=0;n<e.length;n++)Kt.set(e.charCodeAt(n),1);const t=".,;";for(let n=0;n<t.length;n++)Kt.set(t.charCodeAt(n),2)}return Kt}class fn{static _createLink(t,n,r,i,a){let s=a-1;do{const u=n.charCodeAt(s);if(t.get(u)!==2)break;s--}while(s>i);if(i>0){const u=n.charCodeAt(i-1),c=n.charCodeAt(s);(u===40&&c===41||u===91&&c===93||u===123&&c===125)&&s--}return{range:{startLineNumber:r,startColumn:i+1,endLineNumber:r,endColumn:s+2},url:n.substring(i,s+1)}}static computeLinks(t,n=Ja()){const r=Za(),i=[];for(let a=1,s=t.getLineCount();a<=s;a++){const u=t.getLineContent(a),c=u.length;let l=0,f=0,h=0,d=1,m=!1,p=!1,y=!1,g=!1;for(;l<c;){let w=!1;const b=u.charCodeAt(l);if(d===13){let v;switch(b){case 40:m=!0,v=0;break;case 41:v=m?0:1;break;case 91:y=!0,p=!0,v=0;break;case 93:y=!1,v=p?0:1;break;case 123:g=!0,v=0;break;case 125:v=g?0:1;break;case 39:v=h===34||h===96?0:1;break;case 34:v=h===39||h===96?0:1;break;case 96:v=h===39||h===34?0:1;break;case 42:v=h===42?1:0;break;case 124:v=h===124?1:0;break;case 32:v=y?0:1;break;default:v=r.get(b)}v===1&&(i.push(fn._createLink(r,u,a,f,l)),w=!0)}else if(d===12){let v;b===91?(p=!0,v=0):v=r.get(b),v===1?w=!0:d=13}else d=n.nextState(d,b),d===0&&(w=!0);w&&(d=1,m=!1,p=!1,g=!1,f=l+1,h=b),l++}d===13&&i.push(fn._createLink(r,u,a,f,c))}return i}}function Qa(e){return!e||typeof e.getLineCount!="function"||typeof e.getLineContent!="function"?[]:fn.computeLinks(e)}class nr{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(t,n,r,i,a){if(t&&n){const s=this.doNavigateValueSet(n,a);if(s)return{range:t,value:s}}if(r&&i){const s=this.doNavigateValueSet(i,a);if(s)return{range:r,value:s}}return null}doNavigateValueSet(t,n){const r=this.numberReplace(t,n);return r!==null?r:this.textReplace(t,n)}numberReplace(t,n){const r=Math.pow(10,t.length-(t.lastIndexOf(".")+1));let i=Number(t),a=parseFloat(t);return!isNaN(i)&&!isNaN(a)&&i===a?i===0&&!n?null:(i=Math.floor(i*r),i+=n?r:-r,String(i/r)):null}textReplace(t,n){return this.valueSetsReplace(this._defaultValueSet,t,n)}valueSetsReplace(t,n,r){let i=null;for(let a=0,s=t.length;i===null&&a<s;a++)i=this.valueSetReplace(t[a],n,r);return i}valueSetReplace(t,n,r){let i=t.indexOf(n);return i>=0?(i+=r?1:-1,i<0?i=t.length-1:i%=t.length,t[i]):null}}nr.INSTANCE=new nr;const Ni=Object.freeze(function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}});var dn;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof mn?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:sn.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Ni})})(dn||(dn={}));class mn{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Ni:(this._emitter||(this._emitter=new Fe),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class Ya{constructor(t){this._token=void 0,this._parentListener=void 0,this._parentListener=t&&t.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new mn),this._token}cancel(){this._token?this._token instanceof mn&&this._token.cancel():this._token=dn.Cancelled}dispose(t=!1){t&&this.cancel(),this._parentListener&&this._parentListener.dispose(),this._token?this._token instanceof mn&&this._token.dispose():this._token=dn.None}}class rr{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(t,n){this._keyCodeToStr[t]=n,this._strToKeyCode[n.toLowerCase()]=t}keyCodeToStr(t){return this._keyCodeToStr[t]}strToKeyCode(t){return this._strToKeyCode[t.toLowerCase()]||0}}const gn=new rr,ir=new rr,or=new rr,Xa=new Array(230),es={},ts=[],ns=Object.create(null),rs=Object.create(null),ki=[],ar=[];for(let e=0;e<=193;e++)ki[e]=-1;for(let e=0;e<=127;e++)ar[e]=-1;(function(){const e="",t=[[0,1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[0,1,1,"Hyper",0,e,0,e,e,e],[0,1,2,"Super",0,e,0,e,e,e],[0,1,3,"Fn",0,e,0,e,e,e],[0,1,4,"FnLock",0,e,0,e,e,e],[0,1,5,"Suspend",0,e,0,e,e,e],[0,1,6,"Resume",0,e,0,e,e,e],[0,1,7,"Turbo",0,e,0,e,e,e],[0,1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[0,1,9,"WakeUp",0,e,0,e,e,e],[31,0,10,"KeyA",31,"A",65,"VK_A",e,e],[32,0,11,"KeyB",32,"B",66,"VK_B",e,e],[33,0,12,"KeyC",33,"C",67,"VK_C",e,e],[34,0,13,"KeyD",34,"D",68,"VK_D",e,e],[35,0,14,"KeyE",35,"E",69,"VK_E",e,e],[36,0,15,"KeyF",36,"F",70,"VK_F",e,e],[37,0,16,"KeyG",37,"G",71,"VK_G",e,e],[38,0,17,"KeyH",38,"H",72,"VK_H",e,e],[39,0,18,"KeyI",39,"I",73,"VK_I",e,e],[40,0,19,"KeyJ",40,"J",74,"VK_J",e,e],[41,0,20,"KeyK",41,"K",75,"VK_K",e,e],[42,0,21,"KeyL",42,"L",76,"VK_L",e,e],[43,0,22,"KeyM",43,"M",77,"VK_M",e,e],[44,0,23,"KeyN",44,"N",78,"VK_N",e,e],[45,0,24,"KeyO",45,"O",79,"VK_O",e,e],[46,0,25,"KeyP",46,"P",80,"VK_P",e,e],[47,0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[48,0,27,"KeyR",48,"R",82,"VK_R",e,e],[49,0,28,"KeyS",49,"S",83,"VK_S",e,e],[50,0,29,"KeyT",50,"T",84,"VK_T",e,e],[51,0,30,"KeyU",51,"U",85,"VK_U",e,e],[52,0,31,"KeyV",52,"V",86,"VK_V",e,e],[53,0,32,"KeyW",53,"W",87,"VK_W",e,e],[54,0,33,"KeyX",54,"X",88,"VK_X",e,e],[55,0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[56,0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[22,0,36,"Digit1",22,"1",49,"VK_1",e,e],[23,0,37,"Digit2",23,"2",50,"VK_2",e,e],[24,0,38,"Digit3",24,"3",51,"VK_3",e,e],[25,0,39,"Digit4",25,"4",52,"VK_4",e,e],[26,0,40,"Digit5",26,"5",53,"VK_5",e,e],[27,0,41,"Digit6",27,"6",54,"VK_6",e,e],[28,0,42,"Digit7",28,"7",55,"VK_7",e,e],[29,0,43,"Digit8",29,"8",56,"VK_8",e,e],[30,0,44,"Digit9",30,"9",57,"VK_9",e,e],[21,0,45,"Digit0",21,"0",48,"VK_0",e,e],[3,1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[9,1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[2,1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[10,1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[83,0,51,"Minus",83,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[81,0,52,"Equal",81,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[87,0,53,"BracketLeft",87,"[",219,"VK_OEM_4","[","OEM_4"],[89,0,54,"BracketRight",89,"]",221,"VK_OEM_6","]","OEM_6"],[88,0,55,"Backslash",88,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,0,56,"IntlHash",0,e,0,e,e,e],[80,0,57,"Semicolon",80,";",186,"VK_OEM_1",";","OEM_1"],[90,0,58,"Quote",90,"'",222,"VK_OEM_7","'","OEM_7"],[86,0,59,"Backquote",86,"`",192,"VK_OEM_3","`","OEM_3"],[82,0,60,"Comma",82,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[84,0,61,"Period",84,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[85,0,62,"Slash",85,"/",191,"VK_OEM_2","/","OEM_2"],[8,1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[59,1,64,"F1",59,"F1",112,"VK_F1",e,e],[60,1,65,"F2",60,"F2",113,"VK_F2",e,e],[61,1,66,"F3",61,"F3",114,"VK_F3",e,e],[62,1,67,"F4",62,"F4",115,"VK_F4",e,e],[63,1,68,"F5",63,"F5",116,"VK_F5",e,e],[64,1,69,"F6",64,"F6",117,"VK_F6",e,e],[65,1,70,"F7",65,"F7",118,"VK_F7",e,e],[66,1,71,"F8",66,"F8",119,"VK_F8",e,e],[67,1,72,"F9",67,"F9",120,"VK_F9",e,e],[68,1,73,"F10",68,"F10",121,"VK_F10",e,e],[69,1,74,"F11",69,"F11",122,"VK_F11",e,e],[70,1,75,"F12",70,"F12",123,"VK_F12",e,e],[0,1,76,"PrintScreen",0,e,0,e,e,e],[79,1,77,"ScrollLock",79,"ScrollLock",145,"VK_SCROLL",e,e],[7,1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[19,1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[14,1,80,"Home",14,"Home",36,"VK_HOME",e,e],[11,1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[20,1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[13,1,83,"End",13,"End",35,"VK_END",e,e],[12,1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[17,1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[15,1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[18,1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[16,1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[78,1,89,"NumLock",78,"NumLock",144,"VK_NUMLOCK",e,e],[108,1,90,"NumpadDivide",108,"NumPad_Divide",111,"VK_DIVIDE",e,e],[103,1,91,"NumpadMultiply",103,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[106,1,92,"NumpadSubtract",106,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[104,1,93,"NumpadAdd",104,"NumPad_Add",107,"VK_ADD",e,e],[3,1,94,"NumpadEnter",3,e,0,e,e,e],[94,1,95,"Numpad1",94,"NumPad1",97,"VK_NUMPAD1",e,e],[95,1,96,"Numpad2",95,"NumPad2",98,"VK_NUMPAD2",e,e],[96,1,97,"Numpad3",96,"NumPad3",99,"VK_NUMPAD3",e,e],[97,1,98,"Numpad4",97,"NumPad4",100,"VK_NUMPAD4",e,e],[98,1,99,"Numpad5",98,"NumPad5",101,"VK_NUMPAD5",e,e],[99,1,100,"Numpad6",99,"NumPad6",102,"VK_NUMPAD6",e,e],[100,1,101,"Numpad7",100,"NumPad7",103,"VK_NUMPAD7",e,e],[101,1,102,"Numpad8",101,"NumPad8",104,"VK_NUMPAD8",e,e],[102,1,103,"Numpad9",102,"NumPad9",105,"VK_NUMPAD9",e,e],[93,1,104,"Numpad0",93,"NumPad0",96,"VK_NUMPAD0",e,e],[107,1,105,"NumpadDecimal",107,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[92,0,106,"IntlBackslash",92,"OEM_102",226,"VK_OEM_102",e,e],[58,1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[0,1,108,"Power",0,e,0,e,e,e],[0,1,109,"NumpadEqual",0,e,0,e,e,e],[71,1,110,"F13",71,"F13",124,"VK_F13",e,e],[72,1,111,"F14",72,"F14",125,"VK_F14",e,e],[73,1,112,"F15",73,"F15",126,"VK_F15",e,e],[74,1,113,"F16",74,"F16",127,"VK_F16",e,e],[75,1,114,"F17",75,"F17",128,"VK_F17",e,e],[76,1,115,"F18",76,"F18",129,"VK_F18",e,e],[77,1,116,"F19",77,"F19",130,"VK_F19",e,e],[0,1,117,"F20",0,e,0,"VK_F20",e,e],[0,1,118,"F21",0,e,0,"VK_F21",e,e],[0,1,119,"F22",0,e,0,"VK_F22",e,e],[0,1,120,"F23",0,e,0,"VK_F23",e,e],[0,1,121,"F24",0,e,0,"VK_F24",e,e],[0,1,122,"Open",0,e,0,e,e,e],[0,1,123,"Help",0,e,0,e,e,e],[0,1,124,"Select",0,e,0,e,e,e],[0,1,125,"Again",0,e,0,e,e,e],[0,1,126,"Undo",0,e,0,e,e,e],[0,1,127,"Cut",0,e,0,e,e,e],[0,1,128,"Copy",0,e,0,e,e,e],[0,1,129,"Paste",0,e,0,e,e,e],[0,1,130,"Find",0,e,0,e,e,e],[0,1,131,"AudioVolumeMute",112,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[0,1,132,"AudioVolumeUp",113,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[0,1,133,"AudioVolumeDown",114,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[105,1,134,"NumpadComma",105,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[110,0,135,"IntlRo",110,"ABNT_C1",193,"VK_ABNT_C1",e,e],[0,1,136,"KanaMode",0,e,0,e,e,e],[0,0,137,"IntlYen",0,e,0,e,e,e],[0,1,138,"Convert",0,e,0,e,e,e],[0,1,139,"NonConvert",0,e,0,e,e,e],[0,1,140,"Lang1",0,e,0,e,e,e],[0,1,141,"Lang2",0,e,0,e,e,e],[0,1,142,"Lang3",0,e,0,e,e,e],[0,1,143,"Lang4",0,e,0,e,e,e],[0,1,144,"Lang5",0,e,0,e,e,e],[0,1,145,"Abort",0,e,0,e,e,e],[0,1,146,"Props",0,e,0,e,e,e],[0,1,147,"NumpadParenLeft",0,e,0,e,e,e],[0,1,148,"NumpadParenRight",0,e,0,e,e,e],[0,1,149,"NumpadBackspace",0,e,0,e,e,e],[0,1,150,"NumpadMemoryStore",0,e,0,e,e,e],[0,1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[0,1,152,"NumpadMemoryClear",0,e,0,e,e,e],[0,1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[0,1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[0,1,155,"NumpadClear",126,"Clear",12,"VK_CLEAR",e,e],[0,1,156,"NumpadClearEntry",0,e,0,e,e,e],[5,1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[4,1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[6,1,0,e,6,"Alt",18,"VK_MENU",e,e],[57,1,0,e,57,"Meta",0,"VK_COMMAND",e,e],[5,1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[4,1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[6,1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[57,1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[5,1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[4,1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[6,1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[57,1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[0,1,165,"BrightnessUp",0,e,0,e,e,e],[0,1,166,"BrightnessDown",0,e,0,e,e,e],[0,1,167,"MediaPlay",0,e,0,e,e,e],[0,1,168,"MediaRecord",0,e,0,e,e,e],[0,1,169,"MediaFastForward",0,e,0,e,e,e],[0,1,170,"MediaRewind",0,e,0,e,e,e],[114,1,171,"MediaTrackNext",119,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[115,1,172,"MediaTrackPrevious",120,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[116,1,173,"MediaStop",121,"MediaStop",178,"VK_MEDIA_STOP",e,e],[0,1,174,"Eject",0,e,0,e,e,e],[117,1,175,"MediaPlayPause",122,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[0,1,176,"MediaSelect",123,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[0,1,177,"LaunchMail",124,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[0,1,178,"LaunchApp2",125,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[0,1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[0,1,180,"SelectTask",0,e,0,e,e,e],[0,1,181,"LaunchScreenSaver",0,e,0,e,e,e],[0,1,182,"BrowserSearch",115,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[0,1,183,"BrowserHome",116,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[112,1,184,"BrowserBack",117,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[113,1,185,"BrowserForward",118,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[0,1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[0,1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[0,1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[0,1,189,"ZoomToggle",0,e,0,e,e,e],[0,1,190,"MailReply",0,e,0,e,e,e],[0,1,191,"MailForward",0,e,0,e,e,e],[0,1,192,"MailSend",0,e,0,e,e,e],[109,1,0,e,109,"KeyInComposition",229,e,e,e],[111,1,0,e,111,"ABNT_C2",194,"VK_ABNT_C2",e,e],[91,1,0,e,91,"OEM_8",223,"VK_OEM_8",e,e],[0,1,0,e,0,e,0,"VK_KANA",e,e],[0,1,0,e,0,e,0,"VK_HANGUL",e,e],[0,1,0,e,0,e,0,"VK_JUNJA",e,e],[0,1,0,e,0,e,0,"VK_FINAL",e,e],[0,1,0,e,0,e,0,"VK_HANJA",e,e],[0,1,0,e,0,e,0,"VK_KANJI",e,e],[0,1,0,e,0,e,0,"VK_CONVERT",e,e],[0,1,0,e,0,e,0,"VK_NONCONVERT",e,e],[0,1,0,e,0,e,0,"VK_ACCEPT",e,e],[0,1,0,e,0,e,0,"VK_MODECHANGE",e,e],[0,1,0,e,0,e,0,"VK_SELECT",e,e],[0,1,0,e,0,e,0,"VK_PRINT",e,e],[0,1,0,e,0,e,0,"VK_EXECUTE",e,e],[0,1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[0,1,0,e,0,e,0,"VK_HELP",e,e],[0,1,0,e,0,e,0,"VK_APPS",e,e],[0,1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[0,1,0,e,0,e,0,"VK_PACKET",e,e],[0,1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[0,1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[0,1,0,e,0,e,0,"VK_ATTN",e,e],[0,1,0,e,0,e,0,"VK_CRSEL",e,e],[0,1,0,e,0,e,0,"VK_EXSEL",e,e],[0,1,0,e,0,e,0,"VK_EREOF",e,e],[0,1,0,e,0,e,0,"VK_PLAY",e,e],[0,1,0,e,0,e,0,"VK_ZOOM",e,e],[0,1,0,e,0,e,0,"VK_NONAME",e,e],[0,1,0,e,0,e,0,"VK_PA1",e,e],[0,1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]];let n=[],r=[];for(const i of t){const[a,s,u,c,l,f,h,d,m,p]=i;if(r[u]||(r[u]=!0,ts[u]=c,ns[c]=u,rs[c.toLowerCase()]=u,s&&(ki[u]=l,l!==0&&l!==3&&l!==5&&l!==4&&l!==6&&l!==57&&(ar[l]=u))),!n[l]){if(n[l]=!0,!f)throw new Error(`String representation missing for key code ${l} around scan code ${c}`);gn.define(l,f),ir.define(l,m||f),or.define(l,p||m||f)}h&&(Xa[h]=l),d&&(es[d]=l)}ar[3]=46})();var Oi;(function(e){function t(u){return gn.keyCodeToStr(u)}e.toString=t;function n(u){return gn.strToKeyCode(u)}e.fromString=n;function r(u){return ir.keyCodeToStr(u)}e.toUserSettingsUS=r;function i(u){return or.keyCodeToStr(u)}e.toUserSettingsGeneral=i;function a(u){return ir.strToKeyCode(u)||or.strToKeyCode(u)}e.fromUserSettings=a;function s(u){if(u>=93&&u<=108)return null;switch(u){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return gn.keyCodeToStr(u)}e.toElectronAccelerator=s})(Oi||(Oi={}));function is(e,t){const n=(t&65535)<<16>>>0;return(e|n)>>>0}class Ce extends J{constructor(t,n,r,i){super(t,n,r,i);this.selectionStartLineNumber=t,this.selectionStartColumn=n,this.positionLineNumber=r,this.positionColumn=i}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(t){return Ce.selectionsEqual(this,t)}static selectionsEqual(t,n){return t.selectionStartLineNumber===n.selectionStartLineNumber&&t.selectionStartColumn===n.selectionStartColumn&&t.positionLineNumber===n.positionLineNumber&&t.positionColumn===n.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new Ce(this.startLineNumber,this.startColumn,t,n):new Ce(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new pe(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new pe(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new Ce(t,n,this.endLineNumber,this.endColumn):new Ce(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new Ce(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new Ce(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new Ce(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new Ce(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(t,n){if(t&&!n||!t&&n)return!1;if(!t&&!n)return!0;if(t.length!==n.length)return!1;for(let r=0,i=t.length;r<i;r++)if(!this.selectionsEqual(t[r],n[r]))return!1;return!0}static isISelection(t){return t&&typeof t.selectionStartLineNumber=="number"&&typeof t.selectionStartColumn=="number"&&typeof t.positionLineNumber=="number"&&typeof t.positionColumn=="number"}static createWithDirection(t,n,r,i,a){return a===0?new Ce(t,n,r,i):new Ce(r,i,t,n)}}var sr=function(e,t,n,r){function i(a){return a instanceof n?a:new n(function(s){s(a)})}return new(n||(n=Promise))(function(a,s){function u(f){try{l(r.next(f))}catch(h){s(h)}}function c(f){try{l(r.throw(f))}catch(h){s(h)}}function l(f){f.done?a(f.value):i(f.value).then(u,c)}l((r=r.apply(e,t||[])).next())})};class os{constructor(){this._map=new Map,this._factories=new Map,this._onDidChange=new Fe,this.onDidChange=this._onDidChange.event,this._colorMap=null}fire(t){this._onDidChange.fire({changedLanguages:t,changedColorMap:!1})}register(t,n){return this._map.set(t,n),this.fire([t]),nn(()=>{this._map.get(t)===n&&(this._map.delete(t),this.fire([t]))})}registerFactory(t,n){var r;(r=this._factories.get(t))===null||r===void 0||r.dispose();const i=new as(this,t,n);return this._factories.set(t,i),nn(()=>{const a=this._factories.get(t);!a||a!==i||(this._factories.delete(t),a.dispose())})}getOrCreate(t){return sr(this,void 0,void 0,function*(){const n=this.get(t);if(n)return n;const r=this._factories.get(t);return!r||r.isResolved?null:(yield r.resolve(),this.get(t))})}get(t){return this._map.get(t)||null}isResolved(t){if(this.get(t))return!0;const n=this._factories.get(t);return!!(!n||n.isResolved)}setColorMap(t){this._colorMap=t,this._onDidChange.fire({changedLanguages:Array.from(this._map.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}}class as extends Nt{constructor(t,n,r){super();this._registry=t,this._languageId=n,this._factory=r,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}get isResolved(){return this._isResolved}dispose(){this._isDisposed=!0,super.dispose()}resolve(){return sr(this,void 0,void 0,function*(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise})}_create(){return sr(this,void 0,void 0,function*(){const t=yield Promise.resolve(this._factory.createTokenizationSupport());this._isResolved=!0,t&&!this._isDisposed&&this._register(this._registry.register(this._languageId,t))})}}function Wc(e){return e?e.replace(/\$\((.*?)\)/g,(t,n)=>` ${n} `).trim():""}class o{constructor(t,n,r){this.id=t,this.definition=n,this.description=r,o._allCodicons.push(this)}get classNames(){return"codicon codicon-"+this.id}get classNamesArray(){return["codicon","codicon-"+this.id]}get cssSelector(){return".codicon.codicon-"+this.id}static getAll(){return o._allCodicons}}o._allCodicons=[],o.add=new o("add",{fontCharacter:"\\ea60"}),o.plus=new o("plus",o.add.definition),o.gistNew=new o("gist-new",o.add.definition),o.repoCreate=new o("repo-create",o.add.definition),o.lightbulb=new o("lightbulb",{fontCharacter:"\\ea61"}),o.lightBulb=new o("light-bulb",{fontCharacter:"\\ea61"}),o.repo=new o("repo",{fontCharacter:"\\ea62"}),o.repoDelete=new o("repo-delete",{fontCharacter:"\\ea62"}),o.gistFork=new o("gist-fork",{fontCharacter:"\\ea63"}),o.repoForked=new o("repo-forked",{fontCharacter:"\\ea63"}),o.gitPullRequest=new o("git-pull-request",{fontCharacter:"\\ea64"}),o.gitPullRequestAbandoned=new o("git-pull-request-abandoned",{fontCharacter:"\\ea64"}),o.recordKeys=new o("record-keys",{fontCharacter:"\\ea65"}),o.keyboard=new o("keyboard",{fontCharacter:"\\ea65"}),o.tag=new o("tag",{fontCharacter:"\\ea66"}),o.tagAdd=new o("tag-add",{fontCharacter:"\\ea66"}),o.tagRemove=new o("tag-remove",{fontCharacter:"\\ea66"}),o.person=new o("person",{fontCharacter:"\\ea67"}),o.personFollow=new o("person-follow",{fontCharacter:"\\ea67"}),o.personOutline=new o("person-outline",{fontCharacter:"\\ea67"}),o.personFilled=new o("person-filled",{fontCharacter:"\\ea67"}),o.gitBranch=new o("git-branch",{fontCharacter:"\\ea68"}),o.gitBranchCreate=new o("git-branch-create",{fontCharacter:"\\ea68"}),o.gitBranchDelete=new o("git-branch-delete",{fontCharacter:"\\ea68"}),o.sourceControl=new o("source-control",{fontCharacter:"\\ea68"}),o.mirror=new o("mirror",{fontCharacter:"\\ea69"}),o.mirrorPublic=new o("mirror-public",{fontCharacter:"\\ea69"}),o.star=new o("star",{fontCharacter:"\\ea6a"}),o.starAdd=new o("star-add",{fontCharacter:"\\ea6a"}),o.starDelete=new o("star-delete",{fontCharacter:"\\ea6a"}),o.starEmpty=new o("star-empty",{fontCharacter:"\\ea6a"}),o.comment=new o("comment",{fontCharacter:"\\ea6b"}),o.commentAdd=new o("comment-add",{fontCharacter:"\\ea6b"}),o.alert=new o("alert",{fontCharacter:"\\ea6c"}),o.warning=new o("warning",{fontCharacter:"\\ea6c"}),o.search=new o("search",{fontCharacter:"\\ea6d"}),o.searchSave=new o("search-save",{fontCharacter:"\\ea6d"}),o.logOut=new o("log-out",{fontCharacter:"\\ea6e"}),o.signOut=new o("sign-out",{fontCharacter:"\\ea6e"}),o.logIn=new o("log-in",{fontCharacter:"\\ea6f"}),o.signIn=new o("sign-in",{fontCharacter:"\\ea6f"}),o.eye=new o("eye",{fontCharacter:"\\ea70"}),o.eyeUnwatch=new o("eye-unwatch",{fontCharacter:"\\ea70"}),o.eyeWatch=new o("eye-watch",{fontCharacter:"\\ea70"}),o.circleFilled=new o("circle-filled",{fontCharacter:"\\ea71"}),o.primitiveDot=new o("primitive-dot",{fontCharacter:"\\ea71"}),o.closeDirty=new o("close-dirty",{fontCharacter:"\\ea71"}),o.debugBreakpoint=new o("debug-breakpoint",{fontCharacter:"\\ea71"}),o.debugBreakpointDisabled=new o("debug-breakpoint-disabled",{fontCharacter:"\\ea71"}),o.debugHint=new o("debug-hint",{fontCharacter:"\\ea71"}),o.primitiveSquare=new o("primitive-square",{fontCharacter:"\\ea72"}),o.edit=new o("edit",{fontCharacter:"\\ea73"}),o.pencil=new o("pencil",{fontCharacter:"\\ea73"}),o.info=new o("info",{fontCharacter:"\\ea74"}),o.issueOpened=new o("issue-opened",{fontCharacter:"\\ea74"}),o.gistPrivate=new o("gist-private",{fontCharacter:"\\ea75"}),o.gitForkPrivate=new o("git-fork-private",{fontCharacter:"\\ea75"}),o.lock=new o("lock",{fontCharacter:"\\ea75"}),o.mirrorPrivate=new o("mirror-private",{fontCharacter:"\\ea75"}),o.close=new o("close",{fontCharacter:"\\ea76"}),o.removeClose=new o("remove-close",{fontCharacter:"\\ea76"}),o.x=new o("x",{fontCharacter:"\\ea76"}),o.repoSync=new o("repo-sync",{fontCharacter:"\\ea77"}),o.sync=new o("sync",{fontCharacter:"\\ea77"}),o.clone=new o("clone",{fontCharacter:"\\ea78"}),o.desktopDownload=new o("desktop-download",{fontCharacter:"\\ea78"}),o.beaker=new o("beaker",{fontCharacter:"\\ea79"}),o.microscope=new o("microscope",{fontCharacter:"\\ea79"}),o.vm=new o("vm",{fontCharacter:"\\ea7a"}),o.deviceDesktop=new o("device-desktop",{fontCharacter:"\\ea7a"}),o.file=new o("file",{fontCharacter:"\\ea7b"}),o.fileText=new o("file-text",{fontCharacter:"\\ea7b"}),o.more=new o("more",{fontCharacter:"\\ea7c"}),o.ellipsis=new o("ellipsis",{fontCharacter:"\\ea7c"}),o.kebabHorizontal=new o("kebab-horizontal",{fontCharacter:"\\ea7c"}),o.mailReply=new o("mail-reply",{fontCharacter:"\\ea7d"}),o.reply=new o("reply",{fontCharacter:"\\ea7d"}),o.organization=new o("organization",{fontCharacter:"\\ea7e"}),o.organizationFilled=new o("organization-filled",{fontCharacter:"\\ea7e"}),o.organizationOutline=new o("organization-outline",{fontCharacter:"\\ea7e"}),o.newFile=new o("new-file",{fontCharacter:"\\ea7f"}),o.fileAdd=new o("file-add",{fontCharacter:"\\ea7f"}),o.newFolder=new o("new-folder",{fontCharacter:"\\ea80"}),o.fileDirectoryCreate=new o("file-directory-create",{fontCharacter:"\\ea80"}),o.trash=new o("trash",{fontCharacter:"\\ea81"}),o.trashcan=new o("trashcan",{fontCharacter:"\\ea81"}),o.history=new o("history",{fontCharacter:"\\ea82"}),o.clock=new o("clock",{fontCharacter:"\\ea82"}),o.folder=new o("folder",{fontCharacter:"\\ea83"}),o.fileDirectory=new o("file-directory",{fontCharacter:"\\ea83"}),o.symbolFolder=new o("symbol-folder",{fontCharacter:"\\ea83"}),o.logoGithub=new o("logo-github",{fontCharacter:"\\ea84"}),o.markGithub=new o("mark-github",{fontCharacter:"\\ea84"}),o.github=new o("github",{fontCharacter:"\\ea84"}),o.terminal=new o("terminal",{fontCharacter:"\\ea85"}),o.console=new o("console",{fontCharacter:"\\ea85"}),o.repl=new o("repl",{fontCharacter:"\\ea85"}),o.zap=new o("zap",{fontCharacter:"\\ea86"}),o.symbolEvent=new o("symbol-event",{fontCharacter:"\\ea86"}),o.error=new o("error",{fontCharacter:"\\ea87"}),o.stop=new o("stop",{fontCharacter:"\\ea87"}),o.variable=new o("variable",{fontCharacter:"\\ea88"}),o.symbolVariable=new o("symbol-variable",{fontCharacter:"\\ea88"}),o.array=new o("array",{fontCharacter:"\\ea8a"}),o.symbolArray=new o("symbol-array",{fontCharacter:"\\ea8a"}),o.symbolModule=new o("symbol-module",{fontCharacter:"\\ea8b"}),o.symbolPackage=new o("symbol-package",{fontCharacter:"\\ea8b"}),o.symbolNamespace=new o("symbol-namespace",{fontCharacter:"\\ea8b"}),o.symbolObject=new o("symbol-object",{fontCharacter:"\\ea8b"}),o.symbolMethod=new o("symbol-method",{fontCharacter:"\\ea8c"}),o.symbolFunction=new o("symbol-function",{fontCharacter:"\\ea8c"}),o.symbolConstructor=new o("symbol-constructor",{fontCharacter:"\\ea8c"}),o.symbolBoolean=new o("symbol-boolean",{fontCharacter:"\\ea8f"}),o.symbolNull=new o("symbol-null",{fontCharacter:"\\ea8f"}),o.symbolNumeric=new o("symbol-numeric",{fontCharacter:"\\ea90"}),o.symbolNumber=new o("symbol-number",{fontCharacter:"\\ea90"}),o.symbolStructure=new o("symbol-structure",{fontCharacter:"\\ea91"}),o.symbolStruct=new o("symbol-struct",{fontCharacter:"\\ea91"}),o.symbolParameter=new o("symbol-parameter",{fontCharacter:"\\ea92"}),o.symbolTypeParameter=new o("symbol-type-parameter",{fontCharacter:"\\ea92"}),o.symbolKey=new o("symbol-key",{fontCharacter:"\\ea93"}),o.symbolText=new o("symbol-text",{fontCharacter:"\\ea93"}),o.symbolReference=new o("symbol-reference",{fontCharacter:"\\ea94"}),o.goToFile=new o("go-to-file",{fontCharacter:"\\ea94"}),o.symbolEnum=new o("symbol-enum",{fontCharacter:"\\ea95"}),o.symbolValue=new o("symbol-value",{fontCharacter:"\\ea95"}),o.symbolRuler=new o("symbol-ruler",{fontCharacter:"\\ea96"}),o.symbolUnit=new o("symbol-unit",{fontCharacter:"\\ea96"}),o.activateBreakpoints=new o("activate-breakpoints",{fontCharacter:"\\ea97"}),o.archive=new o("archive",{fontCharacter:"\\ea98"}),o.arrowBoth=new o("arrow-both",{fontCharacter:"\\ea99"}),o.arrowDown=new o("arrow-down",{fontCharacter:"\\ea9a"}),o.arrowLeft=new o("arrow-left",{fontCharacter:"\\ea9b"}),o.arrowRight=new o("arrow-right",{fontCharacter:"\\ea9c"}),o.arrowSmallDown=new o("arrow-small-down",{fontCharacter:"\\ea9d"}),o.arrowSmallLeft=new o("arrow-small-left",{fontCharacter:"\\ea9e"}),o.arrowSmallRight=new o("arrow-small-right",{fontCharacter:"\\ea9f"}),o.arrowSmallUp=new o("arrow-small-up",{fontCharacter:"\\eaa0"}),o.arrowUp=new o("arrow-up",{fontCharacter:"\\eaa1"}),o.bell=new o("bell",{fontCharacter:"\\eaa2"}),o.bold=new o("bold",{fontCharacter:"\\eaa3"}),o.book=new o("book",{fontCharacter:"\\eaa4"}),o.bookmark=new o("bookmark",{fontCharacter:"\\eaa5"}),o.debugBreakpointConditionalUnverified=new o("debug-breakpoint-conditional-unverified",{fontCharacter:"\\eaa6"}),o.debugBreakpointConditional=new o("debug-breakpoint-conditional",{fontCharacter:"\\eaa7"}),o.debugBreakpointConditionalDisabled=new o("debug-breakpoint-conditional-disabled",{fontCharacter:"\\eaa7"}),o.debugBreakpointDataUnverified=new o("debug-breakpoint-data-unverified",{fontCharacter:"\\eaa8"}),o.debugBreakpointData=new o("debug-breakpoint-data",{fontCharacter:"\\eaa9"}),o.debugBreakpointDataDisabled=new o("debug-breakpoint-data-disabled",{fontCharacter:"\\eaa9"}),o.debugBreakpointLogUnverified=new o("debug-breakpoint-log-unverified",{fontCharacter:"\\eaaa"}),o.debugBreakpointLog=new o("debug-breakpoint-log",{fontCharacter:"\\eaab"}),o.debugBreakpointLogDisabled=new o("debug-breakpoint-log-disabled",{fontCharacter:"\\eaab"}),o.briefcase=new o("briefcase",{fontCharacter:"\\eaac"}),o.broadcast=new o("broadcast",{fontCharacter:"\\eaad"}),o.browser=new o("browser",{fontCharacter:"\\eaae"}),o.bug=new o("bug",{fontCharacter:"\\eaaf"}),o.calendar=new o("calendar",{fontCharacter:"\\eab0"}),o.caseSensitive=new o("case-sensitive",{fontCharacter:"\\eab1"}),o.check=new o("check",{fontCharacter:"\\eab2"}),o.checklist=new o("checklist",{fontCharacter:"\\eab3"}),o.chevronDown=new o("chevron-down",{fontCharacter:"\\eab4"}),o.dropDownButton=new o("drop-down-button",o.chevronDown.definition),o.chevronLeft=new o("chevron-left",{fontCharacter:"\\eab5"}),o.chevronRight=new o("chevron-right",{fontCharacter:"\\eab6"}),o.chevronUp=new o("chevron-up",{fontCharacter:"\\eab7"}),o.chromeClose=new o("chrome-close",{fontCharacter:"\\eab8"}),o.chromeMaximize=new o("chrome-maximize",{fontCharacter:"\\eab9"}),o.chromeMinimize=new o("chrome-minimize",{fontCharacter:"\\eaba"}),o.chromeRestore=new o("chrome-restore",{fontCharacter:"\\eabb"}),o.circleOutline=new o("circle-outline",{fontCharacter:"\\eabc"}),o.debugBreakpointUnverified=new o("debug-breakpoint-unverified",{fontCharacter:"\\eabc"}),o.circleSlash=new o("circle-slash",{fontCharacter:"\\eabd"}),o.circuitBoard=new o("circuit-board",{fontCharacter:"\\eabe"}),o.clearAll=new o("clear-all",{fontCharacter:"\\eabf"}),o.clippy=new o("clippy",{fontCharacter:"\\eac0"}),o.closeAll=new o("close-all",{fontCharacter:"\\eac1"}),o.cloudDownload=new o("cloud-download",{fontCharacter:"\\eac2"}),o.cloudUpload=new o("cloud-upload",{fontCharacter:"\\eac3"}),o.code=new o("code",{fontCharacter:"\\eac4"}),o.collapseAll=new o("collapse-all",{fontCharacter:"\\eac5"}),o.colorMode=new o("color-mode",{fontCharacter:"\\eac6"}),o.commentDiscussion=new o("comment-discussion",{fontCharacter:"\\eac7"}),o.compareChanges=new o("compare-changes",{fontCharacter:"\\eafd"}),o.creditCard=new o("credit-card",{fontCharacter:"\\eac9"}),o.dash=new o("dash",{fontCharacter:"\\eacc"}),o.dashboard=new o("dashboard",{fontCharacter:"\\eacd"}),o.database=new o("database",{fontCharacter:"\\eace"}),o.debugContinue=new o("debug-continue",{fontCharacter:"\\eacf"}),o.debugDisconnect=new o("debug-disconnect",{fontCharacter:"\\ead0"}),o.debugPause=new o("debug-pause",{fontCharacter:"\\ead1"}),o.debugRestart=new o("debug-restart",{fontCharacter:"\\ead2"}),o.debugStart=new o("debug-start",{fontCharacter:"\\ead3"}),o.debugStepInto=new o("debug-step-into",{fontCharacter:"\\ead4"}),o.debugStepOut=new o("debug-step-out",{fontCharacter:"\\ead5"}),o.debugStepOver=new o("debug-step-over",{fontCharacter:"\\ead6"}),o.debugStop=new o("debug-stop",{fontCharacter:"\\ead7"}),o.debug=new o("debug",{fontCharacter:"\\ead8"}),o.deviceCameraVideo=new o("device-camera-video",{fontCharacter:"\\ead9"}),o.deviceCamera=new o("device-camera",{fontCharacter:"\\eada"}),o.deviceMobile=new o("device-mobile",{fontCharacter:"\\eadb"}),o.diffAdded=new o("diff-added",{fontCharacter:"\\eadc"}),o.diffIgnored=new o("diff-ignored",{fontCharacter:"\\eadd"}),o.diffModified=new o("diff-modified",{fontCharacter:"\\eade"}),o.diffRemoved=new o("diff-removed",{fontCharacter:"\\eadf"}),o.diffRenamed=new o("diff-renamed",{fontCharacter:"\\eae0"}),o.diff=new o("diff",{fontCharacter:"\\eae1"}),o.discard=new o("discard",{fontCharacter:"\\eae2"}),o.editorLayout=new o("editor-layout",{fontCharacter:"\\eae3"}),o.emptyWindow=new o("empty-window",{fontCharacter:"\\eae4"}),o.exclude=new o("exclude",{fontCharacter:"\\eae5"}),o.extensions=new o("extensions",{fontCharacter:"\\eae6"}),o.eyeClosed=new o("eye-closed",{fontCharacter:"\\eae7"}),o.fileBinary=new o("file-binary",{fontCharacter:"\\eae8"}),o.fileCode=new o("file-code",{fontCharacter:"\\eae9"}),o.fileMedia=new o("file-media",{fontCharacter:"\\eaea"}),o.filePdf=new o("file-pdf",{fontCharacter:"\\eaeb"}),o.fileSubmodule=new o("file-submodule",{fontCharacter:"\\eaec"}),o.fileSymlinkDirectory=new o("file-symlink-directory",{fontCharacter:"\\eaed"}),o.fileSymlinkFile=new o("file-symlink-file",{fontCharacter:"\\eaee"}),o.fileZip=new o("file-zip",{fontCharacter:"\\eaef"}),o.files=new o("files",{fontCharacter:"\\eaf0"}),o.filter=new o("filter",{fontCharacter:"\\eaf1"}),o.flame=new o("flame",{fontCharacter:"\\eaf2"}),o.foldDown=new o("fold-down",{fontCharacter:"\\eaf3"}),o.foldUp=new o("fold-up",{fontCharacter:"\\eaf4"}),o.fold=new o("fold",{fontCharacter:"\\eaf5"}),o.folderActive=new o("folder-active",{fontCharacter:"\\eaf6"}),o.folderOpened=new o("folder-opened",{fontCharacter:"\\eaf7"}),o.gear=new o("gear",{fontCharacter:"\\eaf8"}),o.gift=new o("gift",{fontCharacter:"\\eaf9"}),o.gistSecret=new o("gist-secret",{fontCharacter:"\\eafa"}),o.gist=new o("gist",{fontCharacter:"\\eafb"}),o.gitCommit=new o("git-commit",{fontCharacter:"\\eafc"}),o.gitCompare=new o("git-compare",{fontCharacter:"\\eafd"}),o.gitMerge=new o("git-merge",{fontCharacter:"\\eafe"}),o.githubAction=new o("github-action",{fontCharacter:"\\eaff"}),o.githubAlt=new o("github-alt",{fontCharacter:"\\eb00"}),o.globe=new o("globe",{fontCharacter:"\\eb01"}),o.grabber=new o("grabber",{fontCharacter:"\\eb02"}),o.graph=new o("graph",{fontCharacter:"\\eb03"}),o.gripper=new o("gripper",{fontCharacter:"\\eb04"}),o.heart=new o("heart",{fontCharacter:"\\eb05"}),o.home=new o("home",{fontCharacter:"\\eb06"}),o.horizontalRule=new o("horizontal-rule",{fontCharacter:"\\eb07"}),o.hubot=new o("hubot",{fontCharacter:"\\eb08"}),o.inbox=new o("inbox",{fontCharacter:"\\eb09"}),o.issueClosed=new o("issue-closed",{fontCharacter:"\\eba4"}),o.issueReopened=new o("issue-reopened",{fontCharacter:"\\eb0b"}),o.issues=new o("issues",{fontCharacter:"\\eb0c"}),o.italic=new o("italic",{fontCharacter:"\\eb0d"}),o.jersey=new o("jersey",{fontCharacter:"\\eb0e"}),o.json=new o("json",{fontCharacter:"\\eb0f"}),o.kebabVertical=new o("kebab-vertical",{fontCharacter:"\\eb10"}),o.key=new o("key",{fontCharacter:"\\eb11"}),o.law=new o("law",{fontCharacter:"\\eb12"}),o.lightbulbAutofix=new o("lightbulb-autofix",{fontCharacter:"\\eb13"}),o.linkExternal=new o("link-external",{fontCharacter:"\\eb14"}),o.link=new o("link",{fontCharacter:"\\eb15"}),o.listOrdered=new o("list-ordered",{fontCharacter:"\\eb16"}),o.listUnordered=new o("list-unordered",{fontCharacter:"\\eb17"}),o.liveShare=new o("live-share",{fontCharacter:"\\eb18"}),o.loading=new o("loading",{fontCharacter:"\\eb19"}),o.location=new o("location",{fontCharacter:"\\eb1a"}),o.mailRead=new o("mail-read",{fontCharacter:"\\eb1b"}),o.mail=new o("mail",{fontCharacter:"\\eb1c"}),o.markdown=new o("markdown",{fontCharacter:"\\eb1d"}),o.megaphone=new o("megaphone",{fontCharacter:"\\eb1e"}),o.mention=new o("mention",{fontCharacter:"\\eb1f"}),o.milestone=new o("milestone",{fontCharacter:"\\eb20"}),o.mortarBoard=new o("mortar-board",{fontCharacter:"\\eb21"}),o.move=new o("move",{fontCharacter:"\\eb22"}),o.multipleWindows=new o("multiple-windows",{fontCharacter:"\\eb23"}),o.mute=new o("mute",{fontCharacter:"\\eb24"}),o.noNewline=new o("no-newline",{fontCharacter:"\\eb25"}),o.note=new o("note",{fontCharacter:"\\eb26"}),o.octoface=new o("octoface",{fontCharacter:"\\eb27"}),o.openPreview=new o("open-preview",{fontCharacter:"\\eb28"}),o.package_=new o("package",{fontCharacter:"\\eb29"}),o.paintcan=new o("paintcan",{fontCharacter:"\\eb2a"}),o.pin=new o("pin",{fontCharacter:"\\eb2b"}),o.play=new o("play",{fontCharacter:"\\eb2c"}),o.run=new o("run",{fontCharacter:"\\eb2c"}),o.plug=new o("plug",{fontCharacter:"\\eb2d"}),o.preserveCase=new o("preserve-case",{fontCharacter:"\\eb2e"}),o.preview=new o("preview",{fontCharacter:"\\eb2f"}),o.project=new o("project",{fontCharacter:"\\eb30"}),o.pulse=new o("pulse",{fontCharacter:"\\eb31"}),o.question=new o("question",{fontCharacter:"\\eb32"}),o.quote=new o("quote",{fontCharacter:"\\eb33"}),o.radioTower=new o("radio-tower",{fontCharacter:"\\eb34"}),o.reactions=new o("reactions",{fontCharacter:"\\eb35"}),o.references=new o("references",{fontCharacter:"\\eb36"}),o.refresh=new o("refresh",{fontCharacter:"\\eb37"}),o.regex=new o("regex",{fontCharacter:"\\eb38"}),o.remoteExplorer=new o("remote-explorer",{fontCharacter:"\\eb39"}),o.remote=new o("remote",{fontCharacter:"\\eb3a"}),o.remove=new o("remove",{fontCharacter:"\\eb3b"}),o.replaceAll=new o("replace-all",{fontCharacter:"\\eb3c"}),o.replace=new o("replace",{fontCharacter:"\\eb3d"}),o.repoClone=new o("repo-clone",{fontCharacter:"\\eb3e"}),o.repoForcePush=new o("repo-force-push",{fontCharacter:"\\eb3f"}),o.repoPull=new o("repo-pull",{fontCharacter:"\\eb40"}),o.repoPush=new o("repo-push",{fontCharacter:"\\eb41"}),o.report=new o("report",{fontCharacter:"\\eb42"}),o.requestChanges=new o("request-changes",{fontCharacter:"\\eb43"}),o.rocket=new o("rocket",{fontCharacter:"\\eb44"}),o.rootFolderOpened=new o("root-folder-opened",{fontCharacter:"\\eb45"}),o.rootFolder=new o("root-folder",{fontCharacter:"\\eb46"}),o.rss=new o("rss",{fontCharacter:"\\eb47"}),o.ruby=new o("ruby",{fontCharacter:"\\eb48"}),o.saveAll=new o("save-all",{fontCharacter:"\\eb49"}),o.saveAs=new o("save-as",{fontCharacter:"\\eb4a"}),o.save=new o("save",{fontCharacter:"\\eb4b"}),o.screenFull=new o("screen-full",{fontCharacter:"\\eb4c"}),o.screenNormal=new o("screen-normal",{fontCharacter:"\\eb4d"}),o.searchStop=new o("search-stop",{fontCharacter:"\\eb4e"}),o.server=new o("server",{fontCharacter:"\\eb50"}),o.settingsGear=new o("settings-gear",{fontCharacter:"\\eb51"}),o.settings=new o("settings",{fontCharacter:"\\eb52"}),o.shield=new o("shield",{fontCharacter:"\\eb53"}),o.smiley=new o("smiley",{fontCharacter:"\\eb54"}),o.sortPrecedence=new o("sort-precedence",{fontCharacter:"\\eb55"}),o.splitHorizontal=new o("split-horizontal",{fontCharacter:"\\eb56"}),o.splitVertical=new o("split-vertical",{fontCharacter:"\\eb57"}),o.squirrel=new o("squirrel",{fontCharacter:"\\eb58"}),o.starFull=new o("star-full",{fontCharacter:"\\eb59"}),o.starHalf=new o("star-half",{fontCharacter:"\\eb5a"}),o.symbolClass=new o("symbol-class",{fontCharacter:"\\eb5b"}),o.symbolColor=new o("symbol-color",{fontCharacter:"\\eb5c"}),o.symbolCustomColor=new o("symbol-customcolor",{fontCharacter:"\\eb5c"}),o.symbolConstant=new o("symbol-constant",{fontCharacter:"\\eb5d"}),o.symbolEnumMember=new o("symbol-enum-member",{fontCharacter:"\\eb5e"}),o.symbolField=new o("symbol-field",{fontCharacter:"\\eb5f"}),o.symbolFile=new o("symbol-file",{fontCharacter:"\\eb60"}),o.symbolInterface=new o("symbol-interface",{fontCharacter:"\\eb61"}),o.symbolKeyword=new o("symbol-keyword",{fontCharacter:"\\eb62"}),o.symbolMisc=new o("symbol-misc",{fontCharacter:"\\eb63"}),o.symbolOperator=new o("symbol-operator",{fontCharacter:"\\eb64"}),o.symbolProperty=new o("symbol-property",{fontCharacter:"\\eb65"}),o.wrench=new o("wrench",{fontCharacter:"\\eb65"}),o.wrenchSubaction=new o("wrench-subaction",{fontCharacter:"\\eb65"}),o.symbolSnippet=new o("symbol-snippet",{fontCharacter:"\\eb66"}),o.tasklist=new o("tasklist",{fontCharacter:"\\eb67"}),o.telescope=new o("telescope",{fontCharacter:"\\eb68"}),o.textSize=new o("text-size",{fontCharacter:"\\eb69"}),o.threeBars=new o("three-bars",{fontCharacter:"\\eb6a"}),o.thumbsdown=new o("thumbsdown",{fontCharacter:"\\eb6b"}),o.thumbsup=new o("thumbsup",{fontCharacter:"\\eb6c"}),o.tools=new o("tools",{fontCharacter:"\\eb6d"}),o.triangleDown=new o("triangle-down",{fontCharacter:"\\eb6e"}),o.triangleLeft=new o("triangle-left",{fontCharacter:"\\eb6f"}),o.triangleRight=new o("triangle-right",{fontCharacter:"\\eb70"}),o.triangleUp=new o("triangle-up",{fontCharacter:"\\eb71"}),o.twitter=new o("twitter",{fontCharacter:"\\eb72"}),o.unfold=new o("unfold",{fontCharacter:"\\eb73"}),o.unlock=new o("unlock",{fontCharacter:"\\eb74"}),o.unmute=new o("unmute",{fontCharacter:"\\eb75"}),o.unverified=new o("unverified",{fontCharacter:"\\eb76"}),o.verified=new o("verified",{fontCharacter:"\\eb77"}),o.versions=new o("versions",{fontCharacter:"\\eb78"}),o.vmActive=new o("vm-active",{fontCharacter:"\\eb79"}),o.vmOutline=new o("vm-outline",{fontCharacter:"\\eb7a"}),o.vmRunning=new o("vm-running",{fontCharacter:"\\eb7b"}),o.watch=new o("watch",{fontCharacter:"\\eb7c"}),o.whitespace=new o("whitespace",{fontCharacter:"\\eb7d"}),o.wholeWord=new o("whole-word",{fontCharacter:"\\eb7e"}),o.window=new o("window",{fontCharacter:"\\eb7f"}),o.wordWrap=new o("word-wrap",{fontCharacter:"\\eb80"}),o.zoomIn=new o("zoom-in",{fontCharacter:"\\eb81"}),o.zoomOut=new o("zoom-out",{fontCharacter:"\\eb82"}),o.listFilter=new o("list-filter",{fontCharacter:"\\eb83"}),o.listFlat=new o("list-flat",{fontCharacter:"\\eb84"}),o.listSelection=new o("list-selection",{fontCharacter:"\\eb85"}),o.selection=new o("selection",{fontCharacter:"\\eb85"}),o.listTree=new o("list-tree",{fontCharacter:"\\eb86"}),o.debugBreakpointFunctionUnverified=new o("debug-breakpoint-function-unverified",{fontCharacter:"\\eb87"}),o.debugBreakpointFunction=new o("debug-breakpoint-function",{fontCharacter:"\\eb88"}),o.debugBreakpointFunctionDisabled=new o("debug-breakpoint-function-disabled",{fontCharacter:"\\eb88"}),o.debugStackframeActive=new o("debug-stackframe-active",{fontCharacter:"\\eb89"}),o.debugStackframeDot=new o("debug-stackframe-dot",{fontCharacter:"\\eb8a"}),o.debugStackframe=new o("debug-stackframe",{fontCharacter:"\\eb8b"}),o.debugStackframeFocused=new o("debug-stackframe-focused",{fontCharacter:"\\eb8b"}),o.debugBreakpointUnsupported=new o("debug-breakpoint-unsupported",{fontCharacter:"\\eb8c"}),o.symbolString=new o("symbol-string",{fontCharacter:"\\eb8d"}),o.debugReverseContinue=new o("debug-reverse-continue",{fontCharacter:"\\eb8e"}),o.debugStepBack=new o("debug-step-back",{fontCharacter:"\\eb8f"}),o.debugRestartFrame=new o("debug-restart-frame",{fontCharacter:"\\eb90"}),o.callIncoming=new o("call-incoming",{fontCharacter:"\\eb92"}),o.callOutgoing=new o("call-outgoing",{fontCharacter:"\\eb93"}),o.menu=new o("menu",{fontCharacter:"\\eb94"}),o.expandAll=new o("expand-all",{fontCharacter:"\\eb95"}),o.feedback=new o("feedback",{fontCharacter:"\\eb96"}),o.groupByRefType=new o("group-by-ref-type",{fontCharacter:"\\eb97"}),o.ungroupByRefType=new o("ungroup-by-ref-type",{fontCharacter:"\\eb98"}),o.account=new o("account",{fontCharacter:"\\eb99"}),o.bellDot=new o("bell-dot",{fontCharacter:"\\eb9a"}),o.debugConsole=new o("debug-console",{fontCharacter:"\\eb9b"}),o.library=new o("library",{fontCharacter:"\\eb9c"}),o.output=new o("output",{fontCharacter:"\\eb9d"}),o.runAll=new o("run-all",{fontCharacter:"\\eb9e"}),o.syncIgnored=new o("sync-ignored",{fontCharacter:"\\eb9f"}),o.pinned=new o("pinned",{fontCharacter:"\\eba0"}),o.githubInverted=new o("github-inverted",{fontCharacter:"\\eba1"}),o.debugAlt=new o("debug-alt",{fontCharacter:"\\eb91"}),o.serverProcess=new o("server-process",{fontCharacter:"\\eba2"}),o.serverEnvironment=new o("server-environment",{fontCharacter:"\\eba3"}),o.pass=new o("pass",{fontCharacter:"\\eba4"}),o.stopCircle=new o("stop-circle",{fontCharacter:"\\eba5"}),o.playCircle=new o("play-circle",{fontCharacter:"\\eba6"}),o.record=new o("record",{fontCharacter:"\\eba7"}),o.debugAltSmall=new o("debug-alt-small",{fontCharacter:"\\eba8"}),o.vmConnect=new o("vm-connect",{fontCharacter:"\\eba9"}),o.cloud=new o("cloud",{fontCharacter:"\\ebaa"}),o.merge=new o("merge",{fontCharacter:"\\ebab"}),o.exportIcon=new o("export",{fontCharacter:"\\ebac"}),o.graphLeft=new o("graph-left",{fontCharacter:"\\ebad"}),o.magnet=new o("magnet",{fontCharacter:"\\ebae"}),o.notebook=new o("notebook",{fontCharacter:"\\ebaf"}),o.redo=new o("redo",{fontCharacter:"\\ebb0"}),o.checkAll=new o("check-all",{fontCharacter:"\\ebb1"}),o.pinnedDirty=new o("pinned-dirty",{fontCharacter:"\\ebb2"}),o.passFilled=new o("pass-filled",{fontCharacter:"\\ebb3"}),o.circleLargeFilled=new o("circle-large-filled",{fontCharacter:"\\ebb4"}),o.circleLargeOutline=new o("circle-large-outline",{fontCharacter:"\\ebb5"}),o.combine=new o("combine",{fontCharacter:"\\ebb6"}),o.gather=new o("gather",{fontCharacter:"\\ebb6"}),o.table=new o("table",{fontCharacter:"\\ebb7"}),o.variableGroup=new o("variable-group",{fontCharacter:"\\ebb8"}),o.typeHierarchy=new o("type-hierarchy",{fontCharacter:"\\ebb9"}),o.typeHierarchySub=new o("type-hierarchy-sub",{fontCharacter:"\\ebba"}),o.typeHierarchySuper=new o("type-hierarchy-super",{fontCharacter:"\\ebbb"}),o.gitPullRequestCreate=new o("git-pull-request-create",{fontCharacter:"\\ebbc"}),o.runAbove=new o("run-above",{fontCharacter:"\\ebbd"}),o.runBelow=new o("run-below",{fontCharacter:"\\ebbe"}),o.notebookTemplate=new o("notebook-template",{fontCharacter:"\\ebbf"}),o.debugRerun=new o("debug-rerun",{fontCharacter:"\\ebc0"}),o.workspaceTrusted=new o("workspace-trusted",{fontCharacter:"\\ebc1"}),o.workspaceUntrusted=new o("workspace-untrusted",{fontCharacter:"\\ebc2"}),o.workspaceUnspecified=new o("workspace-unspecified",{fontCharacter:"\\ebc3"}),o.terminalCmd=new o("terminal-cmd",{fontCharacter:"\\ebc4"}),o.terminalDebian=new o("terminal-debian",{fontCharacter:"\\ebc5"}),o.terminalLinux=new o("terminal-linux",{fontCharacter:"\\ebc6"}),o.terminalPowershell=new o("terminal-powershell",{fontCharacter:"\\ebc7"}),o.terminalTmux=new o("terminal-tmux",{fontCharacter:"\\ebc8"}),o.terminalUbuntu=new o("terminal-ubuntu",{fontCharacter:"\\ebc9"}),o.terminalBash=new o("terminal-bash",{fontCharacter:"\\ebca"}),o.arrowSwap=new o("arrow-swap",{fontCharacter:"\\ebcb"}),o.copy=new o("copy",{fontCharacter:"\\ebcc"}),o.personAdd=new o("person-add",{fontCharacter:"\\ebcd"}),o.filterFilled=new o("filter-filled",{fontCharacter:"\\ebce"}),o.wand=new o("wand",{fontCharacter:"\\ebcf"}),o.debugLineByLine=new o("debug-line-by-line",{fontCharacter:"\\ebd0"}),o.inspect=new o("inspect",{fontCharacter:"\\ebd1"}),o.layers=new o("layers",{fontCharacter:"\\ebd2"}),o.layersDot=new o("layers-dot",{fontCharacter:"\\ebd3"}),o.layersActive=new o("layers-active",{fontCharacter:"\\ebd4"}),o.compass=new o("compass",{fontCharacter:"\\ebd5"}),o.compassDot=new o("compass-dot",{fontCharacter:"\\ebd6"}),o.compassActive=new o("compass-active",{fontCharacter:"\\ebd7"}),o.azure=new o("azure",{fontCharacter:"\\ebd8"}),o.issueDraft=new o("issue-draft",{fontCharacter:"\\ebd9"}),o.gitPullRequestClosed=new o("git-pull-request-closed",{fontCharacter:"\\ebda"}),o.gitPullRequestDraft=new o("git-pull-request-draft",{fontCharacter:"\\ebdb"}),o.debugAll=new o("debug-all",{fontCharacter:"\\ebdc"}),o.debugCoverage=new o("debug-coverage",{fontCharacter:"\\ebdd"}),o.runErrors=new o("run-errors",{fontCharacter:"\\ebde"}),o.folderLibrary=new o("folder-library",{fontCharacter:"\\ebdf"}),o.debugContinueSmall=new o("debug-continue-small",{fontCharacter:"\\ebe0"}),o.beakerStop=new o("beaker-stop",{fontCharacter:"\\ebe1"}),o.graphLine=new o("graph-line",{fontCharacter:"\\ebe2"}),o.graphScatter=new o("graph-scatter",{fontCharacter:"\\ebe3"}),o.pieChart=new o("pie-chart",{fontCharacter:"\\ebe4"}),o.bracket=new o("bracket",o.json.definition),o.bracketDot=new o("bracket-dot",{fontCharacter:"\\ebe5"}),o.bracketError=new o("bracket-error",{fontCharacter:"\\ebe6"}),o.lockSmall=new o("lock-small",{fontCharacter:"\\ebe7"}),o.azureDevops=new o("azure-devops",{fontCharacter:"\\ebe8"}),o.verifiedFilled=new o("verified-filled",{fontCharacter:"\\ebe9"}),o.newLine=new o("newline",{fontCharacter:"\\ebea"}),o.layout=new o("layout",{fontCharacter:"\\ebeb"}),o.layoutActivitybarLeft=new o("layout-activitybar-left",{fontCharacter:"\\ebec"}),o.layoutActivitybarRight=new o("layout-activitybar-right",{fontCharacter:"\\ebed"}),o.layoutPanelLeft=new o("layout-panel-left",{fontCharacter:"\\ebee"}),o.layoutPanelCenter=new o("layout-panel-center",{fontCharacter:"\\ebef"}),o.layoutPanelJustify=new o("layout-panel-justify",{fontCharacter:"\\ebf0"}),o.layoutPanelRight=new o("layout-panel-right",{fontCharacter:"\\ebf1"}),o.layoutPanel=new o("layout-panel",{fontCharacter:"\\ebf2"}),o.layoutSidebarLeft=new o("layout-sidebar-left",{fontCharacter:"\\ebf3"}),o.layoutSidebarRight=new o("layout-sidebar-right",{fontCharacter:"\\ebf4"}),o.layoutStatusbar=new o("layout-statusbar",{fontCharacter:"\\ebf5"}),o.layoutMenubar=new o("layout-menubar",{fontCharacter:"\\ebf6"}),o.layoutCentered=new o("layout-centered",{fontCharacter:"\\ebf7"}),o.target=new o("target",{fontCharacter:"\\ebf8"}),o.indent=new o("indent",{fontCharacter:"\\ebf9"}),o.recordSmall=new o("record-small",{fontCharacter:"\\ebfa"}),o.errorSmall=new o("error-small",{fontCharacter:"\\ebfb"}),o.arrowCircleDown=new o("arrow-circle-down",{fontCharacter:"\\ebfc"}),o.arrowCircleLeft=new o("arrow-circle-left",{fontCharacter:"\\ebfd"}),o.arrowCircleRight=new o("arrow-circle-right",{fontCharacter:"\\ebfe"}),o.arrowCircleUp=new o("arrow-circle-up",{fontCharacter:"\\ebff"}),o.dialogError=new o("dialog-error",o.error.definition),o.dialogWarning=new o("dialog-warning",o.warning.definition),o.dialogInfo=new o("dialog-info",o.info.definition),o.dialogClose=new o("dialog-close",o.close.definition),o.treeItemExpanded=new o("tree-item-expanded",o.chevronDown.definition),o.treeFilterOnTypeOn=new o("tree-filter-on-type-on",o.listFilter.definition),o.treeFilterOnTypeOff=new o("tree-filter-on-type-off",o.listSelection.definition),o.treeFilterClear=new o("tree-filter-clear",o.close.definition),o.treeItemLoading=new o("tree-item-loading",o.loading.definition),o.menuSelection=new o("menu-selection",o.check.definition),o.menuSubmenu=new o("menu-submenu",o.chevronRight.definition),o.menuBarMore=new o("menubar-more",o.more.definition),o.scrollbarButtonLeft=new o("scrollbar-button-left",o.triangleLeft.definition),o.scrollbarButtonRight=new o("scrollbar-button-right",o.triangleRight.definition),o.scrollbarButtonUp=new o("scrollbar-button-up",o.triangleUp.definition),o.scrollbarButtonDown=new o("scrollbar-button-down",o.triangleDown.definition),o.toolBarMore=new o("toolbar-more",o.more.definition),o.quickInputBack=new o("quick-input-back",o.arrowLeft.definition);var Mi;(function(e){e.iconNameSegment="[A-Za-z0-9]+",e.iconNameExpression="[A-Za-z0-9-]+",e.iconModifierExpression="~[A-Za-z]+",e.iconNameCharacter="[A-Za-z0-9~-]";const t=new RegExp(`^(${e.iconNameExpression})(${e.iconModifierExpression})?$`);function n(a){if(a instanceof o)return["codicon","codicon-"+a.id];const s=t.exec(a.id);if(!s)return n(o.error);let[,u,c]=s;const l=["codicon","codicon-"+u];return c&&l.push("codicon-modifier-"+c.substr(1)),l}e.asClassNameArray=n;function r(a){return n(a).join(" ")}e.asClassName=r;function i(a){return"."+n(a).join(".")}e.asCSSSelector=i})(Mi||(Mi={}));class Hc{static getLanguageId(t){return(t&255)>>>0}static getTokenType(t){return(t&768)>>>8}static getFontStyle(t){return(t&15360)>>>10}static getForeground(t){return(t&8372224)>>>14}static getBackground(t){return(t&4286578688)>>>23}static getClassNameFromMetadata(t){const n=this.getForeground(t);let r="mtk"+n;const i=this.getFontStyle(t);return i&1&&(r+=" mtki"),i&2&&(r+=" mtkb"),i&4&&(r+=" mtku"),i&8&&(r+=" mtks"),r}static getInlineStyleFromMetadata(t,n){const r=this.getForeground(t),i=this.getFontStyle(t);let a=`color: ${n[r]};`;i&1&&(a+="font-style: italic;"),i&2&&(a+="font-weight: bold;");let s="";return i&4&&(s+=" underline"),i&8&&(s+=" line-through"),s&&(a+=`text-decoration:${s};`),a}static getPresentationFromMetadata(t){const n=this.getForeground(t),r=this.getFontStyle(t);return{foreground:n,italic:Boolean(r&1),bold:Boolean(r&2),underline:Boolean(r&4),strikethrough:Boolean(r&8)}}}class ss{constructor(t,n,r){this._tokenBrand=void 0,this.offset=t,this.type=n,this.language=r}toString(){return"("+this.offset+", "+this.type+")"}}class zc{constructor(t,n){this._tokenizationResultBrand=void 0,this.tokens=t,this.endState=n}}class Gc{constructor(t,n){this._encodedTokenizationResultBrand=void 0,this.tokens=t,this.endState=n}}var Ti;(function(e){const t=new Map;t.set(0,o.symbolMethod),t.set(1,o.symbolFunction),t.set(2,o.symbolConstructor),t.set(3,o.symbolField),t.set(4,o.symbolVariable),t.set(5,o.symbolClass),t.set(6,o.symbolStruct),t.set(7,o.symbolInterface),t.set(8,o.symbolModule),t.set(9,o.symbolProperty),t.set(10,o.symbolEvent),t.set(11,o.symbolOperator),t.set(12,o.symbolUnit),t.set(13,o.symbolValue),t.set(15,o.symbolEnum),t.set(14,o.symbolConstant),t.set(15,o.symbolEnum),t.set(16,o.symbolEnumMember),t.set(17,o.symbolKeyword),t.set(27,o.symbolSnippet),t.set(18,o.symbolText),t.set(19,o.symbolColor),t.set(20,o.symbolFile),t.set(21,o.symbolReference),t.set(22,o.symbolCustomColor),t.set(23,o.symbolFolder),t.set(24,o.symbolTypeParameter),t.set(25,o.account),t.set(26,o.issues);function n(a){let s=t.get(a);return s||(console.info("No codicon found for CompletionItemKind "+a),s=o.symbolProperty),s}e.toIcon=n;const r=new Map;r.set("method",0),r.set("function",1),r.set("constructor",2),r.set("field",3),r.set("variable",4),r.set("class",5),r.set("struct",6),r.set("interface",7),r.set("module",8),r.set("property",9),r.set("event",10),r.set("operator",11),r.set("unit",12),r.set("value",13),r.set("constant",14),r.set("enum",15),r.set("enum-member",16),r.set("enumMember",16),r.set("keyword",17),r.set("snippet",27),r.set("text",18),r.set("color",19),r.set("file",20),r.set("reference",21),r.set("customcolor",22),r.set("folder",23),r.set("type-parameter",24),r.set("typeParameter",24),r.set("account",25),r.set("issue",26);function i(a,s){let u=r.get(a);return typeof u=="undefined"&&!s&&(u=9),u}e.fromString=i})(Ti||(Ti={}));var Ii;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(Ii||(Ii={}));var Pi;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(Pi||(Pi={}));var Fi;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(Fi||(Fi={}));function Jc(e){return e&&URI.isUri(e.uri)&&Range.isIRange(e.range)&&(Range.isIRange(e.originSelectionRange)||Range.isIRange(e.targetSelectionRange))}var Ri;(function(e){const t=new Map;t.set(0,o.symbolFile),t.set(1,o.symbolModule),t.set(2,o.symbolNamespace),t.set(3,o.symbolPackage),t.set(4,o.symbolClass),t.set(5,o.symbolMethod),t.set(6,o.symbolProperty),t.set(7,o.symbolField),t.set(8,o.symbolConstructor),t.set(9,o.symbolEnum),t.set(10,o.symbolInterface),t.set(11,o.symbolFunction),t.set(12,o.symbolVariable),t.set(13,o.symbolConstant),t.set(14,o.symbolString),t.set(15,o.symbolNumber),t.set(16,o.symbolBoolean),t.set(17,o.symbolArray),t.set(18,o.symbolObject),t.set(19,o.symbolKey),t.set(20,o.symbolNull),t.set(21,o.symbolEnumMember),t.set(22,o.symbolStruct),t.set(23,o.symbolEvent),t.set(24,o.symbolOperator),t.set(25,o.symbolTypeParameter);function n(r){let i=t.get(r);return i||(console.info("No codicon found for SymbolKind "+r),i=o.symbolProperty),i}e.toIcon=n})(Ri||(Ri={}));class yt{constructor(t){this.value=t}}yt.Comment=new yt("comment"),yt.Imports=new yt("imports"),yt.Region=new yt("region");var Vi;(function(e){function t(n){return!n||typeof n!="object"?!1:typeof n.id=="string"&&typeof n.title=="string"}e.is=t})(Vi||(Vi={}));var Di;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(Di||(Di={}));const Zc=new os;var Ki;(function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"})(Ki||(Ki={}));var ji;(function(e){e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(ji||(ji={}));var Bi;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"})(Bi||(Bi={}));var Ui;(function(e){e[e.Deprecated=1]="Deprecated"})(Ui||(Ui={}));var $i;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})($i||($i={}));var qi;(function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"})(qi||(qi={}));var Wi;(function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"})(Wi||(Wi={}));var Hi;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Hi||(Hi={}));var zi;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(zi||(zi={}));var Gi;(function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"})(Gi||(Gi={}));var Ji;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.autoClosingBrackets=5]="autoClosingBrackets",e[e.autoClosingDelete=6]="autoClosingDelete",e[e.autoClosingOvertype=7]="autoClosingOvertype",e[e.autoClosingQuotes=8]="autoClosingQuotes",e[e.autoIndent=9]="autoIndent",e[e.automaticLayout=10]="automaticLayout",e[e.autoSurround=11]="autoSurround",e[e.bracketPairColorization=12]="bracketPairColorization",e[e.guides=13]="guides",e[e.codeLens=14]="codeLens",e[e.codeLensFontFamily=15]="codeLensFontFamily",e[e.codeLensFontSize=16]="codeLensFontSize",e[e.colorDecorators=17]="colorDecorators",e[e.columnSelection=18]="columnSelection",e[e.comments=19]="comments",e[e.contextmenu=20]="contextmenu",e[e.copyWithSyntaxHighlighting=21]="copyWithSyntaxHighlighting",e[e.cursorBlinking=22]="cursorBlinking",e[e.cursorSmoothCaretAnimation=23]="cursorSmoothCaretAnimation",e[e.cursorStyle=24]="cursorStyle",e[e.cursorSurroundingLines=25]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=26]="cursorSurroundingLinesStyle",e[e.cursorWidth=27]="cursorWidth",e[e.disableLayerHinting=28]="disableLayerHinting",e[e.disableMonospaceOptimizations=29]="disableMonospaceOptimizations",e[e.domReadOnly=30]="domReadOnly",e[e.dragAndDrop=31]="dragAndDrop",e[e.emptySelectionClipboard=32]="emptySelectionClipboard",e[e.extraEditorClassName=33]="extraEditorClassName",e[e.fastScrollSensitivity=34]="fastScrollSensitivity",e[e.find=35]="find",e[e.fixedOverflowWidgets=36]="fixedOverflowWidgets",e[e.folding=37]="folding",e[e.foldingStrategy=38]="foldingStrategy",e[e.foldingHighlight=39]="foldingHighlight",e[e.foldingImportsByDefault=40]="foldingImportsByDefault",e[e.foldingMaximumRegions=41]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=42]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=43]="fontFamily",e[e.fontInfo=44]="fontInfo",e[e.fontLigatures=45]="fontLigatures",e[e.fontSize=46]="fontSize",e[e.fontWeight=47]="fontWeight",e[e.formatOnPaste=48]="formatOnPaste",e[e.formatOnType=49]="formatOnType",e[e.glyphMargin=50]="glyphMargin",e[e.gotoLocation=51]="gotoLocation",e[e.hideCursorInOverviewRuler=52]="hideCursorInOverviewRuler",e[e.hover=53]="hover",e[e.inDiffEditor=54]="inDiffEditor",e[e.inlineSuggest=55]="inlineSuggest",e[e.letterSpacing=56]="letterSpacing",e[e.lightbulb=57]="lightbulb",e[e.lineDecorationsWidth=58]="lineDecorationsWidth",e[e.lineHeight=59]="lineHeight",e[e.lineNumbers=60]="lineNumbers",e[e.lineNumbersMinChars=61]="lineNumbersMinChars",e[e.linkedEditing=62]="linkedEditing",e[e.links=63]="links",e[e.matchBrackets=64]="matchBrackets",e[e.minimap=65]="minimap",e[e.mouseStyle=66]="mouseStyle",e[e.mouseWheelScrollSensitivity=67]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=68]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=69]="multiCursorMergeOverlapping",e[e.multiCursorModifier=70]="multiCursorModifier",e[e.multiCursorPaste=71]="multiCursorPaste",e[e.occurrencesHighlight=72]="occurrencesHighlight",e[e.overviewRulerBorder=73]="overviewRulerBorder",e[e.overviewRulerLanes=74]="overviewRulerLanes",e[e.padding=75]="padding",e[e.parameterHints=76]="parameterHints",e[e.peekWidgetDefaultFocus=77]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=78]="definitionLinkOpensInPeek",e[e.quickSuggestions=79]="quickSuggestions",e[e.quickSuggestionsDelay=80]="quickSuggestionsDelay",e[e.readOnly=81]="readOnly",e[e.renameOnType=82]="renameOnType",e[e.renderControlCharacters=83]="renderControlCharacters",e[e.renderFinalNewline=84]="renderFinalNewline",e[e.renderLineHighlight=85]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=86]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=87]="renderValidationDecorations",e[e.renderWhitespace=88]="renderWhitespace",e[e.revealHorizontalRightPadding=89]="revealHorizontalRightPadding",e[e.roundedSelection=90]="roundedSelection",e[e.rulers=91]="rulers",e[e.scrollbar=92]="scrollbar",e[e.scrollBeyondLastColumn=93]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=94]="scrollBeyondLastLine",e[e.scrollPredominantAxis=95]="scrollPredominantAxis",e[e.selectionClipboard=96]="selectionClipboard",e[e.selectionHighlight=97]="selectionHighlight",e[e.selectOnLineNumbers=98]="selectOnLineNumbers",e[e.showFoldingControls=99]="showFoldingControls",e[e.showUnused=100]="showUnused",e[e.snippetSuggestions=101]="snippetSuggestions",e[e.smartSelect=102]="smartSelect",e[e.smoothScrolling=103]="smoothScrolling",e[e.stickyTabStops=104]="stickyTabStops",e[e.stopRenderingLineAfter=105]="stopRenderingLineAfter",e[e.suggest=106]="suggest",e[e.suggestFontSize=107]="suggestFontSize",e[e.suggestLineHeight=108]="suggestLineHeight",e[e.suggestOnTriggerCharacters=109]="suggestOnTriggerCharacters",e[e.suggestSelection=110]="suggestSelection",e[e.tabCompletion=111]="tabCompletion",e[e.tabIndex=112]="tabIndex",e[e.unicodeHighlighting=113]="unicodeHighlighting",e[e.unusualLineTerminators=114]="unusualLineTerminators",e[e.useShadowDOM=115]="useShadowDOM",e[e.useTabStops=116]="useTabStops",e[e.wordSeparators=117]="wordSeparators",e[e.wordWrap=118]="wordWrap",e[e.wordWrapBreakAfterCharacters=119]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=120]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=121]="wordWrapColumn",e[e.wordWrapOverride1=122]="wordWrapOverride1",e[e.wordWrapOverride2=123]="wordWrapOverride2",e[e.wrappingIndent=124]="wrappingIndent",e[e.wrappingStrategy=125]="wrappingStrategy",e[e.showDeprecated=126]="showDeprecated",e[e.inlayHints=127]="inlayHints",e[e.editorClassName=128]="editorClassName",e[e.pixelRatio=129]="pixelRatio",e[e.tabFocusMode=130]="tabFocusMode",e[e.layoutInfo=131]="layoutInfo",e[e.wrappingInfo=132]="wrappingInfo"})(Ji||(Ji={}));var Zi;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Zi||(Zi={}));var Qi;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(Qi||(Qi={}));var Yi;(function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"})(Yi||(Yi={}));var Xi;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(Xi||(Xi={}));var eo;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(eo||(eo={}));var to;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(to||(to={}));var ur;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.NumLock=78]="NumLock",e[e.ScrollLock=79]="ScrollLock",e[e.Semicolon=80]="Semicolon",e[e.Equal=81]="Equal",e[e.Comma=82]="Comma",e[e.Minus=83]="Minus",e[e.Period=84]="Period",e[e.Slash=85]="Slash",e[e.Backquote=86]="Backquote",e[e.BracketLeft=87]="BracketLeft",e[e.Backslash=88]="Backslash",e[e.BracketRight=89]="BracketRight",e[e.Quote=90]="Quote",e[e.OEM_8=91]="OEM_8",e[e.IntlBackslash=92]="IntlBackslash",e[e.Numpad0=93]="Numpad0",e[e.Numpad1=94]="Numpad1",e[e.Numpad2=95]="Numpad2",e[e.Numpad3=96]="Numpad3",e[e.Numpad4=97]="Numpad4",e[e.Numpad5=98]="Numpad5",e[e.Numpad6=99]="Numpad6",e[e.Numpad7=100]="Numpad7",e[e.Numpad8=101]="Numpad8",e[e.Numpad9=102]="Numpad9",e[e.NumpadMultiply=103]="NumpadMultiply",e[e.NumpadAdd=104]="NumpadAdd",e[e.NUMPAD_SEPARATOR=105]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=106]="NumpadSubtract",e[e.NumpadDecimal=107]="NumpadDecimal",e[e.NumpadDivide=108]="NumpadDivide",e[e.KEY_IN_COMPOSITION=109]="KEY_IN_COMPOSITION",e[e.ABNT_C1=110]="ABNT_C1",e[e.ABNT_C2=111]="ABNT_C2",e[e.AudioVolumeMute=112]="AudioVolumeMute",e[e.AudioVolumeUp=113]="AudioVolumeUp",e[e.AudioVolumeDown=114]="AudioVolumeDown",e[e.BrowserSearch=115]="BrowserSearch",e[e.BrowserHome=116]="BrowserHome",e[e.BrowserBack=117]="BrowserBack",e[e.BrowserForward=118]="BrowserForward",e[e.MediaTrackNext=119]="MediaTrackNext",e[e.MediaTrackPrevious=120]="MediaTrackPrevious",e[e.MediaStop=121]="MediaStop",e[e.MediaPlayPause=122]="MediaPlayPause",e[e.LaunchMediaPlayer=123]="LaunchMediaPlayer",e[e.LaunchMail=124]="LaunchMail",e[e.LaunchApp2=125]="LaunchApp2",e[e.Clear=126]="Clear",e[e.MAX_VALUE=127]="MAX_VALUE"})(ur||(ur={}));var cr;(function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"})(cr||(cr={}));var lr;(function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"})(lr||(lr={}));var no;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(no||(no={}));var ro;(function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(ro||(ro={}));var io;(function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"})(io||(io={}));var oo;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(oo||(oo={}));var ao;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None"})(ao||(ao={}));var so;(function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"})(so||(so={}));var uo;(function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"})(uo||(uo={}));var co;(function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"})(co||(co={}));var lo;(function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"})(lo||(lo={}));var hr;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(hr||(hr={}));var ho;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(ho||(ho={}));var fo;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(fo||(fo={}));var mo;(function(e){e[e.Deprecated=1]="Deprecated"})(mo||(mo={}));var go;(function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"})(go||(go={}));var po;(function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"})(po||(po={}));var bo;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(bo||(bo={}));var vo;(function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"})(vo||(vo={}));class jt{static chord(t,n){return is(t,n)}}jt.CtrlCmd=2048,jt.Shift=1024,jt.Alt=512,jt.WinCtrl=256;function us(){return{editor:void 0,languages:void 0,CancellationTokenSource:Ya,Emitter:Fe,KeyCode:ur,KeyMod:jt,Position:pe,Range:J,Selection:Ce,SelectionDirection:hr,MarkerSeverity:cr,MarkerTag:lr,Uri:at,Token:ss}}class cs extends Dt{constructor(t){super(0);for(let n=0,r=t.length;n<r;n++)this.set(t.charCodeAt(n),2);this.set(32,1),this.set(9,1)}}function ls(e){const t={};return n=>(t.hasOwnProperty(n)||(t[n]=e(n)),t[n])}const Qc=ls(e=>new cs(e));function hs(e){if(!e||typeof e!="object"||e instanceof RegExp)return e;const t=Array.isArray(e)?[]:{};return Object.keys(e).forEach(n=>{e[n]&&typeof e[n]=="object"?t[n]=hs(e[n]):t[n]=e[n]}),t}function Yc(e){if(!e||typeof e!="object")return e;const t=[e];for(;t.length>0;){const n=t.shift();Object.freeze(n);for(const r in n)if(yo.call(n,r)){const i=n[r];typeof i=="object"&&!Object.isFrozen(i)&&t.push(i)}}return e}const yo=Object.prototype.hasOwnProperty;function Xc(e,t){return fr(e,t,new Set)}function fr(e,t,n){if(isUndefinedOrNull(e))return e;const r=t(e);if(typeof r!="undefined")return r;if(isArray(e)){const i=[];for(const a of e)i.push(fr(a,t,n));return i}if(isObject(e)){if(n.has(e))throw new Error("Cannot clone recursive data-structure");n.add(e);const i={};for(let a in e)yo.call(e,a)&&(i[a]=fr(e[a],t,n));return n.delete(e),i}return e}function fs(e,t,n=!0){return isObject(e)?(isObject(t)&&Object.keys(t).forEach(r=>{r in e?n&&(isObject(e[r])&&isObject(t[r])?fs(e[r],t[r],n):e[r]=t[r]):e[r]=t[r]}),e):t}function dr(e,t){if(e===t)return!0;if(e==null||t===null||t===void 0||typeof e!=typeof t||typeof e!="object"||Array.isArray(e)!==Array.isArray(t))return!1;let n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!dr(e[n],t[n]))return!1}else{const i=[];for(r in e)i.push(r);i.sort();const a=[];for(r in t)a.push(r);if(a.sort(),!dr(i,a))return!1;for(n=0;n<i.length;n++)if(!dr(e[i[n]],t[i[n]]))return!1}return!0}function el(e,t,n){const r=t(e);return typeof r=="undefined"?n:r}var Co;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(Co||(Co={}));var wo;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(wo||(wo={}));var _o;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(_o||(_o={}));class tl{constructor(t){this._textModelResolvedOptionsBrand=void 0,this.tabSize=Math.max(1,t.tabSize|0),this.indentSize=t.tabSize|0,this.insertSpaces=Boolean(t.insertSpaces),this.defaultEOL=t.defaultEOL|0,this.trimAutoWhitespace=Boolean(t.trimAutoWhitespace),this.bracketPairColorizationOptions=t.bracketPairColorizationOptions}equals(t){return this.tabSize===t.tabSize&&this.indentSize===t.indentSize&&this.insertSpaces===t.insertSpaces&&this.defaultEOL===t.defaultEOL&&this.trimAutoWhitespace===t.trimAutoWhitespace&&equals(this.bracketPairColorizationOptions,t.bracketPairColorizationOptions)}createChangeEvent(t){return{tabSize:this.tabSize!==t.tabSize,indentSize:this.indentSize!==t.indentSize,insertSpaces:this.insertSpaces!==t.insertSpaces,trimAutoWhitespace:this.trimAutoWhitespace!==t.trimAutoWhitespace}}}class nl{constructor(t,n){this._findMatchBrand=void 0,this.range=t,this.matches=n}}class rl{constructor(t,n,r,i,a,s){this.identifier=t,this.range=n,this.text=r,this.forceMoveMarkers=i,this.isAutoWhitespaceEdit=a,this._isTracked=s}}class il{constructor(t,n,r){this.regex=t,this.wordSeparators=n,this.simpleSearch=r}}class ol{constructor(t,n,r){this.reverseEdits=t,this.changes=n,this.trimAutoWhitespaceLineNumbers=r}}function al(e){return!e.isTooLargeForSyncing()&&!e.isForSimpleWidget}const ds=999;class sl{constructor(t,n,r,i){this.searchString=t,this.isRegex=n,this.matchCase=r,this.wordSeparators=i}parseSearchRequest(){if(this.searchString==="")return null;let t;this.isRegex?t=ms(this.searchString):t=this.searchString.indexOf(`
`)>=0;let n=null;try{n=strings.createRegExp(this.searchString,this.isRegex,{matchCase:this.matchCase,wholeWord:!1,multiline:t,global:!0,unicode:!0})}catch(i){return null}if(!n)return null;let r=!this.isRegex&&!t;return r&&this.searchString.toLowerCase()!==this.searchString.toUpperCase()&&(r=this.matchCase),new SearchData(n,this.wordSeparators?getMapForWordSeparators(this.wordSeparators):null,r?this.searchString:null)}}function ms(e){if(!e||e.length===0)return!1;for(let t=0,n=e.length;t<n;t++)if(e.charCodeAt(t)===92){if(t++,t>=n)break;const r=e.charCodeAt(t);if(r===110||r===114||r===87)return!0}return!1}function Bt(e,t,n){if(!n)return new FindMatch(e,null);const r=[];for(let i=0,a=t.length;i<a;i++)r[i]=t[i];return new FindMatch(e,r)}class So{constructor(t){const n=[];let r=0;for(let i=0,a=t.length;i<a;i++)t.charCodeAt(i)===10&&(n[r++]=i);this._lineFeedsOffsets=n}findLineFeedCountBeforeOffset(t){const n=this._lineFeedsOffsets;let r=0,i=n.length-1;if(i===-1||t<=n[0])return 0;for(;r<i;){const a=r+((i-r)/2>>0);n[a]>=t?i=a-1:n[a+1]>=t?(r=a,i=a):r=a+1}return r+1}}class ul{static findMatches(t,n,r,i,a){const s=n.parseSearchRequest();return s?s.regex.multiline?this._doFindMatchesMultiline(t,r,new Ut(s.wordSeparators,s.regex),i,a):this._doFindMatchesLineByLine(t,r,s,i,a):[]}static _getMultilineMatchRange(t,n,r,i,a,s){let u,c=0;i?(c=i.findLineFeedCountBeforeOffset(a),u=n+a+c):u=n+a;let l;if(i){const d=i.findLineFeedCountBeforeOffset(a+s.length)-c;l=u+s.length+d}else l=u+s.length;const f=t.getPositionAt(u),h=t.getPositionAt(l);return new Range(f.lineNumber,f.column,h.lineNumber,h.column)}static _doFindMatchesMultiline(t,n,r,i,a){const s=t.getOffsetAt(n.getStartPosition()),u=t.getValueInRange(n,1),c=t.getEOL()===`\r
`?new So(u):null,l=[];let f=0,h;for(r.reset(0);h=r.next(u);)if(l[f++]=Bt(this._getMultilineMatchRange(t,s,u,c,h.index,h[0]),h,i),f>=a)return l;return l}static _doFindMatchesLineByLine(t,n,r,i,a){const s=[];let u=0;if(n.startLineNumber===n.endLineNumber){const l=t.getLineContent(n.startLineNumber).substring(n.startColumn-1,n.endColumn-1);return u=this._findMatchesInLine(r,l,n.startLineNumber,n.startColumn-1,u,s,i,a),s}const c=t.getLineContent(n.startLineNumber).substring(n.startColumn-1);u=this._findMatchesInLine(r,c,n.startLineNumber,n.startColumn-1,u,s,i,a);for(let l=n.startLineNumber+1;l<n.endLineNumber&&u<a;l++)u=this._findMatchesInLine(r,t.getLineContent(l),l,0,u,s,i,a);if(u<a){const l=t.getLineContent(n.endLineNumber).substring(0,n.endColumn-1);u=this._findMatchesInLine(r,l,n.endLineNumber,0,u,s,i,a)}return s}static _findMatchesInLine(t,n,r,i,a,s,u,c){const l=t.wordSeparators;if(!u&&t.simpleSearch){const d=t.simpleSearch,m=d.length,p=n.length;let y=-m;for(;(y=n.indexOf(d,y+m))!==-1;)if((!l||Ao(l,n,p,y,m))&&(s[a++]=new FindMatch(new Range(r,y+1+i,r,y+1+m+i),null),a>=c))return a;return a}const f=new Ut(t.wordSeparators,t.regex);let h;f.reset(0);do if(h=f.next(n),h&&(s[a++]=Bt(new Range(r,h.index+1+i,r,h.index+1+h[0].length+i),h,u),a>=c))return a;while(h);return a}static findNextMatch(t,n,r,i){const a=n.parseSearchRequest();if(!a)return null;const s=new Ut(a.wordSeparators,a.regex);return a.regex.multiline?this._doFindNextMatchMultiline(t,r,s,i):this._doFindNextMatchLineByLine(t,r,s,i)}static _doFindNextMatchMultiline(t,n,r,i){const a=new Position(n.lineNumber,1),s=t.getOffsetAt(a),u=t.getLineCount(),c=t.getValueInRange(new Range(a.lineNumber,a.column,u,t.getLineMaxColumn(u)),1),l=t.getEOL()===`\r
`?new So(c):null;r.reset(n.column-1);let f=r.next(c);return f?Bt(this._getMultilineMatchRange(t,s,c,l,f.index,f[0]),f,i):n.lineNumber!==1||n.column!==1?this._doFindNextMatchMultiline(t,new Position(1,1),r,i):null}static _doFindNextMatchLineByLine(t,n,r,i){const a=t.getLineCount(),s=n.lineNumber,u=t.getLineContent(s),c=this._findFirstMatchInLine(r,u,s,n.column,i);if(c)return c;for(let l=1;l<=a;l++){const f=(s+l-1)%a,h=t.getLineContent(f+1),d=this._findFirstMatchInLine(r,h,f+1,1,i);if(d)return d}return null}static _findFirstMatchInLine(t,n,r,i,a){t.reset(i-1);const s=t.next(n);return s?Bt(new Range(r,s.index+1,r,s.index+1+s[0].length),s,a):null}static findPreviousMatch(t,n,r,i){const a=n.parseSearchRequest();if(!a)return null;const s=new Ut(a.wordSeparators,a.regex);return a.regex.multiline?this._doFindPreviousMatchMultiline(t,r,s,i):this._doFindPreviousMatchLineByLine(t,r,s,i)}static _doFindPreviousMatchMultiline(t,n,r,i){const a=this._doFindMatchesMultiline(t,new Range(1,1,n.lineNumber,n.column),r,i,10*ds);if(a.length>0)return a[a.length-1];const s=t.getLineCount();return n.lineNumber!==s||n.column!==t.getLineMaxColumn(s)?this._doFindPreviousMatchMultiline(t,new Position(s,t.getLineMaxColumn(s)),r,i):null}static _doFindPreviousMatchLineByLine(t,n,r,i){const a=t.getLineCount(),s=n.lineNumber,u=t.getLineContent(s).substring(0,n.column-1),c=this._findLastMatchInLine(r,u,s,i);if(c)return c;for(let l=1;l<=a;l++){const f=(a+s-l-1)%a,h=t.getLineContent(f+1),d=this._findLastMatchInLine(r,h,f+1,i);if(d)return d}return null}static _findLastMatchInLine(t,n,r,i){let a=null,s;for(t.reset(0);s=t.next(n);)a=Bt(new Range(r,s.index+1,r,s.index+1+s[0].length),s,i);return a}}function gs(e,t,n,r,i){if(r===0)return!0;const a=t.charCodeAt(r-1);if(e.get(a)!==0||a===13||a===10)return!0;if(i>0){const s=t.charCodeAt(r);if(e.get(s)!==0)return!0}return!1}function ps(e,t,n,r,i){if(r+i===n)return!0;const a=t.charCodeAt(r+i);if(e.get(a)!==0||a===13||a===10)return!0;if(i>0){const s=t.charCodeAt(r+i-1);if(e.get(s)!==0)return!0}return!1}function Ao(e,t,n,r,i){return gs(e,t,n,r,i)&&ps(e,t,n,r,i)}class Ut{constructor(t,n){this._wordSeparators=t,this._searchRegex=n,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(t){this._searchRegex.lastIndex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(t){const n=t.length;let r;do{if(this._prevMatchStartIndex+this._prevMatchLength===n||(r=this._searchRegex.exec(t),!r))return null;const i=r.index,a=r[0].length;if(i===this._prevMatchStartIndex&&a===this._prevMatchLength){if(a===0){ni(t,n,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=i,this._prevMatchLength=a,!this._wordSeparators||Ao(this._wordSeparators,t,n,i,a))return r}while(r);return null}}class bs{static computeUnicodeHighlights(t,n,r){const i=r?r.startLineNumber:1,a=r?r.endLineNumber:t.getLineCount(),s=new xo(n),u=s.getCandidateCodePoints();let c;u==="allNonBasicAscii"?c=new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):c=new RegExp(`${vs(Array.from(u))}`,"g");const l=new Ut(null,c),f=[];let h=!1,d,m=0,p=0,y=0;e:for(let g=i,w=a;g<=w;g++){const b=t.getLineContent(g),v=b.length;l.reset(0);do if(d=l.next(b),d){let L=d.index,x=d.index+d[0].length;if(L>0){const S=b.charCodeAt(L-1);It(S)&&L--}if(x+1<v){const S=b.charCodeAt(x-1);It(S)&&x++}const A=b.substring(L,x),C=Xn(L+1,Li,b,0),_=s.shouldHighlightNonBasicASCII(A,C?C.word:null);if(_!==0){_===3?m++:_===2?p++:_===1?y++:J1(_);const S=1e3;if(f.length>=S){h=!0;break e}f.push(new J(g,L+1,g,x+1))}}while(d)}return{ranges:f,hasMore:h,ambiguousCharacterCount:m,invisibleCharacterCount:p,nonBasicAsciiCharacterCount:y}}static computeUnicodeHighlightReason(t,n){const r=new xo(n);switch(r.shouldHighlightNonBasicASCII(t,null)){case 0:return null;case 2:return{kind:1};case 3:{const i=t.codePointAt(0),a=r.ambiguousCharacters.getPrimaryConfusable(i),s=xe.getLocales().filter(u=>!xe.getInstance(new Set([...n.allowedLocales,u])).isAmbiguous(i));return{kind:0,confusableWith:String.fromCodePoint(a),notAmbiguousInLocales:s}}case 1:return{kind:2}}}}function vs(e,t){return`[${Xr(e.map(n=>String.fromCodePoint(n)).join(""))}]`}class xo{constructor(t){this.options=t,this.allowedCodePoints=new Set(t.allowedCodePoints),this.ambiguousCharacters=xe.getInstance(new Set(t.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const t=new Set;if(this.options.invisibleCharacters)for(const n of Ge.codePoints)Eo(String.fromCodePoint(n))||t.add(n);if(this.options.ambiguousCharacters)for(const n of this.ambiguousCharacters.getConfusableCodePoints())t.add(n);for(const n of this.allowedCodePoints)t.delete(n);return t}shouldHighlightNonBasicASCII(t,n){const r=t.codePointAt(0);if(this.allowedCodePoints.has(r))return 0;if(this.options.nonBasicASCII)return 1;let i=!1,a=!1;if(n)for(let s of n){const u=s.codePointAt(0),c=ca(s);i=i||c,!c&&!this.ambiguousCharacters.isAmbiguous(u)&&!Ge.isInvisibleCharacter(u)&&(a=!0)}return!i&&a?0:this.options.invisibleCharacters&&!Eo(t)&&Ge.isInvisibleCharacter(r)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(r)?3:0}}function Eo(e){return e===" "||e===`
`||e==="	"}var st=function(e,t,n,r){function i(a){return a instanceof n?a:new n(function(s){s(a)})}return new(n||(n=Promise))(function(a,s){function u(f){try{l(r.next(f))}catch(h){s(h)}}function c(f){try{l(r.throw(f))}catch(h){s(h)}}function l(f){f.done?a(f.value):i(f.value).then(u,c)}l((r=r.apply(e,t||[])).next())})};class ys extends Ba{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(t){return this._lines[t-1]}getWordAtPosition(t,n){const r=Xn(t.column,qa(n),this._lines[t.lineNumber-1],0);return r?new J(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn):null}words(t){const n=this._lines,r=this._wordenize.bind(this);let i=0,a="",s=0,u=[];return{*[Symbol.iterator](){for(;;)if(s<u.length){const c=a.substring(u[s].start,u[s].end);s+=1,yield c}else if(i<n.length)a=n[i],u=r(a,t),s=0,i+=1;else break}}}getLineWords(t,n){const r=this._lines[t-1],i=this._wordenize(r,n),a=[];for(const s of i)a.push({word:r.substring(s.start,s.end),startColumn:s.start+1,endColumn:s.end+1});return a}_wordenize(t,n){const r=[];let i;for(n.lastIndex=0;(i=n.exec(t))&&i[0].length!==0;)r.push({start:i.index,end:i.index+i[0].length});return r}getValueInRange(t){if(t=this._validateRange(t),t.startLineNumber===t.endLineNumber)return this._lines[t.startLineNumber-1].substring(t.startColumn-1,t.endColumn-1);const n=this._eol,r=t.startLineNumber-1,i=t.endLineNumber-1,a=[];a.push(this._lines[r].substring(t.startColumn-1));for(let s=r+1;s<i;s++)a.push(this._lines[s]);return a.push(this._lines[i].substring(0,t.endColumn-1)),a.join(n)}offsetAt(t){return t=this._validatePosition(t),this._ensureLineStarts(),this._lineStarts.getPrefixSum(t.lineNumber-2)+(t.column-1)}positionAt(t){t=Math.floor(t),t=Math.max(0,t),this._ensureLineStarts();const n=this._lineStarts.getIndexOf(t),r=this._lines[n.index].length;return{lineNumber:1+n.index,column:1+Math.min(n.remainder,r)}}_validateRange(t){const n=this._validatePosition({lineNumber:t.startLineNumber,column:t.startColumn}),r=this._validatePosition({lineNumber:t.endLineNumber,column:t.endColumn});return n.lineNumber!==t.startLineNumber||n.column!==t.startColumn||r.lineNumber!==t.endLineNumber||r.column!==t.endColumn?{startLineNumber:n.lineNumber,startColumn:n.column,endLineNumber:r.lineNumber,endColumn:r.column}:t}_validatePosition(t){if(!pe.isIPosition(t))throw new Error("bad position");let{lineNumber:n,column:r}=t,i=!1;if(n<1)n=1,r=1,i=!0;else if(n>this._lines.length)n=this._lines.length,r=this._lines[n-1].length+1,i=!0;else{const a=this._lines[n-1].length+1;r<1?(r=1,i=!0):r>a&&(r=a,i=!0)}return i?{lineNumber:n,column:r}:t}}class ut{constructor(t,n){this._host=t,this._models=Object.create(null),this._foreignModuleFactory=n,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(t){return this._models[t]}_getModels(){const t=[];return Object.keys(this._models).forEach(n=>t.push(this._models[n])),t}acceptNewModel(t){this._models[t.url]=new ys(at.parse(t.url),t.lines,t.EOL,t.versionId)}acceptModelChanged(t,n){!this._models[t]||this._models[t].onEvents(n)}acceptRemovedModel(t){!this._models[t]||delete this._models[t]}computeUnicodeHighlights(t,n,r){return st(this,void 0,void 0,function*(){const i=this._getModel(t);return i?bs.computeUnicodeHighlights(i,n,r):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}})}computeDiff(t,n,r,i){return st(this,void 0,void 0,function*(){const a=this._getModel(t),s=this._getModel(n);if(!a||!s)return null;const u=a.getLinesContent(),c=s.getLinesContent(),l=new Ka(u,c,{shouldComputeCharChanges:!0,shouldPostProcessCharChanges:!0,shouldIgnoreTrimWhitespace:r,shouldMakePrettyDiff:!0,maxComputationTime:i}).computeDiff(),f=l.changes.length>0?!1:this._modelsAreIdentical(a,s);return{quitEarly:l.quitEarly,identical:f,changes:l.changes}})}_modelsAreIdentical(t,n){const r=t.getLineCount(),i=n.getLineCount();if(r!==i)return!1;for(let a=1;a<=r;a++){const s=t.getLineContent(a),u=n.getLineContent(a);if(s!==u)return!1}return!0}computeMoreMinimalEdits(t,n){return st(this,void 0,void 0,function*(){const r=this._getModel(t);if(!r)return n;const i=[];let a;n=n.slice(0).sort((s,u)=>{if(s.range&&u.range)return J.compareRangesUsingStarts(s.range,u.range);const c=s.range?0:1,l=u.range?0:1;return c-l});for(let{range:s,text:u,eol:c}of n){if(typeof c=="number"&&(a=c),J.isEmpty(s)&&!u)continue;const l=r.getValueInRange(s);if(u=u.replace(/\r\n|\n|\r/g,r.eol),l===u)continue;if(Math.max(u.length,l.length)>ut._diffLimit){i.push({range:s,text:u});continue}const f=Sa(l,u,!1),h=r.offsetAt(J.lift(s).getStartPosition());for(const d of f){const m=r.positionAt(h+d.originalStart),p=r.positionAt(h+d.originalStart+d.originalLength),y={text:u.substr(d.modifiedStart,d.modifiedLength),range:{startLineNumber:m.lineNumber,startColumn:m.column,endLineNumber:p.lineNumber,endColumn:p.column}};r.getValueInRange(y.range)!==y.text&&i.push(y)}}return typeof a=="number"&&i.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i})}computeLinks(t){return st(this,void 0,void 0,function*(){const n=this._getModel(t);return n?Qa(n):null})}textualSuggest(t,n,r,i){return st(this,void 0,void 0,function*(){const a=new an(!0),s=new RegExp(r,i),u=new Set;e:for(let c of t){const l=this._getModel(c);if(l){for(let f of l.words(s))if(!(f===n||!isNaN(Number(f)))&&(u.add(f),u.size>ut._suggestionsLimit))break e}}return{words:Array.from(u),duration:a.elapsed()}})}computeWordRanges(t,n,r,i){return st(this,void 0,void 0,function*(){const a=this._getModel(t);if(!a)return Object.create(null);const s=new RegExp(r,i),u=Object.create(null);for(let c=n.startLineNumber;c<n.endLineNumber;c++){const l=a.getLineWords(c,s);for(const f of l){if(!isNaN(Number(f.word)))continue;let h=u[f.word];h||(h=[],u[f.word]=h),h.push({startLineNumber:c,startColumn:f.startColumn,endLineNumber:c,endColumn:f.endColumn})}}return u})}navigateValueSet(t,n,r,i,a){return st(this,void 0,void 0,function*(){const s=this._getModel(t);if(!s)return null;const u=new RegExp(i,a);n.startColumn===n.endColumn&&(n={startLineNumber:n.startLineNumber,startColumn:n.startColumn,endLineNumber:n.endLineNumber,endColumn:n.endColumn+1});const c=s.getValueInRange(n),l=s.getWordAtPosition({lineNumber:n.startLineNumber,column:n.startColumn},u);if(!l)return null;const f=s.getValueInRange(l);return nr.INSTANCE.navigateValueSet(n,c,l,f,r)})}loadForeignModule(t,n,r){const i={host:G1(r,(a,s)=>this._host.fhr(a,s)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(i,n),Promise.resolve(Vn(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(t,n){if(!this._foreignModule||typeof this._foreignModule[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._foreignModule[t].apply(this._foreignModule,n))}catch(r){return Promise.reject(r)}}}ut._diffLimit=1e5,ut._suggestionsLimit=1e4;function cl(e){return new ut(e,null)}typeof importScripts=="function"&&(ie.monaco=us());let mr=!1;function Lo(e){if(mr)return;mr=!0;const t=new li(n=>{self.postMessage(n)},n=>new ut(n,e));self.onmessage=n=>{t.onmessage(n.data)}}self.onmessage=e=>{mr||Lo(null)};var gr=ce(155);/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/function pr(e,t){t===void 0&&(t=!1);var n=e.length,r=0,i="",a=0,s=16,u=0,c=0,l=0,f=0,h=0;function d(v,L){for(var x=0,A=0;x<v||!L;){var C=e.charCodeAt(r);if(C>=48&&C<=57)A=A*16+C-48;else if(C>=65&&C<=70)A=A*16+C-65+10;else if(C>=97&&C<=102)A=A*16+C-97+10;else break;r++,x++}return x<v&&(A=-1),A}function m(v){r=v,i="",a=0,s=16,h=0}function p(){var v=r;if(e.charCodeAt(r)===48)r++;else for(r++;r<e.length&&Ct(e.charCodeAt(r));)r++;if(r<e.length&&e.charCodeAt(r)===46)if(r++,r<e.length&&Ct(e.charCodeAt(r)))for(r++;r<e.length&&Ct(e.charCodeAt(r));)r++;else return h=3,e.substring(v,r);var L=r;if(r<e.length&&(e.charCodeAt(r)===69||e.charCodeAt(r)===101))if(r++,(r<e.length&&e.charCodeAt(r)===43||e.charCodeAt(r)===45)&&r++,r<e.length&&Ct(e.charCodeAt(r))){for(r++;r<e.length&&Ct(e.charCodeAt(r));)r++;L=r}else h=3;return e.substring(v,L)}function y(){for(var v="",L=r;;){if(r>=n){v+=e.substring(L,r),h=2;break}var x=e.charCodeAt(r);if(x===34){v+=e.substring(L,r),r++;break}if(x===92){if(v+=e.substring(L,r),r++,r>=n){h=2;break}var A=e.charCodeAt(r++);switch(A){case 34:v+='"';break;case 92:v+="\\";break;case 47:v+="/";break;case 98:v+="\b";break;case 102:v+="\f";break;case 110:v+=`
`;break;case 114:v+="\r";break;case 116:v+="	";break;case 117:var C=d(4,!0);C>=0?v+=String.fromCharCode(C):h=4;break;default:h=5}L=r;continue}if(x>=0&&x<=31)if($t(x)){v+=e.substring(L,r),h=2;break}else h=6;r++}return v}function g(){if(i="",h=0,a=r,c=u,f=l,r>=n)return a=n,s=17;var v=e.charCodeAt(r);if(br(v)){do r++,i+=String.fromCharCode(v),v=e.charCodeAt(r);while(br(v));return s=15}if($t(v))return r++,i+=String.fromCharCode(v),v===13&&e.charCodeAt(r)===10&&(r++,i+=`
`),u++,l=r,s=14;switch(v){case 123:return r++,s=1;case 125:return r++,s=2;case 91:return r++,s=3;case 93:return r++,s=4;case 58:return r++,s=6;case 44:return r++,s=5;case 34:return r++,i=y(),s=10;case 47:var L=r-1;if(e.charCodeAt(r+1)===47){for(r+=2;r<n&&!$t(e.charCodeAt(r));)r++;return i=e.substring(L,r),s=12}if(e.charCodeAt(r+1)===42){r+=2;for(var x=n-1,A=!1;r<x;){var C=e.charCodeAt(r);if(C===42&&e.charCodeAt(r+1)===47){r+=2,A=!0;break}r++,$t(C)&&(C===13&&e.charCodeAt(r)===10&&r++,u++,l=r)}return A||(r++,h=1),i=e.substring(L,r),s=13}return i+=String.fromCharCode(v),r++,s=16;case 45:if(i+=String.fromCharCode(v),r++,r===n||!Ct(e.charCodeAt(r)))return s=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=p(),s=11;default:for(;r<n&&w(v);)r++,v=e.charCodeAt(r);if(a!==r){switch(i=e.substring(a,r),i){case"true":return s=8;case"false":return s=9;case"null":return s=7}return s=16}return i+=String.fromCharCode(v),r++,s=16}}function w(v){if(br(v)||$t(v))return!1;switch(v){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}function b(){var v;do v=g();while(v>=12&&v<=15);return v}return{setPosition:m,getPosition:function(){return r},scan:t?b:g,getToken:function(){return s},getTokenValue:function(){return i},getTokenOffset:function(){return a},getTokenLength:function(){return r-a},getTokenStartLine:function(){return c},getTokenStartCharacter:function(){return a-f},getTokenError:function(){return h}}}function br(e){return e===32||e===9||e===11||e===12||e===160||e===5760||e>=8192&&e<=8203||e===8239||e===8287||e===12288||e===65279}function $t(e){return e===10||e===13||e===8232||e===8233}function Ct(e){return e>=48&&e<=57}function Cs(e,t,n){var r,i,a,s,u;if(t){for(s=t.offset,u=s+t.length,a=s;a>0&&!No(e,a-1);)a--;for(var c=u;c<e.length&&!No(e,c);)c++;i=e.substring(a,c),r=ws(i,n)}else i=e,r=0,a=0,s=0,u=e.length;var l=_s(n,e),f=!1,h=0,d;n.insertSpaces?d=vr(" ",n.tabSize||4):d="	";var m=pr(i,!1),p=!1;function y(){return l+vr(d,r+h)}function g(){var T=m.scan();for(f=!1;T===15||T===14;)f=f||T===14,T=m.scan();return p=T===16||m.getTokenError()!==0,T}var w=[];function b(T,R,j){!p&&(!t||R<u&&j>s)&&e.substring(R,j)!==T&&w.push({offset:R,length:j-R,content:T})}var v=g();if(v!==17){var L=m.getTokenOffset()+a,x=vr(d,r);b(x,a,L)}for(;v!==17;){for(var A=m.getTokenOffset()+m.getTokenLength()+a,C=g(),_="",S=!1;!f&&(C===12||C===13);){var k=m.getTokenOffset()+a;b(" ",A,k),A=m.getTokenOffset()+m.getTokenLength()+a,S=C===12,_=S?y():"",C=g()}if(C===2)v!==1&&(h--,_=y());else if(C===4)v!==3&&(h--,_=y());else{switch(v){case 3:case 1:h++,_=y();break;case 5:case 12:_=y();break;case 13:f?_=y():S||(_=" ");break;case 6:S||(_=" ");break;case 10:if(C===6){S||(_="");break}case 7:case 8:case 9:case 11:case 2:case 4:C===12||C===13?S||(_=" "):C!==5&&C!==17&&(p=!0);break;case 16:p=!0;break}f&&(C===12||C===13)&&(_=y())}C===17&&(_=n.insertFinalNewline?l:"");var E=m.getTokenOffset()+a;b(_,A,E),v=C}return w}function vr(e,t){for(var n="",r=0;r<t;r++)n+=e;return n}function ws(e,t){for(var n=0,r=0,i=t.tabSize||4;n<e.length;){var a=e.charAt(n);if(a===" ")r++;else if(a==="	")r+=i;else break;n++}return Math.floor(r/i)}function _s(e,t){for(var n=0;n<t.length;n++){var r=t.charAt(n);if(r==="\r")return n+1<t.length&&t.charAt(n+1)===`
`?`\r
`:"\r";if(r===`
`)return`
`}return e&&e.eol||`
`}function No(e,t){return`\r
`.indexOf(e.charAt(t))!==-1}var pn;(function(e){e.DEFAULT={allowTrailingComma:!1}})(pn||(pn={}));function Ss(e,t,n){t===void 0&&(t=[]),n===void 0&&(n=pn.DEFAULT);var r=null,i=[],a=[];function s(c){Array.isArray(i)?i.push(c):r!==null&&(i[r]=c)}var u={onObjectBegin:function(){var c={};s(c),a.push(i),i=c,r=null},onObjectProperty:function(c){r=c},onObjectEnd:function(){i=a.pop()},onArrayBegin:function(){var c=[];s(c),a.push(i),i=c,r=null},onArrayEnd:function(){i=a.pop()},onLiteralValue:s,onError:function(c,l,f){t.push({error:c,offset:l,length:f})}};return xs(e,u,n),i[0]}function ko(e){if(!e.parent||!e.parent.children)return[];var t=ko(e.parent);if(e.parent.type==="property"){var n=e.parent.children[0].value;t.push(n)}else if(e.parent.type==="array"){var r=e.parent.children.indexOf(e);r!==-1&&t.push(r)}return t}function yr(e){switch(e.type){case"array":return e.children.map(yr);case"object":for(var t=Object.create(null),n=0,r=e.children;n<r.length;n++){var i=r[n],a=i.children[1];a&&(t[i.children[0].value]=yr(a))}return t;case"null":case"string":case"number":case"boolean":return e.value;default:return}}function As(e,t,n){return n===void 0&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}function Oo(e,t,n){if(n===void 0&&(n=!1),As(e,t,n)){var r=e.children;if(Array.isArray(r))for(var i=0;i<r.length&&r[i].offset<=t;i++){var a=Oo(r[i],t,n);if(a)return a}return e}}function xs(e,t,n){n===void 0&&(n=pn.DEFAULT);var r=pr(e,!1);function i(S){return S?function(){return S(r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter())}:function(){return!0}}function a(S){return S?function(k){return S(k,r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter())}:function(){return!0}}var s=i(t.onObjectBegin),u=a(t.onObjectProperty),c=i(t.onObjectEnd),l=i(t.onArrayBegin),f=i(t.onArrayEnd),h=a(t.onLiteralValue),d=a(t.onSeparator),m=i(t.onComment),p=a(t.onError),y=n&&n.disallowComments,g=n&&n.allowTrailingComma;function w(){for(;;){var S=r.scan();switch(r.getTokenError()){case 4:b(14);break;case 5:b(15);break;case 3:b(13);break;case 1:y||b(11);break;case 2:b(12);break;case 6:b(16);break}switch(S){case 12:case 13:y?b(10):m();break;case 16:b(1);break;case 15:case 14:break;default:return S}}}function b(S,k,E){if(k===void 0&&(k=[]),E===void 0&&(E=[]),p(S),k.length+E.length>0)for(var T=r.getToken();T!==17;){if(k.indexOf(T)!==-1){w();break}else if(E.indexOf(T)!==-1)break;T=w()}}function v(S){var k=r.getTokenValue();return S?h(k):u(k),w(),!0}function L(){switch(r.getToken()){case 11:var S=r.getTokenValue(),k=Number(S);isNaN(k)&&(b(2),k=0),h(k);break;case 7:h(null);break;case 8:h(!0);break;case 9:h(!1);break;default:return!1}return w(),!0}function x(){return r.getToken()!==10?(b(3,[],[2,5]),!1):(v(!1),r.getToken()===6?(d(":"),w(),_()||b(4,[],[2,5])):b(5,[],[2,5]),!0)}function A(){s(),w();for(var S=!1;r.getToken()!==2&&r.getToken()!==17;){if(r.getToken()===5){if(S||b(4,[],[]),d(","),w(),r.getToken()===2&&g)break}else S&&b(6,[],[]);x()||b(4,[],[2,5]),S=!0}return c(),r.getToken()!==2?b(7,[2],[]):w(),!0}function C(){l(),w();for(var S=!1;r.getToken()!==4&&r.getToken()!==17;){if(r.getToken()===5){if(S||b(4,[],[]),d(","),w(),r.getToken()===4&&g)break}else S&&b(6,[],[]);_()||b(4,[],[4,5]),S=!0}return f(),r.getToken()!==4?b(8,[4],[]):w(),!0}function _(){switch(r.getToken()){case 3:return C();case 1:return A();case 10:return v(!0);default:return L()}}return w(),r.getToken()===17?n.allowEmptyContent?!0:(b(4,[],[]),!1):_()?(r.getToken()!==17&&b(9,[],[]),!0):(b(4,[],[]),!1)}var wt=pr,Es=Ss,Ls=Oo,Ns=ko,ks=yr;function Os(e,t,n){return Cs(e,t,n)}function qt(e,t){if(e===t)return!0;if(e==null||t===null||t===void 0||typeof e!=typeof t||typeof e!="object"||Array.isArray(e)!==Array.isArray(t))return!1;var n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!qt(e[n],t[n]))return!1}else{var i=[];for(r in e)i.push(r);i.sort();var a=[];for(r in t)a.push(r);if(a.sort(),!qt(i,a))return!1;for(n=0;n<i.length;n++)if(!qt(e[i[n]],t[i[n]]))return!1}return!0}function we(e){return typeof e=="number"}function Ue(e){return typeof e!="undefined"}function Re(e){return typeof e=="boolean"}function Ms(e){return typeof e=="string"}function Ts(e,t){if(e.length<t.length)return!1;for(var n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0}function Wt(e,t){var n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:n===0?e===t:!1}function bn(e){var t="";Ts(e,"(?i)")&&(e=e.substring(4),t="i");try{return new RegExp(e,t+"u")}catch(n){try{return new RegExp(e,t)}catch(r){return}}}var Mo;(function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647})(Mo||(Mo={}));var vn;(function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647})(vn||(vn={}));var Oe;(function(e){function t(r,i){return r===Number.MAX_VALUE&&(r=vn.MAX_VALUE),i===Number.MAX_VALUE&&(i=vn.MAX_VALUE),{line:r,character:i}}e.create=t;function n(r){var i=r;return N.objectLiteral(i)&&N.uinteger(i.line)&&N.uinteger(i.character)}e.is=n})(Oe||(Oe={}));var W;(function(e){function t(r,i,a,s){if(N.uinteger(r)&&N.uinteger(i)&&N.uinteger(a)&&N.uinteger(s))return{start:Oe.create(r,i),end:Oe.create(a,s)};if(Oe.is(r)&&Oe.is(i))return{start:r,end:i};throw new Error("Range#create called with invalid arguments["+r+", "+i+", "+a+", "+s+"]")}e.create=t;function n(r){var i=r;return N.objectLiteral(i)&&Oe.is(i.start)&&Oe.is(i.end)}e.is=n})(W||(W={}));var Ht;(function(e){function t(r,i){return{uri:r,range:i}}e.create=t;function n(r){var i=r;return N.defined(i)&&W.is(i.range)&&(N.string(i.uri)||N.undefined(i.uri))}e.is=n})(Ht||(Ht={}));var To;(function(e){function t(r,i,a,s){return{targetUri:r,targetRange:i,targetSelectionRange:a,originSelectionRange:s}}e.create=t;function n(r){var i=r;return N.defined(i)&&W.is(i.targetRange)&&N.string(i.targetUri)&&(W.is(i.targetSelectionRange)||N.undefined(i.targetSelectionRange))&&(W.is(i.originSelectionRange)||N.undefined(i.originSelectionRange))}e.is=n})(To||(To={}));var Cr;(function(e){function t(r,i,a,s){return{red:r,green:i,blue:a,alpha:s}}e.create=t;function n(r){var i=r;return N.numberRange(i.red,0,1)&&N.numberRange(i.green,0,1)&&N.numberRange(i.blue,0,1)&&N.numberRange(i.alpha,0,1)}e.is=n})(Cr||(Cr={}));var Io;(function(e){function t(r,i){return{range:r,color:i}}e.create=t;function n(r){var i=r;return W.is(i.range)&&Cr.is(i.color)}e.is=n})(Io||(Io={}));var Po;(function(e){function t(r,i,a){return{label:r,textEdit:i,additionalTextEdits:a}}e.create=t;function n(r){var i=r;return N.string(i.label)&&(N.undefined(i.textEdit)||Me.is(i))&&(N.undefined(i.additionalTextEdits)||N.typedArray(i.additionalTextEdits,Me.is))}e.is=n})(Po||(Po={}));var zt;(function(e){e.Comment="comment",e.Imports="imports",e.Region="region"})(zt||(zt={}));var Fo;(function(e){function t(r,i,a,s,u){var c={startLine:r,endLine:i};return N.defined(a)&&(c.startCharacter=a),N.defined(s)&&(c.endCharacter=s),N.defined(u)&&(c.kind=u),c}e.create=t;function n(r){var i=r;return N.uinteger(i.startLine)&&N.uinteger(i.startLine)&&(N.undefined(i.startCharacter)||N.uinteger(i.startCharacter))&&(N.undefined(i.endCharacter)||N.uinteger(i.endCharacter))&&(N.undefined(i.kind)||N.string(i.kind))}e.is=n})(Fo||(Fo={}));var wr;(function(e){function t(r,i){return{location:r,message:i}}e.create=t;function n(r){var i=r;return N.defined(i)&&Ht.is(i.location)&&N.string(i.message)}e.is=n})(wr||(wr={}));var _e;(function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4})(_e||(_e={}));var Ro;(function(e){e.Unnecessary=1,e.Deprecated=2})(Ro||(Ro={}));var Vo;(function(e){function t(n){var r=n;return r!=null&&N.string(r.href)}e.is=t})(Vo||(Vo={}));var $e;(function(e){function t(r,i,a,s,u,c){var l={range:r,message:i};return N.defined(a)&&(l.severity=a),N.defined(s)&&(l.code=s),N.defined(u)&&(l.source=u),N.defined(c)&&(l.relatedInformation=c),l}e.create=t;function n(r){var i,a=r;return N.defined(a)&&W.is(a.range)&&N.string(a.message)&&(N.number(a.severity)||N.undefined(a.severity))&&(N.integer(a.code)||N.string(a.code)||N.undefined(a.code))&&(N.undefined(a.codeDescription)||N.string((i=a.codeDescription)===null||i===void 0?void 0:i.href))&&(N.string(a.source)||N.undefined(a.source))&&(N.undefined(a.relatedInformation)||N.typedArray(a.relatedInformation,wr.is))}e.is=n})($e||($e={}));var Gt;(function(e){function t(r,i){for(var a=[],s=2;s<arguments.length;s++)a[s-2]=arguments[s];var u={title:r,command:i};return N.defined(a)&&a.length>0&&(u.arguments=a),u}e.create=t;function n(r){var i=r;return N.defined(i)&&N.string(i.title)&&N.string(i.command)}e.is=n})(Gt||(Gt={}));var Me;(function(e){function t(a,s){return{range:a,newText:s}}e.replace=t;function n(a,s){return{range:{start:a,end:a},newText:s}}e.insert=n;function r(a){return{range:a,newText:""}}e.del=r;function i(a){var s=a;return N.objectLiteral(s)&&N.string(s.newText)&&W.is(s.range)}e.is=i})(Me||(Me={}));var _t;(function(e){function t(r,i,a){var s={label:r};return i!==void 0&&(s.needsConfirmation=i),a!==void 0&&(s.description=a),s}e.create=t;function n(r){var i=r;return i!==void 0&&N.objectLiteral(i)&&N.string(i.label)&&(N.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(N.string(i.description)||i.description===void 0)}e.is=n})(_t||(_t={}));var se;(function(e){function t(n){var r=n;return typeof r=="string"}e.is=t})(se||(se={}));var et;(function(e){function t(a,s,u){return{range:a,newText:s,annotationId:u}}e.replace=t;function n(a,s,u){return{range:{start:a,end:a},newText:s,annotationId:u}}e.insert=n;function r(a,s){return{range:a,newText:"",annotationId:s}}e.del=r;function i(a){var s=a;return Me.is(s)&&(_t.is(s.annotationId)||se.is(s.annotationId))}e.is=i})(et||(et={}));var yn;(function(e){function t(r,i){return{textDocument:r,edits:i}}e.create=t;function n(r){var i=r;return N.defined(i)&&wn.is(i.textDocument)&&Array.isArray(i.edits)}e.is=n})(yn||(yn={}));var Jt;(function(e){function t(r,i,a){var s={kind:"create",uri:r};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(s.options=i),a!==void 0&&(s.annotationId=a),s}e.create=t;function n(r){var i=r;return i&&i.kind==="create"&&N.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||N.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||N.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||se.is(i.annotationId))}e.is=n})(Jt||(Jt={}));var Zt;(function(e){function t(r,i,a,s){var u={kind:"rename",oldUri:r,newUri:i};return a!==void 0&&(a.overwrite!==void 0||a.ignoreIfExists!==void 0)&&(u.options=a),s!==void 0&&(u.annotationId=s),u}e.create=t;function n(r){var i=r;return i&&i.kind==="rename"&&N.string(i.oldUri)&&N.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||N.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||N.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||se.is(i.annotationId))}e.is=n})(Zt||(Zt={}));var Qt;(function(e){function t(r,i,a){var s={kind:"delete",uri:r};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(s.options=i),a!==void 0&&(s.annotationId=a),s}e.create=t;function n(r){var i=r;return i&&i.kind==="delete"&&N.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||N.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||N.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||se.is(i.annotationId))}e.is=n})(Qt||(Qt={}));var _r;(function(e){function t(n){var r=n;return r&&(r.changes!==void 0||r.documentChanges!==void 0)&&(r.documentChanges===void 0||r.documentChanges.every(function(i){return N.string(i.kind)?Jt.is(i)||Zt.is(i)||Qt.is(i):yn.is(i)}))}e.is=t})(_r||(_r={}));var Cn=function(){function e(t,n){this.edits=t,this.changeAnnotations=n}return e.prototype.insert=function(t,n,r){var i,a;if(r===void 0?i=Me.insert(t,n):se.is(r)?(a=r,i=et.insert(t,n,r)):(this.assertChangeAnnotations(this.changeAnnotations),a=this.changeAnnotations.manage(r),i=et.insert(t,n,a)),this.edits.push(i),a!==void 0)return a},e.prototype.replace=function(t,n,r){var i,a;if(r===void 0?i=Me.replace(t,n):se.is(r)?(a=r,i=et.replace(t,n,r)):(this.assertChangeAnnotations(this.changeAnnotations),a=this.changeAnnotations.manage(r),i=et.replace(t,n,a)),this.edits.push(i),a!==void 0)return a},e.prototype.delete=function(t,n){var r,i;if(n===void 0?r=Me.del(t):se.is(n)?(i=n,r=et.del(t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=et.del(t,i)),this.edits.push(r),i!==void 0)return i},e.prototype.add=function(t){this.edits.push(t)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e.prototype.assertChangeAnnotations=function(t){if(t===void 0)throw new Error("Text edit change is not configured to manage change annotations.")},e}(),Do=function(){function e(t){this._annotations=t===void 0?Object.create(null):t,this._counter=0,this._size=0}return e.prototype.all=function(){return this._annotations},Object.defineProperty(e.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.manage=function(t,n){var r;if(se.is(t)?r=t:(r=this.nextId(),n=t),this._annotations[r]!==void 0)throw new Error("Id "+r+" is already in use.");if(n===void 0)throw new Error("No annotation provided for id "+r);return this._annotations[r]=n,this._size++,r},e.prototype.nextId=function(){return this._counter++,this._counter.toString()},e}(),ll=function(){function e(t){var n=this;this._textEditChanges=Object.create(null),t!==void 0?(this._workspaceEdit=t,t.documentChanges?(this._changeAnnotations=new Do(t.changeAnnotations),t.changeAnnotations=this._changeAnnotations.all(),t.documentChanges.forEach(function(r){if(yn.is(r)){var i=new Cn(r.edits,n._changeAnnotations);n._textEditChanges[r.textDocument.uri]=i}})):t.changes&&Object.keys(t.changes).forEach(function(r){var i=new Cn(t.changes[r]);n._textEditChanges[r]=i})):this._workspaceEdit={}}return Object.defineProperty(e.prototype,"edit",{get:function(){return this.initDocumentChanges(),this._changeAnnotations!==void 0&&(this._changeAnnotations.size===0?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),e.prototype.getTextEditChange=function(t){if(wn.is(t)){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var n={uri:t.uri,version:t.version},r=this._textEditChanges[n.uri];if(!r){var i=[],a={textDocument:n,edits:i};this._workspaceEdit.documentChanges.push(a),r=new Cn(i,this._changeAnnotations),this._textEditChanges[n.uri]=r}return r}else{if(this.initChanges(),this._workspaceEdit.changes===void 0)throw new Error("Workspace edit is not configured for normal text edit changes.");var r=this._textEditChanges[t];if(!r){var i=[];this._workspaceEdit.changes[t]=i,r=new Cn(i),this._textEditChanges[t]=r}return r}},e.prototype.initDocumentChanges=function(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._changeAnnotations=new Do,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},e.prototype.initChanges=function(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._workspaceEdit.changes=Object.create(null))},e.prototype.createFile=function(t,n,r){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var i;_t.is(n)||se.is(n)?i=n:r=n;var a,s;if(i===void 0?a=Jt.create(t,r):(s=se.is(i)?i:this._changeAnnotations.manage(i),a=Jt.create(t,r,s)),this._workspaceEdit.documentChanges.push(a),s!==void 0)return s},e.prototype.renameFile=function(t,n,r,i){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var a;_t.is(r)||se.is(r)?a=r:i=r;var s,u;if(a===void 0?s=Zt.create(t,n,i):(u=se.is(a)?a:this._changeAnnotations.manage(a),s=Zt.create(t,n,i,u)),this._workspaceEdit.documentChanges.push(s),u!==void 0)return u},e.prototype.deleteFile=function(t,n,r){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var i;_t.is(n)||se.is(n)?i=n:r=n;var a,s;if(i===void 0?a=Qt.create(t,r):(s=se.is(i)?i:this._changeAnnotations.manage(i),a=Qt.create(t,r,s)),this._workspaceEdit.documentChanges.push(a),s!==void 0)return s},e}(),Ko;(function(e){function t(r){return{uri:r}}e.create=t;function n(r){var i=r;return N.defined(i)&&N.string(i.uri)}e.is=n})(Ko||(Ko={}));var jo;(function(e){function t(r,i){return{uri:r,version:i}}e.create=t;function n(r){var i=r;return N.defined(i)&&N.string(i.uri)&&N.integer(i.version)}e.is=n})(jo||(jo={}));var wn;(function(e){function t(r,i){return{uri:r,version:i}}e.create=t;function n(r){var i=r;return N.defined(i)&&N.string(i.uri)&&(i.version===null||N.integer(i.version))}e.is=n})(wn||(wn={}));var Bo;(function(e){function t(r,i,a,s){return{uri:r,languageId:i,version:a,text:s}}e.create=t;function n(r){var i=r;return N.defined(i)&&N.string(i.uri)&&N.string(i.languageId)&&N.integer(i.version)&&N.string(i.text)}e.is=n})(Bo||(Bo={}));var qe;(function(e){e.PlainText="plaintext",e.Markdown="markdown"})(qe||(qe={})),function(e){function t(n){var r=n;return r===e.PlainText||r===e.Markdown}e.is=t}(qe||(qe={}));var Sr;(function(e){function t(n){var r=n;return N.objectLiteral(n)&&qe.is(r.kind)&&N.string(r.value)}e.is=t})(Sr||(Sr={}));var be;(function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25})(be||(be={}));var ee;(function(e){e.PlainText=1,e.Snippet=2})(ee||(ee={}));var Uo;(function(e){e.Deprecated=1})(Uo||(Uo={}));var $o;(function(e){function t(r,i,a){return{newText:r,insert:i,replace:a}}e.create=t;function n(r){var i=r;return i&&N.string(i.newText)&&W.is(i.insert)&&W.is(i.replace)}e.is=n})($o||($o={}));var qo;(function(e){e.asIs=1,e.adjustIndentation=2})(qo||(qo={}));var Ar;(function(e){function t(n){return{label:n}}e.create=t})(Ar||(Ar={}));var Wo;(function(e){function t(n,r){return{items:n||[],isIncomplete:!!r}}e.create=t})(Wo||(Wo={}));var _n;(function(e){function t(r){return r.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}e.fromPlainText=t;function n(r){var i=r;return N.string(i)||N.objectLiteral(i)&&N.string(i.language)&&N.string(i.value)}e.is=n})(_n||(_n={}));var Ho;(function(e){function t(n){var r=n;return!!r&&N.objectLiteral(r)&&(Sr.is(r.contents)||_n.is(r.contents)||N.typedArray(r.contents,_n.is))&&(n.range===void 0||W.is(n.range))}e.is=t})(Ho||(Ho={}));var zo;(function(e){function t(n,r){return r?{label:n,documentation:r}:{label:n}}e.create=t})(zo||(zo={}));var Go;(function(e){function t(n,r){for(var i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];var s={label:n};return N.defined(r)&&(s.documentation=r),N.defined(i)?s.parameters=i:s.parameters=[],s}e.create=t})(Go||(Go={}));var Jo;(function(e){e.Text=1,e.Read=2,e.Write=3})(Jo||(Jo={}));var Zo;(function(e){function t(n,r){var i={range:n};return N.number(r)&&(i.kind=r),i}e.create=t})(Zo||(Zo={}));var Ve;(function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26})(Ve||(Ve={}));var Qo;(function(e){e.Deprecated=1})(Qo||(Qo={}));var Yo;(function(e){function t(n,r,i,a,s){var u={name:n,kind:r,location:{uri:a,range:i}};return s&&(u.containerName=s),u}e.create=t})(Yo||(Yo={}));var Xo;(function(e){function t(r,i,a,s,u,c){var l={name:r,detail:i,kind:a,range:s,selectionRange:u};return c!==void 0&&(l.children=c),l}e.create=t;function n(r){var i=r;return i&&N.string(i.name)&&N.number(i.kind)&&W.is(i.range)&&W.is(i.selectionRange)&&(i.detail===void 0||N.string(i.detail))&&(i.deprecated===void 0||N.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}e.is=n})(Xo||(Xo={}));var e1;(function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"})(e1||(e1={}));var t1;(function(e){function t(r,i){var a={diagnostics:r};return i!=null&&(a.only=i),a}e.create=t;function n(r){var i=r;return N.defined(i)&&N.typedArray(i.diagnostics,$e.is)&&(i.only===void 0||N.typedArray(i.only,N.string))}e.is=n})(t1||(t1={}));var n1;(function(e){function t(r,i,a){var s={title:r},u=!0;return typeof i=="string"?(u=!1,s.kind=i):Gt.is(i)?s.command=i:s.edit=i,u&&a!==void 0&&(s.kind=a),s}e.create=t;function n(r){var i=r;return i&&N.string(i.title)&&(i.diagnostics===void 0||N.typedArray(i.diagnostics,$e.is))&&(i.kind===void 0||N.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Gt.is(i.command))&&(i.isPreferred===void 0||N.boolean(i.isPreferred))&&(i.edit===void 0||_r.is(i.edit))}e.is=n})(n1||(n1={}));var r1;(function(e){function t(r,i){var a={range:r};return N.defined(i)&&(a.data=i),a}e.create=t;function n(r){var i=r;return N.defined(i)&&W.is(i.range)&&(N.undefined(i.command)||Gt.is(i.command))}e.is=n})(r1||(r1={}));var i1;(function(e){function t(r,i){return{tabSize:r,insertSpaces:i}}e.create=t;function n(r){var i=r;return N.defined(i)&&N.uinteger(i.tabSize)&&N.boolean(i.insertSpaces)}e.is=n})(i1||(i1={}));var o1;(function(e){function t(r,i,a){return{range:r,target:i,data:a}}e.create=t;function n(r){var i=r;return N.defined(i)&&W.is(i.range)&&(N.undefined(i.target)||N.string(i.target))}e.is=n})(o1||(o1={}));var Sn;(function(e){function t(r,i){return{range:r,parent:i}}e.create=t;function n(r){var i=r;return i!==void 0&&W.is(i.range)&&(i.parent===void 0||e.is(i.parent))}e.is=n})(Sn||(Sn={}));var a1;(function(e){function t(a,s,u,c){return new Is(a,s,u,c)}e.create=t;function n(a){var s=a;return!!(N.defined(s)&&N.string(s.uri)&&(N.undefined(s.languageId)||N.string(s.languageId))&&N.uinteger(s.lineCount)&&N.func(s.getText)&&N.func(s.positionAt)&&N.func(s.offsetAt))}e.is=n;function r(a,s){for(var u=a.getText(),c=i(s,function(p,y){var g=p.range.start.line-y.range.start.line;return g===0?p.range.start.character-y.range.start.character:g}),l=u.length,f=c.length-1;f>=0;f--){var h=c[f],d=a.offsetAt(h.range.start),m=a.offsetAt(h.range.end);if(m<=l)u=u.substring(0,d)+h.newText+u.substring(m,u.length);else throw new Error("Overlapping edit");l=d}return u}e.applyEdits=r;function i(a,s){if(a.length<=1)return a;var u=a.length/2|0,c=a.slice(0,u),l=a.slice(u);i(c,s),i(l,s);for(var f=0,h=0,d=0;f<c.length&&h<l.length;){var m=s(c[f],l[h]);m<=0?a[d++]=c[f++]:a[d++]=l[h++]}for(;f<c.length;)a[d++]=c[f++];for(;h<l.length;)a[d++]=l[h++];return a}})(a1||(a1={}));var Is=function(){function e(t,n,r,i){this._uri=t,this._languageId=n,this._version=r,this._content=i,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),e.prototype.getText=function(t){if(t){var n=this.offsetAt(t.start),r=this.offsetAt(t.end);return this._content.substring(n,r)}return this._content},e.prototype.update=function(t,n){this._content=t.text,this._version=n,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(this._lineOffsets===void 0){for(var t=[],n=this._content,r=!0,i=0;i<n.length;i++){r&&(t.push(i),r=!1);var a=n.charAt(i);r=a==="\r"||a===`
`,a==="\r"&&i+1<n.length&&n.charAt(i+1)===`
`&&i++}r&&n.length>0&&t.push(n.length),this._lineOffsets=t}return this._lineOffsets},e.prototype.positionAt=function(t){t=Math.max(Math.min(t,this._content.length),0);var n=this.getLineOffsets(),r=0,i=n.length;if(i===0)return Oe.create(0,t);for(;r<i;){var a=Math.floor((r+i)/2);n[a]>t?i=a:r=a+1}var s=r-1;return Oe.create(s,t-n[s])},e.prototype.offsetAt=function(t){var n=this.getLineOffsets();if(t.line>=n.length)return this._content.length;if(t.line<0)return 0;var r=n[t.line],i=t.line+1<n.length?n[t.line+1]:this._content.length;return Math.max(Math.min(r+t.character,i),r)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),e}(),N;(function(e){var t=Object.prototype.toString;function n(m){return typeof m!="undefined"}e.defined=n;function r(m){return typeof m=="undefined"}e.undefined=r;function i(m){return m===!0||m===!1}e.boolean=i;function a(m){return t.call(m)==="[object String]"}e.string=a;function s(m){return t.call(m)==="[object Number]"}e.number=s;function u(m,p,y){return t.call(m)==="[object Number]"&&p<=m&&m<=y}e.numberRange=u;function c(m){return t.call(m)==="[object Number]"&&-2147483648<=m&&m<=2147483647}e.integer=c;function l(m){return t.call(m)==="[object Number]"&&0<=m&&m<=2147483647}e.uinteger=l;function f(m){return t.call(m)==="[object Function]"}e.func=f;function h(m){return m!==null&&typeof m=="object"}e.objectLiteral=h;function d(m,p){return Array.isArray(m)&&m.every(p)}e.typedArray=d})(N||(N={}));var An=class{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){for(let n of e)if(An.isIncremental(n)){const r=u1(n.range),i=this.offsetAt(r.start),a=this.offsetAt(r.end);this._content=this._content.substring(0,i)+n.text+this._content.substring(a,this._content.length);const s=Math.max(r.start.line,0),u=Math.max(r.end.line,0);let c=this._lineOffsets;const l=s1(n.text,!1,i);if(u-s===l.length)for(let h=0,d=l.length;h<d;h++)c[h+s+1]=l[h];else l.length<1e4?c.splice(s+1,u-s,...l):this._lineOffsets=c=c.slice(0,s+1).concat(l,c.slice(u+1));const f=n.text.length-(a-i);if(f!==0)for(let h=s+1+l.length,d=c.length;h<d;h++)c[h]=c[h]+f}else if(An.isFull(n))this._content=n.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=t}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=s1(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(r===0)return{line:0,character:e};for(;n<r;){let a=Math.floor((n+r)/2);t[a]>e?r=a:n=a+1}let i=n-1;return{line:i,character:e-t[i]}}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let t=e;return t!=null&&typeof t.text=="string"&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength=="number")}static isFull(e){let t=e;return t!=null&&typeof t.text=="string"&&t.range===void 0&&t.rangeLength===void 0}},xr;(function(e){function t(i,a,s,u){return new An(i,a,s,u)}e.create=t;function n(i,a,s){if(i instanceof An)return i.update(a,s),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}e.update=n;function r(i,a){let s=i.getText(),u=Er(a.map(Ps),(f,h)=>{let d=f.range.start.line-h.range.start.line;return d===0?f.range.start.character-h.range.start.character:d}),c=0;const l=[];for(const f of u){let h=i.offsetAt(f.range.start);if(h<c)throw new Error("Overlapping edit");h>c&&l.push(s.substring(c,h)),f.newText.length&&l.push(f.newText),c=i.offsetAt(f.range.end)}return l.push(s.substr(c)),l.join("")}e.applyEdits=r})(xr||(xr={}));function Er(e,t){if(e.length<=1)return e;const n=e.length/2|0,r=e.slice(0,n),i=e.slice(n);Er(r,t),Er(i,t);let a=0,s=0,u=0;for(;a<r.length&&s<i.length;)t(r[a],i[s])<=0?e[u++]=r[a++]:e[u++]=i[s++];for(;a<r.length;)e[u++]=r[a++];for(;s<i.length;)e[u++]=i[s++];return e}function s1(e,t,n=0){const r=t?[n]:[];for(let i=0;i<e.length;i++){let a=e.charCodeAt(i);(a===13||a===10)&&(a===13&&i+1<e.length&&e.charCodeAt(i+1)===10&&i++,r.push(n+i+1))}return r}function u1(e){const t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function Ps(e){const t=u1(e.range);return t!==e.range?{newText:e.newText,range:t}:e}var U;(function(e){e[e.Undefined=0]="Undefined",e[e.EnumValueMismatch=1]="EnumValueMismatch",e[e.Deprecated=2]="Deprecated",e[e.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=258]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",e[e.InvalidUnicode=260]="InvalidUnicode",e[e.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",e[e.InvalidCharacter=262]="InvalidCharacter",e[e.PropertyExpected=513]="PropertyExpected",e[e.CommaExpected=514]="CommaExpected",e[e.ColonExpected=515]="ColonExpected",e[e.ValueExpected=516]="ValueExpected",e[e.CommaOrCloseBacketExpected=517]="CommaOrCloseBacketExpected",e[e.CommaOrCloseBraceExpected=518]="CommaOrCloseBraceExpected",e[e.TrailingComma=519]="TrailingComma",e[e.DuplicateKey=520]="DuplicateKey",e[e.CommentNotPermitted=521]="CommentNotPermitted",e[e.SchemaResolveError=768]="SchemaResolveError"})(U||(U={}));var c1;(function(e){e.LATEST={textDocument:{completion:{completionItem:{documentationFormat:[qe.Markdown,qe.PlainText],commitCharactersSupport:!0}}}}})(c1||(c1={}));function Fs(e,t){let n;return t.length===0?n=e:n=e.replace(/\{(\d+)\}/g,(r,i)=>{let a=i[0];return typeof t[a]!="undefined"?t[a]:r}),n}function Rs(e,t,...n){return Fs(t,n)}function Yt(e){return Rs}var ct=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),F=Yt(),Vs={"color-hex":{errorMessage:F("colorHexFormatWarning","Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:F("dateTimeFormatWarning","String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:F("dateFormatWarning","String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:F("timeFormatWarning","String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:F("emailFormatWarning","String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}))$/},hostname:{errorMessage:F("hostnameFormatWarning","String is not a hostname."),pattern:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i},ipv4:{errorMessage:F("ipv4FormatWarning","String is not an IPv4 address."),pattern:/^(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/},ipv6:{errorMessage:F("ipv6FormatWarning","String is not an IPv6 address."),pattern:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i}},lt=function(){function e(t,n,r){r===void 0&&(r=0),this.offset=n,this.length=r,this.parent=t}return Object.defineProperty(e.prototype,"children",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")},e}(),Ds=function(e){ct(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.type="null",i.value=null,i}return t}(lt),l1=function(e){ct(t,e);function t(n,r,i){var a=e.call(this,n,i)||this;return a.type="boolean",a.value=r,a}return t}(lt),Ks=function(e){ct(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.type="array",i.items=[],i}return Object.defineProperty(t.prototype,"children",{get:function(){return this.items},enumerable:!1,configurable:!0}),t}(lt),js=function(e){ct(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.type="number",i.isInteger=!0,i.value=Number.NaN,i}return t}(lt),Lr=function(e){ct(t,e);function t(n,r,i){var a=e.call(this,n,r,i)||this;return a.type="string",a.value="",a}return t}(lt),Bs=function(e){ct(t,e);function t(n,r,i){var a=e.call(this,n,r)||this;return a.type="property",a.colonOffset=-1,a.keyNode=i,a}return Object.defineProperty(t.prototype,"children",{get:function(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]},enumerable:!1,configurable:!0}),t}(lt),Us=function(e){ct(t,e);function t(n,r){var i=e.call(this,n,r)||this;return i.type="object",i.properties=[],i}return Object.defineProperty(t.prototype,"children",{get:function(){return this.properties},enumerable:!1,configurable:!0}),t}(lt);function ve(e){return Re(e)?e?{}:{not:{}}:e}var h1;(function(e){e[e.Key=0]="Key",e[e.Enum=1]="Enum"})(h1||(h1={}));var $s=function(){function e(t,n){t===void 0&&(t=-1),this.focusOffset=t,this.exclude=n,this.schemas=[]}return e.prototype.add=function(t){this.schemas.push(t)},e.prototype.merge=function(t){Array.prototype.push.apply(this.schemas,t.schemas)},e.prototype.include=function(t){return(this.focusOffset===-1||f1(t,this.focusOffset))&&t!==this.exclude},e.prototype.newSub=function(){return new e(-1,this.exclude)},e}(),Nr=function(){function e(){}return Object.defineProperty(e.prototype,"schemas",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.add=function(t){},e.prototype.merge=function(t){},e.prototype.include=function(t){return!0},e.prototype.newSub=function(){return this},e.instance=new e,e}(),ye=function(){function e(){this.problems=[],this.propertiesMatches=0,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=void 0}return e.prototype.hasProblems=function(){return!!this.problems.length},e.prototype.mergeAll=function(t){for(var n=0,r=t;n<r.length;n++){var i=r[n];this.merge(i)}},e.prototype.merge=function(t){this.problems=this.problems.concat(t.problems)},e.prototype.mergeEnumValues=function(t){if(!this.enumValueMatch&&!t.enumValueMatch&&this.enumValues&&t.enumValues){this.enumValues=this.enumValues.concat(t.enumValues);for(var n=0,r=this.problems;n<r.length;n++){var i=r[n];i.code===U.EnumValueMismatch&&(i.message=F("enumWarning","Value is not accepted. Valid values: {0}.",this.enumValues.map(function(a){return JSON.stringify(a)}).join(", ")))}}},e.prototype.mergePropertyMatch=function(t){this.merge(t),this.propertiesMatches++,(t.enumValueMatch||!t.hasProblems()&&t.propertiesMatches)&&this.propertiesValueMatches++,t.enumValueMatch&&t.enumValues&&t.enumValues.length===1&&this.primaryValueMatches++},e.prototype.compare=function(t){var n=this.hasProblems();return n!==t.hasProblems()?n?-1:1:this.enumValueMatch!==t.enumValueMatch?t.enumValueMatch?-1:1:this.primaryValueMatches!==t.primaryValueMatches?this.primaryValueMatches-t.primaryValueMatches:this.propertiesValueMatches!==t.propertiesValueMatches?this.propertiesValueMatches-t.propertiesValueMatches:this.propertiesMatches-t.propertiesMatches},e}();function qs(e,t){return t===void 0&&(t=[]),new d1(e,t,[])}function ht(e){return ks(e)}function kr(e){return Ns(e)}function f1(e,t,n){return n===void 0&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}var d1=function(){function e(t,n,r){n===void 0&&(n=[]),r===void 0&&(r=[]),this.root=t,this.syntaxErrors=n,this.comments=r}return e.prototype.getNodeFromOffset=function(t,n){if(n===void 0&&(n=!1),this.root)return Ls(this.root,t,n)},e.prototype.visit=function(t){if(this.root){var n=function(r){var i=t(r),a=r.children;if(Array.isArray(a))for(var s=0;s<a.length&&i;s++)i=n(a[s]);return i};n(this.root)}},e.prototype.validate=function(t,n,r){if(r===void 0&&(r=_e.Warning),this.root&&n){var i=new ye;return oe(this.root,n,i,Nr.instance),i.problems.map(function(a){var s,u=W.create(t.positionAt(a.location.offset),t.positionAt(a.location.offset+a.location.length));return $e.create(u,a.message,(s=a.severity)!==null&&s!==void 0?s:r,a.code)})}},e.prototype.getMatchingSchemas=function(t,n,r){n===void 0&&(n=-1);var i=new $s(n,r);return this.root&&t&&oe(this.root,t,new ye,i),i.schemas},e}();function oe(e,t,n,r){if(!e||!r.include(e))return;var i=e;switch(i.type){case"object":l(i,t,n,r);break;case"array":c(i,t,n,r);break;case"string":u(i,t,n,r);break;case"number":s(i,t,n,r);break;case"property":return oe(i.valueNode,t,n,r)}a(),r.add({node:i,schema:t});function a(){function f(R){return i.type===R||R==="integer"&&i.type==="number"&&i.isInteger}if(Array.isArray(t.type)?t.type.some(f)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||F("typeArrayMismatchWarning","Incorrect type. Expected one of {0}.",t.type.join(", "))}):t.type&&(f(t.type)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||F("typeMismatchWarning",'Incorrect type. Expected "{0}".',t.type)})),Array.isArray(t.allOf))for(var h=0,d=t.allOf;h<d.length;h++){var m=d[h];oe(i,ve(m),n,r)}var p=ve(t.not);if(p){var y=new ye,g=r.newSub();oe(i,p,y,g),y.hasProblems()||n.problems.push({location:{offset:i.offset,length:i.length},message:F("notSchemaWarning","Matches a schema that is not allowed.")});for(var w=0,b=g.schemas;w<b.length;w++){var v=b[w];v.inverted=!v.inverted,r.add(v)}}var L=function(R,j){for(var te=[],q=void 0,M=0,O=R;M<O.length;M++){var I=O[M],P=ve(I),V=new ye,K=r.newSub();if(oe(i,P,V,K),V.hasProblems()||te.push(P),!q)q={schema:P,validationResult:V,matchingSchemas:K};else if(!j&&!V.hasProblems()&&!q.validationResult.hasProblems())q.matchingSchemas.merge(K),q.validationResult.propertiesMatches+=V.propertiesMatches,q.validationResult.propertiesValueMatches+=V.propertiesValueMatches;else{var $=V.compare(q.validationResult);$>0?q={schema:P,validationResult:V,matchingSchemas:K}:$===0&&(q.matchingSchemas.merge(K),q.validationResult.mergeEnumValues(V))}}return te.length>1&&j&&n.problems.push({location:{offset:i.offset,length:1},message:F("oneOfWarning","Matches multiple schemas when only one must validate.")}),q&&(n.merge(q.validationResult),n.propertiesMatches+=q.validationResult.propertiesMatches,n.propertiesValueMatches+=q.validationResult.propertiesValueMatches,r.merge(q.matchingSchemas)),te.length};Array.isArray(t.anyOf)&&L(t.anyOf,!1),Array.isArray(t.oneOf)&&L(t.oneOf,!0);var x=function(R){var j=new ye,te=r.newSub();oe(i,ve(R),j,te),n.merge(j),n.propertiesMatches+=j.propertiesMatches,n.propertiesValueMatches+=j.propertiesValueMatches,r.merge(te)},A=function(R,j,te){var q=ve(R),M=new ye,O=r.newSub();oe(i,q,M,O),r.merge(O),M.hasProblems()?te&&x(te):j&&x(j)},C=ve(t.if);if(C&&A(C,ve(t.then),ve(t.else)),Array.isArray(t.enum)){for(var _=ht(i),S=!1,k=0,E=t.enum;k<E.length;k++){var T=E[k];if(qt(_,T)){S=!0;break}}n.enumValues=t.enum,n.enumValueMatch=S,S||n.problems.push({location:{offset:i.offset,length:i.length},code:U.EnumValueMismatch,message:t.errorMessage||F("enumWarning","Value is not accepted. Valid values: {0}.",t.enum.map(function(R){return JSON.stringify(R)}).join(", "))})}if(Ue(t.const)){var _=ht(i);qt(_,t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:i.offset,length:i.length},code:U.EnumValueMismatch,message:t.errorMessage||F("constWarning","Value must be {0}.",JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]}t.deprecationMessage&&i.parent&&n.problems.push({location:{offset:i.parent.offset,length:i.parent.length},severity:_e.Warning,message:t.deprecationMessage,code:U.Deprecated})}function s(f,h,d,m){var p=f.value;function y(k){var E,T=/^(-?\d+)(?:\.(\d+))?(?:e([-+]\d+))?$/.exec(k.toString());return T&&{value:Number(T[1]+(T[2]||"")),multiplier:(((E=T[2])===null||E===void 0?void 0:E.length)||0)-(parseInt(T[3])||0)}}if(we(h.multipleOf)){var g=-1;if(Number.isInteger(h.multipleOf))g=p%h.multipleOf;else{var w=y(h.multipleOf),b=y(p);if(w&&b){var v=Math.pow(10,Math.abs(b.multiplier-w.multiplier));b.multiplier<w.multiplier?b.value*=v:w.value*=v,g=b.value%w.value}}g!==0&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("multipleOfWarning","Value is not divisible by {0}.",h.multipleOf)})}function L(k,E){if(we(E))return E;if(Re(E)&&E)return k}function x(k,E){if(!Re(E)||!E)return k}var A=L(h.minimum,h.exclusiveMinimum);we(A)&&p<=A&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("exclusiveMinimumWarning","Value is below the exclusive minimum of {0}.",A)});var C=L(h.maximum,h.exclusiveMaximum);we(C)&&p>=C&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("exclusiveMaximumWarning","Value is above the exclusive maximum of {0}.",C)});var _=x(h.minimum,h.exclusiveMinimum);we(_)&&p<_&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("minimumWarning","Value is below the minimum of {0}.",_)});var S=x(h.maximum,h.exclusiveMaximum);we(S)&&p>S&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("maximumWarning","Value is above the maximum of {0}.",S)})}function u(f,h,d,m){if(we(h.minLength)&&f.value.length<h.minLength&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("minLengthWarning","String is shorter than the minimum length of {0}.",h.minLength)}),we(h.maxLength)&&f.value.length>h.maxLength&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("maxLengthWarning","String is longer than the maximum length of {0}.",h.maxLength)}),Ms(h.pattern)){var p=bn(h.pattern);(p==null?void 0:p.test(f.value))||d.problems.push({location:{offset:f.offset,length:f.length},message:h.patternErrorMessage||h.errorMessage||F("patternWarning",'String does not match the pattern of "{0}".',h.pattern)})}if(h.format)switch(h.format){case"uri":case"uri-reference":{var y=void 0;if(!f.value)y=F("uriEmpty","URI expected.");else{var g=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(f.value);g?!g[2]&&h.format==="uri"&&(y=F("uriSchemeMissing","URI with a scheme is expected.")):y=F("uriMissing","URI is expected.")}y&&d.problems.push({location:{offset:f.offset,length:f.length},message:h.patternErrorMessage||h.errorMessage||F("uriFormatWarning","String is not a URI: {0}",y)})}break;case"color-hex":case"date-time":case"date":case"time":case"email":case"hostname":case"ipv4":case"ipv6":var w=Vs[h.format];(!f.value||!w.pattern.exec(f.value))&&d.problems.push({location:{offset:f.offset,length:f.length},message:h.patternErrorMessage||h.errorMessage||w.errorMessage});default:}}function c(f,h,d,m){if(Array.isArray(h.items)){for(var p=h.items,y=0;y<p.length;y++){var g=p[y],w=ve(g),b=new ye,v=f.items[y];v?(oe(v,w,b,m),d.mergePropertyMatch(b)):f.items.length>=p.length&&d.propertiesValueMatches++}if(f.items.length>p.length)if(typeof h.additionalItems=="object")for(var L=p.length;L<f.items.length;L++){var b=new ye;oe(f.items[L],h.additionalItems,b,m),d.mergePropertyMatch(b)}else h.additionalItems===!1&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("additionalItemsWarning","Array has too many items according to schema. Expected {0} or fewer.",p.length)})}else{var x=ve(h.items);if(x)for(var A=0,C=f.items;A<C.length;A++){var v=C[A],b=new ye;oe(v,x,b,m),d.mergePropertyMatch(b)}}var _=ve(h.contains);if(_){var S=f.items.some(function(T){var R=new ye;return oe(T,_,R,Nr.instance),!R.hasProblems()});S||d.problems.push({location:{offset:f.offset,length:f.length},message:h.errorMessage||F("requiredItemMissingWarning","Array does not contain required item.")})}if(we(h.minItems)&&f.items.length<h.minItems&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("minItemsWarning","Array has too few items. Expected {0} or more.",h.minItems)}),we(h.maxItems)&&f.items.length>h.maxItems&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("maxItemsWarning","Array has too many items. Expected {0} or fewer.",h.maxItems)}),h.uniqueItems===!0){var k=ht(f),E=k.some(function(T,R){return R!==k.lastIndexOf(T)});E&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("uniqueItemsWarning","Array has duplicate items.")})}}function l(f,h,d,m){for(var p=Object.create(null),y=[],g=0,w=f.properties;g<w.length;g++){var b=w[g],v=b.keyNode.value;p[v]=b.valueNode,y.push(v)}if(Array.isArray(h.required))for(var L=0,x=h.required;L<x.length;L++){var A=x[L];if(!p[A]){var C=f.parent&&f.parent.type==="property"&&f.parent.keyNode,_=C?{offset:C.offset,length:C.length}:{offset:f.offset,length:1};d.problems.push({location:_,message:F("MissingRequiredPropWarning",'Missing property "{0}".',A)})}}var S=function(E1){for(var Kr=y.indexOf(E1);Kr>=0;)y.splice(Kr,1),Kr=y.indexOf(E1)};if(h.properties)for(var k=0,E=Object.keys(h.properties);k<E.length;k++){var A=E[k];S(A);var T=h.properties[A],R=p[A];if(R)if(Re(T))if(T)d.propertiesMatches++,d.propertiesValueMatches++;else{var b=R.parent;d.problems.push({location:{offset:b.keyNode.offset,length:b.keyNode.length},message:h.errorMessage||F("DisallowedExtraPropWarning","Property {0} is not allowed.",A)})}else{var j=new ye;oe(R,T,j,m),d.mergePropertyMatch(j)}}if(h.patternProperties)for(var te=0,q=Object.keys(h.patternProperties);te<q.length;te++)for(var M=q[te],O=bn(M),I=0,P=y.slice(0);I<P.length;I++){var A=P[I];if(O==null?void 0:O.test(A)){S(A);var R=p[A];if(R){var T=h.patternProperties[M];if(Re(T))if(T)d.propertiesMatches++,d.propertiesValueMatches++;else{var b=R.parent;d.problems.push({location:{offset:b.keyNode.offset,length:b.keyNode.length},message:h.errorMessage||F("DisallowedExtraPropWarning","Property {0} is not allowed.",A)})}else{var j=new ye;oe(R,T,j,m),d.mergePropertyMatch(j)}}}}if(typeof h.additionalProperties=="object")for(var V=0,K=y;V<K.length;V++){var A=K[V],R=p[A];if(R){var j=new ye;oe(R,h.additionalProperties,j,m),d.mergePropertyMatch(j)}}else if(h.additionalProperties===!1&&y.length>0)for(var $=0,Ke=y;$<Ke.length;$++){var A=Ke[$],R=p[A];if(R){var b=R.parent;d.problems.push({location:{offset:b.keyNode.offset,length:b.keyNode.length},message:h.errorMessage||F("DisallowedExtraPropWarning","Property {0} is not allowed.",A)})}}if(we(h.maxProperties)&&f.properties.length>h.maxProperties&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("MaxPropWarning","Object has more properties than limit of {0}.",h.maxProperties)}),we(h.minProperties)&&f.properties.length<h.minProperties&&d.problems.push({location:{offset:f.offset,length:f.length},message:F("MinPropWarning","Object has fewer properties than the required number of {0}",h.minProperties)}),h.dependencies)for(var ue=0,Ee=Object.keys(h.dependencies);ue<Ee.length;ue++){var v=Ee[ue],xt=p[v];if(xt){var je=h.dependencies[v];if(Array.isArray(je))for(var Vr=0,_1=je;Vr<_1.length;Vr++){var S1=_1[Vr];p[S1]?d.propertiesValueMatches++:d.problems.push({location:{offset:f.offset,length:f.length},message:F("RequiredDependentPropWarning","Object is missing property {0} required by property {1}.",S1,v)})}else{var T=ve(je);if(T){var j=new ye;oe(f,T,j,m),d.mergePropertyMatch(j)}}}}var A1=ve(h.propertyNames);if(A1)for(var Dr=0,x1=f.properties;Dr<x1.length;Dr++){var Lu=x1[Dr],v=Lu.keyNode;v&&oe(v,A1,d,Nr.instance)}}}function Ws(e,t){var n=[],r=-1,i=e.getText(),a=wt(i,!1),s=t&&t.collectComments?[]:void 0;function u(){for(;;){var C=a.scan();switch(h(),C){case 12:case 13:Array.isArray(s)&&s.push(W.create(e.positionAt(a.getTokenOffset()),e.positionAt(a.getTokenOffset()+a.getTokenLength())));break;case 15:case 14:break;default:return C}}}function c(C){return a.getToken()===C?(u(),!0):!1}function l(C,_,S,k,E){if(E===void 0&&(E=_e.Error),n.length===0||S!==r){var T=W.create(e.positionAt(S),e.positionAt(k));n.push($e.create(T,C,E,_,e.languageId)),r=S}}function f(C,_,S,k,E){S===void 0&&(S=void 0),k===void 0&&(k=[]),E===void 0&&(E=[]);var T=a.getTokenOffset(),R=a.getTokenOffset()+a.getTokenLength();if(T===R&&T>0){for(T--;T>0&&/\s/.test(i.charAt(T));)T--;R=T+1}if(l(C,_,T,R),S&&d(S,!1),k.length+E.length>0)for(var j=a.getToken();j!==17;){if(k.indexOf(j)!==-1){u();break}else if(E.indexOf(j)!==-1)break;j=u()}return S}function h(){switch(a.getTokenError()){case 4:return f(F("InvalidUnicode","Invalid unicode sequence in string."),U.InvalidUnicode),!0;case 5:return f(F("InvalidEscapeCharacter","Invalid escape character in string."),U.InvalidEscapeCharacter),!0;case 3:return f(F("UnexpectedEndOfNumber","Unexpected end of number."),U.UnexpectedEndOfNumber),!0;case 1:return f(F("UnexpectedEndOfComment","Unexpected end of comment."),U.UnexpectedEndOfComment),!0;case 2:return f(F("UnexpectedEndOfString","Unexpected end of string."),U.UnexpectedEndOfString),!0;case 6:return f(F("InvalidCharacter","Invalid characters in string. Control characters must be escaped."),U.InvalidCharacter),!0}return!1}function d(C,_){return C.length=a.getTokenOffset()+a.getTokenLength()-C.offset,_&&u(),C}function m(C){if(a.getToken()===3){var _=new Ks(C,a.getTokenOffset());u();for(var S=0,k=!1;a.getToken()!==4&&a.getToken()!==17;){if(a.getToken()===5){k||f(F("ValueExpected","Value expected"),U.ValueExpected);var E=a.getTokenOffset();if(u(),a.getToken()===4){k&&l(F("TrailingComma","Trailing comma"),U.TrailingComma,E,E+1);continue}}else k&&f(F("ExpectedComma","Expected comma"),U.CommaExpected);var T=L(_);T?_.items.push(T):f(F("PropertyExpected","Value expected"),U.ValueExpected,void 0,[],[4,5]),k=!0}return a.getToken()!==4?f(F("ExpectedCloseBracket","Expected comma or closing bracket"),U.CommaOrCloseBacketExpected,_):d(_,!0)}}var p=new Lr(void 0,0,0);function y(C,_){var S=new Bs(C,a.getTokenOffset(),p),k=w(S);if(!k)if(a.getToken()===16){f(F("DoubleQuotesExpected","Property keys must be doublequoted"),U.Undefined);var E=new Lr(S,a.getTokenOffset(),a.getTokenLength());E.value=a.getTokenValue(),k=E,u()}else return;S.keyNode=k;var T=_[k.value];if(T?(l(F("DuplicateKeyWarning","Duplicate object key"),U.DuplicateKey,S.keyNode.offset,S.keyNode.offset+S.keyNode.length,_e.Warning),typeof T=="object"&&l(F("DuplicateKeyWarning","Duplicate object key"),U.DuplicateKey,T.keyNode.offset,T.keyNode.offset+T.keyNode.length,_e.Warning),_[k.value]=!0):_[k.value]=S,a.getToken()===6)S.colonOffset=a.getTokenOffset(),u();else if(f(F("ColonExpected","Colon expected"),U.ColonExpected),a.getToken()===10&&e.positionAt(k.offset+k.length).line<e.positionAt(a.getTokenOffset()).line)return S.length=k.length,S;var R=L(S);return R?(S.valueNode=R,S.length=R.offset+R.length-S.offset,S):f(F("ValueExpected","Value expected"),U.ValueExpected,S,[],[2,5])}function g(C){if(a.getToken()===1){var _=new Us(C,a.getTokenOffset()),S=Object.create(null);u();for(var k=!1;a.getToken()!==2&&a.getToken()!==17;){if(a.getToken()===5){k||f(F("PropertyExpected","Property expected"),U.PropertyExpected);var E=a.getTokenOffset();if(u(),a.getToken()===2){k&&l(F("TrailingComma","Trailing comma"),U.TrailingComma,E,E+1);continue}}else k&&f(F("ExpectedComma","Expected comma"),U.CommaExpected);var T=y(_,S);T?_.properties.push(T):f(F("PropertyExpected","Property expected"),U.PropertyExpected,void 0,[],[2,5]),k=!0}return a.getToken()!==2?f(F("ExpectedCloseBrace","Expected comma or closing brace"),U.CommaOrCloseBraceExpected,_):d(_,!0)}}function w(C){if(a.getToken()===10){var _=new Lr(C,a.getTokenOffset());return _.value=a.getTokenValue(),d(_,!0)}}function b(C){if(a.getToken()===11){var _=new js(C,a.getTokenOffset());if(a.getTokenError()===0){var S=a.getTokenValue();try{var k=JSON.parse(S);if(!we(k))return f(F("InvalidNumberFormat","Invalid number format."),U.Undefined,_);_.value=k}catch(E){return f(F("InvalidNumberFormat","Invalid number format."),U.Undefined,_)}_.isInteger=S.indexOf(".")===-1}return d(_,!0)}}function v(C){var _;switch(a.getToken()){case 7:return d(new Ds(C,a.getTokenOffset()),!0);case 8:return d(new l1(C,!0,a.getTokenOffset()),!0);case 9:return d(new l1(C,!1,a.getTokenOffset()),!0);default:return}}function L(C){return m(C)||g(C)||w(C)||b(C)||v(C)}var x=void 0,A=u();return A!==17&&(x=L(x),x?a.getToken()!==17&&f(F("End of file expected","End of file expected."),U.Undefined):f(F("Invalid symbol","Expected a JSON object, array or literal."),U.Undefined)),new d1(x,n,s)}function Or(e,t,n){if(e!==null&&typeof e=="object"){var r=t+"	";if(Array.isArray(e)){if(e.length===0)return"[]";for(var i=`[
`,a=0;a<e.length;a++)i+=r+Or(e[a],r,n),a<e.length-1&&(i+=","),i+=`
`;return i+=t+"]",i}else{var s=Object.keys(e);if(s.length===0)return"{}";for(var i=`{
`,a=0;a<s.length;a++){var u=s[a];i+=r+JSON.stringify(u)+": "+Or(e[u],r,n),a<s.length-1&&(i+=","),i+=`
`}return i+=t+"}",i}}return n(e)}var Mr=Yt(),Hs=[",","}","]"],zs=[":"],Gs=function(){function e(t,n,r,i){n===void 0&&(n=[]),r===void 0&&(r=Promise),i===void 0&&(i={}),this.schemaService=t,this.contributions=n,this.promiseConstructor=r,this.clientCapabilities=i}return e.prototype.doResolve=function(t){for(var n=this.contributions.length-1;n>=0;n--){var r=this.contributions[n].resolveCompletion;if(r){var i=r(t);if(i)return i}}return this.promiseConstructor.resolve(t)},e.prototype.doComplete=function(t,n,r){var i=this,a={items:[],isIncomplete:!1},s=t.getText(),u=t.offsetAt(n),c=r.getNodeFromOffset(u,!0);if(this.isInComment(t,c?c.offset:0,u))return Promise.resolve(a);if(c&&u===c.offset+c.length&&u>0){var l=s[u-1];(c.type==="object"&&l==="}"||c.type==="array"&&l==="]")&&(c=c.parent)}var f=this.getCurrentWord(t,u),h;if(c&&(c.type==="string"||c.type==="number"||c.type==="boolean"||c.type==="null"))h=W.create(t.positionAt(c.offset),t.positionAt(c.offset+c.length));else{var d=u-f.length;d>0&&s[d-1]==='"'&&d--,h=W.create(t.positionAt(d),n)}var m=!1,p={},y={add:function(g){var w=g.label,b=p[w];if(b)b.documentation||(b.documentation=g.documentation),b.detail||(b.detail=g.detail);else{if(w=w.replace(/[\n]/g,"\u21B5"),w.length>60){var v=w.substr(0,57).trim()+"...";p[v]||(w=v)}h&&g.insertText!==void 0&&(g.textEdit=Me.replace(h,g.insertText)),m&&(g.commitCharacters=g.kind===be.Property?zs:Hs),g.label=w,p[w]=g,a.items.push(g)}},setAsIncomplete:function(){a.isIncomplete=!0},error:function(g){console.error(g)},log:function(g){console.log(g)},getNumberOfProposals:function(){return a.items.length}};return this.schemaService.getSchemaForResource(t.uri,r).then(function(g){var w=[],b=!0,v="",L=void 0;if(c&&c.type==="string"){var x=c.parent;x&&x.type==="property"&&x.keyNode===c&&(b=!x.valueNode,L=x,v=s.substr(c.offset+1,c.length-2),x&&(c=x.parent))}if(c&&c.type==="object"){if(c.offset===u)return a;var A=c.properties;A.forEach(function(k){(!L||L!==k)&&(p[k.keyNode.value]=Ar.create("__"))});var C="";b&&(C=i.evaluateSeparatorAfter(t,t.offsetAt(h.end))),g?i.getPropertyCompletions(g,r,c,b,C,y):i.getSchemaLessPropertyCompletions(r,c,v,y);var _=kr(c);i.contributions.forEach(function(k){var E=k.collectPropertyCompletions(t.uri,_,f,b,C==="",y);E&&w.push(E)}),!g&&f.length>0&&s.charAt(u-f.length-1)!=='"'&&(y.add({kind:be.Property,label:i.getLabelForValue(f),insertText:i.getInsertTextForProperty(f,void 0,!1,C),insertTextFormat:ee.Snippet,documentation:""}),y.setAsIncomplete())}var S={};return g?i.getValueCompletions(g,r,c,u,t,y,S):i.getSchemaLessValueCompletions(r,c,u,t,y),i.contributions.length>0&&i.getContributedValueCompletions(r,c,u,t,y,w),i.promiseConstructor.all(w).then(function(){if(y.getNumberOfProposals()===0){var k=u;c&&(c.type==="string"||c.type==="number"||c.type==="boolean"||c.type==="null")&&(k=c.offset+c.length);var E=i.evaluateSeparatorAfter(t,k);i.addFillerValueCompletions(S,E,y)}return a})})},e.prototype.getPropertyCompletions=function(t,n,r,i,a,s){var u=this,c=n.getMatchingSchemas(t.schema,r.offset);c.forEach(function(l){if(l.node===r&&!l.inverted){var f=l.schema.properties;f&&Object.keys(f).forEach(function(y){var g=f[y];if(typeof g=="object"&&!g.deprecationMessage&&!g.doNotSuggest){var w={kind:be.Property,label:y,insertText:u.getInsertTextForProperty(y,g,i,a),insertTextFormat:ee.Snippet,filterText:u.getFilterTextForValue(y),documentation:u.fromMarkup(g.markdownDescription)||g.description||""};g.suggestSortText!==void 0&&(w.sortText=g.suggestSortText),w.insertText&&Wt(w.insertText,"$1".concat(a))&&(w.command={title:"Suggest",command:"editor.action.triggerSuggest"}),s.add(w)}});var h=l.schema.propertyNames;if(typeof h=="object"&&!h.deprecationMessage&&!h.doNotSuggest){var d=function(y,g){g===void 0&&(g=void 0);var w={kind:be.Property,label:y,insertText:u.getInsertTextForProperty(y,void 0,i,a),insertTextFormat:ee.Snippet,filterText:u.getFilterTextForValue(y),documentation:g||u.fromMarkup(h.markdownDescription)||h.description||""};h.suggestSortText!==void 0&&(w.sortText=h.suggestSortText),w.insertText&&Wt(w.insertText,"$1".concat(a))&&(w.command={title:"Suggest",command:"editor.action.triggerSuggest"}),s.add(w)};if(h.enum)for(var m=0;m<h.enum.length;m++){var p=void 0;h.markdownEnumDescriptions&&m<h.markdownEnumDescriptions.length?p=u.fromMarkup(h.markdownEnumDescriptions[m]):h.enumDescriptions&&m<h.enumDescriptions.length&&(p=h.enumDescriptions[m]),d(h.enum[m],p)}h.const&&d(h.const)}}})},e.prototype.getSchemaLessPropertyCompletions=function(t,n,r,i){var a=this,s=function(c){c.properties.forEach(function(l){var f=l.keyNode.value;i.add({kind:be.Property,label:f,insertText:a.getInsertTextForValue(f,""),insertTextFormat:ee.Snippet,filterText:a.getFilterTextForValue(f),documentation:""})})};if(n.parent)if(n.parent.type==="property"){var u=n.parent.keyNode.value;t.visit(function(c){return c.type==="property"&&c!==n.parent&&c.keyNode.value===u&&c.valueNode&&c.valueNode.type==="object"&&s(c.valueNode),!0})}else n.parent.type==="array"&&n.parent.items.forEach(function(c){c.type==="object"&&c!==n&&s(c)});else n.type==="object"&&i.add({kind:be.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",void 0,!0,""),insertTextFormat:ee.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})},e.prototype.getSchemaLessValueCompletions=function(t,n,r,i,a){var s=this,u=r;if(n&&(n.type==="string"||n.type==="number"||n.type==="boolean"||n.type==="null")&&(u=n.offset+n.length,n=n.parent),!n){a.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:ee.Snippet,documentation:""}),a.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:ee.Snippet,documentation:""});return}var c=this.evaluateSeparatorAfter(i,u),l=function(m){m.parent&&!f1(m.parent,r,!0)&&a.add({kind:s.getSuggestionKind(m.type),label:s.getLabelTextForMatchingNode(m,i),insertText:s.getInsertTextForMatchingNode(m,i,c),insertTextFormat:ee.Snippet,documentation:""}),m.type==="boolean"&&s.addBooleanValueCompletion(!m.value,c,a)};if(n.type==="property"&&r>(n.colonOffset||0)){var f=n.valueNode;if(f&&(r>f.offset+f.length||f.type==="object"||f.type==="array"))return;var h=n.keyNode.value;t.visit(function(m){return m.type==="property"&&m.keyNode.value===h&&m.valueNode&&l(m.valueNode),!0}),h==="$schema"&&n.parent&&!n.parent.parent&&this.addDollarSchemaCompletions(c,a)}if(n.type==="array")if(n.parent&&n.parent.type==="property"){var d=n.parent.keyNode.value;t.visit(function(m){return m.type==="property"&&m.keyNode.value===d&&m.valueNode&&m.valueNode.type==="array"&&m.valueNode.items.forEach(l),!0})}else n.items.forEach(l)},e.prototype.getValueCompletions=function(t,n,r,i,a,s,u){var c=i,l=void 0,f=void 0;if(r&&(r.type==="string"||r.type==="number"||r.type==="boolean"||r.type==="null")&&(c=r.offset+r.length,f=r,r=r.parent),!r){this.addSchemaValueCompletions(t.schema,"",s,u);return}if(r.type==="property"&&i>(r.colonOffset||0)){var h=r.valueNode;if(h&&i>h.offset+h.length)return;l=r.keyNode.value,r=r.parent}if(r&&(l!==void 0||r.type==="array")){for(var d=this.evaluateSeparatorAfter(a,c),m=n.getMatchingSchemas(t.schema,r.offset,f),p=0,y=m;p<y.length;p++){var g=y[p];if(g.node===r&&!g.inverted&&g.schema){if(r.type==="array"&&g.schema.items)if(Array.isArray(g.schema.items)){var w=this.findItemAtOffset(r,a,i);w<g.schema.items.length&&this.addSchemaValueCompletions(g.schema.items[w],d,s,u)}else this.addSchemaValueCompletions(g.schema.items,d,s,u);if(l!==void 0){var b=!1;if(g.schema.properties){var v=g.schema.properties[l];v&&(b=!0,this.addSchemaValueCompletions(v,d,s,u))}if(g.schema.patternProperties&&!b)for(var L=0,x=Object.keys(g.schema.patternProperties);L<x.length;L++){var A=x[L],C=bn(A);if(C==null?void 0:C.test(l)){b=!0;var v=g.schema.patternProperties[A];this.addSchemaValueCompletions(v,d,s,u)}}if(g.schema.additionalProperties&&!b){var v=g.schema.additionalProperties;this.addSchemaValueCompletions(v,d,s,u)}}}}l==="$schema"&&!r.parent&&this.addDollarSchemaCompletions(d,s),u.boolean&&(this.addBooleanValueCompletion(!0,d,s),this.addBooleanValueCompletion(!1,d,s)),u.null&&this.addNullValueCompletion(d,s)}},e.prototype.getContributedValueCompletions=function(t,n,r,i,a,s){if(!n)this.contributions.forEach(function(f){var h=f.collectDefaultCompletions(i.uri,a);h&&s.push(h)});else if((n.type==="string"||n.type==="number"||n.type==="boolean"||n.type==="null")&&(n=n.parent),n&&n.type==="property"&&r>(n.colonOffset||0)){var u=n.keyNode.value,c=n.valueNode;if((!c||r<=c.offset+c.length)&&n.parent){var l=kr(n.parent);this.contributions.forEach(function(f){var h=f.collectValueCompletions(i.uri,l,u,a);h&&s.push(h)})}}},e.prototype.addSchemaValueCompletions=function(t,n,r,i){var a=this;typeof t=="object"&&(this.addEnumValueCompletions(t,n,r),this.addDefaultValueCompletions(t,n,r),this.collectTypes(t,i),Array.isArray(t.allOf)&&t.allOf.forEach(function(s){return a.addSchemaValueCompletions(s,n,r,i)}),Array.isArray(t.anyOf)&&t.anyOf.forEach(function(s){return a.addSchemaValueCompletions(s,n,r,i)}),Array.isArray(t.oneOf)&&t.oneOf.forEach(function(s){return a.addSchemaValueCompletions(s,n,r,i)}))},e.prototype.addDefaultValueCompletions=function(t,n,r,i){var a=this;i===void 0&&(i=0);var s=!1;if(Ue(t.default)){for(var u=t.type,c=t.default,l=i;l>0;l--)c=[c],u="array";r.add({kind:this.getSuggestionKind(u),label:this.getLabelForValue(c),insertText:this.getInsertTextForValue(c,n),insertTextFormat:ee.Snippet,detail:Mr("json.suggest.default","Default value")}),s=!0}Array.isArray(t.examples)&&t.examples.forEach(function(f){for(var h=t.type,d=f,m=i;m>0;m--)d=[d],h="array";r.add({kind:a.getSuggestionKind(h),label:a.getLabelForValue(d),insertText:a.getInsertTextForValue(d,n),insertTextFormat:ee.Snippet}),s=!0}),Array.isArray(t.defaultSnippets)&&t.defaultSnippets.forEach(function(f){var h=t.type,d=f.body,m=f.label,p,y;if(Ue(d)){for(var g=t.type,w=i;w>0;w--)d=[d],g="array";p=a.getInsertTextForSnippetValue(d,n),y=a.getFilterTextForSnippetValue(d),m=m||a.getLabelForSnippetValue(d)}else if(typeof f.bodyText=="string"){for(var b="",v="",L="",w=i;w>0;w--)b=b+L+`[
`,v=v+`
`+L+"]",L+="	",h="array";p=b+L+f.bodyText.split(`
`).join(`
`+L)+v+n,m=m||p,y=p.replace(/[\n]/g,"")}else return;r.add({kind:a.getSuggestionKind(h),label:m,documentation:a.fromMarkup(f.markdownDescription)||f.description,insertText:p,insertTextFormat:ee.Snippet,filterText:y}),s=!0}),!s&&typeof t.items=="object"&&!Array.isArray(t.items)&&i<5&&this.addDefaultValueCompletions(t.items,n,r,i+1)},e.prototype.addEnumValueCompletions=function(t,n,r){if(Ue(t.const)&&r.add({kind:this.getSuggestionKind(t.type),label:this.getLabelForValue(t.const),insertText:this.getInsertTextForValue(t.const,n),insertTextFormat:ee.Snippet,documentation:this.fromMarkup(t.markdownDescription)||t.description}),Array.isArray(t.enum))for(var i=0,a=t.enum.length;i<a;i++){var s=t.enum[i],u=this.fromMarkup(t.markdownDescription)||t.description;t.markdownEnumDescriptions&&i<t.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?u=this.fromMarkup(t.markdownEnumDescriptions[i]):t.enumDescriptions&&i<t.enumDescriptions.length&&(u=t.enumDescriptions[i]),r.add({kind:this.getSuggestionKind(t.type),label:this.getLabelForValue(s),insertText:this.getInsertTextForValue(s,n),insertTextFormat:ee.Snippet,documentation:u})}},e.prototype.collectTypes=function(t,n){if(!(Array.isArray(t.enum)||Ue(t.const))){var r=t.type;Array.isArray(r)?r.forEach(function(i){return n[i]=!0}):r&&(n[r]=!0)}},e.prototype.addFillerValueCompletions=function(t,n,r){t.object&&r.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},n),insertTextFormat:ee.Snippet,detail:Mr("defaults.object","New object"),documentation:""}),t.array&&r.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],n),insertTextFormat:ee.Snippet,detail:Mr("defaults.array","New array"),documentation:""})},e.prototype.addBooleanValueCompletion=function(t,n,r){r.add({kind:this.getSuggestionKind("boolean"),label:t?"true":"false",insertText:this.getInsertTextForValue(t,n),insertTextFormat:ee.Snippet,documentation:""})},e.prototype.addNullValueCompletion=function(t,n){n.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+t,insertTextFormat:ee.Snippet,documentation:""})},e.prototype.addDollarSchemaCompletions=function(t,n){var r=this,i=this.schemaService.getRegisteredSchemaIds(function(a){return a==="http"||a==="https"});i.forEach(function(a){return n.add({kind:be.Module,label:r.getLabelForValue(a),filterText:r.getFilterTextForValue(a),insertText:r.getInsertTextForValue(a,t),insertTextFormat:ee.Snippet,documentation:""})})},e.prototype.getLabelForValue=function(t){return JSON.stringify(t)},e.prototype.getFilterTextForValue=function(t){return JSON.stringify(t)},e.prototype.getFilterTextForSnippetValue=function(t){return JSON.stringify(t).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getLabelForSnippetValue=function(t){var n=JSON.stringify(t);return n.replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getInsertTextForPlainText=function(t){return t.replace(/[\\\$\}]/g,"\\$&")},e.prototype.getInsertTextForValue=function(t,n){var r=JSON.stringify(t,null,"	");return r==="{}"?"{$1}"+n:r==="[]"?"[$1]"+n:this.getInsertTextForPlainText(r+n)},e.prototype.getInsertTextForSnippetValue=function(t,n){var r=function(i){return typeof i=="string"&&i[0]==="^"?i.substr(1):JSON.stringify(i)};return Or(t,"",r)+n},e.prototype.getInsertTextForGuessedValue=function(t,n){switch(typeof t){case"object":return t===null?"${1:null}"+n:this.getInsertTextForValue(t,n);case"string":var r=JSON.stringify(t);return r=r.substr(1,r.length-2),r=this.getInsertTextForPlainText(r),'"${1:'+r+'}"'+n;case"number":case"boolean":return"${1:"+JSON.stringify(t)+"}"+n}return this.getInsertTextForValue(t,n)},e.prototype.getSuggestionKind=function(t){if(Array.isArray(t)){var n=t;t=n.length>0?n[0]:void 0}if(!t)return be.Value;switch(t){case"string":return be.Value;case"object":return be.Module;case"property":return be.Property;default:return be.Value}},e.prototype.getLabelTextForMatchingNode=function(t,n){switch(t.type){case"array":return"[]";case"object":return"{}";default:var r=n.getText().substr(t.offset,t.length);return r}},e.prototype.getInsertTextForMatchingNode=function(t,n,r){switch(t.type){case"array":return this.getInsertTextForValue([],r);case"object":return this.getInsertTextForValue({},r);default:var i=n.getText().substr(t.offset,t.length)+r;return this.getInsertTextForPlainText(i)}},e.prototype.getInsertTextForProperty=function(t,n,r,i){var a=this.getInsertTextForValue(t,"");if(!r)return a;var s=a+": ",u,c=0;if(n){if(Array.isArray(n.defaultSnippets)){if(n.defaultSnippets.length===1){var l=n.defaultSnippets[0].body;Ue(l)&&(u=this.getInsertTextForSnippetValue(l,""))}c+=n.defaultSnippets.length}if(n.enum&&(!u&&n.enum.length===1&&(u=this.getInsertTextForGuessedValue(n.enum[0],"")),c+=n.enum.length),Ue(n.default)&&(u||(u=this.getInsertTextForGuessedValue(n.default,"")),c++),Array.isArray(n.examples)&&n.examples.length&&(u||(u=this.getInsertTextForGuessedValue(n.examples[0],"")),c+=n.examples.length),c===0){var f=Array.isArray(n.type)?n.type[0]:n.type;switch(f||(n.properties?f="object":n.items&&(f="array")),f){case"boolean":u="$1";break;case"string":u='"$1"';break;case"object":u="{$1}";break;case"array":u="[$1]";break;case"number":case"integer":u="${1:0}";break;case"null":u="${1:null}";break;default:return a}}}return(!u||c>1)&&(u="$1"),s+u+i},e.prototype.getCurrentWord=function(t,n){for(var r=n-1,i=t.getText();r>=0&&` 	
\r\v":{[,]}`.indexOf(i.charAt(r))===-1;)r--;return i.substring(r+1,n)},e.prototype.evaluateSeparatorAfter=function(t,n){var r=wt(t.getText(),!0);r.setPosition(n);var i=r.scan();switch(i){case 5:case 2:case 4:case 17:return"";default:return","}},e.prototype.findItemAtOffset=function(t,n,r){for(var i=wt(n.getText(),!0),a=t.items,s=a.length-1;s>=0;s--){var u=a[s];if(r>u.offset+u.length){i.setPosition(u.offset+u.length);var c=i.scan();return c===5&&r>=i.getTokenOffset()+i.getTokenLength()?s+1:s}else if(r>=u.offset)return s}return 0},e.prototype.isInComment=function(t,n,r){var i=wt(t.getText(),!1);i.setPosition(n);for(var a=i.scan();a!==17&&i.getTokenOffset()+i.getTokenLength()<r;)a=i.scan();return(a===12||a===13)&&i.getTokenOffset()<=r},e.prototype.fromMarkup=function(t){if(t&&this.doesSupportMarkdown())return{kind:qe.Markdown,value:t}},e.prototype.doesSupportMarkdown=function(){if(!Ue(this.supportsMarkdown)){var t=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsMarkdown=t&&t.completionItem&&Array.isArray(t.completionItem.documentationFormat)&&t.completionItem.documentationFormat.indexOf(qe.Markdown)!==-1}return this.supportsMarkdown},e.prototype.doesSupportsCommitCharacters=function(){if(!Ue(this.supportsCommitCharacters)){var t=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsCommitCharacters=t&&t.completionItem&&!!t.completionItem.commitCharactersSupport}return this.supportsCommitCharacters},e}(),Js=function(){function e(t,n,r){n===void 0&&(n=[]),this.schemaService=t,this.contributions=n,this.promise=r||Promise}return e.prototype.doHover=function(t,n,r){var i=t.offsetAt(n),a=r.getNodeFromOffset(i);if(!a||(a.type==="object"||a.type==="array")&&i>a.offset+1&&i<a.offset+a.length-1)return this.promise.resolve(null);var s=a;if(a.type==="string"){var u=a.parent;if(u&&u.type==="property"&&u.keyNode===a&&(a=u.valueNode,!a))return this.promise.resolve(null)}for(var c=W.create(t.positionAt(s.offset),t.positionAt(s.offset+s.length)),l=function(p){var y={contents:p,range:c};return y},f=kr(a),h=this.contributions.length-1;h>=0;h--){var d=this.contributions[h],m=d.getInfoContribution(t.uri,f);if(m)return m.then(function(p){return l(p)})}return this.schemaService.getSchemaForResource(t.uri,r).then(function(p){if(p&&a){var y=r.getMatchingSchemas(p.schema,a.offset),g=void 0,w=void 0,b=void 0,v=void 0;y.every(function(x){if(x.node===a&&!x.inverted&&x.schema&&(g=g||x.schema.title,w=w||x.schema.markdownDescription||Tr(x.schema.description),x.schema.enum)){var A=x.schema.enum.indexOf(ht(a));x.schema.markdownEnumDescriptions?b=x.schema.markdownEnumDescriptions[A]:x.schema.enumDescriptions&&(b=Tr(x.schema.enumDescriptions[A])),b&&(v=x.schema.enum[A],typeof v!="string"&&(v=JSON.stringify(v)))}return!0});var L="";return g&&(L=Tr(g)),w&&(L.length>0&&(L+=`

`),L+=w),b&&(L.length>0&&(L+=`

`),L+="`".concat(Zs(v),"`: ").concat(b)),l([L])}return null})},e}();function Tr(e){if(e){var t=e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,`$1

$3`);return t.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}}function Zs(e){return e.indexOf("`")!==-1?"`` "+e+" ``":e}var Qs=Yt(),Ys=function(){function e(t,n){this.jsonSchemaService=t,this.promise=n,this.validationEnabled=!0}return e.prototype.configure=function(t){t&&(this.validationEnabled=t.validate!==!1,this.commentSeverity=t.allowComments?void 0:_e.Error)},e.prototype.doValidation=function(t,n,r,i){var a=this;if(!this.validationEnabled)return this.promise.resolve([]);var s=[],u={},c=function(d){var m=d.range.start.line+" "+d.range.start.character+" "+d.message;u[m]||(u[m]=!0,s.push(d))},l=function(d){var m=(r==null?void 0:r.trailingCommas)?xn(r.trailingCommas):_e.Error,p=(r==null?void 0:r.comments)?xn(r.comments):a.commentSeverity,y=(r==null?void 0:r.schemaValidation)?xn(r.schemaValidation):_e.Warning,g=(r==null?void 0:r.schemaRequest)?xn(r.schemaRequest):_e.Warning;if(d){if(d.errors.length&&n.root&&g){var w=n.root,b=w.type==="object"?w.properties[0]:void 0;if(b&&b.keyNode.value==="$schema"){var v=b.valueNode||b,L=W.create(t.positionAt(v.offset),t.positionAt(v.offset+v.length));c($e.create(L,d.errors[0],g,U.SchemaResolveError))}else{var L=W.create(t.positionAt(w.offset),t.positionAt(w.offset+1));c($e.create(L,d.errors[0],g,U.SchemaResolveError))}}else if(y){var x=n.validate(t,d.schema,y);x&&x.forEach(c)}m1(d.schema)&&(p=void 0),g1(d.schema)&&(m=void 0)}for(var A=0,C=n.syntaxErrors;A<C.length;A++){var _=C[A];if(_.code===U.TrailingComma){if(typeof m!="number")continue;_.severity=m}c(_)}if(typeof p=="number"){var S=Qs("InvalidCommentToken","Comments are not permitted in JSON.");n.comments.forEach(function(k){c($e.create(k,S,p,U.CommentNotPermitted))})}return s};if(i){var f=i.id||"schemaservice://untitled/"+Xs++,h=this.jsonSchemaService.registerExternalSchema(f,[],i);return h.getResolvedSchema().then(function(d){return l(d)})}return this.jsonSchemaService.getSchemaForResource(t.uri,n).then(function(d){return l(d)})},e.prototype.getLanguageStatus=function(t,n){return{schemas:this.jsonSchemaService.getSchemaURIsForResource(t.uri,n)}},e}(),Xs=0;function m1(e){if(e&&typeof e=="object"){if(Re(e.allowComments))return e.allowComments;if(e.allOf)for(var t=0,n=e.allOf;t<n.length;t++){var r=n[t],i=m1(r);if(Re(i))return i}}}function g1(e){if(e&&typeof e=="object"){if(Re(e.allowTrailingCommas))return e.allowTrailingCommas;var t=e;if(Re(t.allowsTrailingCommas))return t.allowsTrailingCommas;if(e.allOf)for(var n=0,r=e.allOf;n<r.length;n++){var i=r[n],a=g1(i);if(Re(a))return a}}}function xn(e){switch(e){case"error":return _e.Error;case"warning":return _e.Warning;case"ignore":return}}var p1=48,eu=57,tu=65,En=97,nu=102;function X(e){return e<p1?0:e<=eu?e-p1:(e<En&&(e+=En-tu),e>=En&&e<=nu?e-En+10:0)}function ru(e){if(e[0]==="#")switch(e.length){case 4:return{red:X(e.charCodeAt(1))*17/255,green:X(e.charCodeAt(2))*17/255,blue:X(e.charCodeAt(3))*17/255,alpha:1};case 5:return{red:X(e.charCodeAt(1))*17/255,green:X(e.charCodeAt(2))*17/255,blue:X(e.charCodeAt(3))*17/255,alpha:X(e.charCodeAt(4))*17/255};case 7:return{red:(X(e.charCodeAt(1))*16+X(e.charCodeAt(2)))/255,green:(X(e.charCodeAt(3))*16+X(e.charCodeAt(4)))/255,blue:(X(e.charCodeAt(5))*16+X(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(X(e.charCodeAt(1))*16+X(e.charCodeAt(2)))/255,green:(X(e.charCodeAt(3))*16+X(e.charCodeAt(4)))/255,blue:(X(e.charCodeAt(5))*16+X(e.charCodeAt(6)))/255,alpha:(X(e.charCodeAt(7))*16+X(e.charCodeAt(8)))/255}}}var iu=function(){function e(t){this.schemaService=t}return e.prototype.findDocumentSymbols=function(t,n,r){var i=this;r===void 0&&(r={resultLimit:Number.MAX_VALUE});var a=n.root;if(!a)return[];var s=r.resultLimit||Number.MAX_VALUE,u=t.uri;if((u==="vscode://defaultsettings/keybindings.json"||Wt(u.toLowerCase(),"/user/keybindings.json"))&&a.type==="array"){for(var c=[],l=0,f=a.items;l<f.length;l++){var h=f[l];if(h.type==="object")for(var d=0,m=h.properties;d<m.length;d++){var p=m[d];if(p.keyNode.value==="key"&&p.valueNode){var y=Ht.create(t.uri,tt(t,h));if(c.push({name:ht(p.valueNode),kind:Ve.Function,location:y}),s--,s<=0)return r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(u),c}}}return c}for(var g=[{node:a,containerName:""}],w=0,b=!1,v=[],L=function(A,C){A.type==="array"?A.items.forEach(function(_){_&&g.push({node:_,containerName:C})}):A.type==="object"&&A.properties.forEach(function(_){var S=_.valueNode;if(S)if(s>0){s--;var k=Ht.create(t.uri,tt(t,_)),E=C?C+"."+_.keyNode.value:_.keyNode.value;v.push({name:i.getKeyLabel(_),kind:i.getSymbolKind(S.type),location:k,containerName:C}),g.push({node:S,containerName:E})}else b=!0})};w<g.length;){var x=g[w++];L(x.node,x.containerName)}return b&&r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(u),v},e.prototype.findDocumentSymbols2=function(t,n,r){var i=this;r===void 0&&(r={resultLimit:Number.MAX_VALUE});var a=n.root;if(!a)return[];var s=r.resultLimit||Number.MAX_VALUE,u=t.uri;if((u==="vscode://defaultsettings/keybindings.json"||Wt(u.toLowerCase(),"/user/keybindings.json"))&&a.type==="array"){for(var c=[],l=0,f=a.items;l<f.length;l++){var h=f[l];if(h.type==="object")for(var d=0,m=h.properties;d<m.length;d++){var p=m[d];if(p.keyNode.value==="key"&&p.valueNode){var y=tt(t,h),g=tt(t,p.keyNode);if(c.push({name:ht(p.valueNode),kind:Ve.Function,range:y,selectionRange:g}),s--,s<=0)return r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(u),c}}}return c}for(var w=[],b=[{node:a,result:w}],v=0,L=!1,x=function(C,_){C.type==="array"?C.items.forEach(function(S,k){if(S)if(s>0){s--;var E=tt(t,S),T=E,R=String(k),j={name:R,kind:i.getSymbolKind(S.type),range:E,selectionRange:T,children:[]};_.push(j),b.push({result:j.children,node:S})}else L=!0}):C.type==="object"&&C.properties.forEach(function(S){var k=S.valueNode;if(k)if(s>0){s--;var E=tt(t,S),T=tt(t,S.keyNode),R=[],j={name:i.getKeyLabel(S),kind:i.getSymbolKind(k.type),range:E,selectionRange:T,children:R,detail:i.getDetail(k)};_.push(j),b.push({result:R,node:k})}else L=!0})};v<b.length;){var A=b[v++];x(A.node,A.result)}return L&&r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(u),w},e.prototype.getSymbolKind=function(t){switch(t){case"object":return Ve.Module;case"string":return Ve.String;case"number":return Ve.Number;case"array":return Ve.Array;case"boolean":return Ve.Boolean;default:return Ve.Variable}},e.prototype.getKeyLabel=function(t){var n=t.keyNode.value;return n&&(n=n.replace(/[\n]/g,"\u21B5")),n&&n.trim()?n:'"'.concat(n,'"')},e.prototype.getDetail=function(t){if(t){if(t.type==="boolean"||t.type==="number"||t.type==="null"||t.type==="string")return String(t.value);if(t.type==="array")return t.children.length?void 0:"[]";if(t.type==="object")return t.children.length?void 0:"{}"}},e.prototype.findDocumentColors=function(t,n,r){return this.schemaService.getSchemaForResource(t.uri,n).then(function(i){var a=[];if(i)for(var s=r&&typeof r.resultLimit=="number"?r.resultLimit:Number.MAX_VALUE,u=n.getMatchingSchemas(i.schema),c={},l=0,f=u;l<f.length;l++){var h=f[l];if(!h.inverted&&h.schema&&(h.schema.format==="color"||h.schema.format==="color-hex")&&h.node&&h.node.type==="string"){var d=String(h.node.offset);if(!c[d]){var m=ru(ht(h.node));if(m){var p=tt(t,h.node);a.push({color:m,range:p})}if(c[d]=!0,s--,s<=0)return r&&r.onResultLimitExceeded&&r.onResultLimitExceeded(t.uri),a}}}return a})},e.prototype.getColorPresentations=function(t,n,r,i){var a=[],s=Math.round(r.red*255),u=Math.round(r.green*255),c=Math.round(r.blue*255);function l(h){var d=h.toString(16);return d.length!==2?"0"+d:d}var f;return r.alpha===1?f="#".concat(l(s)).concat(l(u)).concat(l(c)):f="#".concat(l(s)).concat(l(u)).concat(l(c)).concat(l(Math.round(r.alpha*255))),a.push({label:f,textEdit:Me.replace(i,JSON.stringify(f))}),a},e}();function tt(e,t){return W.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}var D=Yt(),Ir={schemaAssociations:[],schemas:{"http://json-schema.org/schema#":{$ref:"http://json-schema.org/draft-07/schema#"},"http://json-schema.org/draft-04/schema#":{$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"http://json-schema.org/draft-07/schema#":{definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}},ou={id:D("schema.json.id","A unique identifier for the schema."),$schema:D("schema.json.$schema","The schema to verify this document against."),title:D("schema.json.title","A descriptive title of the element."),description:D("schema.json.description","A long description of the element. Used in hover menus and suggestions."),default:D("schema.json.default","A default value. Used by suggestions."),multipleOf:D("schema.json.multipleOf","A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:D("schema.json.maximum","The maximum numerical value, inclusive by default."),exclusiveMaximum:D("schema.json.exclusiveMaximum","Makes the maximum property exclusive."),minimum:D("schema.json.minimum","The minimum numerical value, inclusive by default."),exclusiveMinimum:D("schema.json.exclusiveMininum","Makes the minimum property exclusive."),maxLength:D("schema.json.maxLength","The maximum length of a string."),minLength:D("schema.json.minLength","The minimum length of a string."),pattern:D("schema.json.pattern","A regular expression to match the string against. It is not implicitly anchored."),additionalItems:D("schema.json.additionalItems","For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail."),items:D("schema.json.items","For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:D("schema.json.maxItems","The maximum number of items that can be inside an array. Inclusive."),minItems:D("schema.json.minItems","The minimum number of items that can be inside an array. Inclusive."),uniqueItems:D("schema.json.uniqueItems","If all of the items in the array must be unique. Defaults to false."),maxProperties:D("schema.json.maxProperties","The maximum number of properties an object can have. Inclusive."),minProperties:D("schema.json.minProperties","The minimum number of properties an object can have. Inclusive."),required:D("schema.json.required","An array of strings that lists the names of all properties required on this object."),additionalProperties:D("schema.json.additionalProperties","Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail."),definitions:D("schema.json.definitions","Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:D("schema.json.properties","A map of property names to schemas for each property."),patternProperties:D("schema.json.patternProperties","A map of regular expressions on property names to schemas for matching properties."),dependencies:D("schema.json.dependencies","A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:D("schema.json.enum","The set of literal values that are valid."),type:D("schema.json.type","Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:D("schema.json.format","Describes the format expected for the value."),allOf:D("schema.json.allOf","An array of schemas, all of which must match."),anyOf:D("schema.json.anyOf","An array of schemas, where at least one must match."),oneOf:D("schema.json.oneOf","An array of schemas, exactly one of which must match."),not:D("schema.json.not","A schema which must not match."),$id:D("schema.json.$id","A unique identifier for the schema."),$ref:D("schema.json.$ref","Reference a definition hosted on any location."),$comment:D("schema.json.$comment","Comments from schema authors to readers or maintainers of the schema."),readOnly:D("schema.json.readOnly","Indicates that the value of the instance is managed exclusively by the owning authority."),examples:D("schema.json.examples","Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:D("schema.json.contains",'An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:D("schema.json.propertyNames","If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:D("schema.json.const","An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:D("schema.json.contentMediaType","Describes the media type of a string property."),contentEncoding:D("schema.json.contentEncoding","Describes the content encoding of a string property."),if:D("schema.json.if",'The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:D("schema.json.then",'The "if" subschema is used for validation when the "if" subschema succeeds.'),else:D("schema.json.else",'The "else" subschema is used for validation when the "if" subschema fails.')};for(b1 in Ir.schemas){Ln=Ir.schemas[b1];for(St in Ln.properties)Nn=Ln.properties[St],typeof Nn=="boolean"&&(Nn=Ln.properties[St]={}),Pr=ou[St],Pr?Nn.description=Pr:console.log("".concat(St,": localize('schema.json.").concat(St,`', "")`))}var Ln,Nn,Pr,St,b1,v1;v1=(()=>{"use strict";var e={470:r=>{function i(u){if(typeof u!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(u))}function a(u,c){for(var l,f="",h=0,d=-1,m=0,p=0;p<=u.length;++p){if(p<u.length)l=u.charCodeAt(p);else{if(l===47)break;l=47}if(l===47){if(!(d===p-1||m===1))if(d!==p-1&&m===2){if(f.length<2||h!==2||f.charCodeAt(f.length-1)!==46||f.charCodeAt(f.length-2)!==46){if(f.length>2){var y=f.lastIndexOf("/");if(y!==f.length-1){y===-1?(f="",h=0):h=(f=f.slice(0,y)).length-1-f.lastIndexOf("/"),d=p,m=0;continue}}else if(f.length===2||f.length===1){f="",h=0,d=p,m=0;continue}}c&&(f.length>0?f+="/..":f="..",h=2)}else f.length>0?f+="/"+u.slice(d+1,p):f=u.slice(d+1,p),h=p-d-1;d=p,m=0}else l===46&&m!==-1?++m:m=-1}return f}var s={resolve:function(){for(var u,c="",l=!1,f=arguments.length-1;f>=-1&&!l;f--){var h;f>=0?h=arguments[f]:(u===void 0&&(u=gr.cwd()),h=u),i(h),h.length!==0&&(c=h+"/"+c,l=h.charCodeAt(0)===47)}return c=a(c,!l),l?c.length>0?"/"+c:"/":c.length>0?c:"."},normalize:function(u){if(i(u),u.length===0)return".";var c=u.charCodeAt(0)===47,l=u.charCodeAt(u.length-1)===47;return(u=a(u,!c)).length!==0||c||(u="."),u.length>0&&l&&(u+="/"),c?"/"+u:u},isAbsolute:function(u){return i(u),u.length>0&&u.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var u,c=0;c<arguments.length;++c){var l=arguments[c];i(l),l.length>0&&(u===void 0?u=l:u+="/"+l)}return u===void 0?".":s.normalize(u)},relative:function(u,c){if(i(u),i(c),u===c||(u=s.resolve(u))===(c=s.resolve(c)))return"";for(var l=1;l<u.length&&u.charCodeAt(l)===47;++l);for(var f=u.length,h=f-l,d=1;d<c.length&&c.charCodeAt(d)===47;++d);for(var m=c.length-d,p=h<m?h:m,y=-1,g=0;g<=p;++g){if(g===p){if(m>p){if(c.charCodeAt(d+g)===47)return c.slice(d+g+1);if(g===0)return c.slice(d+g)}else h>p&&(u.charCodeAt(l+g)===47?y=g:g===0&&(y=0));break}var w=u.charCodeAt(l+g);if(w!==c.charCodeAt(d+g))break;w===47&&(y=g)}var b="";for(g=l+y+1;g<=f;++g)g!==f&&u.charCodeAt(g)!==47||(b.length===0?b+="..":b+="/..");return b.length>0?b+c.slice(d+y):(d+=y,c.charCodeAt(d)===47&&++d,c.slice(d))},_makeLong:function(u){return u},dirname:function(u){if(i(u),u.length===0)return".";for(var c=u.charCodeAt(0),l=c===47,f=-1,h=!0,d=u.length-1;d>=1;--d)if((c=u.charCodeAt(d))===47){if(!h){f=d;break}}else h=!1;return f===-1?l?"/":".":l&&f===1?"//":u.slice(0,f)},basename:function(u,c){if(c!==void 0&&typeof c!="string")throw new TypeError('"ext" argument must be a string');i(u);var l,f=0,h=-1,d=!0;if(c!==void 0&&c.length>0&&c.length<=u.length){if(c.length===u.length&&c===u)return"";var m=c.length-1,p=-1;for(l=u.length-1;l>=0;--l){var y=u.charCodeAt(l);if(y===47){if(!d){f=l+1;break}}else p===-1&&(d=!1,p=l+1),m>=0&&(y===c.charCodeAt(m)?--m==-1&&(h=l):(m=-1,h=p))}return f===h?h=p:h===-1&&(h=u.length),u.slice(f,h)}for(l=u.length-1;l>=0;--l)if(u.charCodeAt(l)===47){if(!d){f=l+1;break}}else h===-1&&(d=!1,h=l+1);return h===-1?"":u.slice(f,h)},extname:function(u){i(u);for(var c=-1,l=0,f=-1,h=!0,d=0,m=u.length-1;m>=0;--m){var p=u.charCodeAt(m);if(p!==47)f===-1&&(h=!1,f=m+1),p===46?c===-1?c=m:d!==1&&(d=1):c!==-1&&(d=-1);else if(!h){l=m+1;break}}return c===-1||f===-1||d===0||d===1&&c===f-1&&c===l+1?"":u.slice(c,f)},format:function(u){if(u===null||typeof u!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof u);return function(c,l){var f=l.dir||l.root,h=l.base||(l.name||"")+(l.ext||"");return f?f===l.root?f+h:f+"/"+h:h}(0,u)},parse:function(u){i(u);var c={root:"",dir:"",base:"",ext:"",name:""};if(u.length===0)return c;var l,f=u.charCodeAt(0),h=f===47;h?(c.root="/",l=1):l=0;for(var d=-1,m=0,p=-1,y=!0,g=u.length-1,w=0;g>=l;--g)if((f=u.charCodeAt(g))!==47)p===-1&&(y=!1,p=g+1),f===46?d===-1?d=g:w!==1&&(w=1):d!==-1&&(w=-1);else if(!y){m=g+1;break}return d===-1||p===-1||w===0||w===1&&d===p-1&&d===m+1?p!==-1&&(c.base=c.name=m===0&&h?u.slice(1,p):u.slice(m,p)):(m===0&&h?(c.name=u.slice(1,d),c.base=u.slice(1,p)):(c.name=u.slice(m,d),c.base=u.slice(m,p)),c.ext=u.slice(d,p)),m>0?c.dir=u.slice(0,m-1):h&&(c.dir="/"),c},sep:"/",delimiter:":",win32:null,posix:null};s.posix=s,r.exports=s},447:(r,i,a)=>{var s;if(a.r(i),a.d(i,{URI:()=>b,Utils:()=>R}),typeof gr=="object")s=gr.platform==="win32";else if(typeof navigator=="object"){var u=navigator.userAgent;s=u.indexOf("Windows")>=0}var c,l,f=(c=function(M,O){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(I,P){I.__proto__=P}||function(I,P){for(var V in P)Object.prototype.hasOwnProperty.call(P,V)&&(I[V]=P[V])})(M,O)},function(M,O){if(typeof O!="function"&&O!==null)throw new TypeError("Class extends value "+String(O)+" is not a constructor or null");function I(){this.constructor=M}c(M,O),M.prototype=O===null?Object.create(O):(I.prototype=O.prototype,new I)}),h=/^\w[\w\d+.-]*$/,d=/^\//,m=/^\/\//;function p(M,O){if(!M.scheme&&O)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'.concat(M.authority,'", path: "').concat(M.path,'", query: "').concat(M.query,'", fragment: "').concat(M.fragment,'"}'));if(M.scheme&&!h.test(M.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(M.path){if(M.authority){if(!d.test(M.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(m.test(M.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}var y="",g="/",w=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,b=function(){function M(O,I,P,V,K,$){$===void 0&&($=!1),typeof O=="object"?(this.scheme=O.scheme||y,this.authority=O.authority||y,this.path=O.path||y,this.query=O.query||y,this.fragment=O.fragment||y):(this.scheme=function(Ke,ue){return Ke||ue?Ke:"file"}(O,$),this.authority=I||y,this.path=function(Ke,ue){switch(Ke){case"https":case"http":case"file":ue?ue[0]!==g&&(ue=g+ue):ue=g}return ue}(this.scheme,P||y),this.query=V||y,this.fragment=K||y,p(this,$))}return M.isUri=function(O){return O instanceof M||!!O&&typeof O.authority=="string"&&typeof O.fragment=="string"&&typeof O.path=="string"&&typeof O.query=="string"&&typeof O.scheme=="string"&&typeof O.fsPath=="string"&&typeof O.with=="function"&&typeof O.toString=="function"},Object.defineProperty(M.prototype,"fsPath",{get:function(){return _(this,!1)},enumerable:!1,configurable:!0}),M.prototype.with=function(O){if(!O)return this;var I=O.scheme,P=O.authority,V=O.path,K=O.query,$=O.fragment;return I===void 0?I=this.scheme:I===null&&(I=y),P===void 0?P=this.authority:P===null&&(P=y),V===void 0?V=this.path:V===null&&(V=y),K===void 0?K=this.query:K===null&&(K=y),$===void 0?$=this.fragment:$===null&&($=y),I===this.scheme&&P===this.authority&&V===this.path&&K===this.query&&$===this.fragment?this:new L(I,P,V,K,$)},M.parse=function(O,I){I===void 0&&(I=!1);var P=w.exec(O);return P?new L(P[2]||y,T(P[4]||y),T(P[5]||y),T(P[7]||y),T(P[9]||y),I):new L(y,y,y,y,y)},M.file=function(O){var I=y;if(s&&(O=O.replace(/\\/g,g)),O[0]===g&&O[1]===g){var P=O.indexOf(g,2);P===-1?(I=O.substring(2),O=g):(I=O.substring(2,P),O=O.substring(P)||g)}return new L("file",I,O,y,y)},M.from=function(O){var I=new L(O.scheme,O.authority,O.path,O.query,O.fragment);return p(I,!0),I},M.prototype.toString=function(O){return O===void 0&&(O=!1),S(this,O)},M.prototype.toJSON=function(){return this},M.revive=function(O){if(O){if(O instanceof M)return O;var I=new L(O);return I._formatted=O.external,I._fsPath=O._sep===v?O.fsPath:null,I}return O},M}(),v=s?1:void 0,L=function(M){function O(){var I=M!==null&&M.apply(this,arguments)||this;return I._formatted=null,I._fsPath=null,I}return f(O,M),Object.defineProperty(O.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=_(this,!1)),this._fsPath},enumerable:!1,configurable:!0}),O.prototype.toString=function(I){return I===void 0&&(I=!1),I?S(this,!0):(this._formatted||(this._formatted=S(this,!1)),this._formatted)},O.prototype.toJSON=function(){var I={$mid:1};return this._fsPath&&(I.fsPath=this._fsPath,I._sep=v),this._formatted&&(I.external=this._formatted),this.path&&(I.path=this.path),this.scheme&&(I.scheme=this.scheme),this.authority&&(I.authority=this.authority),this.query&&(I.query=this.query),this.fragment&&(I.fragment=this.fragment),I},O}(b),x=((l={})[58]="%3A",l[47]="%2F",l[63]="%3F",l[35]="%23",l[91]="%5B",l[93]="%5D",l[64]="%40",l[33]="%21",l[36]="%24",l[38]="%26",l[39]="%27",l[40]="%28",l[41]="%29",l[42]="%2A",l[43]="%2B",l[44]="%2C",l[59]="%3B",l[61]="%3D",l[32]="%20",l);function A(M,O){for(var I=void 0,P=-1,V=0;V<M.length;V++){var K=M.charCodeAt(V);if(K>=97&&K<=122||K>=65&&K<=90||K>=48&&K<=57||K===45||K===46||K===95||K===126||O&&K===47)P!==-1&&(I+=encodeURIComponent(M.substring(P,V)),P=-1),I!==void 0&&(I+=M.charAt(V));else{I===void 0&&(I=M.substr(0,V));var $=x[K];$!==void 0?(P!==-1&&(I+=encodeURIComponent(M.substring(P,V)),P=-1),I+=$):P===-1&&(P=V)}}return P!==-1&&(I+=encodeURIComponent(M.substring(P))),I!==void 0?I:M}function C(M){for(var O=void 0,I=0;I<M.length;I++){var P=M.charCodeAt(I);P===35||P===63?(O===void 0&&(O=M.substr(0,I)),O+=x[P]):O!==void 0&&(O+=M[I])}return O!==void 0?O:M}function _(M,O){var I;return I=M.authority&&M.path.length>1&&M.scheme==="file"?"//".concat(M.authority).concat(M.path):M.path.charCodeAt(0)===47&&(M.path.charCodeAt(1)>=65&&M.path.charCodeAt(1)<=90||M.path.charCodeAt(1)>=97&&M.path.charCodeAt(1)<=122)&&M.path.charCodeAt(2)===58?O?M.path.substr(1):M.path[1].toLowerCase()+M.path.substr(2):M.path,s&&(I=I.replace(/\//g,"\\")),I}function S(M,O){var I=O?C:A,P="",V=M.scheme,K=M.authority,$=M.path,Ke=M.query,ue=M.fragment;if(V&&(P+=V,P+=":"),(K||V==="file")&&(P+=g,P+=g),K){var Ee=K.indexOf("@");if(Ee!==-1){var xt=K.substr(0,Ee);K=K.substr(Ee+1),(Ee=xt.indexOf(":"))===-1?P+=I(xt,!1):(P+=I(xt.substr(0,Ee),!1),P+=":",P+=I(xt.substr(Ee+1),!1)),P+="@"}(Ee=(K=K.toLowerCase()).indexOf(":"))===-1?P+=I(K,!1):(P+=I(K.substr(0,Ee),!1),P+=K.substr(Ee))}if($){if($.length>=3&&$.charCodeAt(0)===47&&$.charCodeAt(2)===58)(je=$.charCodeAt(1))>=65&&je<=90&&($="/".concat(String.fromCharCode(je+32),":").concat($.substr(3)));else if($.length>=2&&$.charCodeAt(1)===58){var je;(je=$.charCodeAt(0))>=65&&je<=90&&($="".concat(String.fromCharCode(je+32),":").concat($.substr(2)))}P+=I($,!0)}return Ke&&(P+="?",P+=I(Ke,!1)),ue&&(P+="#",P+=O?ue:A(ue,!1)),P}function k(M){try{return decodeURIComponent(M)}catch(O){return M.length>3?M.substr(0,3)+k(M.substr(3)):M}}var E=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function T(M){return M.match(E)?M.replace(E,function(O){return k(O)}):M}var R,j=a(470),te=function(M,O,I){if(I||arguments.length===2)for(var P,V=0,K=O.length;V<K;V++)!P&&V in O||(P||(P=Array.prototype.slice.call(O,0,V)),P[V]=O[V]);return M.concat(P||Array.prototype.slice.call(O))},q=j.posix||j;(function(M){M.joinPath=function(O){for(var I=[],P=1;P<arguments.length;P++)I[P-1]=arguments[P];return O.with({path:q.join.apply(q,te([O.path],I,!1))})},M.resolvePath=function(O){for(var I=[],P=1;P<arguments.length;P++)I[P-1]=arguments[P];var V=O.path||"/";return O.with({path:q.resolve.apply(q,te([V],I,!1))})},M.dirname=function(O){var I=q.dirname(O.path);return I.length===1&&I.charCodeAt(0)===46?O:O.with({path:I})},M.basename=function(O){return q.basename(O.path)},M.extname=function(O){return q.extname(O.path)}})(R||(R={}))}},t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}return n.d=(r,i)=>{for(var a in i)n.o(i,a)&&!n.o(r,a)&&Object.defineProperty(r,a,{enumerable:!0,get:i[a]})},n.o=(r,i)=>Object.prototype.hasOwnProperty.call(r,i),n.r=r=>{typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},n(447)})();var{URI:At,Utils:hl}=v1;function au(e,t){if(typeof e!="string")throw new TypeError("Expected a string");for(var n=String(e),r="",i=t?!!t.extended:!1,a=t?!!t.globstar:!1,s=!1,u=t&&typeof t.flags=="string"?t.flags:"",c,l=0,f=n.length;l<f;l++)switch(c=n[l],c){case"/":case"$":case"^":case"+":case".":case"(":case")":case"=":case"!":case"|":r+="\\"+c;break;case"?":if(i){r+=".";break}case"[":case"]":if(i){r+=c;break}case"{":if(i){s=!0,r+="(";break}case"}":if(i){s=!1,r+=")";break}case",":if(s){r+="|";break}r+="\\"+c;break;case"*":for(var h=n[l-1],d=1;n[l+1]==="*";)d++,l++;var m=n[l+1];if(!a)r+=".*";else{var p=d>1&&(h==="/"||h===void 0||h==="{"||h===",")&&(m==="/"||m===void 0||m===","||m==="}");p?(m==="/"?l++:h==="/"&&r.endsWith("\\/")&&(r=r.substr(0,r.length-2)),r+="((?:[^/]*(?:/|$))*)"):r+="([^/]*)"}break;default:r+=c}return(!u||!~u.indexOf("g"))&&(r="^"+r+"$"),new RegExp(r,u)}var De=Yt(),su="!",uu="/",cu=function(){function e(t,n){this.globWrappers=[];try{for(var r=0,i=t;r<i.length;r++){var a=i[r],s=a[0]!==su;s||(a=a.substring(1)),a.length>0&&(a[0]===uu&&(a=a.substring(1)),this.globWrappers.push({regexp:au("**/"+a,{extended:!0,globstar:!0}),include:s}))}this.uris=n}catch(u){this.globWrappers.length=0,this.uris=[]}}return e.prototype.matchesPattern=function(t){for(var n=!1,r=0,i=this.globWrappers;r<i.length;r++){var a=i[r],s=a.regexp,u=a.include;s.test(t)&&(n=u)}return n},e.prototype.getURIs=function(){return this.uris},e}(),lu=function(){function e(t,n,r){this.service=t,this.uri=n,this.dependencies=new Set,this.anchors=void 0,r&&(this.unresolvedSchema=this.service.promise.resolve(new Xt(r)))}return e.prototype.getUnresolvedSchema=function(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.uri)),this.unresolvedSchema},e.prototype.getResolvedSchema=function(){var t=this;return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then(function(n){return t.service.resolveSchemaContent(n,t)})),this.resolvedSchema},e.prototype.clearSchema=function(){var t=!!this.unresolvedSchema;return this.resolvedSchema=void 0,this.unresolvedSchema=void 0,this.dependencies.clear(),this.anchors=void 0,t},e}(),Xt=function(){function e(t,n){n===void 0&&(n=[]),this.schema=t,this.errors=n}return e}(),y1=function(){function e(t,n){n===void 0&&(n=[]),this.schema=t,this.errors=n}return e.prototype.getSection=function(t){var n=this.getSectionRecursive(t,this.schema);if(n)return ve(n)},e.prototype.getSectionRecursive=function(t,n){if(!n||typeof n=="boolean"||t.length===0)return n;var r=t.shift();if(n.properties&&typeof n.properties[r])return this.getSectionRecursive(t,n.properties[r]);if(n.patternProperties)for(var i=0,a=Object.keys(n.patternProperties);i<a.length;i++){var s=a[i],u=bn(s);if(u==null?void 0:u.test(r))return this.getSectionRecursive(t,n.patternProperties[s])}else{if(typeof n.additionalProperties=="object")return this.getSectionRecursive(t,n.additionalProperties);if(r.match("[0-9]+")){if(Array.isArray(n.items)){var c=parseInt(r,10);if(!isNaN(c)&&n.items[c])return this.getSectionRecursive(t,n.items[c])}else if(n.items)return this.getSectionRecursive(t,n.items)}}},e}(),hu=function(){function e(t,n,r){this.contextService=n,this.requestService=t,this.promiseConstructor=r||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations=[],this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={}}return e.prototype.getRegisteredSchemaIds=function(t){return Object.keys(this.registeredSchemasIds).filter(function(n){var r=At.parse(n).scheme;return r!=="schemaservice"&&(!t||t(r))})},Object.defineProperty(e.prototype,"promise",{get:function(){return this.promiseConstructor},enumerable:!1,configurable:!0}),e.prototype.dispose=function(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()},e.prototype.onResourceChange=function(t){var n=this;this.cachedSchemaForResource=void 0;var r=!1;t=nt(t);for(var i=[t],a=Object.keys(this.schemasById).map(function(l){return n.schemasById[l]});i.length;)for(var s=i.pop(),u=0;u<a.length;u++){var c=a[u];c&&(c.uri===s||c.dependencies.has(s))&&(c.uri!==s&&i.push(c.uri),c.clearSchema()&&(r=!0),a[u]=void 0)}return r},e.prototype.setSchemaContributions=function(t){if(t.schemas){var n=t.schemas;for(var r in n){var i=nt(r);this.contributionSchemas[i]=this.addSchemaHandle(i,n[r])}}if(Array.isArray(t.schemaAssociations))for(var a=t.schemaAssociations,s=0,u=a;s<u.length;s++){var c=u[s],l=c.uris.map(nt),f=this.addFilePatternAssociation(c.pattern,l);this.contributionAssociations.push(f)}},e.prototype.addSchemaHandle=function(t,n){var r=new lu(this,t,n);return this.schemasById[t]=r,r},e.prototype.getOrAddSchemaHandle=function(t,n){return this.schemasById[t]||this.addSchemaHandle(t,n)},e.prototype.addFilePatternAssociation=function(t,n){var r=new cu(t,n);return this.filePatternAssociations.push(r),r},e.prototype.registerExternalSchema=function(t,n,r){var i=nt(t);return this.registeredSchemasIds[i]=!0,this.cachedSchemaForResource=void 0,n&&this.addFilePatternAssociation(n,[i]),r?this.addSchemaHandle(i,r):this.getOrAddSchemaHandle(i)},e.prototype.clearExternalSchemas=function(){this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={},this.cachedSchemaForResource=void 0;for(var t in this.contributionSchemas)this.schemasById[t]=this.contributionSchemas[t],this.registeredSchemasIds[t]=!0;for(var n=0,r=this.contributionAssociations;n<r.length;n++){var i=r[n];this.filePatternAssociations.push(i)}},e.prototype.getResolvedSchema=function(t){var n=nt(t),r=this.schemasById[n];return r?r.getResolvedSchema():this.promise.resolve(void 0)},e.prototype.loadSchema=function(t){if(!this.requestService){var n=De("json.schema.norequestservice","Unable to load schema from '{0}'. No schema request service available",kn(t));return this.promise.resolve(new Xt({},[n]))}return this.requestService(t).then(function(r){if(!r){var i=De("json.schema.nocontent","Unable to load schema from '{0}': No content.",kn(t));return new Xt({},[i])}var a={},s=[];a=Es(r,s);var u=s.length?[De("json.schema.invalidFormat","Unable to parse content from '{0}': Parse error at offset {1}.",kn(t),s[0].offset)]:[];return new Xt(a,u)},function(r){var i=r.toString(),a=r.toString().split("Error: ");return a.length>1&&(i=a[1]),Wt(i,".")&&(i=i.substr(0,i.length-1)),new Xt({},[De("json.schema.nocontent","Unable to load schema from '{0}': {1}.",kn(t),i)])})},e.prototype.resolveSchemaContent=function(t,n){var r=this,i=t.errors.slice(0),a=t.schema;if(a.$schema){var s=nt(a.$schema);if(s==="http://json-schema.org/draft-03/schema")return this.promise.resolve(new y1({},[De("json.schema.draft03.notsupported","Draft-03 schemas are not supported.")]));s==="https://json-schema.org/draft/2019-09/schema"?i.push(De("json.schema.draft201909.notsupported","Draft 2019-09 schemas are not yet fully supported.")):s==="https://json-schema.org/draft/2020-12/schema"&&i.push(De("json.schema.draft202012.notsupported","Draft 2020-12 schemas are not yet fully supported."))}var u=this.contextService,c=function(y,g){g=decodeURIComponent(g);var w=y;return g[0]==="/"&&(g=g.substring(1)),g.split("/").some(function(b){return b=b.replace(/~1/g,"/").replace(/~0/g,"~"),w=w[b],!w}),w},l=function(y,g,w){return g.anchors||(g.anchors=p(y)),g.anchors.get(w)},f=function(y,g){for(var w in g)g.hasOwnProperty(w)&&!y.hasOwnProperty(w)&&w!=="id"&&w!=="$id"&&(y[w]=g[w])},h=function(y,g,w,b){var v;b===void 0||b.length===0?v=g:b.charAt(0)==="/"?v=c(g,b):v=l(g,w,b),v?f(y,v):i.push(De("json.schema.invalidid","$ref '{0}' in '{1}' can not be resolved.",b,w.uri))},d=function(y,g,w,b){u&&!/^[A-Za-z][A-Za-z0-9+\-.+]*:\/\/.*/.test(g)&&(g=u.resolveRelativePath(g,b.uri)),g=nt(g);var v=r.getOrAddSchemaHandle(g);return v.getUnresolvedSchema().then(function(L){if(b.dependencies.add(g),L.errors.length){var x=w?g+"#"+w:g;i.push(De("json.schema.problemloadingref","Problems loading reference '{0}': {1}",x,L.errors[0]))}return h(y,L.schema,v,w),m(y,L.schema,v)})},m=function(y,g,w){var b=[];return r.traverseNodes(y,function(v){for(var L=new Set;v.$ref;){var x=v.$ref,A=x.split("#",2);if(delete v.$ref,A[0].length>0){b.push(d(v,A[0],A[1],w));return}else if(!L.has(x)){var C=A[1];h(v,g,w,C),L.add(x)}}}),r.promise.all(b)},p=function(y){var g=new Map;return r.traverseNodes(y,function(w){var b=w.$id||w.id;if(typeof b=="string"&&b.charAt(0)==="#"){var v=b.substring(1);g.has(v)?i.push(De("json.schema.duplicateid","Duplicate id declaration: '{0}'",b)):g.set(v,w)}}),g};return m(a,a,n).then(function(y){return new y1(a,i)})},e.prototype.traverseNodes=function(t,n){if(!t||typeof t!="object")return Promise.resolve(null);for(var r=new Set,i=function(){for(var l=[],f=0;f<arguments.length;f++)l[f]=arguments[f];for(var h=0,d=l;h<d.length;h++){var m=d[h];typeof m=="object"&&u.push(m)}},a=function(){for(var l=[],f=0;f<arguments.length;f++)l[f]=arguments[f];for(var h=0,d=l;h<d.length;h++){var m=d[h];if(typeof m=="object")for(var p in m){var y=p,g=m[y];typeof g=="object"&&u.push(g)}}},s=function(){for(var l=[],f=0;f<arguments.length;f++)l[f]=arguments[f];for(var h=0,d=l;h<d.length;h++){var m=d[h];if(Array.isArray(m))for(var p=0,y=m;p<y.length;p++){var g=y[p];typeof g=="object"&&u.push(g)}}},u=[t],c=u.pop();c;)r.has(c)||(r.add(c),n(c),i(c.items,c.additionalItems,c.additionalProperties,c.not,c.contains,c.propertyNames,c.if,c.then,c.else),a(c.definitions,c.properties,c.patternProperties,c.dependencies),s(c.anyOf,c.allOf,c.oneOf,c.items)),c=u.pop()},e.prototype.getSchemaFromProperty=function(t,n){var r,i;if(((r=n.root)===null||r===void 0?void 0:r.type)==="object")for(var a=0,s=n.root.properties;a<s.length;a++){var u=s[a];if(u.keyNode.value==="$schema"&&((i=u.valueNode)===null||i===void 0?void 0:i.type)==="string"){var c=u.valueNode.value;return this.contextService&&!/^\w[\w\d+.-]*:/.test(c)&&(c=this.contextService.resolveRelativePath(c,t)),c}}},e.prototype.getAssociatedSchemas=function(t){for(var n=Object.create(null),r=[],i=du(t),a=0,s=this.filePatternAssociations;a<s.length;a++){var u=s[a];if(u.matchesPattern(i))for(var c=0,l=u.getURIs();c<l.length;c++){var f=l[c];n[f]||(r.push(f),n[f]=!0)}}return r},e.prototype.getSchemaURIsForResource=function(t,n){var r=n&&this.getSchemaFromProperty(t,n);return r?[r]:this.getAssociatedSchemas(t)},e.prototype.getSchemaForResource=function(t,n){if(n){var r=this.getSchemaFromProperty(t,n);if(r){var i=nt(r);return this.getOrAddSchemaHandle(i).getResolvedSchema()}}if(this.cachedSchemaForResource&&this.cachedSchemaForResource.resource===t)return this.cachedSchemaForResource.resolvedSchema;var a=this.getAssociatedSchemas(t),s=a.length>0?this.createCombinedSchema(t,a).getResolvedSchema():this.promise.resolve(void 0);return this.cachedSchemaForResource={resource:t,resolvedSchema:s},s},e.prototype.createCombinedSchema=function(t,n){if(n.length===1)return this.getOrAddSchemaHandle(n[0]);var r="schemaservice://combinedSchema/"+encodeURIComponent(t),i={allOf:n.map(function(a){return{$ref:a}})};return this.addSchemaHandle(r,i)},e.prototype.getMatchingSchemas=function(t,n,r){if(r){var i=r.id||"schemaservice://untitled/matchingSchemas/"+fu++,a=this.addSchemaHandle(i,r);return a.getResolvedSchema().then(function(s){return n.getMatchingSchemas(s.schema).filter(function(u){return!u.inverted})})}return this.getSchemaForResource(t.uri,n).then(function(s){return s?n.getMatchingSchemas(s.schema).filter(function(u){return!u.inverted}):[]})},e}(),fu=0;function nt(e){try{return At.parse(e).toString()}catch(t){return e}}function du(e){try{return At.parse(e).with({fragment:null,query:null}).toString()}catch(t){return e}}function kn(e){try{var t=At.parse(e);if(t.scheme==="file")return t.fsPath}catch(n){}return e}function mu(e,t){var n=[],r=[],i=[],a=-1,s=wt(e.getText(),!1),u=s.scan();function c(k){n.push(k),r.push(i.length)}for(;u!==17;){switch(u){case 1:case 3:{var l=e.positionAt(s.getTokenOffset()).line,f={startLine:l,endLine:l,kind:u===1?"object":"array"};i.push(f);break}case 2:case 4:{var h=u===2?"object":"array";if(i.length>0&&i[i.length-1].kind===h){var f=i.pop(),d=e.positionAt(s.getTokenOffset()).line;f&&d>f.startLine+1&&a!==f.startLine&&(f.endLine=d-1,c(f),a=f.startLine)}break}case 13:{var l=e.positionAt(s.getTokenOffset()).line,m=e.positionAt(s.getTokenOffset()+s.getTokenLength()).line;s.getTokenError()===1&&l+1<e.lineCount?s.setPosition(e.offsetAt(Oe.create(l+1,0))):l<m&&(c({startLine:l,endLine:m,kind:zt.Comment}),a=l);break}case 12:{var p=e.getText().substr(s.getTokenOffset(),s.getTokenLength()),y=p.match(/^\/\/\s*#(region\b)|(endregion\b)/);if(y){var d=e.positionAt(s.getTokenOffset()).line;if(y[1]){var f={startLine:d,endLine:d,kind:zt.Region};i.push(f)}else{for(var g=i.length-1;g>=0&&i[g].kind!==zt.Region;)g--;if(g>=0){var f=i[g];i.length=g,d>f.startLine&&a!==f.startLine&&(f.endLine=d,c(f),a=f.startLine)}}}break}}u=s.scan()}var w=t&&t.rangeLimit;if(typeof w!="number"||n.length<=w)return n;t&&t.onRangeLimitExceeded&&t.onRangeLimitExceeded(e.uri);for(var b=[],v=0,L=r;v<L.length;v++){var x=L[v];x<30&&(b[x]=(b[x]||0)+1)}for(var A=0,C=0,g=0;g<b.length;g++){var _=b[g];if(_){if(_+A>w){C=g;break}A+=_}}for(var S=[],g=0;g<n.length;g++){var x=r[g];typeof x=="number"&&(x<C||x===C&&A++<w)&&S.push(n[g])}return S}function gu(e,t,n){function r(u){for(var c=e.offsetAt(u),l=n.getNodeFromOffset(c,!0),f=[];l;){switch(l.type){case"string":case"object":case"array":var h=l.offset+1,d=l.offset+l.length-1;h<d&&c>=h&&c<=d&&f.push(i(h,d)),f.push(i(l.offset,l.offset+l.length));break;case"number":case"boolean":case"null":case"property":f.push(i(l.offset,l.offset+l.length));break}if(l.type==="property"||l.parent&&l.parent.type==="array"){var m=s(l.offset+l.length,5);m!==-1&&f.push(i(l.offset,m))}l=l.parent}for(var p=void 0,y=f.length-1;y>=0;y--)p=Sn.create(f[y],p);return p||(p=Sn.create(W.create(u,u))),p}function i(u,c){return W.create(e.positionAt(u),e.positionAt(c))}var a=wt(e.getText(),!0);function s(u,c){a.setPosition(u);var l=a.scan();return l===c?a.getTokenOffset()+a.getTokenLength():-1}return t.map(r)}function pu(e,t){var n=[];return t.visit(function(r){var i;if(r.type==="property"&&r.keyNode.value==="$ref"&&((i=r.valueNode)===null||i===void 0?void 0:i.type)==="string"){var a=r.valueNode.value,s=vu(t,a);if(s){var u=e.positionAt(s.offset);n.push({target:"".concat(e.uri,"#").concat(u.line+1,",").concat(u.character+1),range:bu(e,r.valueNode)})}}return!0}),Promise.resolve(n)}function bu(e,t){return W.create(e.positionAt(t.offset+1),e.positionAt(t.offset+t.length-1))}function vu(e,t){var n=yu(t);return n?Fr(n,e.root):null}function Fr(e,t){if(!t)return null;if(e.length===0)return t;var n=e.shift();if(t&&t.type==="object"){var r=t.properties.find(function(s){return s.keyNode.value===n});return r?Fr(e,r.valueNode):null}else if(t&&t.type==="array"&&n.match(/^(0|[1-9][0-9]*)$/)){var i=Number.parseInt(n),a=t.items[i];return a?Fr(e,a):null}return null}function yu(e){return e==="#"?[]:e[0]!=="#"||e[1]!=="/"?null:e.substring(2).split(/\//).map(Cu)}function Cu(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function wu(e){var t=e.promiseConstructor||Promise,n=new hu(e.schemaRequestService,e.workspaceContext,t);n.setSchemaContributions(Ir);var r=new Gs(n,e.contributions,t,e.clientCapabilities),i=new Js(n,e.contributions,t),a=new iu(n),s=new Ys(n,t);return{configure:function(u){n.clearExternalSchemas(),u.schemas&&u.schemas.forEach(function(c){n.registerExternalSchema(c.uri,c.fileMatch,c.schema)}),s.configure(u)},resetSchema:function(u){return n.onResourceChange(u)},doValidation:s.doValidation.bind(s),getLanguageStatus:s.getLanguageStatus.bind(s),parseJSONDocument:function(u){return Ws(u,{collectComments:!0})},newJSONDocument:function(u,c){return qs(u,c)},getMatchingSchemas:n.getMatchingSchemas.bind(n),doResolve:r.doResolve.bind(r),doComplete:r.doComplete.bind(r),findDocumentSymbols:a.findDocumentSymbols.bind(a),findDocumentSymbols2:a.findDocumentSymbols2.bind(a),findDocumentColors:a.findDocumentColors.bind(a),getColorPresentations:a.getColorPresentations.bind(a),doHover:i.doHover.bind(i),getFoldingRanges:mu,getSelectionRanges:gu,findDefinition:function(){return Promise.resolve([])},findLinks:pu,format:function(u,c,l){var f=void 0;if(c){var h=u.offsetAt(c.start),d=u.offsetAt(c.end)-h;f={offset:h,length:d}}var m={tabSize:l?l.tabSize:4,insertSpaces:(l==null?void 0:l.insertSpaces)===!0,insertFinalNewline:(l==null?void 0:l.insertFinalNewline)===!0,eol:`
`};return Os(u.getText(),f,m).map(function(p){return Me.replace(W.create(u.positionAt(p.offset),u.positionAt(p.offset+p.length)),p.content)})}}}var C1;typeof fetch!="undefined"&&(C1=function(e){return fetch(e).then(t=>t.text())});var _u=class{constructor(e,t){er(this,"_ctx"),er(this,"_languageService"),er(this,"_languageSettings"),er(this,"_languageId"),this._ctx=e,this._languageSettings=t.languageSettings,this._languageId=t.languageId,this._languageService=wu({workspaceContext:{resolveRelativePath:(n,r)=>{const i=r.substr(0,r.lastIndexOf("/")+1);return xu(i,n)}},schemaRequestService:t.enableSchemaRequest?C1:void 0}),this._languageService.configure(this._languageSettings)}doValidation(e){return ke(this,null,function*(){let t=this._getTextDocument(e);if(t){let n=this._languageService.parseJSONDocument(t);return this._languageService.doValidation(t,n,this._languageSettings)}return Promise.resolve([])})}doComplete(e,t){return ke(this,null,function*(){let n=this._getTextDocument(e);if(!n)return null;let r=this._languageService.parseJSONDocument(n);return this._languageService.doComplete(n,t,r)})}doResolve(e){return ke(this,null,function*(){return this._languageService.doResolve(e)})}doHover(e,t){return ke(this,null,function*(){let n=this._getTextDocument(e);if(!n)return null;let r=this._languageService.parseJSONDocument(n);return this._languageService.doHover(n,t,r)})}format(e,t,n){return ke(this,null,function*(){let r=this._getTextDocument(e);if(!r)return[];let i=this._languageService.format(r,t,n);return Promise.resolve(i)})}resetSchema(e){return ke(this,null,function*(){return Promise.resolve(this._languageService.resetSchema(e))})}findDocumentSymbols(e){return ke(this,null,function*(){let t=this._getTextDocument(e);if(!t)return[];let n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentSymbols(t,n);return Promise.resolve(r)})}findDocumentColors(e){return ke(this,null,function*(){let t=this._getTextDocument(e);if(!t)return[];let n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentColors(t,n);return Promise.resolve(r)})}getColorPresentations(e,t,n){return ke(this,null,function*(){let r=this._getTextDocument(e);if(!r)return[];let i=this._languageService.parseJSONDocument(r),a=this._languageService.getColorPresentations(r,i,t,n);return Promise.resolve(a)})}getFoldingRanges(e,t){return ke(this,null,function*(){let n=this._getTextDocument(e);if(!n)return[];let r=this._languageService.getFoldingRanges(n,t);return Promise.resolve(r)})}getSelectionRanges(e,t){return ke(this,null,function*(){let n=this._getTextDocument(e);if(!n)return[];let r=this._languageService.parseJSONDocument(n),i=this._languageService.getSelectionRanges(n,t,r);return Promise.resolve(i)})}_getTextDocument(e){let t=this._ctx.getMirrorModels();for(let n of t)if(n.uri.toString()===e)return xr.create(e,this._languageId,n.version,n.getValue());return null}},Su="/".charCodeAt(0),Rr=".".charCodeAt(0);function Au(e){return e.charCodeAt(0)===Su}function xu(e,t){if(Au(t)){const n=At.parse(e),r=t.split("/");return n.with({path:w1(r)}).toString()}return Eu(e,t)}function w1(e){const t=[];for(const r of e)r.length===0||r.length===1&&r.charCodeAt(0)===Rr||(r.length===2&&r.charCodeAt(0)===Rr&&r.charCodeAt(1)===Rr?t.pop():t.push(r));e.length>1&&e[e.length-1].length===0&&t.push("");let n=t.join("/");return e[0].length===0&&(n="/"+n),n}function Eu(e,...t){const n=At.parse(e),r=n.path.split("/");for(let i of t)r.push(...i.split("/"));return n.with({path:w1(r)}).toString()}self.onmessage=()=>{Lo((e,t)=>new _u(e,t))}})()})();
