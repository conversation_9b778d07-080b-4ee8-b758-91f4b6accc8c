(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_ant-design_pro-layout_es_index_js-node_modules_antd_es__util_getDataOrAr-a4d3c4"],{

/***/ "./node_modules/@ant-design/pro-layout/es/BasicLayout.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/BasicLayout.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_layout_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/layout/style */ "./node_modules/antd/es/layout/style/index.js");
/* harmony import */ var antd_es_layout__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! antd/es/layout */ "./node_modules/antd/es/layout/index.js");
/* harmony import */ var antd_es_config_provider_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! antd/es/config-provider/style */ "./node_modules/antd/es/config-provider/style/index.js");
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! antd/es/config-provider */ "./node_modules/antd/es/config-provider/index.js");
/* harmony import */ var _BasicLayout_less__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BasicLayout.less */ "./node_modules/@ant-design/pro-layout/es/BasicLayout.less");
/* harmony import */ var _BasicLayout_less__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_BasicLayout_less__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! warning */ "./node_modules/warning/warning.js");
/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(warning__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ "./node_modules/rc-util/es/hooks/useMergedState.js");
/* harmony import */ var use_media_antd_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! use-media-antd-query */ "./node_modules/use-media-antd-query/es/index.js");
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/useMountMergeState/index.js");
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/hooks/useDocumentTitle/index.js");
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/isBrowser/index.js");
/* harmony import */ var omit_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! omit.js */ "./node_modules/omit.js/es/index.js");
/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! swr */ "./node_modules/swr/dist/index.esm.js");
/* harmony import */ var _umijs_route_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @umijs/route-utils */ "./node_modules/@umijs/route-utils/es/index.js");
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Header */ "./node_modules/@ant-design/pro-layout/es/Header.js");
/* harmony import */ var _getPageTitle__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./getPageTitle */ "./node_modules/@ant-design/pro-layout/es/getPageTitle.js");
/* harmony import */ var _defaultSettings__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./defaultSettings */ "./node_modules/@ant-design/pro-layout/es/defaultSettings.js");
/* harmony import */ var _locales__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./locales */ "./node_modules/@ant-design/pro-layout/es/locales/index.js");
/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Footer */ "./node_modules/@ant-design/pro-layout/es/Footer.js");
/* harmony import */ var _RouteContext__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./RouteContext */ "./node_modules/@ant-design/pro-layout/es/RouteContext.js");
/* harmony import */ var _components_SiderMenu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./components/SiderMenu */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.js");
/* harmony import */ var _utils_getBreadcrumbProps__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./utils/getBreadcrumbProps */ "./node_modules/@ant-design/pro-layout/es/utils/getBreadcrumbProps.js");
/* harmony import */ var _utils_getMenuData__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./utils/getMenuData */ "./node_modules/@ant-design/pro-layout/es/utils/getMenuData.js");
/* harmony import */ var _components_PageLoading__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./components/PageLoading */ "./node_modules/@ant-design/pro-layout/es/components/PageLoading/index.js");
/* harmony import */ var _components_SiderMenu_Counter__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./components/SiderMenu/Counter */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/Counter.js");
/* harmony import */ var _WrapContent__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./WrapContent */ "./node_modules/@ant-design/pro-layout/es/WrapContent.js");
/* harmony import */ var _utils_compatibleLayout__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./utils/compatibleLayout */ "./node_modules/@ant-design/pro-layout/es/utils/compatibleLayout.js");
/* harmony import */ var _utils_useCurrentMenuLayoutProps__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./utils/useCurrentMenuLayoutProps */ "./node_modules/@ant-design/pro-layout/es/utils/useCurrentMenuLayoutProps.js");
/* harmony import */ var _utils_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./utils/utils */ "./node_modules/@ant-design/pro-layout/es/utils/utils.js");
/* harmony import */ var use_json_comparison__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! use-json-comparison */ "./node_modules/use-json-comparison/dist/index.esm.js");




var _excluded = ["id", "defaultMessage"],
    _excluded2 = ["fixSiderbar", "navTheme", "layout"];

function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }




























var layoutIndex = 0;

var headerRender = function headerRender(props, matchMenuKeys) {
  if (props.headerRender === false || props.pure) {
    return null;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_Header__WEBPACK_IMPORTED_MODULE_12__.default, _extends({
    matchMenuKeys: matchMenuKeys
  }, props));
};

var footerRender = function footerRender(props) {
  if (props.footerRender === false || props.pure) {
    return null;
  }

  if (props.footerRender) {
    return props.footerRender(_objectSpread({}, props), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_Footer__WEBPACK_IMPORTED_MODULE_13__.default, null));
  }

  return null;
};

var renderSiderMenu = function renderSiderMenu(props, matchMenuKeys) {
  var layout = props.layout,
      isMobile = props.isMobile,
      openKeys = props.openKeys,
      splitMenus = props.splitMenus,
      menuRender = props.menuRender;

  if (props.menuRender === false || props.pure) {
    return null;
  }

  var menuData = props.menuData;
  /** 如果是分割菜单模式，需要专门实现一下 */

  if (splitMenus && (openKeys !== false || layout === 'mix') && !isMobile) {
    var _matchMenuKeys = _slicedToArray(matchMenuKeys, 1),
        key = _matchMenuKeys[0];

    if (key) {
      var _props$menuData, _props$menuData$find;

      menuData = ((_props$menuData = props.menuData) === null || _props$menuData === void 0 ? void 0 : (_props$menuData$find = _props$menuData.find(function (item) {
        return item.key === key;
      })) === null || _props$menuData$find === void 0 ? void 0 : _props$menuData$find.routes) || [];
    } else {
      menuData = [];
    }
  } // 这里走了可以少一次循环


  var clearMenuData = (0,_utils_utils__WEBPACK_IMPORTED_MODULE_14__.clearMenuItem)(menuData || []);

  if (clearMenuData && (clearMenuData === null || clearMenuData === void 0 ? void 0 : clearMenuData.length) < 1 && splitMenus) {
    return null;
  }

  if (layout === 'top' && !isMobile) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_components_SiderMenu__WEBPACK_IMPORTED_MODULE_15__.default, _extends({
      matchMenuKeys: matchMenuKeys
    }, props, {
      hide: true
    }));
  }

  if (menuRender) {
    var defaultDom = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_components_SiderMenu__WEBPACK_IMPORTED_MODULE_15__.default, _extends({
      matchMenuKeys: matchMenuKeys
    }, props, {
      // 这里走了可以少一次循环
      menuData: clearMenuData
    }));
    return menuRender(props, defaultDom);
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_components_SiderMenu__WEBPACK_IMPORTED_MODULE_15__.default, _extends({
    matchMenuKeys: matchMenuKeys
  }, props, {
    // 这里走了可以少一次循环
    menuData: clearMenuData
  }));
};

var defaultPageTitleRender = function defaultPageTitleRender(pageProps, props) {
  var pageTitleRender = props.pageTitleRender;
  var pageTitleInfo = (0,_getPageTitle__WEBPACK_IMPORTED_MODULE_16__.getPageTitleInfo)(pageProps);

  if (pageTitleRender === false) {
    return {
      title: props.title || '',
      id: '',
      pageName: ''
    };
  }

  if (pageTitleRender) {
    var title = pageTitleRender(pageProps, pageTitleInfo.title, pageTitleInfo);

    if (typeof title === 'string') {
      return _objectSpread(_objectSpread({}, pageTitleInfo), {}, {
        title: title
      });
    }

    warning__WEBPACK_IMPORTED_MODULE_5___default()(typeof title === 'string', 'pro-layout: renderPageTitle return value should be a string');
  }

  return pageTitleInfo;
};

var getPaddingLeft = function getPaddingLeft(hasLeftPadding, collapsed, siderWidth) {
  if (hasLeftPadding) {
    return collapsed ? 48 : siderWidth;
  }

  return 0;
};
/**
 * 🌃 Powerful and easy to use beautiful layout 🏄‍ Support multiple topics and layout types
 *
 * @param props
 */


var BasicLayout = function BasicLayout(props) {
  var _props$prefixCls, _classNames, _classNames2, _location$pathname;

  var _ref = props || {},
      children = _ref.children,
      propsOnCollapse = _ref.onCollapse,
      _ref$location = _ref.location,
      location = _ref$location === void 0 ? {
    pathname: '/'
  } : _ref$location,
      contentStyle = _ref.contentStyle,
      route = _ref.route,
      defaultCollapsed = _ref.defaultCollapsed,
      style = _ref.style,
      disableContentMargin = _ref.disableContentMargin,
      _ref$siderWidth = _ref.siderWidth,
      siderWidth = _ref$siderWidth === void 0 ? 208 : _ref$siderWidth,
      menu = _ref.menu,
      propsIsChildrenLayout = _ref.isChildrenLayout,
      menuDataRender = _ref.menuDataRender,
      actionRef = _ref.actionRef,
      propsFormatMessage = _ref.formatMessage,
      loading = _ref.loading;

  var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(antd_es_config_provider__WEBPACK_IMPORTED_MODULE_17__.default.ConfigContext);
  var prefixCls = (_props$prefixCls = props.prefixCls) !== null && _props$prefixCls !== void 0 ? _props$prefixCls : context.getPrefixCls('pro');

  var _useMountMergeState = (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_18__.default)(false, {
    value: menu === null || menu === void 0 ? void 0 : menu.loading,
    onChange: menu === null || menu === void 0 ? void 0 : menu.onLoadingChange
  }),
      _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2),
      menuLoading = _useMountMergeState2[0],
      setMenuLoading = _useMountMergeState2[1]; // give a default key for swr


  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(function () {
    layoutIndex += 1;
    return "pro-layout-".concat(layoutIndex);
  }),
      _useState2 = _slicedToArray(_useState, 1),
      defaultId = _useState2[0];

  var formatMessage = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (_ref2) {
    var id = _ref2.id,
        defaultMessage = _ref2.defaultMessage,
        restParams = _objectWithoutProperties(_ref2, _excluded);

    if (propsFormatMessage) {
      return propsFormatMessage(_objectSpread({
        id: id,
        defaultMessage: defaultMessage
      }, restParams));
    }

    var locales = (0,_locales__WEBPACK_IMPORTED_MODULE_19__.default)();
    return locales[id] ? locales[id] : defaultMessage;
  }, [propsFormatMessage]);
  var swrKey = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    if (!(menu === null || menu === void 0 ? void 0 : menu.params)) return [defaultId];
    return [defaultId, menu === null || menu === void 0 ? void 0 : menu.params]; // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultId, (0,use_json_comparison__WEBPACK_IMPORTED_MODULE_11__.stringify)(menu === null || menu === void 0 ? void 0 : menu.params)]);
  var preData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(undefined);

  var _useSWR = (0,swr__WEBPACK_IMPORTED_MODULE_9__.default)(swrKey, /*#__PURE__*/function () {
    var _ref3 = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee(_, params) {
      var _menu$request;

      var msg;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              setMenuLoading(true);
              _context.next = 3;
              return menu === null || menu === void 0 ? void 0 : (_menu$request = menu.request) === null || _menu$request === void 0 ? void 0 : _menu$request.call(menu, params || {}, (route === null || route === void 0 ? void 0 : route.routes) || []);

            case 3:
              msg = _context.sent;
              setMenuLoading(false);
              return _context.abrupt("return", msg);

            case 6:
            case "end":
              return _context.stop();
          }
        }
      }, _callee);
    }));

    return function (_x, _x2) {
      return _ref3.apply(this, arguments);
    };
  }(), {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
    revalidateOnReconnect: false
  }),
      data = _useSWR.data;

  preData.current = data; // params 更新的时候重新请求

  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!preData.current) {
      return;
    }

    (0,swr__WEBPACK_IMPORTED_MODULE_9__.mutate)(swrKey); // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [swrKey]);
  var menuInfoData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return (0,_utils_getMenuData__WEBPACK_IMPORTED_MODULE_20__.default)(data || (route === null || route === void 0 ? void 0 : route.routes) || [], menu, formatMessage, menuDataRender);
  }, [formatMessage, menu, menuDataRender, data, route === null || route === void 0 ? void 0 : route.routes]);

  var _ref4 = menuInfoData || {},
      _ref4$breadcrumb = _ref4.breadcrumb,
      breadcrumb = _ref4$breadcrumb === void 0 ? {} : _ref4$breadcrumb,
      breadcrumbMap = _ref4.breadcrumbMap,
      _ref4$menuData = _ref4.menuData,
      menuData = _ref4$menuData === void 0 ? [] : _ref4$menuData;

  if (actionRef && (menu === null || menu === void 0 ? void 0 : menu.request)) {
    actionRef.current = {
      reload: function reload() {
        (0,swr__WEBPACK_IMPORTED_MODULE_9__.mutate)(swrKey);
      }
    };
  }

  var matchMenus = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return (0,_umijs_route_utils__WEBPACK_IMPORTED_MODULE_10__.getMatchMenu)(location.pathname || '/', menuData || [], true);
  }, [location.pathname, menuData]);
  var matchMenuKeys = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return Array.from(new Set(matchMenus.map(function (item) {
      return item.key || item.path || '';
    })));
  }, [matchMenus]); // 当前选中的menu，一般不会为空

  var currentMenu = matchMenus[matchMenus.length - 1] || {};
  var currentMenuLayoutProps = (0,_utils_useCurrentMenuLayoutProps__WEBPACK_IMPORTED_MODULE_21__.default)(currentMenu);

  var _props$currentMenuLay = _objectSpread(_objectSpread({}, props), currentMenuLayoutProps),
      fixSiderbar = _props$currentMenuLay.fixSiderbar,
      navTheme = _props$currentMenuLay.navTheme,
      defaultPropsLayout = _props$currentMenuLay.layout,
      rest = _objectWithoutProperties(_props$currentMenuLay, _excluded2);

  var propsLayout = (0,_utils_compatibleLayout__WEBPACK_IMPORTED_MODULE_22__.default)(defaultPropsLayout);
  var colSize = (0,use_media_antd_query__WEBPACK_IMPORTED_MODULE_7__.default)();
  var isMobile = (colSize === 'sm' || colSize === 'xs') && !props.disableMobile; // If it is a fix menu, calculate padding
  // don't need padding in phone mode

  var hasLeftPadding = propsLayout !== 'top' && !isMobile;

  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_6__.default)(function () {
    return defaultCollapsed || false;
  }, {
    value: props.collapsed,
    onChange: propsOnCollapse
  }),
      _useMergedState2 = _slicedToArray(_useMergedState, 2),
      collapsed = _useMergedState2[0],
      onCollapse = _useMergedState2[1]; // Splicing parameters, adding menuData and formatMessage in props


  var defaultProps = (0,omit_js__WEBPACK_IMPORTED_MODULE_8__.default)(_objectSpread(_objectSpread(_objectSpread({
    prefixCls: prefixCls
  }, props), {}, {
    siderWidth: siderWidth
  }, currentMenuLayoutProps), {}, {
    formatMessage: formatMessage,
    breadcrumb: breadcrumb,
    menu: _objectSpread(_objectSpread({}, menu), {}, {
      loading: menuLoading
    }),
    layout: propsLayout
  }), ['className', 'style', 'breadcrumbRender']); // gen page title

  var pageTitleInfo = defaultPageTitleRender(_objectSpread(_objectSpread({
    pathname: location.pathname
  }, defaultProps), {}, {
    breadcrumbMap: breadcrumbMap
  }), props); // gen breadcrumbProps, parameter for pageHeader

  var breadcrumbProps = (0,_utils_getBreadcrumbProps__WEBPACK_IMPORTED_MODULE_23__.getBreadcrumbProps)(_objectSpread(_objectSpread({}, defaultProps), {}, {
    breadcrumbRender: props.breadcrumbRender,
    breadcrumbMap: breadcrumbMap
  }), props); // render sider dom

  var siderMenuDom = renderSiderMenu(_objectSpread(_objectSpread({}, defaultProps), {}, {
    menuData: menuData,
    onCollapse: onCollapse,
    isMobile: isMobile,
    theme: (navTheme || 'dark').toLocaleLowerCase().includes('dark') ? 'dark' : 'light',
    collapsed: collapsed
  }), matchMenuKeys); // render header dom

  var headerDom = headerRender(_objectSpread(_objectSpread({}, defaultProps), {}, {
    hasSiderMenu: !!siderMenuDom,
    menuData: menuData,
    isMobile: isMobile,
    collapsed: collapsed,
    onCollapse: onCollapse,
    theme: (navTheme || 'dark').toLocaleLowerCase().includes('dark') ? 'dark' : 'light'
  }), matchMenuKeys); // render footer dom

  var footerDom = footerRender(_objectSpread({
    isMobile: isMobile,
    collapsed: collapsed
  }, defaultProps));

  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_RouteContext__WEBPACK_IMPORTED_MODULE_24__.default),
      contextIsChildrenLayout = _useContext.isChildrenLayout; // 如果 props 中定义，以 props 为准


  var isChildrenLayout = propsIsChildrenLayout !== undefined ? propsIsChildrenLayout : contextIsChildrenLayout;
  var baseClassName = "".concat(prefixCls, "-basicLayout"); // gen className

  var className = classnames__WEBPACK_IMPORTED_MODULE_4___default()(props.className, 'ant-design-pro', baseClassName, (_classNames = {}, _defineProperty(_classNames, "screen-".concat(colSize), colSize), _defineProperty(_classNames, "".concat(baseClassName, "-top-menu"), propsLayout === 'top'), _defineProperty(_classNames, "".concat(baseClassName, "-is-children"), isChildrenLayout), _defineProperty(_classNames, "".concat(baseClassName, "-fix-siderbar"), fixSiderbar), _defineProperty(_classNames, "".concat(baseClassName, "-").concat(propsLayout), propsLayout), _classNames));
  /** 计算 slider 的宽度 */

  var leftSiderWidth = getPaddingLeft(!!hasLeftPadding, collapsed, siderWidth); // siderMenuDom 为空的时候，不需要 padding

  var genLayoutStyle = {
    position: 'relative'
  }; // if is some layout children, don't need min height

  if (isChildrenLayout || contentStyle && contentStyle.minHeight) {
    genLayoutStyle.minHeight = 0;
  }

  var contentClassName = classnames__WEBPACK_IMPORTED_MODULE_4___default()("".concat(baseClassName, "-content"), (_classNames2 = {}, _defineProperty(_classNames2, "".concat(baseClassName, "-has-header"), headerDom), _defineProperty(_classNames2, "".concat(baseClassName, "-content-disable-margin"), disableContentMargin), _classNames2));
  /** 页面切换的时候触发 */

  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    var _props$onPageChange;

    (_props$onPageChange = props.onPageChange) === null || _props$onPageChange === void 0 ? void 0 : _props$onPageChange.call(props, props.location); // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname, (_location$pathname = location.pathname) === null || _location$pathname === void 0 ? void 0 : _location$pathname.search]);

  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
      _useState4 = _slicedToArray(_useState3, 2),
      hasFooterToolbar = _useState4[0],
      setHasFooterToolbar = _useState4[1];

  (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_25__.default)(pageTitleInfo, props.title || false);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_components_SiderMenu_Counter__WEBPACK_IMPORTED_MODULE_26__.default.Provider, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_RouteContext__WEBPACK_IMPORTED_MODULE_24__.default.Provider, {
    value: _objectSpread(_objectSpread({}, defaultProps), {}, {
      breadcrumb: breadcrumbProps,
      menuData: menuData,
      isMobile: isMobile,
      collapsed: collapsed,
      isChildrenLayout: true,
      title: pageTitleInfo.pageName,
      hasSiderMenu: !!siderMenuDom,
      hasHeader: !!headerDom,
      siderWidth: leftSiderWidth,
      hasFooter: !!footerDom,
      hasFooterToolbar: hasFooterToolbar,
      setHasFooterToolbar: setHasFooterToolbar,
      pageTitleInfo: pageTitleInfo,
      matchMenus: matchMenus,
      matchMenuKeys: matchMenuKeys,
      currentMenu: currentMenu
    })
  }, props.pure ? children : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: className
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_layout__WEBPACK_IMPORTED_MODULE_27__.default, {
    style: _objectSpread({
      minHeight: '100%'
    }, style)
  }, siderMenuDom, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: genLayoutStyle,
    className: context.getPrefixCls('layout')
  }, headerDom, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_WrapContent__WEBPACK_IMPORTED_MODULE_28__.default, _extends({
    isChildrenLayout: isChildrenLayout
  }, rest, {
    className: contentClassName,
    style: contentStyle
  }), loading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_components_PageLoading__WEBPACK_IMPORTED_MODULE_29__.default, null) : children), footerDom)))));
};

var Logo = function Logo() {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("svg", {
    width: "32px",
    height: "32px",
    viewBox: "0 0 200 200"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("defs", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("linearGradient", {
    x1: "62.1023273%",
    y1: "0%",
    x2: "108.19718%",
    y2: "37.8635764%",
    id: "linearGradient-1"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#4285EB",
    offset: "0%"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#2EC7FF",
    offset: "100%"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("linearGradient", {
    x1: "69.644116%",
    y1: "0%",
    x2: "54.0428975%",
    y2: "108.456714%",
    id: "linearGradient-2"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#29CDFF",
    offset: "0%"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#148EFF",
    offset: "37.8600687%"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#0A60FF",
    offset: "100%"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("linearGradient", {
    x1: "69.6908165%",
    y1: "-12.9743587%",
    x2: "16.7228981%",
    y2: "117.391248%",
    id: "linearGradient-3"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#FA816E",
    offset: "0%"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#F74A5C",
    offset: "41.472606%"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#F51D2C",
    offset: "100%"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("linearGradient", {
    x1: "68.1279872%",
    y1: "-35.6905737%",
    x2: "30.4400914%",
    y2: "114.942679%",
    id: "linearGradient-4"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#FA8E7D",
    offset: "0%"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#F74A5C",
    offset: "51.2635191%"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("stop", {
    stopColor: "#F51D2C",
    offset: "100%"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("g", {
    stroke: "none",
    strokeWidth: 1,
    fill: "none",
    fillRule: "evenodd"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("g", {
    transform: "translate(-20.000000, -20.000000)"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("g", {
    transform: "translate(20.000000, 20.000000)"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("g", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("g", {
    fillRule: "nonzero"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("g", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("path", {
    d: "M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",
    fill: "url(#linearGradient-1)"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("path", {
    d: "M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",
    fill: "url(#linearGradient-2)"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("path", {
    d: "M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",
    fill: "url(#linearGradient-3)"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("ellipse", {
    fill: "url(#linearGradient-4)",
    cx: "100.519339",
    cy: "100.436681",
    rx: "23.6001926",
    ry: "23.580786"
  }))))));
};

BasicLayout.defaultProps = _objectSpread(_objectSpread({
  logo: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Logo, null)
}, _defaultSettings__WEBPACK_IMPORTED_MODULE_30__.default), {}, {
  location: (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_31__.default)() ? window.location : undefined
});
/* harmony default export */ __webpack_exports__["default"] = (BasicLayout);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/Footer.js":
/*!**********************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/Footer.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_layout_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/layout/style */ "./node_modules/antd/es/layout/style/index.js");
/* harmony import */ var antd_es_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/es/layout */ "./node_modules/antd/es/layout/index.js");
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/GithubOutlined.js");
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/CopyrightOutlined.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _components_GlobalFooter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/GlobalFooter */ "./node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.js");



function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }




var Footer = antd_es_layout__WEBPACK_IMPORTED_MODULE_2__.default.Footer;
var defaultLinks = [{
  key: 'Ant Design Pro',
  title: 'Ant Design Pro',
  href: 'https://pro.ant.design',
  blankTarget: true
}, {
  key: 'github',
  title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__.default, null),
  href: 'https://github.com/ant-design/ant-design-pro',
  blankTarget: true
}, {
  key: 'Ant Design',
  title: 'Ant Design',
  href: 'https://ant.design',
  blankTarget: true
}];
var defaultCopyright = '2019 蚂蚁金服体验技术部出品';

var FooterView = function FooterView(_ref) {
  var links = _ref.links,
      copyright = _ref.copyright,
      style = _ref.style,
      className = _ref.className,
      prefixCls = _ref.prefixCls;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Footer, {
    className: className,
    style: _objectSpread({
      padding: 0
    }, style)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_GlobalFooter__WEBPACK_IMPORTED_MODULE_4__.default, {
    links: links !== undefined ? links : defaultLinks,
    prefixCls: prefixCls,
    copyright: copyright === false ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__.default, null), " ", copyright || defaultCopyright)
  }));
};

/* harmony default export */ __webpack_exports__["default"] = (FooterView);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/Header.js":
/*!**********************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/Header.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_layout_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/layout/style */ "./node_modules/antd/es/layout/style/index.js");
/* harmony import */ var antd_es_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! antd/es/layout */ "./node_modules/antd/es/layout/index.js");
/* harmony import */ var _Header_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header.less */ "./node_modules/@ant-design/pro-layout/es/Header.less");
/* harmony import */ var _Header_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Header_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _components_GlobalHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/GlobalHeader */ "./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.js");
/* harmony import */ var _components_TopNavHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/TopNavHeader */ "./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js");
/* harmony import */ var _utils_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/utils */ "./node_modules/@ant-design/pro-layout/es/utils/utils.js");
function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }




function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }

function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }

function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }

function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }

function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }

function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }

function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }







var Header = antd_es_layout__WEBPACK_IMPORTED_MODULE_4__.default.Header;

var HeaderView = /*#__PURE__*/function (_Component) {
  _inherits(HeaderView, _Component);

  var _super = _createSuper(HeaderView);

  function HeaderView() {
    var _this;

    _classCallCheck(this, HeaderView);

    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }

    _this = _super.call.apply(_super, [this].concat(args));

    _this.renderContent = function () {
      var _this$props = _this.props,
          isMobile = _this$props.isMobile,
          onCollapse = _this$props.onCollapse,
          navTheme = _this$props.navTheme,
          layout = _this$props.layout,
          headerRender = _this$props.headerRender,
          headerContentRender = _this$props.headerContentRender;
      var isTop = layout === 'top';
      var clearMenuData = (0,_utils_utils__WEBPACK_IMPORTED_MODULE_5__.clearMenuItem)(_this.props.menuData || []);
      var defaultDom = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_GlobalHeader__WEBPACK_IMPORTED_MODULE_6__.default, _extends({
        onCollapse: onCollapse
      }, _this.props, {
        menuData: clearMenuData
      }), headerContentRender && headerContentRender(_this.props));

      if (isTop && !isMobile) {
        defaultDom = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_TopNavHeader__WEBPACK_IMPORTED_MODULE_7__.default, _extends({
          theme: navTheme,
          mode: "horizontal",
          onCollapse: onCollapse
        }, _this.props, {
          menuData: clearMenuData
        }));
      }

      if (headerRender && typeof headerRender === 'function') {
        return headerRender(_this.props, defaultDom);
      }

      return defaultDom;
    };

    return _this;
  }

  _createClass(HeaderView, [{
    key: "render",
    value: function render() {
      var _classNames;

      var _this$props2 = this.props,
          fixedHeader = _this$props2.fixedHeader,
          layout = _this$props2.layout,
          propsClassName = _this$props2.className,
          style = _this$props2.style,
          navTheme = _this$props2.navTheme,
          collapsed = _this$props2.collapsed,
          siderWidth = _this$props2.siderWidth,
          hasSiderMenu = _this$props2.hasSiderMenu,
          isMobile = _this$props2.isMobile,
          prefixCls = _this$props2.prefixCls,
          headerHeight = _this$props2.headerHeight;
      var needFixedHeader = fixedHeader || layout === 'mix';
      var isTop = layout === 'top';
      var needSettingWidth = needFixedHeader && hasSiderMenu && !isTop && !isMobile;
      var className = classnames__WEBPACK_IMPORTED_MODULE_3___default()(propsClassName, (_classNames = {}, _defineProperty(_classNames, "".concat(prefixCls, "-fixed-header"), needFixedHeader), _defineProperty(_classNames, "".concat(prefixCls, "-fixed-header-action"), !collapsed), _defineProperty(_classNames, "".concat(prefixCls, "-top-menu"), isTop), _defineProperty(_classNames, "".concat(prefixCls, "-header-").concat(navTheme), navTheme && layout !== 'mix'), _classNames));
      /** 计算侧边栏的宽度，不然导致左边的样式会出问题 */

      var width = layout !== 'mix' && needSettingWidth ? "calc(100% - ".concat(collapsed ? 48 : siderWidth, "px)") : '100%';
      var right = needFixedHeader ? 0 : undefined;
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, needFixedHeader && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Header, {
        style: {
          height: headerHeight,
          lineHeight: "".concat(headerHeight, "px"),
          background: 'transparent'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Header, {
        style: _objectSpread({
          padding: 0,
          height: headerHeight,
          lineHeight: "".concat(headerHeight, "px"),
          width: width,
          zIndex: layout === 'mix' ? 100 : 19,
          right: right
        }, style),
        className: className
      }, this.renderContent()));
    }
  }]);

  return HeaderView;
}(react__WEBPACK_IMPORTED_MODULE_2__.Component);

/* harmony default export */ __webpack_exports__["default"] = (HeaderView);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/RouteContext.js":
/*!****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/RouteContext.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

var routeContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});
/* harmony default export */ __webpack_exports__["default"] = (routeContext);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/WrapContent.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/WrapContent.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_layout_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/layout/style */ "./node_modules/antd/es/layout/style/index.js");
/* harmony import */ var antd_es_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! antd/es/layout */ "./node_modules/antd/es/layout/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _ant_design_pro_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ant-design/pro-provider */ "./node_modules/@ant-design/pro-provider/es/index.js");
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/components/ErrorBoundary/index.js");






var WrapContent = function WrapContent(props) {
  var style = props.style,
      className = props.className,
      children = props.children;
  var ErrorComponent = props.ErrorBoundary || _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_3__.default;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_pro_provider__WEBPACK_IMPORTED_MODULE_2__.ConfigProviderWrap, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ErrorComponent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd_es_layout__WEBPACK_IMPORTED_MODULE_4__.default.Content, {
    className: className,
    style: style
  }, children)));
};

/* harmony default export */ __webpack_exports__["default"] = (WrapContent);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_config_provider_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/config-provider/style */ "./node_modules/antd/es/config-provider/style/index.js");
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! antd/es/config-provider */ "./node_modules/antd/es/config-provider/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var omit_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! omit.js */ "./node_modules/omit.js/es/index.js");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.less */ "./node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../index */ "./node_modules/@ant-design/pro-layout/es/RouteContext.js");


var _excluded = ["children", "className", "extra", "style", "renderContent"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }







var FooterToolbar = function FooterToolbar(props) {
  var children = props.children,
      className = props.className,
      extra = props.extra,
      style = props.style,
      renderContent = props.renderContent,
      restProps = _objectWithoutProperties(props, _excluded);

  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(antd_es_config_provider__WEBPACK_IMPORTED_MODULE_5__.default.ConfigContext),
      getPrefixCls = _useContext.getPrefixCls;

  var prefixCls = props.prefixCls || getPrefixCls('pro');
  var baseClassName = "".concat(prefixCls, "-footer-bar");
  var value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_index__WEBPACK_IMPORTED_MODULE_6__.default);
  var width = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
    var hasSiderMenu = value.hasSiderMenu,
        isMobile = value.isMobile,
        siderWidth = value.siderWidth;

    if (!hasSiderMenu) {
      return undefined;
    } // 0 or undefined


    if (!siderWidth) {
      return '100%';
    }

    return isMobile ? '100%' : "calc(100% - ".concat(siderWidth, "px)"); // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value.collapsed, value.hasSiderMenu, value.isMobile, value.siderWidth]);
  var dom = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: "".concat(baseClassName, "-left")
  }, extra), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: "".concat(baseClassName, "-right")
  }, children));
  /** 告诉 props 是否存在 footerBar */

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (!value || !(value === null || value === void 0 ? void 0 : value.setHasFooterToolbar)) {
      return function () {};
    }

    value === null || value === void 0 ? void 0 : value.setHasFooterToolbar(true);
    return function () {
      var _value$setHasFooterTo;

      value === null || value === void 0 ? void 0 : (_value$setHasFooterTo = value.setHasFooterToolbar) === null || _value$setHasFooterTo === void 0 ? void 0 : _value$setHasFooterTo.call(value, false);
    }; // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", _extends({
    className: classnames__WEBPACK_IMPORTED_MODULE_2___default()(className, "".concat(baseClassName)),
    style: _objectSpread({
      width: width
    }, style)
  }, (0,omit_js__WEBPACK_IMPORTED_MODULE_3__.default)(restProps, ['prefixCls'])), renderContent ? renderContent(_objectSpread(_objectSpread(_objectSpread({}, props), value), {}, {
    leftWidth: width
  }), dom) : dom);
};

/* harmony default export */ __webpack_exports__["default"] = (FooterToolbar);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_config_provider_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/config-provider/style */ "./node_modules/antd/es/config-provider/style/index.js");
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! antd/es/config-provider */ "./node_modules/antd/es/config-provider/index.js");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);





/* harmony default export */ __webpack_exports__["default"] = (function (_ref) {
  var className = _ref.className,
      prefixCls = _ref.prefixCls,
      links = _ref.links,
      copyright = _ref.copyright,
      style = _ref.style;
  var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(antd_es_config_provider__WEBPACK_IMPORTED_MODULE_4__.default.ConfigContext);
  var baseClassName = context.getPrefixCls(prefixCls || 'pro-global-footer');

  if ((links == null || links === false || Array.isArray(links) && links.length === 0) && (copyright == null || copyright === false)) {
    return null;
  }

  var clsString = classnames__WEBPACK_IMPORTED_MODULE_3___default()(baseClassName, className);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: clsString,
    style: style
  }, links && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(baseClassName, "-links")
  }, links.map(function (link) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("a", {
      key: link.key,
      title: link.key,
      target: link.blankTarget ? '_blank' : '_self',
      href: link.href
    }, link.title);
  })), copyright && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(baseClassName, "-copyright")
  }, copyright));
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.less */ "./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _SiderMenu_SiderMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../SiderMenu/SiderMenu */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js");
/* harmony import */ var _TopNavHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../TopNavHeader */ "./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js");
/* harmony import */ var _utils_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/utils */ "./node_modules/@ant-design/pro-layout/es/utils/utils.js");
function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }








var renderLogo = function renderLogo(menuHeaderRender, logoDom) {
  if (menuHeaderRender === false) {
    return null;
  }

  if (menuHeaderRender) {
    return menuHeaderRender(logoDom, null);
  }

  return logoDom;
};

var GlobalHeader = function GlobalHeader(props) {
  var isMobile = props.isMobile,
      logo = props.logo,
      collapsed = props.collapsed,
      onCollapse = props.onCollapse,
      _props$collapsedButto = props.collapsedButtonRender,
      collapsedButtonRender = _props$collapsedButto === void 0 ? _SiderMenu_SiderMenu__WEBPACK_IMPORTED_MODULE_3__.defaultRenderCollapsedButton : _props$collapsedButto,
      rightContentRender = props.rightContentRender,
      menuHeaderRender = props.menuHeaderRender,
      onMenuHeaderClick = props.onMenuHeaderClick,
      propClassName = props.className,
      style = props.style,
      layout = props.layout,
      children = props.children,
      _props$headerTheme = props.headerTheme,
      headerTheme = _props$headerTheme === void 0 ? 'dark' : _props$headerTheme,
      splitMenus = props.splitMenus,
      menuData = props.menuData,
      prefixCls = props.prefixCls;
  var baseClassName = "".concat(prefixCls, "-global-header");
  var className = classnames__WEBPACK_IMPORTED_MODULE_2___default()(propClassName, baseClassName, _defineProperty({}, "".concat(baseClassName, "-layout-").concat(layout), layout && headerTheme === 'dark'));

  if (layout === 'mix' && !isMobile && splitMenus) {
    var noChildrenMenuData = (menuData || []).map(function (item) {
      return _objectSpread(_objectSpread({}, item), {}, {
        children: undefined,
        routes: undefined
      });
    });
    var clearMenuData = (0,_utils_utils__WEBPACK_IMPORTED_MODULE_4__.clearMenuItem)(noChildrenMenuData);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_TopNavHeader__WEBPACK_IMPORTED_MODULE_5__.default, _extends({
      mode: "horizontal"
    }, props, {
      splitMenus: false,
      menuData: clearMenuData,
      theme: headerTheme
    }));
  }

  var logoDom = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("span", {
    className: "".concat(baseClassName, "-logo"),
    key: "logo"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("a", null, (0,_SiderMenu_SiderMenu__WEBPACK_IMPORTED_MODULE_3__.defaultRenderLogo)(logo)));
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: className,
    style: _objectSpread({}, style)
  }, isMobile && renderLogo(menuHeaderRender, logoDom), isMobile && collapsedButtonRender && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("span", {
    className: "".concat(baseClassName, "-collapsed-button"),
    onClick: function onClick() {
      if (onCollapse) {
        onCollapse(!collapsed);
      }
    }
  }, collapsedButtonRender(collapsed)), layout === 'mix' && !isMobile && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: "".concat(baseClassName, "-logo"),
    onClick: onMenuHeaderClick
  }, (0,_SiderMenu_SiderMenu__WEBPACK_IMPORTED_MODULE_3__.defaultRenderLogoAndTitle)(_objectSpread(_objectSpread({}, props), {}, {
    collapsed: false
  }), 'headerTitleRender'))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      flex: 1
    }
  }, children), rightContentRender && rightContentRender(props));
};

/* harmony default export */ __webpack_exports__["default"] = (GlobalHeader);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/GridContent/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/GridContent/index.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_config_provider_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/config-provider/style */ "./node_modules/antd/es/config-provider/style/index.js");
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! antd/es/config-provider */ "./node_modules/antd/es/config-provider/index.js");
/* harmony import */ var _GridContent_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GridContent.less */ "./node_modules/@ant-design/pro-layout/es/components/GridContent/GridContent.less");
/* harmony import */ var _GridContent_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_GridContent_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _RouteContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../RouteContext */ "./node_modules/@ant-design/pro-layout/es/RouteContext.js");






/**
 * This component can support contentWidth so you don't need to calculate the width
 * contentWidth=Fixed, width will is 1200
 *
 * @param props
 */

var GridContent = function GridContent(props) {
  var value = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_RouteContext__WEBPACK_IMPORTED_MODULE_4__.default);
  var children = props.children,
      propsContentWidth = props.contentWidth,
      propsClassName = props.className,
      style = props.style;

  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(antd_es_config_provider__WEBPACK_IMPORTED_MODULE_5__.default.ConfigContext),
      getPrefixCls = _useContext.getPrefixCls;

  var prefixCls = props.prefixCls || getPrefixCls('pro');
  var contentWidth = propsContentWidth || value.contentWidth;
  var className = "".concat(prefixCls, "-grid-content");
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(className, propsClassName, {
      wide: contentWidth === 'Fixed'
    }),
    style: style
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(prefixCls, "-grid-content-children")
  }, children));
};

/* harmony default export */ __webpack_exports__["default"] = (GridContent);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ProPageHeader": function() { return /* binding */ ProPageHeader; },
/* harmony export */   "ProBreadcrumb": function() { return /* binding */ ProBreadcrumb; }
/* harmony export */ });
/* harmony import */ var antd_es_affix_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/affix/style */ "./node_modules/antd/es/affix/style/index.js");
/* harmony import */ var antd_es_affix__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! antd/es/affix */ "./node_modules/antd/es/affix/index.js");
/* harmony import */ var antd_es_config_provider_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! antd/es/config-provider/style */ "./node_modules/antd/es/config-provider/style/index.js");
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! antd/es/config-provider */ "./node_modules/antd/es/config-provider/index.js");
/* harmony import */ var antd_es_page_header_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/es/page-header/style */ "./node_modules/antd/es/page-header/style/index.js");
/* harmony import */ var antd_es_page_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! antd/es/page-header */ "./node_modules/antd/es/page-header/index.js");
/* harmony import */ var antd_es_breadcrumb_style__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/es/breadcrumb/style */ "./node_modules/antd/es/breadcrumb/style/index.js");
/* harmony import */ var antd_es_breadcrumb__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! antd/es/breadcrumb */ "./node_modules/antd/es/breadcrumb/index.js");
/* harmony import */ var antd_es_tabs_style__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! antd/es/tabs/style */ "./node_modules/antd/es/tabs/style/index.js");
/* harmony import */ var antd_es_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! antd/es/tabs */ "./node_modules/antd/es/tabs/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _RouteContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../RouteContext */ "./node_modules/@ant-design/pro-layout/es/RouteContext.js");
/* harmony import */ var _GridContent__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../GridContent */ "./node_modules/@ant-design/pro-layout/es/components/GridContent/index.js");
/* harmony import */ var _FooterToolbar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../FooterToolbar */ "./node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.js");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./index.less */ "./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _PageLoading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../PageLoading */ "./node_modules/@ant-design/pro-layout/es/components/PageLoading/index.js");
/* harmony import */ var _WaterMark__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../WaterMark */ "./node_modules/@ant-design/pro-layout/es/components/WaterMark/index.js");










var _excluded = ["title", "content", "pageHeaderRender", "header", "prefixedClassName", "extraContent", "style", "prefixCls", "breadcrumbRender"],
    _excluded2 = ["children", "loading", "className", "style", "footer", "affixProps", "ghost", "fixedHeader"];

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }










function genLoading(spinProps) {
  if (_typeof(spinProps) === 'object') {
    return spinProps;
  }

  return {
    spinning: spinProps
  };
}
/**
 * Render Footer tabList In order to be compatible with the old version of the PageHeader basically
 * all the functions are implemented.
 */


var renderFooter = function renderFooter(_ref) {
  var tabList = _ref.tabList,
      tabActiveKey = _ref.tabActiveKey,
      onTabChange = _ref.onTabChange,
      tabBarExtraContent = _ref.tabBarExtraContent,
      tabProps = _ref.tabProps,
      prefixedClassName = _ref.prefixedClassName;

  if (tabList && tabList.length || tabBarExtraContent) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd_es_tabs__WEBPACK_IMPORTED_MODULE_8__.default, _extends({
      className: "".concat(prefixedClassName, "-tabs"),
      activeKey: tabActiveKey,
      onChange: function onChange(key) {
        if (onTabChange) {
          onTabChange(key);
        }
      },
      tabBarExtraContent: tabBarExtraContent
    }, tabProps), tabList === null || tabList === void 0 ? void 0 : tabList.map(function (item, index) {
      return (
        /*#__PURE__*/
        // eslint-disable-next-line react/no-array-index-key
        react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd_es_tabs__WEBPACK_IMPORTED_MODULE_8__.default.TabPane, _extends({}, item, {
          tab: item.tab,
          key: item.key || index
        }))
      );
    }));
  }

  return null;
};

var renderPageHeader = function renderPageHeader(content, extraContent, prefixedClassName) {
  if (!content && !extraContent) {
    return null;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "".concat(prefixedClassName, "-detail")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "".concat(prefixedClassName, "-main")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "".concat(prefixedClassName, "-row")
  }, content && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "".concat(prefixedClassName, "-content")
  }, content), extraContent && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "".concat(prefixedClassName, "-extraContent")
  }, extraContent))));
};
/**
 * 配置与面包屑相同，只是增加了自动根据路由计算面包屑的功能。此功能必须要在 ProLayout 中使用。
 *
 * @param props
 * @returns
 */


var ProBreadcrumb = function ProBreadcrumb(props) {
  var value = (0,react__WEBPACK_IMPORTED_MODULE_5__.useContext)(_RouteContext__WEBPACK_IMPORTED_MODULE_9__.default);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      height: '100%',
      display: 'flex',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd_es_breadcrumb__WEBPACK_IMPORTED_MODULE_10__.default, _extends({}, value === null || value === void 0 ? void 0 : value.breadcrumb, value === null || value === void 0 ? void 0 : value.breadcrumbProps, props)));
};

var ProPageHeader = function ProPageHeader(props) {
  var _breadcrumb$routes;

  var value = (0,react__WEBPACK_IMPORTED_MODULE_5__.useContext)(_RouteContext__WEBPACK_IMPORTED_MODULE_9__.default);

  var title = props.title,
      content = props.content,
      pageHeaderRender = props.pageHeaderRender,
      header = props.header,
      prefixedClassName = props.prefixedClassName,
      extraContent = props.extraContent,
      style = props.style,
      prefixCls = props.prefixCls,
      breadcrumbRender = props.breadcrumbRender,
      restProps = _objectWithoutProperties(props, _excluded);

  var getBreadcrumbRender = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    if (!breadcrumbRender) {
      return undefined;
    }

    return breadcrumbRender;
  }, [breadcrumbRender]);

  if (pageHeaderRender === false) {
    return null;
  }

  if (pageHeaderRender) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Fragment, null, " ", pageHeaderRender(_objectSpread(_objectSpread({}, props), value)));
  }

  var pageHeaderTitle = title;

  if (!title && title !== false) {
    pageHeaderTitle = value.title;
  }

  var pageHeaderProps = _objectSpread(_objectSpread(_objectSpread({}, value), {}, {
    title: pageHeaderTitle
  }, restProps), {}, {
    footer: renderFooter(_objectSpread(_objectSpread({}, restProps), {}, {
      breadcrumbRender: breadcrumbRender,
      prefixedClassName: prefixedClassName
    }))
  }, header);

  var breadcrumb = pageHeaderProps.breadcrumb;
  var noHasBreadCrumb = (!breadcrumb || !(breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.itemRender) && !(breadcrumb === null || breadcrumb === void 0 ? void 0 : (_breadcrumb$routes = breadcrumb.routes) === null || _breadcrumb$routes === void 0 ? void 0 : _breadcrumb$routes.length)) && !breadcrumbRender;

  if (['title', 'subTitle', 'extra', 'tags', 'footer', 'avatar', 'backIcon'].every(function (item) {
    return !pageHeaderProps[item];
  }) && noHasBreadCrumb && !content && !extraContent) {
    return null;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "".concat(prefixedClassName, "-warp")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd_es_page_header__WEBPACK_IMPORTED_MODULE_11__.default, _extends({}, pageHeaderProps, {
    breadcrumb: breadcrumbRender === false ? undefined : _objectSpread(_objectSpread({}, pageHeaderProps.breadcrumb), value.breadcrumbProps),
    breadcrumbRender: getBreadcrumbRender,
    prefixCls: prefixCls
  }), (header === null || header === void 0 ? void 0 : header.children) || renderPageHeader(content, extraContent, prefixedClassName)));
};

var PageContainer = function PageContainer(props) {
  var _classNames;

  var children = props.children,
      _props$loading = props.loading,
      loading = _props$loading === void 0 ? false : _props$loading,
      className = props.className,
      style = props.style,
      footer = props.footer,
      affixProps = props.affixProps,
      ghost = props.ghost,
      fixedHeader = props.fixedHeader,
      restProps = _objectWithoutProperties(props, _excluded2);

  var value = (0,react__WEBPACK_IMPORTED_MODULE_5__.useContext)(_RouteContext__WEBPACK_IMPORTED_MODULE_9__.default);

  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_5__.useContext)(antd_es_config_provider__WEBPACK_IMPORTED_MODULE_12__.default.ConfigContext),
      getPrefixCls = _useContext.getPrefixCls;

  var prefixCls = props.prefixCls || getPrefixCls('pro');
  var prefixedClassName = "".concat(prefixCls, "-page-container");
  var containerClassName = classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixedClassName, className, (_classNames = {}, _defineProperty(_classNames, "".concat(prefixCls, "-page-container-ghost"), ghost), _defineProperty(_classNames, "".concat(prefixCls, "-page-container-with-footer"), footer), _classNames));
  var content = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    return children ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      className: "".concat(prefixedClassName, "-children-content")
    }, children), value.hasFooterToolbar && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        height: 48,
        marginTop: 24
      }
    })) : null;
  }, [children, prefixedClassName, value.hasFooterToolbar]);
  var pageHeaderDom = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ProPageHeader, _extends({}, restProps, {
    ghost: ghost,
    prefixCls: undefined,
    prefixedClassName: prefixedClassName
  }));
  var loadingDom = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    // 当loading时一个合法的ReactNode时，说明用户使用了自定义loading,直接返回改自定义loading
    if ( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.isValidElement(loading)) {
      return loading;
    } // 当传递过来的是布尔值，并且为false时，说明不需要显示loading,返回null


    if (typeof loading === 'boolean' && !loading) {
      return null;
    } // 如非上述两种情况，那么要么用户传了一个true,要么用户传了loading配置，使用genLoading生成loading配置后返回PageLoading


    var spinProps = genLoading(loading);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_PageLoading__WEBPACK_IMPORTED_MODULE_13__.default, spinProps);
  }, [loading]);
  var renderContentDom = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    // 只要loadingDom非空我们就渲染loadingDom,否则渲染内容
    var dom = loadingDom || content;

    if (props.waterMarkProps || value.waterMarkProps) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_WaterMark__WEBPACK_IMPORTED_MODULE_14__.default, props.waterMarkProps || value.waterMarkProps, dom);
    }

    return dom;
  }, [props.waterMarkProps, value.waterMarkProps, loadingDom, content]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: style,
    className: containerClassName
  }, fixedHeader && pageHeaderDom ?
  /*#__PURE__*/
  // 在 hasHeader 且 fixedHeader 的情况下，才需要设置高度
  react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd_es_affix__WEBPACK_IMPORTED_MODULE_15__.default, _extends({
    offsetTop: value.hasHeader && value.fixedHeader ? value.headerHeight : 0
  }, affixProps), pageHeaderDom) : pageHeaderDom, renderContentDom && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_GridContent__WEBPACK_IMPORTED_MODULE_16__.default, null, renderContentDom), footer && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_FooterToolbar__WEBPACK_IMPORTED_MODULE_17__.default, {
    prefixCls: prefixCls
  }, footer));
};


/* harmony default export */ __webpack_exports__["default"] = (PageContainer);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/PageLoading/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/PageLoading/index.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_spin_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/spin/style */ "./node_modules/antd/es/spin/style/index.js");
/* harmony import */ var antd_es_spin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/es/spin */ "./node_modules/antd/es/spin/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


var _excluded = ["isLoading", "pastDelay", "timedOut", "error", "retry"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }



var PageLoading = function PageLoading(_ref) {
  var isLoading = _ref.isLoading,
      pastDelay = _ref.pastDelay,
      timedOut = _ref.timedOut,
      error = _ref.error,
      retry = _ref.retry,
      reset = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      paddingTop: 100,
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd_es_spin__WEBPACK_IMPORTED_MODULE_2__.default, _extends({
    size: "large"
  }, reset)));
};

/* harmony default export */ __webpack_exports__["default"] = (PageLoading);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/BlockCheckbox.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/BlockCheckbox.js ***!
  \******************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_tooltip_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/tooltip/style */ "./node_modules/antd/es/tooltip/style/index.js");
/* harmony import */ var antd_es_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/es/tooltip */ "./node_modules/antd/es/tooltip/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/CheckOutlined.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);



function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }





var BlockCheckbox = function BlockCheckbox(_ref) {
  var value = _ref.value,
      configType = _ref.configType,
      onChange = _ref.onChange,
      list = _ref.list,
      prefixCls = _ref.prefixCls;
  var baseClassName = "".concat(prefixCls, "-drawer-block-checkbox");

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]),
      _useState2 = _slicedToArray(_useState, 2),
      dom = _useState2[0],
      setDom = _useState2[1];

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var domList = (list || []).map(function (item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd_es_tooltip__WEBPACK_IMPORTED_MODULE_3__.default, {
        title: item.title,
        key: item.key
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
        className: classnames__WEBPACK_IMPORTED_MODULE_2___default()("".concat(baseClassName, "-item"), "".concat(baseClassName, "-item-").concat(item.key), "".concat(baseClassName, "-").concat(configType, "-item")),
        onClick: function onClick() {
          return onChange(item.key);
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__.default, {
        className: "".concat(baseClassName, "-selectIcon"),
        style: {
          display: value === item.key ? 'block' : 'none'
        }
      })));
    });
    setDom(domList); // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, list === null || list === void 0 ? void 0 : list.length]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: baseClassName,
    style: {
      minHeight: 42
    }
  }, dom);
};

/* harmony default export */ __webpack_exports__["default"] = (BlockCheckbox);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/LayoutChange.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/LayoutChange.js ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "renderLayoutSettingItem": function() { return /* binding */ renderLayoutSettingItem; }
/* harmony export */ });
/* harmony import */ var antd_es_switch_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/switch/style */ "./node_modules/antd/es/switch/style/index.js");
/* harmony import */ var antd_es_switch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! antd/es/switch */ "./node_modules/antd/es/switch/index.js");
/* harmony import */ var antd_es_select_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! antd/es/select/style */ "./node_modules/antd/es/select/style/index.js");
/* harmony import */ var antd_es_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! antd/es/select */ "./node_modules/antd/es/select/index.js");
/* harmony import */ var antd_es_tooltip_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/es/tooltip/style */ "./node_modules/antd/es/tooltip/style/index.js");
/* harmony import */ var antd_es_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! antd/es/tooltip */ "./node_modules/antd/es/tooltip/index.js");
/* harmony import */ var antd_es_list_style__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/es/list/style */ "./node_modules/antd/es/list/style/index.js");
/* harmony import */ var antd_es_list__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! antd/es/list */ "./node_modules/antd/es/list/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _defaultSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../defaultSettings */ "./node_modules/@ant-design/pro-layout/es/defaultSettings.js");
/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./index */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.js");











var renderLayoutSettingItem = function renderLayoutSettingItem(item) {
  var action = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.cloneElement(item.action, {
    disabled: item.disabled
  });
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_tooltip__WEBPACK_IMPORTED_MODULE_5__.default, {
    title: item.disabled ? item.disabledReason : '',
    placement: "left"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_list__WEBPACK_IMPORTED_MODULE_6__.default.Item, {
    actions: [action]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
    style: {
      opacity: item.disabled ? 0.5 : 1
    }
  }, item.title)));
};

var LayoutSetting = function LayoutSetting(_ref) {
  var _ref$settings = _ref.settings,
      settings = _ref$settings === void 0 ? {} : _ref$settings,
      changeSetting = _ref.changeSetting;
  var formatMessage = (0,_index__WEBPACK_IMPORTED_MODULE_7__.getFormatMessage)();

  var _ref2 = settings || _defaultSettings__WEBPACK_IMPORTED_MODULE_8__.default,
      contentWidth = _ref2.contentWidth,
      splitMenus = _ref2.splitMenus,
      fixedHeader = _ref2.fixedHeader,
      layout = _ref2.layout,
      fixSiderbar = _ref2.fixSiderbar;

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_list__WEBPACK_IMPORTED_MODULE_6__.default, {
    split: false,
    dataSource: [{
      title: formatMessage({
        id: 'app.setting.content-width',
        defaultMessage: 'Content Width'
      }),
      action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_select__WEBPACK_IMPORTED_MODULE_9__.default, {
        value: contentWidth || 'Fixed',
        size: "small",
        className: "content-width",
        onSelect: function onSelect(value) {
          changeSetting('contentWidth', value);
        },
        style: {
          width: 80
        }
      }, layout === 'side' ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_select__WEBPACK_IMPORTED_MODULE_9__.default.Option, {
        value: "Fixed"
      }, formatMessage({
        id: 'app.setting.content-width.fixed',
        defaultMessage: 'Fixed'
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_select__WEBPACK_IMPORTED_MODULE_9__.default.Option, {
        value: "Fluid"
      }, formatMessage({
        id: 'app.setting.content-width.fluid',
        defaultMessage: 'Fluid'
      })))
    }, {
      title: formatMessage({
        id: 'app.setting.fixedheader',
        defaultMessage: 'Fixed Header'
      }),
      action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_switch__WEBPACK_IMPORTED_MODULE_10__.default, {
        size: "small",
        className: "fixed-header",
        checked: !!fixedHeader,
        onChange: function onChange(checked) {
          changeSetting('fixedHeader', checked);
        }
      })
    }, {
      title: formatMessage({
        id: 'app.setting.fixedsidebar',
        defaultMessage: 'Fixed Sidebar'
      }),
      disabled: layout === 'top',
      disabledReason: formatMessage({
        id: 'app.setting.fixedsidebar.hint',
        defaultMessage: 'Works on Side Menu Layout'
      }),
      action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_switch__WEBPACK_IMPORTED_MODULE_10__.default, {
        size: "small",
        className: "fix-siderbar",
        checked: !!fixSiderbar,
        onChange: function onChange(checked) {
          return changeSetting('fixSiderbar', checked);
        }
      })
    }, {
      title: formatMessage({
        id: 'app.setting.splitMenus'
      }),
      disabled: layout !== 'mix',
      action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd_es_switch__WEBPACK_IMPORTED_MODULE_10__.default, {
        size: "small",
        checked: !!splitMenus,
        className: "split-menus",
        onChange: function onChange(checked) {
          changeSetting('splitMenus', checked);
        }
      })
    }],
    renderItem: renderLayoutSettingItem
  });
};

/* harmony default export */ __webpack_exports__["default"] = (LayoutSetting);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/RegionalChange.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/RegionalChange.js ***!
  \*******************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_list_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/list/style */ "./node_modules/antd/es/list/style/index.js");
/* harmony import */ var antd_es_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! antd/es/list */ "./node_modules/antd/es/list/index.js");
/* harmony import */ var antd_es_switch_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! antd/es/switch/style */ "./node_modules/antd/es/switch/style/index.js");
/* harmony import */ var antd_es_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! antd/es/switch */ "./node_modules/antd/es/switch/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.js");
/* harmony import */ var _LayoutChange__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./LayoutChange */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/LayoutChange.js");








var RegionalSetting = function RegionalSetting(_ref) {
  var _ref$settings = _ref.settings,
      settings = _ref$settings === void 0 ? {} : _ref$settings,
      changeSetting = _ref.changeSetting;
  var formatMessage = (0,_index__WEBPACK_IMPORTED_MODULE_3__.getFormatMessage)();
  var regionalSetting = ['header', 'footer', 'menu', 'menuHeader'];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd_es_list__WEBPACK_IMPORTED_MODULE_4__.default, {
    split: false,
    renderItem: _LayoutChange__WEBPACK_IMPORTED_MODULE_5__.renderLayoutSettingItem,
    dataSource: regionalSetting.map(function (key) {
      return {
        title: formatMessage({
          id: "app.setting.regionalsettings.".concat(key)
        }),
        action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd_es_switch__WEBPACK_IMPORTED_MODULE_6__.default, {
          size: "small",
          className: "regional-".concat(key),
          checked: settings["".concat(key, "Render")] || settings["".concat(key, "Render")] === undefined,
          onChange: function onChange(checked) {
            return changeSetting("".concat(key, "Render"), checked === true ? undefined : false);
          }
        })
      };
    })
  });
};

/* harmony default export */ __webpack_exports__["default"] = (RegionalSetting);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/ThemeColor.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/ThemeColor.js ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_tooltip_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/tooltip/style */ "./node_modules/antd/es/tooltip/style/index.js");
/* harmony import */ var antd_es_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! antd/es/tooltip */ "./node_modules/antd/es/tooltip/index.js");
/* harmony import */ var _ThemeColor_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeColor.less */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/ThemeColor.less");
/* harmony import */ var _ThemeColor_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_ThemeColor_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/CheckOutlined.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/utils */ "./node_modules/@ant-design/pro-layout/es/utils/utils.js");


var _excluded = ["color", "check"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }





var Tag = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function (_ref, ref) {
  var color = _ref.color,
      check = _ref.check,
      rest = _objectWithoutProperties(_ref, _excluded);

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", _extends({}, rest, {
    style: {
      backgroundColor: color
    },
    ref: ref
  }), check ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__.default, null) : '');
});

var ThemeColor = function ThemeColor(_ref2, ref) {
  var colors = _ref2.colors,
      value = _ref2.value,
      onChange = _ref2.onChange,
      formatMessage = _ref2.formatMessage;
  var colorList = colors || [];

  if (colorList.length < 1) {
    return null;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "theme-color",
    ref: ref
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "theme-color-content"
  }, colorList.map(function (_ref3) {
    var key = _ref3.key,
        color = _ref3.color;
    var themeKey = (0,_utils_utils__WEBPACK_IMPORTED_MODULE_4__.genThemeToString)(color);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd_es_tooltip__WEBPACK_IMPORTED_MODULE_5__.default, {
      key: color,
      title: themeKey ? formatMessage({
        id: "app.setting.themecolor.".concat(themeKey)
      }) : key
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Tag, {
      className: "theme-color-block",
      color: color,
      check: value === key || (0,_utils_utils__WEBPACK_IMPORTED_MODULE_4__.genThemeToString)(value) === key,
      onClick: function onClick() {
        return onChange && onChange(key);
      }
    }));
  })));
};

/* harmony default export */ __webpack_exports__["default"] = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(ThemeColor));

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getFormatMessage": function() { return /* binding */ getFormatMessage; }
/* harmony export */ });
/* harmony import */ var antd_es_drawer_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/drawer/style */ "./node_modules/antd/es/drawer/style/index.js");
/* harmony import */ var antd_es_drawer__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! antd/es/drawer */ "./node_modules/antd/es/drawer/index.js");
/* harmony import */ var antd_es_button_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! antd/es/button/style */ "./node_modules/antd/es/button/style/index.js");
/* harmony import */ var antd_es_button__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! antd/es/button */ "./node_modules/antd/es/button/index.js");
/* harmony import */ var antd_es_alert_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/es/alert/style */ "./node_modules/antd/es/alert/style/index.js");
/* harmony import */ var antd_es_alert__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! antd/es/alert */ "./node_modules/antd/es/alert/index.js");
/* harmony import */ var antd_es_list_style__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/es/list/style */ "./node_modules/antd/es/list/style/index.js");
/* harmony import */ var antd_es_list__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! antd/es/list */ "./node_modules/antd/es/list/index.js");
/* harmony import */ var antd_es_switch_style__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! antd/es/switch/style */ "./node_modules/antd/es/switch/style/index.js");
/* harmony import */ var antd_es_switch__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! antd/es/switch */ "./node_modules/antd/es/switch/index.js");
/* harmony import */ var antd_es_divider_style__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! antd/es/divider/style */ "./node_modules/antd/es/divider/style/index.js");
/* harmony import */ var antd_es_divider__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! antd/es/divider */ "./node_modules/antd/es/divider/index.js");
/* harmony import */ var antd_es_message_style__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! antd/es/message/style */ "./node_modules/antd/es/message/style/index.js");
/* harmony import */ var antd_es_message__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! antd/es/message */ "./node_modules/antd/es/message/index.js");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./index.less */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/CloseOutlined.js");
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/SettingOutlined.js");
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/NotificationOutlined.js");
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/CopyOutlined.js");
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/isBrowser/index.js");
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/merge/index.js");
/* harmony import */ var _umijs_use_params__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @umijs/use-params */ "./node_modules/@umijs/use-params/es/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ "./node_modules/rc-util/es/hooks/useMergedState.js");
/* harmony import */ var omit_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! omit.js */ "./node_modules/omit.js/es/index.js");
/* harmony import */ var _defaultSettings__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../defaultSettings */ "./node_modules/@ant-design/pro-layout/es/defaultSettings.js");
/* harmony import */ var _BlockCheckbox__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./BlockCheckbox */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/BlockCheckbox.js");
/* harmony import */ var _ThemeColor__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./ThemeColor */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/ThemeColor.js");
/* harmony import */ var _locales__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../locales */ "./node_modules/@ant-design/pro-layout/es/locales/index.js");
/* harmony import */ var _utils_utils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../utils/utils */ "./node_modules/@ant-design/pro-layout/es/utils/utils.js");
/* harmony import */ var _LayoutChange__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./LayoutChange */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/LayoutChange.js");
/* harmony import */ var _RegionalChange__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./RegionalChange */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/RegionalChange.js");















function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
















var Body = function Body(_ref) {
  var children = _ref.children,
      prefixCls = _ref.prefixCls,
      title = _ref.title;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("h3", {
    className: "".concat(prefixCls, "-drawer-title")
  }, title), children);
};

var getDifferentSetting = function getDifferentSetting(state) {
  var stateObj = {};
  Object.keys(state).forEach(function (key) {
    if (state[key] !== _defaultSettings__WEBPACK_IMPORTED_MODULE_12__.default[key] && key !== 'collapse') {
      stateObj[key] = state[key];
    } else {
      stateObj[key] = undefined;
    }

    if (key.includes('Render')) {
      stateObj[key] = state[key] === false ? false : undefined;
    }
  });
  stateObj.menu = undefined;
  return stateObj;
};

var getFormatMessage = function getFormatMessage() {
  var formatMessage = function formatMessage(_ref2) {
    var id = _ref2.id;
    var locales = (0,_locales__WEBPACK_IMPORTED_MODULE_13__.default)();
    return locales[id];
  };

  return formatMessage;
};

var updateTheme = function updateTheme(dark, color) {
  var publicPath = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '/theme';
  var hideMessageLoading = arguments.length > 3 ? arguments[3] : undefined;

  // ssr
  if (typeof window === 'undefined' || !window.umi_plugin_ant_themeVar) {
    return;
  }

  var formatMessage = getFormatMessage();

  var hide = function hide() {
    return null;
  };

  if (!hideMessageLoading) {
    hide = antd_es_message__WEBPACK_IMPORTED_MODULE_14__.default.loading(formatMessage({
      id: 'app.setting.loading',
      defaultMessage: '正在加载主题'
    }));
  }

  var href = dark ? "".concat(publicPath, "/dark") : "".concat(publicPath, "/"); // 如果是 dark，并且是 color=daybreak，无需进行拼接

  var colorFileName = dark && color ? "-".concat(encodeURIComponent(color)) : encodeURIComponent(color || '');

  if (color === 'daybreak' && dark) {
    colorFileName = '';
  }

  var dom = document.getElementById('theme-style'); // 如果这两个都是空

  if (!href && !colorFileName) {
    if (dom) {
      dom.remove();
      localStorage.removeItem('site-theme');
    }

    return;
  }

  var url = "".concat(href).concat(colorFileName || '', ".css");

  if (dom) {
    dom.onload = function () {
      window.setTimeout(function () {
        hide();
      });
    };

    dom.href = url;
  } else {
    var style = document.createElement('link');
    style.type = 'text/css';
    style.rel = 'stylesheet';
    style.id = 'theme-style';

    style.onload = function () {
      window.setTimeout(function () {
        hide();
      });
    };

    style.href = url;

    if (document.body.append) {
      document.body.append(style);
    } else {
      document.body.appendChild(style);
    }
  }

  localStorage.setItem('site-theme', dark ? 'dark' : 'light');
};

var getThemeList = function getThemeList(settings) {
  var formatMessage = getFormatMessage();

  var getList = function getList() {
    if (typeof window === 'undefined') return [];
    return window.umi_plugin_ant_themeVar || [];
  };

  var list = getList() || [];
  var themeList = [{
    key: 'light',
    title: formatMessage({
      id: 'app.setting.pagestyle.light'
    })
  }];
  var darkColorList = [{
    key: 'daybreak',
    color: '#1890ff',
    theme: 'dark'
  }];
  var lightColorList = [{
    key: 'daybreak',
    color: '#1890ff',
    theme: 'dark'
  }];

  if (settings.layout !== 'mix') {
    themeList.push({
      key: 'dark',
      title: formatMessage({
        id: 'app.setting.pagestyle.dark',
        defaultMessage: ''
      })
    });
  }

  if (list.find(function (item) {
    return item.theme === 'dark';
  })) {
    themeList.push({
      key: 'realDark',
      title: formatMessage({
        id: 'app.setting.pagestyle.dark',
        defaultMessage: ''
      })
    });
  } // insert  theme color List


  list.forEach(function (item) {
    var color = (item.modifyVars || {})['@primary-color'];

    if (item.theme === 'dark' && color) {
      darkColorList.push(_objectSpread({
        color: color
      }, item));
    }

    if (!item.theme || item.theme === 'light') {
      lightColorList.push(_objectSpread({
        color: color
      }, item));
    }
  });
  return {
    colorList: {
      dark: darkColorList,
      light: lightColorList
    },
    themeList: themeList
  };
};
/**
 * 初始化的时候需要做的工作
 *
 * @param param0
 */


var initState = function initState(urlParams, settings, onSettingChange, publicPath) {
  if (!(0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_15__.default)()) return;
  var loadedStyle = false;
  var replaceSetting = {};
  Object.keys(urlParams).forEach(function (key) {
    if (_defaultSettings__WEBPACK_IMPORTED_MODULE_12__.default[key] || _defaultSettings__WEBPACK_IMPORTED_MODULE_12__.default[key] === undefined) {
      replaceSetting[key] = urlParams[key];
    }
  }); // 同步数据到外部

  onSettingChange === null || onSettingChange === void 0 ? void 0 : onSettingChange((0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_16__.merge)({}, settings, replaceSetting)); // 如果 url 中设置主题，进行一次加载。

  if (_defaultSettings__WEBPACK_IMPORTED_MODULE_12__.default.navTheme !== urlParams.navTheme && urlParams.navTheme) {
    updateTheme(settings.navTheme === 'realDark', urlParams.primaryColor, publicPath, true);
    loadedStyle = true;
  }

  if (loadedStyle) {
    return;
  } // 如果 url 中没有设置主题，并且 url 中的没有加载，进行一次加载。


  if (_defaultSettings__WEBPACK_IMPORTED_MODULE_12__.default.navTheme !== settings.navTheme && settings.navTheme) {
    updateTheme(settings.navTheme === 'realDark', settings.primaryColor, publicPath, true);
  }
};

var getParamsFromUrl = function getParamsFromUrl(urlParams, settings) {
  if (!(0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_15__.default)()) return _defaultSettings__WEBPACK_IMPORTED_MODULE_12__.default;
  return _objectSpread(_objectSpread(_objectSpread({}, _defaultSettings__WEBPACK_IMPORTED_MODULE_12__.default), settings || {}), urlParams);
};

var genCopySettingJson = function genCopySettingJson(settingState) {
  return JSON.stringify((0,omit_js__WEBPACK_IMPORTED_MODULE_11__.default)(_objectSpread(_objectSpread({}, settingState), {}, {
    primaryColor: (0,_utils_utils__WEBPACK_IMPORTED_MODULE_17__.genStringToTheme)(settingState.primaryColor)
  }), ['colorWeak']), null, 2);
};
/**
 * 可视化配置组件
 *
 * @param props
 */


var SettingDrawer = function SettingDrawer(props) {
  var _props$settings = props.settings,
      propsSettings = _props$settings === void 0 ? undefined : _props$settings,
      _props$hideLoading = props.hideLoading,
      hideLoading = _props$hideLoading === void 0 ? false : _props$hideLoading,
      hideColors = props.hideColors,
      hideHintAlert = props.hideHintAlert,
      hideCopyButton = props.hideCopyButton,
      getContainer = props.getContainer,
      onSettingChange = props.onSettingChange,
      _props$prefixCls = props.prefixCls,
      prefixCls = _props$prefixCls === void 0 ? 'ant-pro' : _props$prefixCls,
      _props$pathname = props.pathname,
      pathname = _props$pathname === void 0 ? window.location.pathname : _props$pathname,
      _props$disableUrlPara = props.disableUrlParams,
      disableUrlParams = _props$disableUrlPara === void 0 ? false : _props$disableUrlPara;
  var firstRender = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(true);

  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__.default)(false, {
    value: props.collapse,
    onChange: props.onCollapseChange
  }),
      _useMergedState2 = _slicedToArray(_useMergedState, 2),
      show = _useMergedState2[0],
      setShow = _useMergedState2[1];

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)((0,_locales__WEBPACK_IMPORTED_MODULE_13__.getLanguage)()),
      _useState2 = _slicedToArray(_useState, 2),
      language = _useState2[0],
      setLanguage = _useState2[1];

  var _useUrlSearchParams = (0,_umijs_use_params__WEBPACK_IMPORTED_MODULE_8__.useUrlSearchParams)({}, {
    disabled: disableUrlParams
  }),
      _useUrlSearchParams2 = _slicedToArray(_useUrlSearchParams, 2),
      urlParams = _useUrlSearchParams2[0],
      setUrlParams = _useUrlSearchParams2[1];

  var _useMergedState3 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__.default)(function () {
    return getParamsFromUrl(urlParams, propsSettings);
  }, {
    value: propsSettings,
    onChange: onSettingChange
  }),
      _useMergedState4 = _slicedToArray(_useMergedState3, 2),
      settingState = _useMergedState4[0],
      setSettingState = _useMergedState4[1];

  var preStateRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(settingState);

  var _ref3 = settingState || {},
      navTheme = _ref3.navTheme,
      primaryColor = _ref3.primaryColor,
      layout = _ref3.layout,
      colorWeak = _ref3.colorWeak;

  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    // 语言修改，这个是和 locale 是配置起来的
    var onLanguageChange = function onLanguageChange() {
      if (language !== (0,_locales__WEBPACK_IMPORTED_MODULE_13__.getLanguage)()) {
        setLanguage((0,_locales__WEBPACK_IMPORTED_MODULE_13__.getLanguage)());
      }
    };
    /** 如果不是浏览器 都没有必要做了 */


    if (!(0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_15__.default)()) return function () {
      return null;
    };
    initState(getParamsFromUrl(urlParams, propsSettings), settingState, setSettingState, props.publicPath);
    window.document.addEventListener('languagechange', onLanguageChange, {
      passive: true
    });
    return function () {
      return window.document.removeEventListener('languagechange', onLanguageChange);
    }; // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  /**
   * 修改设置
   *
   * @param key
   * @param value
   * @param hideMessageLoading
   */

  var changeSetting = function changeSetting(key, value, hideMessageLoading) {
    var nextState = _objectSpread({}, preStateRef.current);

    nextState[key] = value;

    if (key === 'navTheme') {
      updateTheme(value === 'realDark', undefined, props.publicPath, !!hideMessageLoading);
      nextState.primaryColor = 'daybreak';
    }

    if (key === 'primaryColor') {
      updateTheme(nextState.navTheme === 'realDark', value === 'daybreak' ? '' : value, props.publicPath, !!hideMessageLoading);
    }

    if (key === 'layout') {
      nextState.contentWidth = value === 'top' ? 'Fixed' : 'Fluid';
    }

    if (key === 'layout' && value !== 'mix') {
      nextState.splitMenus = false;
    }

    if (key === 'layout' && value === 'mix') {
      nextState.navTheme = 'light';
    }

    if (key === 'colorWeak' && value === true) {
      var dom = document.querySelector('body');

      if (dom) {
        dom.dataset.prosettingdrawer = dom.style.filter;
        dom.style.filter = 'invert(80%)';
      }
    }

    if (key === 'colorWeak' && value === false) {
      var _dom = document.querySelector('body');

      if (_dom) {
        _dom.style.filter = _dom.dataset.prosettingdrawer || 'none';
        delete _dom.dataset.prosettingdrawer;
      }
    }

    preStateRef.current = nextState;
    delete nextState.title;
    setSettingState(nextState);
  };

  var formatMessage = getFormatMessage();
  var themeList = getThemeList(settingState);
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    /** 如果不是浏览器 都没有必要做了 */
    if (!(0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_15__.default)()) return;
    if (disableUrlParams) return;

    if (firstRender.current) {
      firstRender.current = false;
      return;
    }

    var diffParams = getDifferentSetting(_objectSpread(_objectSpread({}, urlParams), settingState));
    setUrlParams(diffParams);
  }, [setUrlParams, settingState, urlParams, pathname, disableUrlParams]);
  var baseClassName = "".concat(prefixCls, "-setting");
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_drawer__WEBPACK_IMPORTED_MODULE_18__.default, {
    visible: show,
    width: 300,
    onClose: function onClose() {
      return setShow(false);
    },
    placement: "right",
    getContainer: getContainer,
    handler: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
      className: "".concat(baseClassName, "-drawer-handle"),
      onClick: function onClick() {
        return setShow(!show);
      }
    }, show ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__.default, {
      style: {
        color: '#fff',
        fontSize: 20
      }
    }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__.default, {
      style: {
        color: '#fff',
        fontSize: 20
      }
    })),
    style: {
      zIndex: 999
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
    className: "".concat(baseClassName, "-drawer-content")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Body, {
    title: formatMessage({
      id: 'app.setting.pagestyle',
      defaultMessage: 'Page style setting'
    }),
    prefixCls: baseClassName
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_BlockCheckbox__WEBPACK_IMPORTED_MODULE_21__.default, {
    prefixCls: baseClassName,
    list: themeList === null || themeList === void 0 ? void 0 : themeList.themeList,
    value: navTheme,
    configType: "theme",
    key: "navTheme",
    onChange: function onChange(value) {
      return changeSetting('navTheme', value, hideLoading);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Body, {
    title: formatMessage({
      id: 'app.setting.themecolor',
      defaultMessage: 'Theme color'
    }),
    prefixCls: baseClassName
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ThemeColor__WEBPACK_IMPORTED_MODULE_22__.default, {
    value: primaryColor,
    colors: hideColors ? [] : themeList.colorList[navTheme === 'realDark' ? 'dark' : 'light'],
    formatMessage: formatMessage,
    onChange: function onChange(color) {
      return changeSetting('primaryColor', color, hideLoading);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_divider__WEBPACK_IMPORTED_MODULE_23__.default, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Body, {
    prefixCls: baseClassName,
    title: formatMessage({
      id: 'app.setting.navigationmode'
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_BlockCheckbox__WEBPACK_IMPORTED_MODULE_21__.default, {
    prefixCls: baseClassName,
    value: layout,
    key: "layout",
    configType: "layout",
    list: [{
      key: 'side',
      title: formatMessage({
        id: 'app.setting.sidemenu'
      })
    }, {
      key: 'top',
      title: formatMessage({
        id: 'app.setting.topmenu'
      })
    }, {
      key: 'mix',
      title: formatMessage({
        id: 'app.setting.mixmenu'
      })
    }],
    onChange: function onChange(value) {
      return changeSetting('layout', value, hideLoading);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_LayoutChange__WEBPACK_IMPORTED_MODULE_24__.default, {
    settings: settingState,
    changeSetting: changeSetting
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_divider__WEBPACK_IMPORTED_MODULE_23__.default, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Body, {
    prefixCls: baseClassName,
    title: formatMessage({
      id: 'app.setting.regionalsettings'
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_RegionalChange__WEBPACK_IMPORTED_MODULE_25__.default, {
    settings: settingState,
    changeSetting: changeSetting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_divider__WEBPACK_IMPORTED_MODULE_23__.default, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Body, {
    prefixCls: baseClassName,
    title: formatMessage({
      id: 'app.setting.othersettings'
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_list__WEBPACK_IMPORTED_MODULE_26__.default, {
    split: false,
    renderItem: _LayoutChange__WEBPACK_IMPORTED_MODULE_24__.renderLayoutSettingItem,
    dataSource: [{
      title: formatMessage({
        id: 'app.setting.weakmode'
      }),
      action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_switch__WEBPACK_IMPORTED_MODULE_27__.default, {
        size: "small",
        className: "color-weak",
        checked: !!colorWeak,
        onChange: function onChange(checked) {
          changeSetting('colorWeak', checked);
        }
      })
    }]
  })), hideHintAlert && hideCopyButton ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_divider__WEBPACK_IMPORTED_MODULE_23__.default, null), hideHintAlert ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_alert__WEBPACK_IMPORTED_MODULE_28__.default, {
    type: "warning",
    message: formatMessage({
      id: 'app.setting.production.hint'
    }),
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__.default, null),
    showIcon: true,
    style: {
      marginBottom: 16
    }
  }), hideCopyButton ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(antd_es_button__WEBPACK_IMPORTED_MODULE_30__.default, {
    block: true,
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__.default, null),
    style: {
      marginBottom: 24
    },
    onClick: /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return navigator.clipboard.writeText(genCopySettingJson(settingState));

            case 3:
              antd_es_message__WEBPACK_IMPORTED_MODULE_14__.default.success(formatMessage({
                id: 'app.setting.copyinfo'
              }));

              _context.next = 8;
              break;

            case 6:
              _context.prev = 6;
              _context.t0 = _context["catch"](0);

            case 8:
            case "end":
              return _context.stop();
          }
        }
      }, _callee, null, [[0, 6]]);
    }))
  }, formatMessage({
    id: 'app.setting.copy'
  }))));
};

/* harmony default export */ __webpack_exports__["default"] = (SettingDrawer);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/BaseMenu.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SiderMenu/BaseMenu.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_skeleton_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/skeleton/style */ "./node_modules/antd/es/skeleton/style/index.js");
/* harmony import */ var antd_es_skeleton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! antd/es/skeleton */ "./node_modules/antd/es/skeleton/index.js");
/* harmony import */ var antd_es_menu_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! antd/es/menu/style */ "./node_modules/antd/es/menu/style/index.js");
/* harmony import */ var antd_es_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! antd/es/menu */ "./node_modules/antd/es/menu/index.js");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.less */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/components/IconFont.js");
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/components/Icon.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/isUrl/index.js");
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/isImg/index.js");
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/useMountMergeState/index.js");
/* harmony import */ var _defaultSettings__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../defaultSettings */ "./node_modules/@ant-design/pro-layout/es/defaultSettings.js");
/* harmony import */ var _utils_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../utils/utils */ "./node_modules/@ant-design/pro-layout/es/utils/utils.js");
/* harmony import */ var _Counter__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Counter */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/Counter.js");





function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }









var SubMenu = antd_es_menu__WEBPACK_IMPORTED_MODULE_5__.default.SubMenu,
    ItemGroup = antd_es_menu__WEBPACK_IMPORTED_MODULE_5__.default.ItemGroup;
var IconFont = (0,_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__.default)({
  scriptUrl: _defaultSettings__WEBPACK_IMPORTED_MODULE_7__.default.iconfontUrl
}); // Allow menu.js config icon as string or ReactNode
//   icon: 'setting',
//   icon: 'icon-geren' #For Iconfont ,
//   icon: 'http://demo.com/icon.png',
//   icon: '/favicon.png',
//   icon: <Icon type="setting" />,

var getIcon = function getIcon(icon) {
  var iconPrefixes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'icon-';

  if (typeof icon === 'string' && icon !== '') {
    if ((0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_8__.default)(icon) || (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_9__.default)(icon)) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__.default, {
        component: function component() {
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("img", {
            src: icon,
            alt: "icon",
            className: "ant-pro-sider-menu-icon"
          });
        }
      });
    }

    if (icon.startsWith(iconPrefixes)) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(IconFont, {
        type: icon
      });
    }
  }

  return icon;
};

var MenuUtil = function MenuUtil(props) {
  var _this = this;

  _classCallCheck(this, MenuUtil);

  this.props = void 0;

  this.getNavMenuItems = function () {
    var menusData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
    var isChildren = arguments.length > 1 ? arguments[1] : undefined;
    return menusData.map(function (item) {
      return _this.getSubMenuOrItem(item, isChildren);
    }).filter(function (item) {
      return item;
    });
  };

  this.getSubMenuOrItem = function (item, isChildren) {
    if (Array.isArray(item.routes) && item && item.routes.length > 0) {
      var name = _this.getIntlName(item);

      var _this$props = _this.props,
          subMenuItemRender = _this$props.subMenuItemRender,
          prefixCls = _this$props.prefixCls,
          menu = _this$props.menu,
          iconPrefixes = _this$props.iconPrefixes; //  get defaultTitle by menuItemRender

      var defaultTitle = item.icon ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
        className: "".concat(prefixCls, "-menu-item"),
        title: name
      }, !isChildren && getIcon(item.icon, iconPrefixes), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
        className: "".concat(prefixCls, "-menu-item-title")
      }, name)) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
        className: "".concat(prefixCls, "-menu-item"),
        title: name
      }, name); // subMenu only title render

      var title = subMenuItemRender ? subMenuItemRender(_objectSpread(_objectSpread({}, item), {}, {
        isUrl: false
      }), defaultTitle) : defaultTitle;
      var MenuComponents = (menu === null || menu === void 0 ? void 0 : menu.type) === 'group' ? ItemGroup : SubMenu;
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(MenuComponents, {
        title: title,
        key: item.key || item.path,
        onTitleClick: item.onTitleClick
      }, _this.getNavMenuItems(item.routes, true));
    }

    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_menu__WEBPACK_IMPORTED_MODULE_5__.default.Item, {
      disabled: item.disabled,
      key: item.key || item.path,
      onClick: item.onTitleClick
    }, _this.getMenuItemPath(item, isChildren));
  };

  this.getIntlName = function (item) {
    var name = item.name,
        locale = item.locale;
    var _this$props2 = _this.props,
        menu = _this$props2.menu,
        formatMessage = _this$props2.formatMessage;

    if (locale && (menu === null || menu === void 0 ? void 0 : menu.locale) !== false) {
      return formatMessage === null || formatMessage === void 0 ? void 0 : formatMessage({
        id: locale,
        defaultMessage: name
      });
    }

    return name;
  };

  this.getMenuItemPath = function (item, isChildren) {
    var itemPath = _this.conversionPath(item.path || '/');

    var _this$props3 = _this.props,
        _this$props3$location = _this$props3.location,
        location = _this$props3$location === void 0 ? {
      pathname: '/'
    } : _this$props3$location,
        isMobile = _this$props3.isMobile,
        onCollapse = _this$props3.onCollapse,
        menuItemRender = _this$props3.menuItemRender,
        iconPrefixes = _this$props3.iconPrefixes; // if local is true formatMessage all name。

    var name = _this.getIntlName(item);

    var prefixCls = _this.props.prefixCls;
    var icon = isChildren ? null : getIcon(item.icon, iconPrefixes);
    var defaultItem = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
      className: "".concat(prefixCls, "-menu-item")
    }, icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
      className: "".concat(prefixCls, "-menu-item-title")
    }, name));
    var isHttpUrl = (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_8__.default)(itemPath); // Is it a http link

    if (isHttpUrl) {
      defaultItem = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
        title: name,
        onClick: function onClick() {
          var _window, _window$open;

          (_window = window) === null || _window === void 0 ? void 0 : (_window$open = _window.open) === null || _window$open === void 0 ? void 0 : _window$open.call(_window, itemPath);
        },
        className: "".concat(prefixCls, "-menu-item ").concat(prefixCls, "-menu-item-link")
      }, icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
        className: "".concat(prefixCls, "-menu-item-title")
      }, name));
    }

    if (menuItemRender) {
      var renderItemProps = _objectSpread(_objectSpread({}, item), {}, {
        isUrl: isHttpUrl,
        itemPath: itemPath,
        isMobile: isMobile,
        replace: itemPath === location.pathname,
        onClick: function onClick() {
          return onCollapse && onCollapse(true);
        },
        children: undefined
      });

      return menuItemRender(renderItemProps, defaultItem, _this.props);
    }

    return defaultItem;
  };

  this.conversionPath = function (path) {
    if (path && path.indexOf('http') === 0) {
      return path;
    }

    return "/".concat(path || '').replace(/\/+/g, '/');
  };

  this.props = props;
};
/**
 * 生成openKeys 的对象，因为设置了openKeys 就会变成受控，所以需要一个空对象
 *
 * @param BaseMenuProps
 */


var getOpenKeysProps = function getOpenKeysProps(openKeys, _ref) {
  var layout = _ref.layout,
      collapsed = _ref.collapsed;
  var openKeysProps = {};

  if (openKeys && !collapsed && ['side', 'mix'].includes(layout || 'mix')) {
    openKeysProps = {
      openKeys: openKeys
    };
  }

  return openKeysProps;
};

var BaseMenu = function BaseMenu(props) {
  var theme = props.theme,
      mode = props.mode,
      className = props.className,
      handleOpenChange = props.handleOpenChange,
      style = props.style,
      menuData = props.menuData,
      menu = props.menu,
      matchMenuKeys = props.matchMenuKeys,
      iconfontUrl = props.iconfontUrl,
      collapsed = props.collapsed,
      propsSelectedKeys = props.selectedKeys,
      onSelect = props.onSelect,
      propsOpenKeys = props.openKeys; // 用于减少 defaultOpenKeys 计算的组件

  var defaultOpenKeysRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)([]);

  var _MenuCounter$useConta = _Counter__WEBPACK_IMPORTED_MODULE_11__.default.useContainer(),
      flatMenuKeys = _MenuCounter$useConta.flatMenuKeys;

  var _useMountMergeState = (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_12__.default)(menu === null || menu === void 0 ? void 0 : menu.defaultOpenAll),
      _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2),
      defaultOpenAll = _useMountMergeState2[0],
      setDefaultOpenAll = _useMountMergeState2[1];

  var _useMountMergeState3 = (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_12__.default)(function () {
    if (menu === null || menu === void 0 ? void 0 : menu.defaultOpenAll) {
      return (0,_utils_utils__WEBPACK_IMPORTED_MODULE_13__.getOpenKeysFromMenuData)(menuData) || [];
    }

    if (propsOpenKeys === false) {
      return false;
    }

    return [];
  }, {
    value: propsOpenKeys === false ? undefined : propsOpenKeys,
    onChange: handleOpenChange
  }),
      _useMountMergeState4 = _slicedToArray(_useMountMergeState3, 2),
      openKeys = _useMountMergeState4[0],
      setOpenKeys = _useMountMergeState4[1];

  var _useMountMergeState5 = (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_12__.default)([], {
    value: propsSelectedKeys,
    onChange: onSelect ? function (keys) {
      if (onSelect && keys) {
        onSelect(keys);
      }
    } : undefined
  }),
      _useMountMergeState6 = _slicedToArray(_useMountMergeState5, 2),
      selectedKeys = _useMountMergeState6[0],
      setSelectedKeys = _useMountMergeState6[1];

  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if ((menu === null || menu === void 0 ? void 0 : menu.defaultOpenAll) || propsOpenKeys === false || flatMenuKeys.length) {
      return;
    }

    if (matchMenuKeys) {
      setOpenKeys(matchMenuKeys);
      setSelectedKeys(matchMenuKeys);
    } // eslint-disable-next-line react-hooks/exhaustive-deps

  }, [matchMenuKeys.join('-')]);
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    // reset IconFont
    if (iconfontUrl) {
      IconFont = (0,_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__.default)({
        scriptUrl: iconfontUrl
      });
    }
  }, [iconfontUrl]);
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    // if pathname can't match, use the nearest parent's key
    if (matchMenuKeys.join('-') !== (selectedKeys || []).join('-')) {
      setSelectedKeys(matchMenuKeys);
    }

    if (!defaultOpenAll && propsOpenKeys !== false && matchMenuKeys.join('-') !== (openKeys || []).join('-')) {
      var newKeys = matchMenuKeys; // 如果不自动关闭，我需要把 openKeys 放进去

      if ((menu === null || menu === void 0 ? void 0 : menu.autoClose) === false) {
        newKeys = Array.from(new Set([].concat(_toConsumableArray(matchMenuKeys), _toConsumableArray(openKeys || []))));
      }

      setOpenKeys(newKeys);
    } else if ((menu === null || menu === void 0 ? void 0 : menu.ignoreFlatMenu) && defaultOpenAll) {
      // 忽略用户手动折叠过的菜单状态，折叠按钮切换之后也可实现默认展开所有菜单
      setOpenKeys((0,_utils_utils__WEBPACK_IMPORTED_MODULE_13__.getOpenKeysFromMenuData)(menuData));
    } else if (flatMenuKeys.length > 0) setDefaultOpenAll(false); // eslint-disable-next-line react-hooks/exhaustive-deps

  }, [matchMenuKeys.join('-'), collapsed]);
  var openKeysProps = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return getOpenKeysProps(openKeys, props);
  }, // eslint-disable-next-line react-hooks/exhaustive-deps
  [openKeys && openKeys.join(','), props.layout, props.collapsed]);

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(function () {
    return new MenuUtil(props);
  }),
      _useState2 = _slicedToArray(_useState, 1),
      menuUtils = _useState2[0];

  if (menu === null || menu === void 0 ? void 0 : menu.loading) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: (mode === null || mode === void 0 ? void 0 : mode.includes('inline')) ? {
        padding: 24
      } : {
        marginTop: 16
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_skeleton__WEBPACK_IMPORTED_MODULE_14__.default, {
      active: true,
      title: false,
      paragraph: {
        rows: (mode === null || mode === void 0 ? void 0 : mode.includes('inline')) ? 6 : 1
      }
    }));
  }

  var cls = classnames__WEBPACK_IMPORTED_MODULE_4___default()(className, {
    'top-nav-menu': mode === 'horizontal'
  }); // sync props

  menuUtils.props = props; // 这次 openKeys === false 的时候的情况，这种情况下帮用户选中一次
  // 第二此不会使用，所以用了 defaultOpenKeys
  // 这里返回 null，是为了让 defaultOpenKeys 生效

  if (props.openKeys === false && !props.handleOpenChange) {
    defaultOpenKeysRef.current = matchMenuKeys;
  }

  var finallyData = props.postMenuData ? props.postMenuData(menuData) : menuData;

  if (finallyData && (finallyData === null || finallyData === void 0 ? void 0 : finallyData.length) < 1) {
    return null;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd_es_menu__WEBPACK_IMPORTED_MODULE_5__.default, _extends({}, openKeysProps, {
    key: "Menu",
    mode: mode,
    inlineIndent: 16,
    defaultOpenKeys: defaultOpenKeysRef.current,
    theme: theme,
    selectedKeys: selectedKeys,
    style: style,
    className: cls,
    onOpenChange: setOpenKeys
  }, props.menuProps), menuUtils.getNavMenuItems(finallyData, false));
};

BaseMenu.defaultProps = {
  postMenuData: function postMenuData(data) {
    return data || [];
  }
};
/* harmony default export */ __webpack_exports__["default"] = (BaseMenu);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/Counter.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SiderMenu/Counter.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unstated-next */ "./node_modules/unstated-next/dist/unstated-next.mjs");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }




function useMenuCounter() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]),
      _useState2 = _slicedToArray(_useState, 2),
      flatMenuKeys = _useState2[0],
      setFlatMenuKeys = _useState2[1];

  return {
    flatMenuKeys: flatMenuKeys,
    setFlatMenuKeys: setFlatMenuKeys
  };
}

var MenuCounter = (0,unstated_next__WEBPACK_IMPORTED_MODULE_1__.createContainer)(useMenuCounter);
/* harmony default export */ __webpack_exports__["default"] = (MenuCounter);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "defaultRenderLogo": function() { return /* binding */ defaultRenderLogo; },
/* harmony export */   "defaultRenderLogoAndTitle": function() { return /* binding */ defaultRenderLogoAndTitle; },
/* harmony export */   "defaultRenderCollapsedButton": function() { return /* binding */ defaultRenderCollapsedButton; }
/* harmony export */ });
/* harmony import */ var antd_es_menu_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/menu/style */ "./node_modules/antd/es/menu/style/index.js");
/* harmony import */ var antd_es_menu__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! antd/es/menu */ "./node_modules/antd/es/menu/index.js");
/* harmony import */ var antd_es_layout_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! antd/es/layout/style */ "./node_modules/antd/es/layout/style/index.js");
/* harmony import */ var antd_es_layout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! antd/es/layout */ "./node_modules/antd/es/layout/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js");
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ant-design/icons */ "./node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.less */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _BaseMenu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./BaseMenu */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/BaseMenu.js");
/* harmony import */ var _Counter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Counter */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/Counter.js");





function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }







var Sider = antd_es_layout__WEBPACK_IMPORTED_MODULE_5__.default.Sider;
var defaultRenderLogo = function defaultRenderLogo(logo) {
  if (typeof logo === 'string') {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("img", {
      src: logo,
      alt: "logo"
    });
  }

  if (typeof logo === 'function') {
    return logo();
  }

  return logo;
};
var defaultRenderLogoAndTitle = function defaultRenderLogoAndTitle(props) {
  var renderKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'menuHeaderRender';
  var logo = props.logo,
      title = props.title,
      layout = props.layout;
  var renderFunction = props[renderKey || ''];

  if (renderFunction === false) {
    return null;
  }

  var logoDom = defaultRenderLogo(logo);
  var titleDom = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("h1", null, title !== null && title !== void 0 ? title : 'Ant Design Pro');

  if (renderFunction) {
    // when collapsed, no render title
    return renderFunction(logoDom, props.collapsed ? null : titleDom, props);
  }

  if (layout === 'mix' && renderKey === 'menuHeaderRender') {
    return null;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("a", null, logoDom, props.collapsed ? null : titleDom);
};
var defaultRenderCollapsedButton = function defaultRenderCollapsedButton(collapsed) {
  return collapsed ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__.default, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__.default, null);
};

var SiderMenu = function SiderMenu(props) {
  var _classNames;

  var collapsed = props.collapsed,
      fixSiderbar = props.fixSiderbar,
      menuFooterRender = props.menuFooterRender,
      _onCollapse = props.onCollapse,
      theme = props.theme,
      siderWidth = props.siderWidth,
      isMobile = props.isMobile,
      onMenuHeaderClick = props.onMenuHeaderClick,
      _props$breakpoint = props.breakpoint,
      breakpoint = _props$breakpoint === void 0 ? 'lg' : _props$breakpoint,
      style = props.style,
      layout = props.layout,
      _props$menuExtraRende = props.menuExtraRender,
      menuExtraRender = _props$menuExtraRende === void 0 ? false : _props$menuExtraRende,
      _props$collapsedButto = props.collapsedButtonRender,
      collapsedButtonRender = _props$collapsedButto === void 0 ? defaultRenderCollapsedButton : _props$collapsedButto,
      links = props.links,
      menuContentRender = props.menuContentRender,
      prefixCls = props.prefixCls,
      onOpenChange = props.onOpenChange,
      headerHeight = props.headerHeight,
      logoStyle = props.logoStyle;
  var baseClassName = "".concat(prefixCls, "-sider");

  var _MenuCounter$useConta = _Counter__WEBPACK_IMPORTED_MODULE_8__.default.useContainer(),
      flatMenuKeys = _MenuCounter$useConta.flatMenuKeys;

  var siderClassName = classnames__WEBPACK_IMPORTED_MODULE_3___default()("".concat(baseClassName), (_classNames = {}, _defineProperty(_classNames, "".concat(baseClassName, "-fixed"), fixSiderbar), _defineProperty(_classNames, "".concat(baseClassName, "-layout-").concat(layout), layout && !isMobile), _defineProperty(_classNames, "".concat(baseClassName, "-light"), theme === 'light'), _classNames));
  var headerDom = defaultRenderLogoAndTitle(props);
  var extraDom = menuExtraRender && menuExtraRender(props);
  var menuDom = menuContentRender !== false && flatMenuKeys && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_BaseMenu__WEBPACK_IMPORTED_MODULE_9__.default, _extends({}, props, {
    key: "base-menu",
    mode: "inline",
    handleOpenChange: onOpenChange,
    style: {
      width: '100%'
    },
    className: "".concat(baseClassName, "-menu")
  }));
  var menuRenderDom = menuContentRender ? menuContentRender(props, menuDom) : menuDom;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, fixSiderbar && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: _objectSpread({
      width: collapsed ? 48 : siderWidth,
      overflow: 'hidden',
      flex: "0 0 ".concat(collapsed ? 48 : siderWidth, "px"),
      maxWidth: collapsed ? 48 : siderWidth,
      minWidth: collapsed ? 48 : siderWidth,
      transition: "background-color 0.3s, min-width 0.3s, max-width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"
    }, style)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Sider, {
    collapsible: true,
    trigger: null,
    collapsed: collapsed,
    breakpoint: breakpoint === false ? undefined : breakpoint,
    onCollapse: function onCollapse(collapse) {
      if (isMobile) return;
      _onCollapse === null || _onCollapse === void 0 ? void 0 : _onCollapse(collapse);
    },
    collapsedWidth: 48,
    style: _objectSpread({
      overflow: 'hidden',
      paddingTop: layout === 'mix' && !isMobile ? headerHeight : undefined
    }, style),
    width: siderWidth,
    theme: theme,
    className: siderClassName
  }, headerDom && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()("".concat(baseClassName, "-logo"), _defineProperty({}, "".concat(baseClassName, "-collapsed"), collapsed)),
    onClick: layout !== 'mix' ? onMenuHeaderClick : undefined,
    id: "logo",
    style: logoStyle
  }, headerDom), extraDom && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(baseClassName, "-extra ").concat(!headerDom && "".concat(baseClassName, "-extra-no-logo"))
  }, extraDom), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      flex: 1,
      overflowY: 'auto',
      overflowX: 'hidden'
    }
  }, menuRenderDom), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(baseClassName, "-links")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd_es_menu__WEBPACK_IMPORTED_MODULE_10__.default, {
    theme: theme,
    inlineIndent: 16,
    className: "".concat(baseClassName, "-link-menu"),
    selectedKeys: [],
    openKeys: [],
    mode: "inline"
  }, (links || []).map(function (node, index) {
    return (
      /*#__PURE__*/
      // eslint-disable-next-line react/no-array-index-key
      react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd_es_menu__WEBPACK_IMPORTED_MODULE_10__.default.Item, {
        className: "".concat(baseClassName, "-link"),
        key: index
      }, node)
    );
  }), collapsedButtonRender && !isMobile && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd_es_menu__WEBPACK_IMPORTED_MODULE_10__.default.Item, {
    className: "".concat(baseClassName, "-collapsed-button"),
    title: false,
    key: "collapsed",
    onClick: function onClick() {
      if (_onCollapse) {
        _onCollapse(!collapsed);
      }
    }
  }, collapsedButtonRender(collapsed)))), menuFooterRender && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()("".concat(baseClassName, "-footer"), _defineProperty({}, "".concat(baseClassName, "-footer-collapsed"), !collapsed))
  }, menuFooterRender(props))));
};

/* harmony default export */ __webpack_exports__["default"] = (SiderMenu);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_drawer_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/drawer/style */ "./node_modules/antd/es/drawer/style/index.js");
/* harmony import */ var antd_es_drawer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! antd/es/drawer */ "./node_modules/antd/es/drawer/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var omit_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! omit.js */ "./node_modules/omit.js/es/index.js");
/* harmony import */ var _umijs_route_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @umijs/route-utils */ "./node_modules/@umijs/route-utils/es/index.js");
/* harmony import */ var _SiderMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SiderMenu */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js");
/* harmony import */ var _Counter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Counter */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/Counter.js");



function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }








var SiderMenuWrapper = function SiderMenuWrapper(props) {
  var isMobile = props.isMobile,
      menuData = props.menuData,
      siderWidth = props.siderWidth,
      collapsed = props.collapsed,
      onCollapse = props.onCollapse,
      style = props.style,
      className = props.className,
      hide = props.hide,
      getContainer = props.getContainer,
      prefixCls = props.prefixCls,
      matchMenuKeys = props.matchMenuKeys;

  var _MenuCounter$useConta = _Counter__WEBPACK_IMPORTED_MODULE_5__.default.useContainer(),
      setFlatMenuKeys = _MenuCounter$useConta.setFlatMenuKeys;

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (!menuData || menuData.length < 1) {
      return;
    } // 当 menu data 改变的时候重新计算这两个参数


    var newFlatMenus = (0,_umijs_route_utils__WEBPACK_IMPORTED_MODULE_4__.getFlatMenus)(menuData);
    setFlatMenuKeys(Object.keys(newFlatMenus)); // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchMenuKeys.join('-')]);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (isMobile === true) {
      onCollapse === null || onCollapse === void 0 ? void 0 : onCollapse(true);
    } // eslint-disable-next-line react-hooks/exhaustive-deps

  }, [isMobile]);
  var omitProps = (0,omit_js__WEBPACK_IMPORTED_MODULE_3__.default)(props, ['className', 'style']);

  if (hide) {
    return null;
  }

  return isMobile ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd_es_drawer__WEBPACK_IMPORTED_MODULE_6__.default, {
    visible: !collapsed,
    placement: "left",
    className: classnames__WEBPACK_IMPORTED_MODULE_2___default()("".concat(prefixCls, "-drawer-sider"), className),
    onClose: function onClose() {
      return onCollapse === null || onCollapse === void 0 ? void 0 : onCollapse(true);
    },
    style: _objectSpread({
      padding: 0,
      height: '100vh'
    }, style),
    getContainer: getContainer,
    width: siderWidth,
    bodyStyle: {
      height: '100vh',
      padding: 0,
      display: 'flex',
      flexDirection: 'row'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_SiderMenu__WEBPACK_IMPORTED_MODULE_7__.default, _extends({}, omitProps, {
    className: classnames__WEBPACK_IMPORTED_MODULE_2___default()("".concat(prefixCls, "-sider"), className),
    collapsed: isMobile ? false : collapsed,
    splitMenus: false
  }))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_SiderMenu__WEBPACK_IMPORTED_MODULE_7__.default, _extends({
    className: classnames__WEBPACK_IMPORTED_MODULE_2___default()("".concat(prefixCls, "-sider"), className)
  }, omitProps, {
    style: style
  }));
};

/* harmony default export */ __webpack_exports__["default"] = (SiderMenuWrapper);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-resize-observer */ "./node_modules/@ant-design/pro-layout/node_modules/rc-resize-observer/es/index.js");
/* harmony import */ var _SiderMenu_SiderMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../SiderMenu/SiderMenu */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.less */ "./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _SiderMenu_BaseMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../SiderMenu/BaseMenu */ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/BaseMenu.js");
var _excluded = ["rightContentRender"];

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }







/**
 * 抽离出来是为了防止 rightSize 经常改变导致菜单 render
 *
 * @param param0
 */

var RightContent = function RightContent(_ref) {
  var rightContentRender = _ref.rightContentRender,
      props = _objectWithoutProperties(_ref, _excluded);

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('auto'),
      _useState2 = _slicedToArray(_useState, 2),
      rightSize = _useState2[0],
      setRightSize = _useState2[1];

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      minWidth: rightSize
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      paddingRight: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_2__.default, {
    onResize: function onResize(_ref2) {
      var width = _ref2.width;
      setRightSize(width);
    }
  }, rightContentRender && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", null, rightContentRender(_objectSpread({}, props))))));
};

var TopNavHeader = function TopNavHeader(props) {
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  var theme = props.theme,
      onMenuHeaderClick = props.onMenuHeaderClick,
      contentWidth = props.contentWidth,
      rightContentRender = props.rightContentRender,
      propsClassName = props.className,
      style = props.style,
      layout = props.layout;
  var prefixCls = "".concat(props.prefixCls || 'ant-pro', "-top-nav-header");
  var headerDom = (0,_SiderMenu_SiderMenu__WEBPACK_IMPORTED_MODULE_4__.defaultRenderLogoAndTitle)(_objectSpread(_objectSpread({}, props), {}, {
    collapsed: false
  }), layout === 'mix' ? 'headerTitleRender' : undefined);
  var className = classnames__WEBPACK_IMPORTED_MODULE_1___default()(prefixCls, propsClassName, {
    light: theme === 'light'
  });
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    className: className,
    style: style
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    ref: ref,
    className: "".concat(prefixCls, "-main ").concat(contentWidth === 'Fixed' ? 'wide' : '')
  }, headerDom && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    className: "".concat(prefixCls, "-main-left"),
    onClick: onMenuHeaderClick
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    className: "".concat(prefixCls, "-logo"),
    key: "logo",
    id: "logo"
  }, headerDom)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    style: {
      flex: 1
    },
    className: "".concat(prefixCls, "-menu")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_SiderMenu_BaseMenu__WEBPACK_IMPORTED_MODULE_5__.default, _extends({}, props, props.menuProps))), rightContentRender && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(RightContent, _extends({
    rightContentRender: rightContentRender
  }, props))));
};

/* harmony default export */ __webpack_exports__["default"] = (TopNavHeader);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/WaterMark/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/WaterMark/index.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd_es_config_provider_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! antd/es/config-provider/style */ "./node_modules/antd/es/config-provider/style/index.js");
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/es/config-provider */ "./node_modules/antd/es/config-provider/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);



function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }



/**
 * 返回当前显示设备的物理像素分辨率与CSS像素分辨率之比
 *
 * @param context
 * @see api 有些废弃了，其实类型 CanvasRenderingContext2D
 */

var getPixelRatio = function getPixelRatio(context) {
  if (!context) {
    return 1;
  }

  var backingStore = context.backingStorePixelRatio || context.webkitBackingStorePixelRatio || context.mozBackingStorePixelRatio || context.msBackingStorePixelRatio || context.oBackingStorePixelRatio || context.backingStorePixelRatio || 1;
  return (window.devicePixelRatio || 1) / backingStore;
};

var WaterMark = function WaterMark(props) {
  var children = props.children,
      style = props.style,
      className = props.className,
      markStyle = props.markStyle,
      markClassName = props.markClassName,
      _props$zIndex = props.zIndex,
      zIndex = _props$zIndex === void 0 ? 9 : _props$zIndex,
      _props$gapX = props.gapX,
      gapX = _props$gapX === void 0 ? 212 : _props$gapX,
      _props$gapY = props.gapY,
      gapY = _props$gapY === void 0 ? 222 : _props$gapY,
      _props$width = props.width,
      width = _props$width === void 0 ? 120 : _props$width,
      _props$height = props.height,
      height = _props$height === void 0 ? 64 : _props$height,
      _props$rotate = props.rotate,
      rotate = _props$rotate === void 0 ? -22 : _props$rotate,
      image = props.image,
      content = props.content,
      offsetLeft = props.offsetLeft,
      offsetTop = props.offsetTop,
      _props$fontStyle = props.fontStyle,
      fontStyle = _props$fontStyle === void 0 ? 'normal' : _props$fontStyle,
      _props$fontWeight = props.fontWeight,
      fontWeight = _props$fontWeight === void 0 ? 'normal' : _props$fontWeight,
      _props$fontColor = props.fontColor,
      fontColor = _props$fontColor === void 0 ? 'rgba(0,0,0,.15)' : _props$fontColor,
      _props$fontSize = props.fontSize,
      fontSize = _props$fontSize === void 0 ? 16 : _props$fontSize,
      _props$fontFamily = props.fontFamily,
      fontFamily = _props$fontFamily === void 0 ? 'sans-serif' : _props$fontFamily,
      customizePrefixCls = props.prefixCls;

  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(antd_es_config_provider__WEBPACK_IMPORTED_MODULE_3__.default.ConfigContext),
      getPrefixCls = _useContext.getPrefixCls;

  var prefixCls = getPrefixCls('pro-layout-watermark', customizePrefixCls);
  var wrapperCls = classnames__WEBPACK_IMPORTED_MODULE_2___default()("".concat(prefixCls, "-wrapper"), className);
  var waterMakrCls = classnames__WEBPACK_IMPORTED_MODULE_2___default()(prefixCls, markClassName);

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''),
      _useState2 = _slicedToArray(_useState, 2),
      base64Url = _useState2[0],
      setBase64Url = _useState2[1];

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var canvas = document.createElement('canvas');
    var ctx = canvas.getContext('2d');
    var ratio = getPixelRatio(ctx);
    var canvasWidth = "".concat((gapX + width) * ratio, "px");
    var canvasHeight = "".concat((gapY + height) * ratio, "px");
    var canvasOffsetLeft = offsetLeft || gapX / 2;
    var canvasOffsetTop = offsetTop || gapY / 2;
    canvas.setAttribute('width', canvasWidth);
    canvas.setAttribute('height', canvasHeight);

    if (ctx) {
      // 旋转字符 rotate
      ctx.translate(canvasOffsetLeft * ratio, canvasOffsetTop * ratio);
      ctx.rotate(Math.PI / 180 * Number(rotate));
      var markWidth = width * ratio;
      var markHeight = height * ratio;

      if (image) {
        var img = new Image();
        img.crossOrigin = 'anonymous';
        img.referrerPolicy = 'no-referrer';
        img.src = image;

        img.onload = function () {
          ctx.drawImage(img, 0, 0, markWidth, markHeight);
          setBase64Url(canvas.toDataURL());
        };
      } else if (content) {
        var markSize = Number(fontSize) * ratio;
        ctx.font = "".concat(fontStyle, " normal ").concat(fontWeight, " ").concat(markSize, "px/").concat(markHeight, "px ").concat(fontFamily);
        ctx.fillStyle = fontColor;
        ctx.fillText(content, 0, 0);
        setBase64Url(canvas.toDataURL());
      }
    } else {
      // eslint-disable-next-line no-console
      console.error('当前环境不支持Canvas');
    }
  }, [gapX, gapY, offsetLeft, offsetTop, rotate, fontStyle, fontWeight, width, height, fontFamily, fontColor, image, content, fontSize]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: _objectSpread({
      position: 'relative'
    }, style),
    className: wrapperCls
  }, children, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: waterMakrCls,
    style: _objectSpread({
      zIndex: zIndex,
      position: 'absolute',
      left: 0,
      top: 0,
      width: '100%',
      height: '100%',
      backgroundSize: "".concat(gapX + width, "px"),
      pointerEvents: 'none',
      backgroundRepeat: 'repeat',
      backgroundImage: "url('".concat(base64Url, "')")
    }, markStyle)
  }));
};

/* harmony default export */ __webpack_exports__["default"] = (WaterMark);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/defaultSettings.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/defaultSettings.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
var defaultSettings = {
  navTheme: 'dark',
  layout: 'side',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: false,
  headerHeight: 48,
  iconfontUrl: '',
  primaryColor: 'daybreak',
  splitMenus: false
};
/* harmony default export */ __webpack_exports__["default"] = (defaultSettings);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/getPageTitle.js":
/*!****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/getPageTitle.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "matchParamsPath": function() { return /* binding */ matchParamsPath; },
/* harmony export */   "getPageTitleInfo": function() { return /* binding */ getPageTitleInfo; }
/* harmony export */ });
/* harmony import */ var path_to_regexp__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path-to-regexp */ "./node_modules/@ant-design/pro-layout/node_modules/path-to-regexp/index.js");
/* harmony import */ var path_to_regexp__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(path_to_regexp__WEBPACK_IMPORTED_MODULE_0__);
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }


var matchParamsPath = function matchParamsPath(pathname, breadcrumb, breadcrumbMap) {
  // Internal logic use breadcrumbMap to ensure the order
  // 内部逻辑使用 breadcrumbMap 来确保查询顺序
  if (breadcrumbMap) {
    var pathKey = _toConsumableArray(breadcrumbMap.keys()).find(function (key) {
      return path_to_regexp__WEBPACK_IMPORTED_MODULE_0___default()(key).test(pathname);
    });

    if (pathKey) {
      return breadcrumbMap.get(pathKey);
    }
  } // External uses use breadcrumb
  // 外部用户使用 breadcrumb 参数


  if (breadcrumb) {
    var _pathKey = Object.keys(breadcrumb).find(function (key) {
      return path_to_regexp__WEBPACK_IMPORTED_MODULE_0___default()(key).test(pathname);
    });

    if (_pathKey) {
      return breadcrumb[_pathKey];
    }
  }

  return {
    path: ''
  };
};
/**
 * 获取关于 pageTitle 的所有信息方便包装
 *
 * @param props
 * @param ignoreTitle
 */

var getPageTitleInfo = function getPageTitleInfo(props, ignoreTitle) {
  var _props$pathname = props.pathname,
      pathname = _props$pathname === void 0 ? '/' : _props$pathname,
      breadcrumb = props.breadcrumb,
      breadcrumbMap = props.breadcrumbMap,
      formatMessage = props.formatMessage,
      title = props.title,
      _props$menu = props.menu,
      menu = _props$menu === void 0 ? {
    locale: false
  } : _props$menu;
  var pageTitle = ignoreTitle ? '' : title || '';
  var currRouterData = matchParamsPath(pathname, breadcrumb, breadcrumbMap);

  if (!currRouterData) {
    return {
      title: pageTitle,
      id: '',
      pageName: pageTitle
    };
  }

  var pageName = currRouterData.name;

  if (menu.locale !== false && currRouterData.locale && formatMessage) {
    pageName = formatMessage({
      id: currRouterData.locale || '',
      defaultMessage: currRouterData.name
    });
  }

  if (!pageName) {
    return {
      title: pageTitle,
      id: currRouterData.locale || '',
      pageName: pageTitle
    };
  }

  if (ignoreTitle || !title) {
    return {
      title: pageName,
      id: currRouterData.locale || '',
      pageName: pageName
    };
  }

  return {
    title: "".concat(pageName, " - ").concat(title),
    id: currRouterData.locale || '',
    pageName: pageName
  };
};



var getPageTitle = function getPageTitle(props, ignoreTitle) {
  return getPageTitleInfo(props, ignoreTitle).title;
};

/* harmony default export */ __webpack_exports__["default"] = (getPageTitle);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/index.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "BasicLayout": function() { return /* reexport safe */ _BasicLayout__WEBPACK_IMPORTED_MODULE_1__.default; },
/* harmony export */   "RouteContext": function() { return /* reexport safe */ _RouteContext__WEBPACK_IMPORTED_MODULE_2__.default; },
/* harmony export */   "PageLoading": function() { return /* reexport safe */ _components_PageLoading__WEBPACK_IMPORTED_MODULE_3__.default; },
/* harmony export */   "GridContent": function() { return /* reexport safe */ _components_GridContent__WEBPACK_IMPORTED_MODULE_4__.default; },
/* harmony export */   "DefaultHeader": function() { return /* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_5__.default; },
/* harmony export */   "TopNavHeader": function() { return /* reexport safe */ _components_TopNavHeader__WEBPACK_IMPORTED_MODULE_6__.default; },
/* harmony export */   "DefaultFooter": function() { return /* reexport safe */ _Footer__WEBPACK_IMPORTED_MODULE_7__.default; },
/* harmony export */   "SettingDrawer": function() { return /* reexport safe */ _components_SettingDrawer__WEBPACK_IMPORTED_MODULE_8__.default; },
/* harmony export */   "getPageTitle": function() { return /* reexport safe */ _getPageTitle__WEBPACK_IMPORTED_MODULE_9__.default; },
/* harmony export */   "PageHeaderWrapper": function() { return /* binding */ PageHeaderWrapper; },
/* harmony export */   "getMenuData": function() { return /* reexport safe */ _utils_getMenuData__WEBPACK_IMPORTED_MODULE_10__.default; },
/* harmony export */   "PageContainer": function() { return /* reexport safe */ _components_PageContainer__WEBPACK_IMPORTED_MODULE_0__.default; },
/* harmony export */   "FooterToolbar": function() { return /* reexport safe */ _components_FooterToolbar__WEBPACK_IMPORTED_MODULE_11__.default; },
/* harmony export */   "WaterMark": function() { return /* reexport safe */ _components_WaterMark__WEBPACK_IMPORTED_MODULE_12__.default; },
/* harmony export */   "ProPageHeader": function() { return /* reexport safe */ _components_PageContainer__WEBPACK_IMPORTED_MODULE_0__.ProPageHeader; },
/* harmony export */   "ProBreadcrumb": function() { return /* reexport safe */ _components_PageContainer__WEBPACK_IMPORTED_MODULE_0__.ProBreadcrumb; }
/* harmony export */ });
/* harmony import */ var _BasicLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BasicLayout */ "./node_modules/@ant-design/pro-layout/es/BasicLayout.js");
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ "./node_modules/@ant-design/pro-layout/es/Header.js");
/* harmony import */ var _components_TopNavHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/TopNavHeader */ "./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js");
/* harmony import */ var _components_SettingDrawer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/SettingDrawer */ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.js");
/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Footer */ "./node_modules/@ant-design/pro-layout/es/Footer.js");
/* harmony import */ var _components_GridContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/GridContent */ "./node_modules/@ant-design/pro-layout/es/components/GridContent/index.js");
/* harmony import */ var _RouteContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RouteContext */ "./node_modules/@ant-design/pro-layout/es/RouteContext.js");
/* harmony import */ var _utils_getMenuData__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./utils/getMenuData */ "./node_modules/@ant-design/pro-layout/es/utils/getMenuData.js");
/* harmony import */ var _getPageTitle__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./getPageTitle */ "./node_modules/@ant-design/pro-layout/es/getPageTitle.js");
/* harmony import */ var _components_PageLoading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/PageLoading */ "./node_modules/@ant-design/pro-layout/es/components/PageLoading/index.js");
/* harmony import */ var _components_FooterToolbar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components/FooterToolbar */ "./node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.js");
/* harmony import */ var _components_WaterMark__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./components/WaterMark */ "./node_modules/@ant-design/pro-layout/es/components/WaterMark/index.js");
/* harmony import */ var _components_PageContainer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/PageContainer */ "./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js");













var PageHeaderWrapper = _components_PageContainer__WEBPACK_IMPORTED_MODULE_0__.default;

/* harmony default export */ __webpack_exports__["default"] = (_BasicLayout__WEBPACK_IMPORTED_MODULE_1__.default);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/en-US.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/en-US.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _en_US_settingDrawer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./en-US/settingDrawer */ "./node_modules/@ant-design/pro-layout/es/locales/en-US/settingDrawer.js");
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }


/* harmony default export */ __webpack_exports__["default"] = (_objectSpread({}, _en_US_settingDrawer__WEBPACK_IMPORTED_MODULE_0__.default));

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/en-US/settingDrawer.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/en-US/settingDrawer.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  'app.setting.pagestyle': 'Page style setting',
  'app.setting.pagestyle.dark': 'Dark style',
  'app.setting.pagestyle.light': 'Light style',
  'app.setting.content-width': 'Content Width',
  'app.setting.content-width.fixed': 'Fixed',
  'app.setting.content-width.fluid': 'Fluid',
  'app.setting.themecolor': 'Theme Color',
  'app.setting.themecolor.dust': 'Dust Red',
  'app.setting.themecolor.volcano': 'Volcano',
  'app.setting.themecolor.sunset': 'Sunset Orange',
  'app.setting.themecolor.cyan': 'Cyan',
  'app.setting.themecolor.green': 'Polar Green',
  'app.setting.themecolor.daybreak': 'Daybreak Blue (default)',
  'app.setting.themecolor.geekblue': 'Geek Blue',
  'app.setting.themecolor.purple': 'Golden Purple',
  'app.setting.navigationmode': 'Navigation Mode',
  'app.setting.regionalsettings': 'Regional Settings',
  'app.setting.regionalsettings.header': 'Header',
  'app.setting.regionalsettings.menu': 'Menu',
  'app.setting.regionalsettings.footer': 'Footer',
  'app.setting.regionalsettings.menuHeader': 'Menu Header',
  'app.setting.sidemenu': 'Side Menu Layout',
  'app.setting.topmenu': 'Top Menu Layout',
  'app.setting.mixmenu': 'Mix Menu Layout',
  'app.setting.splitMenus': 'Split Menus',
  'app.setting.fixedheader': 'Fixed Header',
  'app.setting.fixedsidebar': 'Fixed Sidebar',
  'app.setting.fixedsidebar.hint': 'Works on Side Menu Layout',
  'app.setting.hideheader': 'Hidden Header when scrolling',
  'app.setting.hideheader.hint': 'Works when Hidden Header is enabled',
  'app.setting.othersettings': 'Other Settings',
  'app.setting.weakmode': 'Weak Mode',
  'app.setting.copy': 'Copy Setting',
  'app.setting.loading': 'Loading theme',
  'app.setting.copyinfo': 'copy success，please replace defaultSettings in src/models/setting.js',
  'app.setting.production.hint': 'Setting panel shows in development environment only, please manually modify'
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/index.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getLanguage": function() { return /* binding */ getLanguage; }
/* harmony export */ });
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/isBrowser/index.js");
/* harmony import */ var _zh_CN__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./zh-CN */ "./node_modules/@ant-design/pro-layout/es/locales/zh-CN.js");
/* harmony import */ var _zh_TW__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./zh-TW */ "./node_modules/@ant-design/pro-layout/es/locales/zh-TW.js");
/* harmony import */ var _en_US__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./en-US */ "./node_modules/@ant-design/pro-layout/es/locales/en-US.js");
/* harmony import */ var _it_IT__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./it-IT */ "./node_modules/@ant-design/pro-layout/es/locales/it-IT.js");
/* harmony import */ var _ko_KR__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ko-KR */ "./node_modules/@ant-design/pro-layout/es/locales/ko-KR.js");






var locales = {
  'zh-CN': _zh_CN__WEBPACK_IMPORTED_MODULE_0__.default,
  'zh-TW': _zh_TW__WEBPACK_IMPORTED_MODULE_1__.default,
  'en-US': _en_US__WEBPACK_IMPORTED_MODULE_2__.default,
  'it-IT': _it_IT__WEBPACK_IMPORTED_MODULE_3__.default,
  'ko-KR': _ko_KR__WEBPACK_IMPORTED_MODULE_4__.default
};

var getLanguage = function getLanguage() {
  // support ssr
  if (!(0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_5__.default)()) return 'zh-CN';
  var lang = window.localStorage.getItem('umi_locale');
  return lang || window.g_locale || navigator.language;
};


/* harmony default export */ __webpack_exports__["default"] = (function () {
  var gLocale = getLanguage();

  if (locales[gLocale]) {
    return locales[gLocale];
  }

  return locales['zh-CN'];
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/it-IT.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/it-IT.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _it_IT_settingDrawer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./it-IT/settingDrawer */ "./node_modules/@ant-design/pro-layout/es/locales/it-IT/settingDrawer.js");
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }


/* harmony default export */ __webpack_exports__["default"] = (_objectSpread({}, _it_IT_settingDrawer__WEBPACK_IMPORTED_MODULE_0__.default));

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/it-IT/settingDrawer.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/it-IT/settingDrawer.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  'app.setting.pagestyle': 'Impostazioni di stile',
  'app.setting.pagestyle.dark': 'Tema scuro',
  'app.setting.pagestyle.light': 'Tema chiaro',
  'app.setting.content-width': 'Largezza contenuto',
  'app.setting.content-width.fixed': 'Fissa',
  'app.setting.content-width.fluid': 'Fluida',
  'app.setting.themecolor': 'Colore del tema',
  'app.setting.themecolor.dust': 'Rosso polvere',
  'app.setting.themecolor.volcano': 'Vulcano',
  'app.setting.themecolor.sunset': 'Arancione tramonto',
  'app.setting.themecolor.cyan': 'Ciano',
  'app.setting.themecolor.green': 'Verde polare',
  'app.setting.themecolor.daybreak': 'Blu cielo mattutino (default)',
  'app.setting.themecolor.geekblue': 'Blu geek',
  'app.setting.themecolor.purple': 'Viola dorato',
  'app.setting.navigationmode': 'Modalità di navigazione',
  'app.setting.sidemenu': 'Menu laterale',
  'app.setting.topmenu': 'Menu in testata',
  'app.setting.mixmenu': 'Menu misto',
  'app.setting.splitMenus': 'Menu divisi',
  'app.setting.fixedheader': 'Testata fissa',
  'app.setting.fixedsidebar': 'Menu laterale fisso',
  'app.setting.fixedsidebar.hint': 'Solo se selezionato Menu laterale',
  'app.setting.hideheader': 'Nascondi testata durante lo scorrimento',
  'app.setting.hideheader.hint': 'Solo se abilitato Nascondi testata durante lo scorrimento',
  'app.setting.othersettings': 'Altre impostazioni',
  'app.setting.weakmode': 'Inverti colori',
  'app.setting.copy': 'Copia impostazioni',
  'app.setting.loading': 'Carico tema...',
  'app.setting.copyinfo': 'Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js',
  'app.setting.production.hint': 'Questo pannello è visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente'
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/ko-KR.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/ko-KR.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _ko_KR_settingDrawer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ko-KR/settingDrawer */ "./node_modules/@ant-design/pro-layout/es/locales/ko-KR/settingDrawer.js");
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }


/* harmony default export */ __webpack_exports__["default"] = (_objectSpread({}, _ko_KR_settingDrawer__WEBPACK_IMPORTED_MODULE_0__.default));

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/ko-KR/settingDrawer.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/ko-KR/settingDrawer.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  'app.setting.pagestyle': '스타일 설정',
  'app.setting.pagestyle.dark': '다크 모드',
  'app.setting.pagestyle.light': '라이트 모드',
  'app.setting.content-width': '컨텐츠 너비',
  'app.setting.content-width.fixed': '고정',
  'app.setting.content-width.fluid': '흐름',
  'app.setting.themecolor': '테마 색상',
  'app.setting.themecolor.dust': 'Dust Red',
  'app.setting.themecolor.volcano': 'Volcano',
  'app.setting.themecolor.sunset': 'Sunset Orange',
  'app.setting.themecolor.cyan': 'Cyan',
  'app.setting.themecolor.green': 'Polar Green',
  'app.setting.themecolor.daybreak': 'Daybreak Blue (default)',
  'app.setting.themecolor.geekblue': 'Geek Blue',
  'app.setting.themecolor.purple': 'Golden Purple',
  'app.setting.navigationmode': '네비게이션 모드',
  'app.setting.regionalsettings': '영역별 설정',
  'app.setting.regionalsettings.header': '헤더',
  'app.setting.regionalsettings.menu': '메뉴',
  'app.setting.regionalsettings.footer': '바닥글',
  'app.setting.regionalsettings.menuHeader': '메뉴 헤더',
  'app.setting.sidemenu': '메뉴 사이드 배치',
  'app.setting.topmenu': '메뉴 상단 배치',
  'app.setting.mixmenu': '혼합형 배치',
  'app.setting.splitMenus': '메뉴 분리',
  'app.setting.fixedheader': '헤더 고정',
  'app.setting.fixedsidebar': '사이드바 고정',
  'app.setting.fixedsidebar.hint': "'메뉴 사이드 배치'를 선택했을 때 동작함",
  'app.setting.hideheader': '스크롤 중 헤더 감추기',
  'app.setting.hideheader.hint': "'헤더 감추기 옵션'을 선택했을 때 동작함",
  'app.setting.othersettings': '다른 설정',
  'app.setting.weakmode': '고대비 모드',
  'app.setting.copy': '설정값 복사',
  'app.setting.loading': '테마 로딩 중',
  'app.setting.copyinfo': '복사 성공. src/models/settings.js에 있는 defaultSettings를 교체해 주세요.',
  'app.setting.production.hint': '설정 판넬은 개발 환경에서만 보여집니다. 직접 수동으로 변경바랍니다.'
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/zh-CN.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/zh-CN.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _zh_CN_settingDrawer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./zh-CN/settingDrawer */ "./node_modules/@ant-design/pro-layout/es/locales/zh-CN/settingDrawer.js");
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }


/* harmony default export */ __webpack_exports__["default"] = (_objectSpread({}, _zh_CN_settingDrawer__WEBPACK_IMPORTED_MODULE_0__.default));

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/zh-CN/settingDrawer.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/zh-CN/settingDrawer.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  'app.setting.pagestyle': '整体风格设置',
  'app.setting.pagestyle.dark': '暗色菜单风格',
  'app.setting.pagestyle.light': '亮色菜单风格',
  'app.setting.content-width': '内容区域宽度',
  'app.setting.content-width.fixed': '定宽',
  'app.setting.content-width.fluid': '流式',
  'app.setting.themecolor': '主题色',
  'app.setting.themecolor.dust': '薄暮',
  'app.setting.themecolor.volcano': '火山',
  'app.setting.themecolor.sunset': '日暮',
  'app.setting.themecolor.cyan': '明青',
  'app.setting.themecolor.green': '极光绿',
  'app.setting.themecolor.daybreak': '拂晓蓝（默认）',
  'app.setting.themecolor.geekblue': '极客蓝',
  'app.setting.themecolor.purple': '酱紫',
  'app.setting.navigationmode': '导航模式',
  'app.setting.regionalsettings': '内容区域',
  'app.setting.regionalsettings.header': '顶栏',
  'app.setting.regionalsettings.menu': '菜单',
  'app.setting.regionalsettings.footer': '页脚',
  'app.setting.regionalsettings.menuHeader': '菜单头',
  'app.setting.sidemenu': '侧边菜单布局',
  'app.setting.topmenu': '顶部菜单布局',
  'app.setting.mixmenu': '混合菜单布局',
  'app.setting.splitMenus': '自动分割菜单',
  'app.setting.fixedheader': '固定 Header',
  'app.setting.fixedsidebar': '固定侧边菜单',
  'app.setting.fixedsidebar.hint': '侧边菜单布局时可配置',
  'app.setting.hideheader': '下滑时隐藏 Header',
  'app.setting.hideheader.hint': '固定 Header 时可配置',
  'app.setting.othersettings': '其他设置',
  'app.setting.weakmode': '色弱模式',
  'app.setting.copy': '拷贝设置',
  'app.setting.loading': '正在加载主题',
  'app.setting.copyinfo': '拷贝成功，请到 src/defaultSettings.js 中替换默认配置',
  'app.setting.production.hint': '配置栏只在开发环境用于预览，生产环境不会展现，请拷贝后手动修改配置文件'
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/zh-TW.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/zh-TW.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _zh_TW_settingDrawer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./zh-TW/settingDrawer */ "./node_modules/@ant-design/pro-layout/es/locales/zh-TW/settingDrawer.js");
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }


/* harmony default export */ __webpack_exports__["default"] = (_objectSpread({}, _zh_TW_settingDrawer__WEBPACK_IMPORTED_MODULE_0__.default));

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/locales/zh-TW/settingDrawer.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/locales/zh-TW/settingDrawer.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  'app.setting.pagestyle': '整體風格設置',
  'app.setting.pagestyle.dark': '暗色菜單風格',
  'app.setting.pagestyle.light': '亮色菜單風格',
  'app.setting.content-width': '內容區域寬度',
  'app.setting.content-width.fixed': '定寬',
  'app.setting.content-width.fluid': '流式',
  'app.setting.themecolor': '主題色',
  'app.setting.themecolor.dust': '薄暮',
  'app.setting.themecolor.volcano': '火山',
  'app.setting.themecolor.sunset': '日暮',
  'app.setting.themecolor.cyan': '明青',
  'app.setting.themecolor.green': '極光綠',
  'app.setting.themecolor.daybreak': '拂曉藍（默認）',
  'app.setting.themecolor.geekblue': '極客藍',
  'app.setting.themecolor.purple': '醬紫',
  'app.setting.navigationmode': '導航模式',
  'app.setting.sidemenu': '側邊菜單布局',
  'app.setting.topmenu': '頂部菜單布局',
  'app.setting.mixmenu': '混合菜單布局',
  'app.setting.splitMenus': '自动分割菜单',
  'app.setting.fixedheader': '固定 Header',
  'app.setting.fixedsidebar': '固定側邊菜單',
  'app.setting.fixedsidebar.hint': '側邊菜單布局時可配置',
  'app.setting.hideheader': '下滑時隱藏 Header',
  'app.setting.hideheader.hint': '固定 Header 時可配置',
  'app.setting.othersettings': '其他設置',
  'app.setting.weakmode': '色弱模式',
  'app.setting.copy': '拷貝設置',
  'app.setting.loading': '正在加載主題',
  'app.setting.copyinfo': '拷貝成功，請到 src/defaultSettings.js 中替換默認配置',
  'app.setting.production.hint': '配置欄只在開發環境用於預覽，生產環境不會展現，請拷貝後手動修改配置文件'
});

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/utils/compatibleLayout.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/utils/compatibleLayout.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
var compatibleLayout = function compatibleLayout(layout) {
  var layoutEnum = ['sidemenu', 'topmenu'];

  if (layoutEnum.includes(layout)) {
    return layout === null || layout === void 0 ? void 0 : layout.replace('menu', '');
  }

  return layout;
};

/* harmony default export */ __webpack_exports__["default"] = (compatibleLayout);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/utils/getBreadcrumbProps.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/utils/getBreadcrumbProps.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getBreadcrumb": function() { return /* binding */ getBreadcrumb; },
/* harmony export */   "getBreadcrumbFromProps": function() { return /* binding */ getBreadcrumbFromProps; },
/* harmony export */   "genBreadcrumbProps": function() { return /* binding */ genBreadcrumbProps; },
/* harmony export */   "getBreadcrumbProps": function() { return /* binding */ getBreadcrumbProps; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var path_to_regexp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path-to-regexp */ "./node_modules/@ant-design/pro-layout/node_modules/path-to-regexp/index.js");
/* harmony import */ var path_to_regexp__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path_to_regexp__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _pathTools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./pathTools */ "./node_modules/@ant-design/pro-layout/es/utils/pathTools.js");


 // 渲染Breadcrumb 子节点
// Render the Breadcrumb child node

var defaultItemRender = function defaultItemRender(_ref) {
  var breadcrumbName = _ref.breadcrumbName,
      path = _ref.path;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("a", {
    href: path
  }, breadcrumbName);
};

var renderItemLocal = function renderItemLocal(item, props) {
  var formatMessage = props.formatMessage,
      menu = props.menu;

  if (item.locale && formatMessage && (menu === null || menu === void 0 ? void 0 : menu.locale) !== false) {
    return formatMessage({
      id: item.locale,
      defaultMessage: item.name
    });
  }

  return item.name;
};

var getBreadcrumb = function getBreadcrumb(breadcrumbMap, url) {
  var breadcrumbItem = breadcrumbMap.get(url);

  if (!breadcrumbItem) {
    // Find the first matching path in the order defined by route config
    // 按照 route config 定义的顺序找到第一个匹配的路径
    var keys = Array.from(breadcrumbMap.keys()) || [];
    var targetPath = keys.find(function (path) {
      return (// remove ? ,不然会重复
        path_to_regexp__WEBPACK_IMPORTED_MODULE_1___default()(path.replace('?', '')).test(url)
      );
    });
    if (targetPath) breadcrumbItem = breadcrumbMap.get(targetPath);
  }

  return breadcrumbItem || {
    path: ''
  };
};
var getBreadcrumbFromProps = function getBreadcrumbFromProps(props) {
  var location = props.location,
      breadcrumbMap = props.breadcrumbMap;
  return {
    location: location,
    breadcrumbMap: breadcrumbMap
  };
};

var conversionFromLocation = function conversionFromLocation(routerLocation, breadcrumbMap, props) {
  // Convertor the url to an array
  var pathSnippets = (0,_pathTools__WEBPACK_IMPORTED_MODULE_2__.urlToList)(routerLocation === null || routerLocation === void 0 ? void 0 : routerLocation.pathname); // Loop data mosaic routing

  var extraBreadcrumbItems = pathSnippets.map(function (url) {
    var currentBreadcrumb = getBreadcrumb(breadcrumbMap, url);
    var name = renderItemLocal(currentBreadcrumb, props);
    var hideInBreadcrumb = currentBreadcrumb.hideInBreadcrumb;
    return name && !hideInBreadcrumb ? {
      path: url,
      breadcrumbName: name,
      component: currentBreadcrumb.component
    } : {
      path: '',
      breadcrumbName: ''
    };
  }).filter(function (item) {
    return item && item.path;
  });
  return extraBreadcrumbItems;
};
/** 将参数转化为面包屑 Convert parameters into breadcrumbs */


var genBreadcrumbProps = function genBreadcrumbProps(props) {
  var _getBreadcrumbFromPro = getBreadcrumbFromProps(props),
      location = _getBreadcrumbFromPro.location,
      breadcrumbMap = _getBreadcrumbFromPro.breadcrumbMap; // 根据 location 生成 面包屑
  // Generate breadcrumbs based on location


  if (location && location.pathname && breadcrumbMap) {
    return conversionFromLocation(location, breadcrumbMap, props);
  }

  return [];
}; // use breadcrumbRender to change routes

var getBreadcrumbProps = function getBreadcrumbProps(props, layoutPros) {
  var breadcrumbRender = props.breadcrumbRender,
      propsItemRender = props.itemRender;

  var _ref2 = layoutPros.breadcrumbProps || {},
      _ref2$minLength = _ref2.minLength,
      minLength = _ref2$minLength === void 0 ? 2 : _ref2$minLength;

  var routesArray = genBreadcrumbProps(props);
  var itemRender = propsItemRender || defaultItemRender;
  var routes = routesArray; // if routes.length =1, don't show it

  if (breadcrumbRender) {
    routes = breadcrumbRender(routes) || [];
  }

  if (routes && routes.length < minLength || breadcrumbRender === false) {
    routes = undefined;
  }

  return {
    routes: routes,
    itemRender: itemRender
  };
};

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/utils/getMenuData.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/utils/getMenuData.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _umijs_route_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @umijs/route-utils */ "./node_modules/@umijs/route-utils/es/index.js");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }



function fromEntries(iterable) {
  return _toConsumableArray(iterable).reduce(function (obj, _ref) {
    var _ref2 = _slicedToArray(_ref, 2),
        key = _ref2[0],
        val = _ref2[1];

    // eslint-disable-next-line no-param-reassign
    obj[key] = val;
    return obj;
  }, {});
}

var getMenuData = function getMenuData(routes, menu, formatMessage, menuDataRender) {
  var _transformRoute = (0,_umijs_route_utils__WEBPACK_IMPORTED_MODULE_0__.transformRoute)(routes, (menu === null || menu === void 0 ? void 0 : menu.locale) || false, formatMessage, true),
      menuData = _transformRoute.menuData,
      breadcrumb = _transformRoute.breadcrumb;

  if (!menuDataRender) {
    return {
      breadcrumb: fromEntries(breadcrumb),
      breadcrumbMap: breadcrumb,
      menuData: menuData
    };
  }

  return getMenuData(menuDataRender(menuData), menu, formatMessage, undefined);
};

/* harmony default export */ __webpack_exports__["default"] = (getMenuData);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/utils/pathTools.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/utils/pathTools.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "urlToList": function() { return /* binding */ urlToList; }
/* harmony export */ });
// /userInfo/2144/id => ['/userInfo','/userInfo/2144,'/userInfo/2144/id']
function urlToList(url) {
  if (!url || url === '/') {
    return ['/'];
  }

  var urlList = url.split('/').filter(function (i) {
    return i;
  });
  return urlList.map(function (urlItem, index) {
    return "/".concat(urlList.slice(0, index + 1).join('/'));
  });
}

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/utils/useCurrentMenuLayoutProps.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/utils/useCurrentMenuLayoutProps.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ant-design/pro-utils */ "./node_modules/@ant-design/pro-utils/es/omitUndefined/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }




var useCurrentMenuLayoutProps = function useCurrentMenuLayoutProps(currentMenu) {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}),
      _useState2 = _slicedToArray(_useState, 2),
      currentMenuLayoutProps = _useState2[0],
      setCurrentMenuLayoutProps = _useState2[1];

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    setCurrentMenuLayoutProps((0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_1__.default)({
      // 有时候会变成对象，是原来的方式
      layout: _typeof(currentMenu.layout) !== 'object' ? currentMenu.layout : undefined,
      navTheme: currentMenu.navTheme,
      menuRender: currentMenu.menuRender,
      footerRender: currentMenu.footerRender,
      menuHeaderRender: currentMenu.menuHeaderRender,
      headerRender: currentMenu.headerRender,
      fixSiderbar: currentMenu.fixSiderbar,
      headerTheme: currentMenu.headerTheme
    }));
  }, [currentMenu.layout, currentMenu.navTheme, currentMenu.menuRender, currentMenu.footerRender, currentMenu.menuHeaderRender, currentMenu.headerRender, currentMenu.fixSiderbar, currentMenu.headerTheme]);
  return currentMenuLayoutProps;
};

/* harmony default export */ __webpack_exports__["default"] = (useCurrentMenuLayoutProps);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/utils/utils.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/utils/utils.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getOpenKeysFromMenuData": function() { return /* binding */ getOpenKeysFromMenuData; },
/* harmony export */   "genThemeToString": function() { return /* binding */ genThemeToString; },
/* harmony export */   "genStringToTheme": function() { return /* binding */ genStringToTheme; },
/* harmony export */   "clearMenuItem": function() { return /* binding */ clearMenuItem; }
/* harmony export */ });
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

var getOpenKeysFromMenuData = function getOpenKeysFromMenuData(menuData) {
  return (menuData || []).reduce(function (pre, item) {
    if (item.key) {
      pre.push(item.key);
    }

    if (item.routes) {
      var newArray = pre.concat(getOpenKeysFromMenuData(item.routes) || []);
      return newArray;
    }

    return pre;
  }, []);
};
var themeConfig = {
  daybreak: 'daybreak',
  '#1890ff': 'daybreak',
  '#F5222D': 'dust',
  '#FA541C': 'volcano',
  '#FAAD14': 'sunset',
  '#13C2C2': 'cyan',
  '#52C41A': 'green',
  '#2F54EB': 'geekblue',
  '#722ED1': 'purple'
}; // eslint-disable-next-line @typescript-eslint/ban-types

var invertKeyValues = function invertKeyValues(obj) {
  return Object.keys(obj).reduce(function (acc, key) {
    acc[obj[key]] = key;
    return acc;
  }, {});
};
/**
 * #1890ff -> daybreak
 *
 * @param val
 */


function genThemeToString(val) {
  return val && themeConfig[val] ? themeConfig[val] : undefined;
}
/**
 * Daybreak-> #1890ff
 *
 * @param val
 */

function genStringToTheme(val) {
  var stringConfig = invertKeyValues(themeConfig);
  return val && stringConfig[val] ? stringConfig[val] : val;
}
function clearMenuItem(menusData) {
  return menusData.map(function (item) {
    var finalItem = _objectSpread({}, item);

    if (!finalItem.name || finalItem.hideInMenu) {
      return null;
    }

    if (finalItem && (finalItem === null || finalItem === void 0 ? void 0 : finalItem.routes)) {
      if (!finalItem.hideChildrenInMenu && finalItem.routes.some(function (child) {
        return child && child.name && !child.hideInMenu;
      })) {
        return _objectSpread(_objectSpread({}, item), {}, {
          routes: clearMenuItem(finalItem.routes)
        });
      } // children 为空就直接删掉


      delete finalItem.routes;
    }

    return finalItem;
  }).filter(function (item) {
    return item;
  });
}

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/node_modules/path-to-regexp/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/node_modules/path-to-regexp/index.js ***!
  \**********************************************************************************/
/***/ (function(module) {

/**
 * Expose `pathToRegexp`.
 */
module.exports = pathToRegexp
module.exports.parse = parse
module.exports.compile = compile
module.exports.tokensToFunction = tokensToFunction
module.exports.tokensToRegExp = tokensToRegExp

/**
 * Default configs.
 */
var DEFAULT_DELIMITER = '/'
var DEFAULT_DELIMITERS = './'

/**
 * The main path matching regexp utility.
 *
 * @type {RegExp}
 */
var PATH_REGEXP = new RegExp([
  // Match escaped characters that would otherwise appear in future matches.
  // This allows the user to escape special characters that won't transform.
  '(\\\\.)',
  // Match Express-style parameters and un-named parameters with a prefix
  // and optional suffixes. Matches appear as:
  //
  // ":test(\\d+)?" => ["test", "\d+", undefined, "?"]
  // "(\\d+)"  => [undefined, undefined, "\d+", undefined]
  '(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?'
].join('|'), 'g')

/**
 * Parse a string for the raw tokens.
 *
 * @param  {string}  str
 * @param  {Object=} options
 * @return {!Array}
 */
function parse (str, options) {
  var tokens = []
  var key = 0
  var index = 0
  var path = ''
  var defaultDelimiter = (options && options.delimiter) || DEFAULT_DELIMITER
  var delimiters = (options && options.delimiters) || DEFAULT_DELIMITERS
  var pathEscaped = false
  var res

  while ((res = PATH_REGEXP.exec(str)) !== null) {
    var m = res[0]
    var escaped = res[1]
    var offset = res.index
    path += str.slice(index, offset)
    index = offset + m.length

    // Ignore already escaped sequences.
    if (escaped) {
      path += escaped[1]
      pathEscaped = true
      continue
    }

    var prev = ''
    var next = str[index]
    var name = res[2]
    var capture = res[3]
    var group = res[4]
    var modifier = res[5]

    if (!pathEscaped && path.length) {
      var k = path.length - 1

      if (delimiters.indexOf(path[k]) > -1) {
        prev = path[k]
        path = path.slice(0, k)
      }
    }

    // Push the current path onto the tokens.
    if (path) {
      tokens.push(path)
      path = ''
      pathEscaped = false
    }

    var partial = prev !== '' && next !== undefined && next !== prev
    var repeat = modifier === '+' || modifier === '*'
    var optional = modifier === '?' || modifier === '*'
    var delimiter = prev || defaultDelimiter
    var pattern = capture || group

    tokens.push({
      name: name || key++,
      prefix: prev,
      delimiter: delimiter,
      optional: optional,
      repeat: repeat,
      partial: partial,
      pattern: pattern ? escapeGroup(pattern) : '[^' + escapeString(delimiter) + ']+?'
    })
  }

  // Push any remaining characters.
  if (path || index < str.length) {
    tokens.push(path + str.substr(index))
  }

  return tokens
}

/**
 * Compile a string to a template function for the path.
 *
 * @param  {string}             str
 * @param  {Object=}            options
 * @return {!function(Object=, Object=)}
 */
function compile (str, options) {
  return tokensToFunction(parse(str, options))
}

/**
 * Expose a method for transforming tokens into the path function.
 */
function tokensToFunction (tokens) {
  // Compile all the tokens into regexps.
  var matches = new Array(tokens.length)

  // Compile all the patterns before compilation.
  for (var i = 0; i < tokens.length; i++) {
    if (typeof tokens[i] === 'object') {
      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$')
    }
  }

  return function (data, options) {
    var path = ''
    var encode = (options && options.encode) || encodeURIComponent

    for (var i = 0; i < tokens.length; i++) {
      var token = tokens[i]

      if (typeof token === 'string') {
        path += token
        continue
      }

      var value = data ? data[token.name] : undefined
      var segment

      if (Array.isArray(value)) {
        if (!token.repeat) {
          throw new TypeError('Expected "' + token.name + '" to not repeat, but got array')
        }

        if (value.length === 0) {
          if (token.optional) continue

          throw new TypeError('Expected "' + token.name + '" to not be empty')
        }

        for (var j = 0; j < value.length; j++) {
          segment = encode(value[j], token)

          if (!matches[i].test(segment)) {
            throw new TypeError('Expected all "' + token.name + '" to match "' + token.pattern + '"')
          }

          path += (j === 0 ? token.prefix : token.delimiter) + segment
        }

        continue
      }

      if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        segment = encode(String(value), token)

        if (!matches[i].test(segment)) {
          throw new TypeError('Expected "' + token.name + '" to match "' + token.pattern + '", but got "' + segment + '"')
        }

        path += token.prefix + segment
        continue
      }

      if (token.optional) {
        // Prepend partial segment prefixes.
        if (token.partial) path += token.prefix

        continue
      }

      throw new TypeError('Expected "' + token.name + '" to be ' + (token.repeat ? 'an array' : 'a string'))
    }

    return path
  }
}

/**
 * Escape a regular expression string.
 *
 * @param  {string} str
 * @return {string}
 */
function escapeString (str) {
  return str.replace(/([.+*?=^!:${}()[\]|/\\])/g, '\\$1')
}

/**
 * Escape the capturing group by escaping special characters and meaning.
 *
 * @param  {string} group
 * @return {string}
 */
function escapeGroup (group) {
  return group.replace(/([=!:$/()])/g, '\\$1')
}

/**
 * Get the flags for a regexp from the options.
 *
 * @param  {Object} options
 * @return {string}
 */
function flags (options) {
  return options && options.sensitive ? '' : 'i'
}

/**
 * Pull out keys from a regexp.
 *
 * @param  {!RegExp} path
 * @param  {Array=}  keys
 * @return {!RegExp}
 */
function regexpToRegexp (path, keys) {
  if (!keys) return path

  // Use a negative lookahead to match only capturing groups.
  var groups = path.source.match(/\((?!\?)/g)

  if (groups) {
    for (var i = 0; i < groups.length; i++) {
      keys.push({
        name: i,
        prefix: null,
        delimiter: null,
        optional: false,
        repeat: false,
        partial: false,
        pattern: null
      })
    }
  }

  return path
}

/**
 * Transform an array into a regexp.
 *
 * @param  {!Array}  path
 * @param  {Array=}  keys
 * @param  {Object=} options
 * @return {!RegExp}
 */
function arrayToRegexp (path, keys, options) {
  var parts = []

  for (var i = 0; i < path.length; i++) {
    parts.push(pathToRegexp(path[i], keys, options).source)
  }

  return new RegExp('(?:' + parts.join('|') + ')', flags(options))
}

/**
 * Create a path regexp from string input.
 *
 * @param  {string}  path
 * @param  {Array=}  keys
 * @param  {Object=} options
 * @return {!RegExp}
 */
function stringToRegexp (path, keys, options) {
  return tokensToRegExp(parse(path, options), keys, options)
}

/**
 * Expose a function for taking tokens and returning a RegExp.
 *
 * @param  {!Array}  tokens
 * @param  {Array=}  keys
 * @param  {Object=} options
 * @return {!RegExp}
 */
function tokensToRegExp (tokens, keys, options) {
  options = options || {}

  var strict = options.strict
  var start = options.start !== false
  var end = options.end !== false
  var delimiter = escapeString(options.delimiter || DEFAULT_DELIMITER)
  var delimiters = options.delimiters || DEFAULT_DELIMITERS
  var endsWith = [].concat(options.endsWith || []).map(escapeString).concat('$').join('|')
  var route = start ? '^' : ''
  var isEndDelimited = tokens.length === 0

  // Iterate over the tokens and create our regexp string.
  for (var i = 0; i < tokens.length; i++) {
    var token = tokens[i]

    if (typeof token === 'string') {
      route += escapeString(token)
      isEndDelimited = i === tokens.length - 1 && delimiters.indexOf(token[token.length - 1]) > -1
    } else {
      var capture = token.repeat
        ? '(?:' + token.pattern + ')(?:' + escapeString(token.delimiter) + '(?:' + token.pattern + '))*'
        : token.pattern

      if (keys) keys.push(token)

      if (token.optional) {
        if (token.partial) {
          route += escapeString(token.prefix) + '(' + capture + ')?'
        } else {
          route += '(?:' + escapeString(token.prefix) + '(' + capture + '))?'
        }
      } else {
        route += escapeString(token.prefix) + '(' + capture + ')'
      }
    }
  }

  if (end) {
    if (!strict) route += '(?:' + delimiter + ')?'

    route += endsWith === '$' ? '$' : '(?=' + endsWith + ')'
  } else {
    if (!strict) route += '(?:' + delimiter + '(?=' + endsWith + '))?'
    if (!isEndDelimited) route += '(?=' + delimiter + '|' + endsWith + ')'
  }

  return new RegExp(route, flags(options))
}

/**
 * Normalize the given path string, returning a regular expression.
 *
 * An empty array can be passed in for the keys, which will hold the
 * placeholder key descriptions. For example, using `/user/:id`, `keys` will
 * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.
 *
 * @param  {(string|RegExp|Array)} path
 * @param  {Array=}                keys
 * @param  {Object=}               options
 * @return {!RegExp}
 */
function pathToRegexp (path, keys, options) {
  if (path instanceof RegExp) {
    return regexpToRegexp(path, keys)
  }

  if (Array.isArray(path)) {
    return arrayToRegexp(/** @type {!Array} */ (path), keys, options)
  }

  return stringToRegexp(/** @type {string} */ (path), keys, options)
}


/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/node_modules/rc-resize-observer/es/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/node_modules/rc-resize-observer/es/index.js ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ "./node_modules/@babel/runtime/helpers/esm/classCallCheck.js");
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ "./node_modules/@babel/runtime/helpers/esm/createClass.js");
/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ "./node_modules/@babel/runtime/helpers/esm/inherits.js");
/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ "./node_modules/@babel/runtime/helpers/esm/createSuper.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ "./node_modules/rc-util/es/Dom/findDOMNode.js");
/* harmony import */ var rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/Children/toArray */ "./node_modules/rc-util/es/Children/toArray.js");
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/rc-util/es/warning.js");
/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/ref */ "./node_modules/rc-util/es/ref.js");
/* harmony import */ var resize_observer_polyfill__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! resize-observer-polyfill */ "./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js");











var INTERNAL_PREFIX_KEY = 'rc-observer-key'; // Still need to be compatible with React 15, we use class component here

var ReactResizeObserver = /*#__PURE__*/function (_React$Component) {
  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_3__.default)(ReactResizeObserver, _React$Component);

  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_4__.default)(ReactResizeObserver);

  function ReactResizeObserver() {
    var _this;

    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_1__.default)(this, ReactResizeObserver);

    _this = _super.apply(this, arguments);
    _this.resizeObserver = null;
    _this.childNode = null;
    _this.currentElement = null;
    _this.state = {
      width: 0,
      height: 0,
      offsetHeight: 0,
      offsetWidth: 0
    };

    _this.onResize = function (entries) {
      var onResize = _this.props.onResize;
      var target = entries[0].target;

      var _target$getBoundingCl = target.getBoundingClientRect(),
          width = _target$getBoundingCl.width,
          height = _target$getBoundingCl.height;

      var offsetWidth = target.offsetWidth,
          offsetHeight = target.offsetHeight;
      /**
       * Resize observer trigger when content size changed.
       * In most case we just care about element size,
       * let's use `boundary` instead of `contentRect` here to avoid shaking.
       */

      var fixedWidth = Math.floor(width);
      var fixedHeight = Math.floor(height);

      if (_this.state.width !== fixedWidth || _this.state.height !== fixedHeight || _this.state.offsetWidth !== offsetWidth || _this.state.offsetHeight !== offsetHeight) {
        var size = {
          width: fixedWidth,
          height: fixedHeight,
          offsetWidth: offsetWidth,
          offsetHeight: offsetHeight
        };

        _this.setState(size);

        if (onResize) {
          // defer the callback but not defer to next frame
          Promise.resolve().then(function () {
            onResize((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)({}, size), {}, {
              offsetWidth: offsetWidth,
              offsetHeight: offsetHeight
            }));
          });
        }
      }
    };

    _this.setChildNode = function (node) {
      _this.childNode = node;
    };

    return _this;
  }

  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_2__.default)(ReactResizeObserver, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      this.onComponentUpdated();
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate() {
      this.onComponentUpdated();
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      this.destroyObserver();
    }
  }, {
    key: "onComponentUpdated",
    value: function onComponentUpdated() {
      var disabled = this.props.disabled; // Unregister if disabled

      if (disabled) {
        this.destroyObserver();
        return;
      } // Unregister if element changed


      var element = (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_6__.default)(this.childNode || this);
      var elementChanged = element !== this.currentElement;

      if (elementChanged) {
        this.destroyObserver();
        this.currentElement = element;
      }

      if (!this.resizeObserver && element) {
        this.resizeObserver = new resize_observer_polyfill__WEBPACK_IMPORTED_MODULE_10__.default(this.onResize);
        this.resizeObserver.observe(element);
      }
    }
  }, {
    key: "destroyObserver",
    value: function destroyObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
    }
  }, {
    key: "render",
    value: function render() {
      var children = this.props.children;
      var childNodes = (0,rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_7__.default)(children);

      if (childNodes.length > 1) {
        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__.default)(false, 'Find more than one child node with `children` in ResizeObserver. Will only observe first one.');
      } else if (childNodes.length === 0) {
        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__.default)(false, '`children` of ResizeObserver is empty. Nothing is in observe.');
        return null;
      }

      var childNode = childNodes[0];

      if (react__WEBPACK_IMPORTED_MODULE_5__.isValidElement(childNode) && (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_9__.supportRef)(childNode)) {
        var ref = childNode.ref;
        childNodes[0] = react__WEBPACK_IMPORTED_MODULE_5__.cloneElement(childNode, {
          ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_9__.composeRef)(ref, this.setChildNode)
        });
      }

      return childNodes.length === 1 ? childNodes[0] : childNodes.map(function (node, index) {
        if (!react__WEBPACK_IMPORTED_MODULE_5__.isValidElement(node) || 'key' in node && node.key !== null) {
          return node;
        }

        return react__WEBPACK_IMPORTED_MODULE_5__.cloneElement(node, {
          key: "".concat(INTERNAL_PREFIX_KEY, "-").concat(index)
        });
      });
    }
  }]);

  return ReactResizeObserver;
}(react__WEBPACK_IMPORTED_MODULE_5__.Component);

ReactResizeObserver.displayName = 'ResizeObserver';
/* harmony default export */ __webpack_exports__["default"] = (ReactResizeObserver);

/***/ }),

/***/ "./node_modules/@ant-design/pro-utils/es/hooks/useDocumentTitle/index.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-utils/es/hooks/useDocumentTitle/index.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _isBrowser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../isBrowser */ "./node_modules/@ant-design/pro-utils/es/isBrowser/index.js");



function useDocumentTitle(titleInfo, appDefaultTitle) {
  var titleText = typeof titleInfo.pageName === 'string' ? titleInfo.title : appDefaultTitle;
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if ((0,_isBrowser__WEBPACK_IMPORTED_MODULE_1__.default)() && titleText) {
      document.title = titleText;
    }
  }, [titleInfo.title, titleText]);
}

/* harmony default export */ __webpack_exports__["default"] = (useDocumentTitle);

/***/ }),

/***/ "./node_modules/@ant-design/pro-utils/es/isImg/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@ant-design/pro-utils/es/isImg/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/** 判断是否是图片链接 */
function isImg(path) {
  return /\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(path);
}

/* harmony default export */ __webpack_exports__["default"] = (isImg);

/***/ }),

/***/ "./node_modules/@ant-design/pro-utils/es/isUrl/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@ant-design/pro-utils/es/isUrl/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
var isUrl = function isUrl(path) {
  if (!path.startsWith('http')) {
    return false;
  }

  try {
    var url = new URL(path);
    return !!url;
  } catch (error) {
    return false;
  }
};

/* harmony default export */ __webpack_exports__["default"] = (isUrl);

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/BasicLayout.less":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/BasicLayout.less ***!
  \*****************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/Header.less":
/*!************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/Header.less ***!
  \************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.less":
/*!************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/FooterToolbar/index.less ***!
  \************************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.less":
/*!***********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.less ***!
  \***********************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.less":
/*!***********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.less ***!
  \***********************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/GridContent/GridContent.less":
/*!****************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/GridContent/GridContent.less ***!
  \****************************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.less":
/*!************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.less ***!
  \************************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/ThemeColor.less":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/ThemeColor.less ***!
  \*****************************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.less":
/*!************************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SettingDrawer/index.less ***!
  \************************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.less":
/*!********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.less ***!
  \********************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.less":
/*!***********************************************************************************!*\
  !*** ./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.less ***!
  \***********************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/affix/style/index.less":
/*!*****************************************************!*\
  !*** ./node_modules/antd/es/affix/style/index.less ***!
  \*****************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/breadcrumb/style/index.less":
/*!**********************************************************!*\
  !*** ./node_modules/antd/es/breadcrumb/style/index.less ***!
  \**********************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/layout/style/index.less":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/layout/style/index.less ***!
  \******************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/list/style/index.less":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/list/style/index.less ***!
  \****************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/page-header/style/index.less":
/*!***********************************************************!*\
  !*** ./node_modules/antd/es/page-header/style/index.less ***!
  \***********************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/skeleton/style/index.less":
/*!********************************************************!*\
  !*** ./node_modules/antd/es/skeleton/style/index.less ***!
  \********************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ "./node_modules/antd/es/_util/getDataOrAriaProps.js":
/*!**********************************************************!*\
  !*** ./node_modules/antd/es/_util/getDataOrAriaProps.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ getDataOrAriaProps; }
/* harmony export */ });
function getDataOrAriaProps(props) {
  return Object.keys(props).reduce(function (prev, key) {
    if ((key.substr(0, 5) === 'data-' || key.substr(0, 5) === 'aria-' || key === 'role') && key.substr(0, 7) !== 'data-__') {
      prev[key] = props[key];
    }

    return prev;
  }, {});
}

/***/ }),

/***/ "./node_modules/antd/es/_util/hooks/useFlexGapSupport.js":
/*!***************************************************************!*\
  !*** ./node_modules/antd/es/_util/hooks/useFlexGapSupport.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _styleChecker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styleChecker */ "./node_modules/antd/es/_util/styleChecker.js");



/* harmony default export */ __webpack_exports__["default"] = (function () {
  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1__.useState(false),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_React$useState, 2),
      flexible = _React$useState2[0],
      setFlexible = _React$useState2[1];

  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {
    setFlexible((0,_styleChecker__WEBPACK_IMPORTED_MODULE_2__.detectFlexGapSupported)());
  }, []);
  return flexible;
});

/***/ }),

/***/ "./node_modules/antd/es/_util/styleChecker.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/_util/styleChecker.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "canUseDocElement": function() { return /* binding */ canUseDocElement; },
/* harmony export */   "isStyleSupport": function() { return /* binding */ isStyleSupport; },
/* harmony export */   "detectFlexGapSupported": function() { return /* binding */ detectFlexGapSupported; }
/* harmony export */ });
/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ "./node_modules/antd/node_modules/rc-util/es/Dom/canUseDom.js");

var canUseDocElement = function canUseDocElement() {
  return (0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__.default)() && window.document.documentElement;
};
var isStyleSupport = function isStyleSupport(styleName) {
  if (canUseDocElement()) {
    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];
    var documentElement = window.document.documentElement;
    return styleNameList.some(function (name) {
      return name in documentElement.style;
    });
  }

  return false;
};
var flexGapSupported;
var detectFlexGapSupported = function detectFlexGapSupported() {
  if (!canUseDocElement()) {
    return false;
  }

  if (flexGapSupported !== undefined) {
    return flexGapSupported;
  } // create flex container with row-gap set


  var flex = document.createElement('div');
  flex.style.display = 'flex';
  flex.style.flexDirection = 'column';
  flex.style.rowGap = '1px'; // create two, elements inside it

  flex.appendChild(document.createElement('div'));
  flex.appendChild(document.createElement('div')); // append to the DOM (needed to obtain scrollHeight)

  document.body.appendChild(flex);
  flexGapSupported = flex.scrollHeight === 1; // flex container should be 1px high from the row-gap

  document.body.removeChild(flex);
  return flexGapSupported;
};

/***/ }),

/***/ "./node_modules/antd/es/_util/transButton.js":
/*!***************************************************!*\
  !*** ./node_modules/antd/es/_util/transButton.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/KeyCode */ "./node_modules/antd/node_modules/rc-util/es/KeyCode.js");


var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
/**
 * Wrap of sub component which need use as Button capacity (like Icon component).
 *
 * This helps accessibility reader to tread as a interactive button to operation.
 */




var inlineStyle = {
  border: 0,
  background: 'transparent',
  padding: 0,
  lineHeight: 'inherit',
  display: 'inline-block'
};
var TransButton = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {
  var onKeyDown = function onKeyDown(event) {
    var keyCode = event.keyCode;

    if (keyCode === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__.default.ENTER) {
      event.preventDefault();
    }
  };

  var onKeyUp = function onKeyUp(event) {
    var keyCode = event.keyCode;
    var onClick = props.onClick;

    if (keyCode === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__.default.ENTER && onClick) {
      onClick();
    }
  };

  var style = props.style,
      noStyle = props.noStyle,
      disabled = props.disabled,
      restProps = __rest(props, ["style", "noStyle", "disabled"]);

  var mergedStyle = {};

  if (!noStyle) {
    mergedStyle = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, inlineStyle);
  }

  if (disabled) {
    mergedStyle.pointerEvents = 'none';
  }

  mergedStyle = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, mergedStyle), style);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    role: "button",
    tabIndex: 0,
    ref: ref
  }, restProps, {
    onKeyDown: onKeyDown,
    onKeyUp: onKeyUp,
    style: mergedStyle
  }));
});
/* harmony default export */ __webpack_exports__["default"] = (TransButton);

/***/ }),

/***/ "./node_modules/antd/es/affix/style/index.js":
/*!***************************************************!*\
  !*** ./node_modules/antd/es/affix/style/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/affix/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/breadcrumb/style/index.js":
/*!********************************************************!*\
  !*** ./node_modules/antd/es/breadcrumb/style/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/breadcrumb/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _menu_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../menu/style */ "./node_modules/antd/es/menu/style/index.js");
/* harmony import */ var _dropdown_style__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../dropdown/style */ "./node_modules/antd/es/dropdown/style/index.js");

 // style dependencies




/***/ }),

/***/ "./node_modules/antd/es/divider/index.js":
/*!***********************************************!*\
  !*** ./node_modules/antd/es/divider/index.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");



var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};





var Divider = function Divider(props) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_config_provider__WEBPACK_IMPORTED_MODULE_4__.ConfigConsumer, null, function (_ref) {
    var _classNames;

    var getPrefixCls = _ref.getPrefixCls,
        direction = _ref.direction;

    var customizePrefixCls = props.prefixCls,
        _props$type = props.type,
        type = _props$type === void 0 ? 'horizontal' : _props$type,
        _props$orientation = props.orientation,
        orientation = _props$orientation === void 0 ? 'center' : _props$orientation,
        className = props.className,
        children = props.children,
        dashed = props.dashed,
        plain = props.plain,
        restProps = __rest(props, ["prefixCls", "type", "orientation", "className", "children", "dashed", "plain"]);

    var prefixCls = getPrefixCls('divider', customizePrefixCls);
    var orientationPrefix = orientation.length > 0 ? "-".concat(orientation) : orientation;
    var hasChildren = !!children;
    var classString = classnames__WEBPACK_IMPORTED_MODULE_3___default()(prefixCls, "".concat(prefixCls, "-").concat(type), (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-with-text"), hasChildren), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-with-text").concat(orientationPrefix), hasChildren), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-dashed"), !!dashed), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-plain"), !!plain), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-rtl"), direction === 'rtl'), _classNames), className);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
      className: classString
    }, restProps, {
      role: "separator"
    }), children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
      className: "".concat(prefixCls, "-inner-text")
    }, children));
  });
};

/* harmony default export */ __webpack_exports__["default"] = (Divider);

/***/ }),

/***/ "./node_modules/antd/es/dropdown/dropdown-button.js":
/*!**********************************************************!*\
  !*** ./node_modules/antd/es/dropdown/dropdown-button.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _ant_design_icons_es_icons_EllipsisOutlined__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ant-design/icons/es/icons/EllipsisOutlined */ "./node_modules/@ant-design/icons/es/icons/EllipsisOutlined.js");
/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../button */ "./node_modules/antd/es/button/index.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./dropdown */ "./node_modules/antd/es/dropdown/dropdown.js");



var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};







var ButtonGroup = _button__WEBPACK_IMPORTED_MODULE_4__.default.Group;

var DropdownButton = function DropdownButton(props) {
  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_5__.ConfigContext),
      getContextPopupContainer = _React$useContext.getPopupContainer,
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var customizePrefixCls = props.prefixCls,
      type = props.type,
      disabled = props.disabled,
      onClick = props.onClick,
      htmlType = props.htmlType,
      children = props.children,
      className = props.className,
      overlay = props.overlay,
      trigger = props.trigger,
      align = props.align,
      visible = props.visible,
      onVisibleChange = props.onVisibleChange,
      placement = props.placement,
      getPopupContainer = props.getPopupContainer,
      href = props.href,
      _props$icon = props.icon,
      icon = _props$icon === void 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons_es_icons_EllipsisOutlined__WEBPACK_IMPORTED_MODULE_6__.default, null) : _props$icon,
      title = props.title,
      buttonsRender = props.buttonsRender,
      mouseEnterDelay = props.mouseEnterDelay,
      mouseLeaveDelay = props.mouseLeaveDelay,
      overlayClassName = props.overlayClassName,
      overlayStyle = props.overlayStyle,
      restProps = __rest(props, ["prefixCls", "type", "disabled", "onClick", "htmlType", "children", "className", "overlay", "trigger", "align", "visible", "onVisibleChange", "placement", "getPopupContainer", "href", "icon", "title", "buttonsRender", "mouseEnterDelay", "mouseLeaveDelay", "overlayClassName", "overlayStyle"]);

  var prefixCls = getPrefixCls('dropdown-button', customizePrefixCls);
  var dropdownProps = {
    align: align,
    overlay: overlay,
    disabled: disabled,
    trigger: disabled ? [] : trigger,
    onVisibleChange: onVisibleChange,
    getPopupContainer: getPopupContainer || getContextPopupContainer,
    mouseEnterDelay: mouseEnterDelay,
    mouseLeaveDelay: mouseLeaveDelay,
    overlayClassName: overlayClassName,
    overlayStyle: overlayStyle
  };

  if ('visible' in props) {
    dropdownProps.visible = visible;
  }

  if ('placement' in props) {
    dropdownProps.placement = placement;
  } else {
    dropdownProps.placement = direction === 'rtl' ? 'bottomLeft' : 'bottomRight';
  }

  var leftButton = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_button__WEBPACK_IMPORTED_MODULE_4__.default, {
    type: type,
    disabled: disabled,
    onClick: onClick,
    htmlType: htmlType,
    href: href,
    title: title
  }, children);
  var rightButton = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_button__WEBPACK_IMPORTED_MODULE_4__.default, {
    type: type,
    icon: icon
  });

  var _buttonsRender = buttonsRender([leftButton, rightButton]),
      _buttonsRender2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__.default)(_buttonsRender, 2),
      leftButtonToRender = _buttonsRender2[0],
      rightButtonToRender = _buttonsRender2[1];

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ButtonGroup, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, restProps, {
    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(prefixCls, className)
  }), leftButtonToRender, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_dropdown__WEBPACK_IMPORTED_MODULE_7__.default, dropdownProps, rightButtonToRender));
};

DropdownButton.__ANT_BUTTON = true;
DropdownButton.defaultProps = {
  type: 'default',
  buttonsRender: function buttonsRender(buttons) {
    return buttons;
  }
};
/* harmony default export */ __webpack_exports__["default"] = (DropdownButton);

/***/ }),

/***/ "./node_modules/antd/es/dropdown/dropdown.js":
/*!***************************************************!*\
  !*** ./node_modules/antd/es/dropdown/dropdown.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_dropdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-dropdown */ "./node_modules/rc-dropdown/es/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _ant_design_icons_es_icons_RightOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ant-design/icons/es/icons/RightOutlined */ "./node_modules/@ant-design/icons/es/icons/RightOutlined.js");
/* harmony import */ var _dropdown_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./dropdown-button */ "./node_modules/antd/es/dropdown/dropdown-button.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _util_devWarning__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../_util/devWarning */ "./node_modules/antd/es/_util/devWarning.js");
/* harmony import */ var _util_type__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../_util/type */ "./node_modules/antd/es/_util/type.js");
/* harmony import */ var _util_reactNode__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../_util/reactNode */ "./node_modules/antd/es/_util/reactNode.js");











var Placements = (0,_util_type__WEBPACK_IMPORTED_MODULE_5__.tuple)('topLeft', 'topCenter', 'topRight', 'bottomLeft', 'bottomCenter', 'bottomRight');

var Dropdown = function Dropdown(props) {
  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_6__.ConfigContext),
      getContextPopupContainer = _React$useContext.getPopupContainer,
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var getTransitionName = function getTransitionName() {
    var rootPrefixCls = getPrefixCls();
    var _props$placement = props.placement,
        placement = _props$placement === void 0 ? '' : _props$placement,
        transitionName = props.transitionName;

    if (transitionName !== undefined) {
      return transitionName;
    }

    if (placement.indexOf('top') >= 0) {
      return "".concat(rootPrefixCls, "-slide-down");
    }

    return "".concat(rootPrefixCls, "-slide-up");
  };

  var renderOverlay = function renderOverlay(prefixCls) {
    // rc-dropdown already can process the function of overlay, but we have check logic here.
    // So we need render the element to check and pass back to rc-dropdown.
    var overlay = props.overlay;
    var overlayNode;

    if (typeof overlay === 'function') {
      overlayNode = overlay();
    } else {
      overlayNode = overlay;
    }

    overlayNode = react__WEBPACK_IMPORTED_MODULE_2__.Children.only(typeof overlayNode === 'string' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", null, overlayNode) : overlayNode);
    var overlayProps = overlayNode.props; // Warning if use other mode

    (0,_util_devWarning__WEBPACK_IMPORTED_MODULE_7__.default)(!overlayProps.mode || overlayProps.mode === 'vertical', 'Dropdown', "mode=\"".concat(overlayProps.mode, "\" is not supported for Dropdown's Menu.")); // menu cannot be selectable in dropdown defaultly

    var _overlayProps$selecta = overlayProps.selectable,
        selectable = _overlayProps$selecta === void 0 ? false : _overlayProps$selecta,
        expandIcon = overlayProps.expandIcon;
    var overlayNodeExpandIcon = typeof expandIcon !== 'undefined' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.isValidElement(expandIcon) ? expandIcon : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
      className: "".concat(prefixCls, "-menu-submenu-arrow")
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons_es_icons_RightOutlined__WEBPACK_IMPORTED_MODULE_8__.default, {
      className: "".concat(prefixCls, "-menu-submenu-arrow-icon")
    }));
    var fixedModeOverlay = typeof overlayNode.type === 'string' ? overlayNode : (0,_util_reactNode__WEBPACK_IMPORTED_MODULE_9__.cloneElement)(overlayNode, {
      mode: 'vertical',
      selectable: selectable,
      expandIcon: overlayNodeExpandIcon
    });
    return fixedModeOverlay;
  };

  var getPlacement = function getPlacement() {
    var placement = props.placement;

    if (placement !== undefined) {
      return placement;
    }

    return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';
  };

  var arrow = props.arrow,
      customizePrefixCls = props.prefixCls,
      children = props.children,
      trigger = props.trigger,
      disabled = props.disabled,
      getPopupContainer = props.getPopupContainer,
      overlayClassName = props.overlayClassName;
  var prefixCls = getPrefixCls('dropdown', customizePrefixCls);
  var child = react__WEBPACK_IMPORTED_MODULE_2__.Children.only(children);
  var dropdownTrigger = (0,_util_reactNode__WEBPACK_IMPORTED_MODULE_9__.cloneElement)(child, {
    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()("".concat(prefixCls, "-trigger"), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(prefixCls, "-rtl"), direction === 'rtl'), child.props.className),
    disabled: disabled
  });
  var overlayClassNameCustomized = classnames__WEBPACK_IMPORTED_MODULE_4___default()(overlayClassName, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(prefixCls, "-rtl"), direction === 'rtl'));
  var triggerActions = disabled ? [] : trigger;
  var alignPoint;

  if (triggerActions && triggerActions.indexOf('contextMenu') !== -1) {
    alignPoint = true;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(rc_dropdown__WEBPACK_IMPORTED_MODULE_3__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    arrow: arrow,
    alignPoint: alignPoint
  }, props, {
    overlayClassName: overlayClassNameCustomized,
    prefixCls: prefixCls,
    getPopupContainer: getPopupContainer || getContextPopupContainer,
    transitionName: getTransitionName(),
    trigger: triggerActions,
    overlay: function overlay() {
      return renderOverlay(prefixCls);
    },
    placement: getPlacement()
  }), dropdownTrigger);
};

Dropdown.Button = _dropdown_button__WEBPACK_IMPORTED_MODULE_10__.default;
Dropdown.defaultProps = {
  mouseEnterDelay: 0.15,
  mouseLeaveDelay: 0.1
};
/* harmony default export */ __webpack_exports__["default"] = (Dropdown);

/***/ }),

/***/ "./node_modules/antd/es/grid/RowContext.js":
/*!*************************************************!*\
  !*** ./node_modules/antd/es/grid/RowContext.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

var RowContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});
/* harmony default export */ __webpack_exports__["default"] = (RowContext);

/***/ }),

/***/ "./node_modules/antd/es/grid/col.js":
/*!******************************************!*\
  !*** ./node_modules/antd/es/grid/col.js ***!
  \******************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _RowContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./RowContext */ "./node_modules/antd/es/grid/RowContext.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");




var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};






function parseFlex(flex) {
  if (typeof flex === 'number') {
    return "".concat(flex, " ").concat(flex, " auto");
  }

  if (/^\d+(\.\d+)?(px|em|rem|%)$/.test(flex)) {
    return "0 0 ".concat(flex);
  }

  return flex;
}

var sizes = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
var Col = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(function (props, ref) {
  var _classNames;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_3__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_5__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var _React$useContext2 = react__WEBPACK_IMPORTED_MODULE_3__.useContext(_RowContext__WEBPACK_IMPORTED_MODULE_6__.default),
      gutter = _React$useContext2.gutter,
      wrap = _React$useContext2.wrap,
      supportFlexGap = _React$useContext2.supportFlexGap;

  var customizePrefixCls = props.prefixCls,
      span = props.span,
      order = props.order,
      offset = props.offset,
      push = props.push,
      pull = props.pull,
      className = props.className,
      children = props.children,
      flex = props.flex,
      style = props.style,
      others = __rest(props, ["prefixCls", "span", "order", "offset", "push", "pull", "className", "children", "flex", "style"]);

  var prefixCls = getPrefixCls('col', customizePrefixCls);
  var sizeClassObj = {};
  sizes.forEach(function (size) {
    var _extends2;

    var sizeProps = {};
    var propSize = props[size];

    if (typeof propSize === 'number') {
      sizeProps.span = propSize;
    } else if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__.default)(propSize) === 'object') {
      sizeProps = propSize || {};
    }

    delete others[size];
    sizeClassObj = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, sizeClassObj), (_extends2 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_extends2, "".concat(prefixCls, "-").concat(size, "-").concat(sizeProps.span), sizeProps.span !== undefined), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_extends2, "".concat(prefixCls, "-").concat(size, "-order-").concat(sizeProps.order), sizeProps.order || sizeProps.order === 0), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_extends2, "".concat(prefixCls, "-").concat(size, "-offset-").concat(sizeProps.offset), sizeProps.offset || sizeProps.offset === 0), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_extends2, "".concat(prefixCls, "-").concat(size, "-push-").concat(sizeProps.push), sizeProps.push || sizeProps.push === 0), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_extends2, "".concat(prefixCls, "-").concat(size, "-pull-").concat(sizeProps.pull), sizeProps.pull || sizeProps.pull === 0), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_extends2, "".concat(prefixCls, "-rtl"), direction === 'rtl'), _extends2));
  });
  var classes = classnames__WEBPACK_IMPORTED_MODULE_4___default()(prefixCls, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-").concat(span), span !== undefined), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-order-").concat(order), order), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-offset-").concat(offset), offset), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-push-").concat(push), push), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-pull-").concat(pull), pull), _classNames), className, sizeClassObj);
  var mergedStyle = {}; // Horizontal gutter use padding

  if (gutter && gutter[0] > 0) {
    var horizontalGutter = gutter[0] / 2;
    mergedStyle.paddingLeft = horizontalGutter;
    mergedStyle.paddingRight = horizontalGutter;
  } // Vertical gutter use padding when gap not support


  if (gutter && gutter[1] > 0 && !supportFlexGap) {
    var verticalGutter = gutter[1] / 2;
    mergedStyle.paddingTop = verticalGutter;
    mergedStyle.paddingBottom = verticalGutter;
  }

  if (flex) {
    mergedStyle.flex = parseFlex(flex); // Hack for Firefox to avoid size issue
    // https://github.com/ant-design/ant-design/pull/20023#issuecomment-564389553

    if (flex === 'auto' && wrap === false && !mergedStyle.minWidth) {
      mergedStyle.minWidth = 0;
    }
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, others, {
    style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, mergedStyle), style),
    className: classes,
    ref: ref
  }), children);
});
Col.displayName = 'Col';
/* harmony default export */ __webpack_exports__["default"] = (Col);

/***/ }),

/***/ "./node_modules/antd/es/grid/row.js":
/*!******************************************!*\
  !*** ./node_modules/antd/es/grid/row.js ***!
  \******************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _RowContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./RowContext */ "./node_modules/antd/es/grid/RowContext.js");
/* harmony import */ var _util_type__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../_util/type */ "./node_modules/antd/es/_util/type.js");
/* harmony import */ var _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../_util/responsiveObserve */ "./node_modules/antd/es/_util/responsiveObserve.js");
/* harmony import */ var _util_hooks_useFlexGapSupport__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../_util/hooks/useFlexGapSupport */ "./node_modules/antd/es/_util/hooks/useFlexGapSupport.js");





var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};








var RowAligns = (0,_util_type__WEBPACK_IMPORTED_MODULE_6__.tuple)('top', 'middle', 'bottom', 'stretch');
var RowJustify = (0,_util_type__WEBPACK_IMPORTED_MODULE_6__.tuple)('start', 'end', 'center', 'space-around', 'space-between');
var Row = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.forwardRef(function (props, ref) {
  var _classNames;

  var customizePrefixCls = props.prefixCls,
      justify = props.justify,
      align = props.align,
      className = props.className,
      style = props.style,
      children = props.children,
      _props$gutter = props.gutter,
      gutter = _props$gutter === void 0 ? 0 : _props$gutter,
      wrap = props.wrap,
      others = __rest(props, ["prefixCls", "justify", "align", "className", "style", "children", "gutter", "wrap"]);

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_4__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_7__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_4__.useState({
    xs: true,
    sm: true,
    md: true,
    lg: true,
    xl: true,
    xxl: true
  }),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(_React$useState, 2),
      screens = _React$useState2[0],
      setScreens = _React$useState2[1];

  var supportFlexGap = (0,_util_hooks_useFlexGapSupport__WEBPACK_IMPORTED_MODULE_8__.default)();
  var gutterRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef(gutter); // ================================== Effect ==================================

  react__WEBPACK_IMPORTED_MODULE_4__.useEffect(function () {
    var token = _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_9__.default.subscribe(function (screen) {
      var currentGutter = gutterRef.current || 0;

      if (!Array.isArray(currentGutter) && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__.default)(currentGutter) === 'object' || Array.isArray(currentGutter) && ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__.default)(currentGutter[0]) === 'object' || (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__.default)(currentGutter[1]) === 'object')) {
        setScreens(screen);
      }
    });
    return function () {
      return _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_9__.default.unsubscribe(token);
    };
  }, []); // ================================== Render ==================================

  var getGutter = function getGutter() {
    var results = [0, 0];
    var normalizedGutter = Array.isArray(gutter) ? gutter : [gutter, 0];
    normalizedGutter.forEach(function (g, index) {
      if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__.default)(g) === 'object') {
        for (var i = 0; i < _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_9__.responsiveArray.length; i++) {
          var breakpoint = _util_responsiveObserve__WEBPACK_IMPORTED_MODULE_9__.responsiveArray[i];

          if (screens[breakpoint] && g[breakpoint] !== undefined) {
            results[index] = g[breakpoint];
            break;
          }
        }
      } else {
        results[index] = g || 0;
      }
    });
    return results;
  };

  var prefixCls = getPrefixCls('row', customizePrefixCls);
  var gutters = getGutter();
  var classes = classnames__WEBPACK_IMPORTED_MODULE_5___default()(prefixCls, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-no-wrap"), wrap === false), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-").concat(justify), justify), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-").concat(align), align), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-rtl"), direction === 'rtl'), _classNames), className); // Add gutter related style

  var rowStyle = {};
  var horizontalGutter = gutters[0] > 0 ? gutters[0] / -2 : undefined;
  var verticalGutter = gutters[1] > 0 ? gutters[1] / -2 : undefined;

  if (horizontalGutter) {
    rowStyle.marginLeft = horizontalGutter;
    rowStyle.marginRight = horizontalGutter;
  }

  if (supportFlexGap) {
    // Set gap direct if flex gap support
    var _gutters = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(gutters, 2);

    rowStyle.rowGap = _gutters[1];
  } else if (verticalGutter) {
    rowStyle.marginTop = verticalGutter;
    rowStyle.marginBottom = verticalGutter;
  }

  var rowContext = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(function () {
    return {
      gutter: gutters,
      wrap: wrap,
      supportFlexGap: supportFlexGap
    };
  }, [gutters, wrap, supportFlexGap]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_RowContext__WEBPACK_IMPORTED_MODULE_10__.default.Provider, {
    value: rowContext
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, others, {
    className: classes,
    style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, rowStyle), style),
    ref: ref
  }), children));
});
Row.displayName = 'Row';
/* harmony default export */ __webpack_exports__["default"] = (Row);

/***/ }),

/***/ "./node_modules/antd/es/layout/style/index.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/layout/style/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/layout/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/list/style/index.js":
/*!**************************************************!*\
  !*** ./node_modules/antd/es/list/style/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/list/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _empty_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../empty/style */ "./node_modules/antd/es/empty/style/index.js");
/* harmony import */ var _spin_style__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../spin/style */ "./node_modules/antd/es/spin/style/index.js");
/* harmony import */ var _pagination_style__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../pagination/style */ "./node_modules/antd/es/pagination/style/index.js");
/* harmony import */ var _grid_style__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../grid/style */ "./node_modules/antd/es/grid/style/index.js");

 // style dependencies






/***/ }),

/***/ "./node_modules/antd/es/page-header/style/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/antd/es/page-header/style/index.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/page-header/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _breadcrumb_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../breadcrumb/style */ "./node_modules/antd/es/breadcrumb/style/index.js");
/* harmony import */ var _avatar_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../avatar/style */ "./node_modules/antd/es/avatar/style/index.js");
 // style dependencies




/***/ }),

/***/ "./node_modules/antd/es/skeleton/style/index.js":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/skeleton/style/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../style/index.less */ "./node_modules/antd/es/style/index.less");
/* harmony import */ var _style_index_less__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_style_index_less__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.less */ "./node_modules/antd/es/skeleton/style/index.less");
/* harmony import */ var _index_less__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_index_less__WEBPACK_IMPORTED_MODULE_1__);



/***/ }),

/***/ "./node_modules/antd/es/switch/index.js":
/*!**********************************************!*\
  !*** ./node_modules/antd/es/switch/index.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_switch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-switch */ "./node_modules/rc-switch/es/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _ant_design_icons_es_icons_LoadingOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ant-design/icons/es/icons/LoadingOutlined */ "./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js");
/* harmony import */ var _util_wave__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../_util/wave */ "./node_modules/antd/es/_util/wave.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../config-provider/SizeContext */ "./node_modules/antd/es/config-provider/SizeContext.js");
/* harmony import */ var _util_devWarning__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../_util/devWarning */ "./node_modules/antd/es/_util/devWarning.js");



var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};









var Switch = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function (_a, ref) {
  var _classNames;

  var customizePrefixCls = _a.prefixCls,
      customizeSize = _a.size,
      loading = _a.loading,
      _a$className = _a.className,
      className = _a$className === void 0 ? '' : _a$className,
      disabled = _a.disabled,
      props = __rest(_a, ["prefixCls", "size", "loading", "className", "disabled"]);

  (0,_util_devWarning__WEBPACK_IMPORTED_MODULE_5__.default)('checked' in props || !('value' in props), 'Switch', '`value` is not a valid prop, do you mean `checked`?');

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_6__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var size = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_7__.default);
  var prefixCls = getPrefixCls('switch', customizePrefixCls);
  var loadingIcon = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(prefixCls, "-handle")
  }, loading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons_es_icons_LoadingOutlined__WEBPACK_IMPORTED_MODULE_8__.default, {
    className: "".concat(prefixCls, "-loading-icon")
  }));
  var classes = classnames__WEBPACK_IMPORTED_MODULE_4___default()((_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-small"), (customizeSize || size) === 'small'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-loading"), loading), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-rtl"), direction === 'rtl'), _classNames), className);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_util_wave__WEBPACK_IMPORTED_MODULE_9__.default, {
    insertExtraNode: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(rc_switch__WEBPACK_IMPORTED_MODULE_3__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    className: classes,
    disabled: disabled || loading,
    ref: ref,
    loadingIcon: loadingIcon
  })));
});
Switch.__ANT_SWITCH = true;
Switch.displayName = 'Switch';
/* harmony default export */ __webpack_exports__["default"] = (Switch);

/***/ }),

/***/ "./node_modules/antd/es/tabs/index.js":
/*!********************************************!*\
  !*** ./node_modules/antd/es/tabs/index.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-tabs */ "./node_modules/rc-tabs/es/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _ant_design_icons_es_icons_EllipsisOutlined__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ant-design/icons/es/icons/EllipsisOutlined */ "./node_modules/@ant-design/icons/es/icons/EllipsisOutlined.js");
/* harmony import */ var _ant_design_icons_es_icons_PlusOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ant-design/icons/es/icons/PlusOutlined */ "./node_modules/@ant-design/icons/es/icons/PlusOutlined.js");
/* harmony import */ var _ant_design_icons_es_icons_CloseOutlined__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ant-design/icons/es/icons/CloseOutlined */ "./node_modules/@ant-design/icons/es/icons/CloseOutlined.js");
/* harmony import */ var _util_devWarning__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../_util/devWarning */ "./node_modules/antd/es/_util/devWarning.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../config-provider/SizeContext */ "./node_modules/antd/es/config-provider/SizeContext.js");



var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};











function Tabs(_a) {
  var type = _a.type,
      className = _a.className,
      propSize = _a.size,
      _onEdit = _a.onEdit,
      hideAdd = _a.hideAdd,
      centered = _a.centered,
      addIcon = _a.addIcon,
      props = __rest(_a, ["type", "className", "size", "onEdit", "hideAdd", "centered", "addIcon"]);

  var customizePrefixCls = props.prefixCls,
      _props$moreIcon = props.moreIcon,
      moreIcon = _props$moreIcon === void 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons_es_icons_EllipsisOutlined__WEBPACK_IMPORTED_MODULE_5__.default, null) : _props$moreIcon;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_6__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var prefixCls = getPrefixCls('tabs', customizePrefixCls);
  var editable;

  if (type === 'editable-card') {
    editable = {
      onEdit: function onEdit(editType, _ref) {
        var key = _ref.key,
            event = _ref.event;
        _onEdit === null || _onEdit === void 0 ? void 0 : _onEdit(editType === 'add' ? event : key, editType);
      },
      removeIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons_es_icons_CloseOutlined__WEBPACK_IMPORTED_MODULE_7__.default, null),
      addIcon: addIcon || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons_es_icons_PlusOutlined__WEBPACK_IMPORTED_MODULE_8__.default, null),
      showAdd: hideAdd !== true
    };
  }

  var rootPrefixCls = getPrefixCls();
  (0,_util_devWarning__WEBPACK_IMPORTED_MODULE_9__.default)(!('onPrevClick' in props) && !('onNextClick' in props), 'Tabs', '`onPrevClick` and `onNextClick` has been removed. Please use `onTabScroll` instead.');
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_10__.default.Consumer, null, function (contextSize) {
    var _classNames;

    var size = propSize !== undefined ? propSize : contextSize;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(rc_tabs__WEBPACK_IMPORTED_MODULE_3__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
      direction: direction,
      moreTransitionName: "".concat(rootPrefixCls, "-slide-up")
    }, props, {
      className: classnames__WEBPACK_IMPORTED_MODULE_4___default()((_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-").concat(size), size), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-card"), ['card', 'editable-card'].includes(type)), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-editable-card"), type === 'editable-card'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-centered"), centered), _classNames), className),
      editable: editable,
      moreIcon: moreIcon,
      prefixCls: prefixCls
    }));
  });
}

Tabs.TabPane = rc_tabs__WEBPACK_IMPORTED_MODULE_3__.TabPane;
/* harmony default export */ __webpack_exports__["default"] = (Tabs);

/***/ }),

/***/ "./node_modules/antd/node_modules/rc-util/es/KeyCode.js":
/*!**************************************************************!*\
  !*** ./node_modules/antd/node_modules/rc-util/es/KeyCode.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/**
 * @ignore
 * some key-codes definition and utils from closure-library
 * <AUTHOR>
 */
var KeyCode = {
  /**
   * MAC_ENTER
   */
  MAC_ENTER: 3,

  /**
   * BACKSPACE
   */
  BACKSPACE: 8,

  /**
   * TAB
   */
  TAB: 9,

  /**
   * NUMLOCK on FF/Safari Mac
   */
  NUM_CENTER: 12,

  /**
   * ENTER
   */
  ENTER: 13,

  /**
   * SHIFT
   */
  SHIFT: 16,

  /**
   * CTRL
   */
  CTRL: 17,

  /**
   * ALT
   */
  ALT: 18,

  /**
   * PAUSE
   */
  PAUSE: 19,

  /**
   * CAPS_LOCK
   */
  CAPS_LOCK: 20,

  /**
   * ESC
   */
  ESC: 27,

  /**
   * SPACE
   */
  SPACE: 32,

  /**
   * PAGE_UP
   */
  PAGE_UP: 33,

  /**
   * PAGE_DOWN
   */
  PAGE_DOWN: 34,

  /**
   * END
   */
  END: 35,

  /**
   * HOME
   */
  HOME: 36,

  /**
   * LEFT
   */
  LEFT: 37,

  /**
   * UP
   */
  UP: 38,

  /**
   * RIGHT
   */
  RIGHT: 39,

  /**
   * DOWN
   */
  DOWN: 40,

  /**
   * PRINT_SCREEN
   */
  PRINT_SCREEN: 44,

  /**
   * INSERT
   */
  INSERT: 45,

  /**
   * DELETE
   */
  DELETE: 46,

  /**
   * ZERO
   */
  ZERO: 48,

  /**
   * ONE
   */
  ONE: 49,

  /**
   * TWO
   */
  TWO: 50,

  /**
   * THREE
   */
  THREE: 51,

  /**
   * FOUR
   */
  FOUR: 52,

  /**
   * FIVE
   */
  FIVE: 53,

  /**
   * SIX
   */
  SIX: 54,

  /**
   * SEVEN
   */
  SEVEN: 55,

  /**
   * EIGHT
   */
  EIGHT: 56,

  /**
   * NINE
   */
  NINE: 57,

  /**
   * QUESTION_MARK
   */
  QUESTION_MARK: 63,

  /**
   * A
   */
  A: 65,

  /**
   * B
   */
  B: 66,

  /**
   * C
   */
  C: 67,

  /**
   * D
   */
  D: 68,

  /**
   * E
   */
  E: 69,

  /**
   * F
   */
  F: 70,

  /**
   * G
   */
  G: 71,

  /**
   * H
   */
  H: 72,

  /**
   * I
   */
  I: 73,

  /**
   * J
   */
  J: 74,

  /**
   * K
   */
  K: 75,

  /**
   * L
   */
  L: 76,

  /**
   * M
   */
  M: 77,

  /**
   * N
   */
  N: 78,

  /**
   * O
   */
  O: 79,

  /**
   * P
   */
  P: 80,

  /**
   * Q
   */
  Q: 81,

  /**
   * R
   */
  R: 82,

  /**
   * S
   */
  S: 83,

  /**
   * T
   */
  T: 84,

  /**
   * U
   */
  U: 85,

  /**
   * V
   */
  V: 86,

  /**
   * W
   */
  W: 87,

  /**
   * X
   */
  X: 88,

  /**
   * Y
   */
  Y: 89,

  /**
   * Z
   */
  Z: 90,

  /**
   * META
   */
  META: 91,

  /**
   * WIN_KEY_RIGHT
   */
  WIN_KEY_RIGHT: 92,

  /**
   * CONTEXT_MENU
   */
  CONTEXT_MENU: 93,

  /**
   * NUM_ZERO
   */
  NUM_ZERO: 96,

  /**
   * NUM_ONE
   */
  NUM_ONE: 97,

  /**
   * NUM_TWO
   */
  NUM_TWO: 98,

  /**
   * NUM_THREE
   */
  NUM_THREE: 99,

  /**
   * NUM_FOUR
   */
  NUM_FOUR: 100,

  /**
   * NUM_FIVE
   */
  NUM_FIVE: 101,

  /**
   * NUM_SIX
   */
  NUM_SIX: 102,

  /**
   * NUM_SEVEN
   */
  NUM_SEVEN: 103,

  /**
   * NUM_EIGHT
   */
  NUM_EIGHT: 104,

  /**
   * NUM_NINE
   */
  NUM_NINE: 105,

  /**
   * NUM_MULTIPLY
   */
  NUM_MULTIPLY: 106,

  /**
   * NUM_PLUS
   */
  NUM_PLUS: 107,

  /**
   * NUM_MINUS
   */
  NUM_MINUS: 109,

  /**
   * NUM_PERIOD
   */
  NUM_PERIOD: 110,

  /**
   * NUM_DIVISION
   */
  NUM_DIVISION: 111,

  /**
   * F1
   */
  F1: 112,

  /**
   * F2
   */
  F2: 113,

  /**
   * F3
   */
  F3: 114,

  /**
   * F4
   */
  F4: 115,

  /**
   * F5
   */
  F5: 116,

  /**
   * F6
   */
  F6: 117,

  /**
   * F7
   */
  F7: 118,

  /**
   * F8
   */
  F8: 119,

  /**
   * F9
   */
  F9: 120,

  /**
   * F10
   */
  F10: 121,

  /**
   * F11
   */
  F11: 122,

  /**
   * F12
   */
  F12: 123,

  /**
   * NUMLOCK
   */
  NUMLOCK: 144,

  /**
   * SEMICOLON
   */
  SEMICOLON: 186,

  /**
   * DASH
   */
  DASH: 189,

  /**
   * EQUALS
   */
  EQUALS: 187,

  /**
   * COMMA
   */
  COMMA: 188,

  /**
   * PERIOD
   */
  PERIOD: 190,

  /**
   * SLASH
   */
  SLASH: 191,

  /**
   * APOSTROPHE
   */
  APOSTROPHE: 192,

  /**
   * SINGLE_QUOTE
   */
  SINGLE_QUOTE: 222,

  /**
   * OPEN_SQUARE_BRACKET
   */
  OPEN_SQUARE_BRACKET: 219,

  /**
   * BACKSLASH
   */
  BACKSLASH: 220,

  /**
   * CLOSE_SQUARE_BRACKET
   */
  CLOSE_SQUARE_BRACKET: 221,

  /**
   * WIN_KEY
   */
  WIN_KEY: 224,

  /**
   * MAC_FF_META
   */
  MAC_FF_META: 224,

  /**
   * WIN_IME
   */
  WIN_IME: 229,
  // ======================== Function ========================

  /**
   * whether text and modified key is entered at the same time.
   */
  isTextModifyingKeyEvent: function isTextModifyingKeyEvent(e) {
    var keyCode = e.keyCode;

    if (e.altKey && !e.ctrlKey || e.metaKey || keyCode >= KeyCode.F1 && keyCode <= KeyCode.F12) {
      return false;
    } // The following keys are quite harmless, even in combination with
    // CTRL, ALT or SHIFT.


    switch (keyCode) {
      case KeyCode.ALT:
      case KeyCode.CAPS_LOCK:
      case KeyCode.CONTEXT_MENU:
      case KeyCode.CTRL:
      case KeyCode.DOWN:
      case KeyCode.END:
      case KeyCode.ESC:
      case KeyCode.HOME:
      case KeyCode.INSERT:
      case KeyCode.LEFT:
      case KeyCode.MAC_FF_META:
      case KeyCode.META:
      case KeyCode.NUMLOCK:
      case KeyCode.NUM_CENTER:
      case KeyCode.PAGE_DOWN:
      case KeyCode.PAGE_UP:
      case KeyCode.PAUSE:
      case KeyCode.PRINT_SCREEN:
      case KeyCode.RIGHT:
      case KeyCode.SHIFT:
      case KeyCode.UP:
      case KeyCode.WIN_KEY:
      case KeyCode.WIN_KEY_RIGHT:
        return false;

      default:
        return true;
    }
  },

  /**
   * whether character is entered.
   */
  isCharacterKey: function isCharacterKey(keyCode) {
    if (keyCode >= KeyCode.ZERO && keyCode <= KeyCode.NINE) {
      return true;
    }

    if (keyCode >= KeyCode.NUM_ZERO && keyCode <= KeyCode.NUM_MULTIPLY) {
      return true;
    }

    if (keyCode >= KeyCode.A && keyCode <= KeyCode.Z) {
      return true;
    } // Safari sends zero key code for non-latin characters.


    if (window.navigator.userAgent.indexOf('WebKit') !== -1 && keyCode === 0) {
      return true;
    }

    switch (keyCode) {
      case KeyCode.SPACE:
      case KeyCode.QUESTION_MARK:
      case KeyCode.NUM_PLUS:
      case KeyCode.NUM_MINUS:
      case KeyCode.NUM_PERIOD:
      case KeyCode.NUM_DIVISION:
      case KeyCode.SEMICOLON:
      case KeyCode.DASH:
      case KeyCode.EQUALS:
      case KeyCode.COMMA:
      case KeyCode.PERIOD:
      case KeyCode.SLASH:
      case KeyCode.APOSTROPHE:
      case KeyCode.SINGLE_QUOTE:
      case KeyCode.OPEN_SQUARE_BRACKET:
      case KeyCode.BACKSLASH:
      case KeyCode.CLOSE_SQUARE_BRACKET:
        return true;

      default:
        return false;
    }
  }
};
/* harmony default export */ __webpack_exports__["default"] = (KeyCode);

/***/ }),

/***/ "./node_modules/rc-switch/es/index.js":
/*!********************************************!*\
  !*** ./node_modules/rc-switch/es/index.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ "./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ "./node_modules/rc-util/es/hooks/useMergedState.js");
/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/KeyCode */ "./node_modules/rc-util/es/KeyCode.js");







var Switch = react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(function (_ref, ref) {
  var _classNames;

  var _ref$prefixCls = _ref.prefixCls,
      prefixCls = _ref$prefixCls === void 0 ? 'rc-switch' : _ref$prefixCls,
      className = _ref.className,
      checked = _ref.checked,
      defaultChecked = _ref.defaultChecked,
      disabled = _ref.disabled,
      loadingIcon = _ref.loadingIcon,
      checkedChildren = _ref.checkedChildren,
      unCheckedChildren = _ref.unCheckedChildren,
      onClick = _ref.onClick,
      onChange = _ref.onChange,
      onKeyDown = _ref.onKeyDown,
      restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__.default)(_ref, ["prefixCls", "className", "checked", "defaultChecked", "disabled", "loadingIcon", "checkedChildren", "unCheckedChildren", "onClick", "onChange", "onKeyDown"]);

  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_5__.default)(false, {
    value: checked,
    defaultValue: defaultChecked
  }),
      _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__.default)(_useMergedState, 2),
      innerChecked = _useMergedState2[0],
      setInnerChecked = _useMergedState2[1];

  function triggerChange(newChecked, event) {
    var mergedChecked = innerChecked;

    if (!disabled) {
      mergedChecked = newChecked;
      setInnerChecked(mergedChecked);
      onChange === null || onChange === void 0 ? void 0 : onChange(mergedChecked, event);
    }

    return mergedChecked;
  }

  function onInternalKeyDown(e) {
    if (e.which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__.default.LEFT) {
      triggerChange(false, e);
    } else if (e.which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__.default.RIGHT) {
      triggerChange(true, e);
    }

    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);
  }

  function onInternalClick(e) {
    var ret = triggerChange(!innerChecked, e); // [Legacy] trigger onClick with value

    onClick === null || onClick === void 0 ? void 0 : onClick(ret, e);
  }

  var switchClassName = classnames__WEBPACK_IMPORTED_MODULE_4___default()(prefixCls, className, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-checked"), innerChecked), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-disabled"), disabled), _classNames));
  return react__WEBPACK_IMPORTED_MODULE_3__.createElement("button", Object.assign({}, restProps, {
    type: "button",
    role: "switch",
    "aria-checked": innerChecked,
    disabled: disabled,
    className: switchClassName,
    ref: ref,
    onKeyDown: onInternalKeyDown,
    onClick: onInternalClick
  }), loadingIcon, react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
    className: "".concat(prefixCls, "-inner")
  }, innerChecked ? checkedChildren : unCheckedChildren));
});
Switch.displayName = 'Switch';
/* harmony default export */ __webpack_exports__["default"] = (Switch);

/***/ }),

/***/ "./node_modules/warning/warning.js":
/*!*****************************************!*\
  !*** ./node_modules/warning/warning.js ***!
  \*****************************************/
/***/ (function(module) {

"use strict";
/**
 * Copyright (c) 2014-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */



/**
 * Similar to invariant but only logs a warning if the condition is not met.
 * This can be used to log issues in development environments in critical
 * paths. Removing the logging code for production environments will keep the
 * same logic and follow the same code paths.
 */

var __DEV__ = "development" !== 'production';

var warning = function() {};

if (__DEV__) {
  var printWarning = function printWarning(format, args) {
    var len = arguments.length;
    args = new Array(len > 1 ? len - 1 : 0);
    for (var key = 1; key < len; key++) {
      args[key - 1] = arguments[key];
    }
    var argIndex = 0;
    var message = 'Warning: ' +
      format.replace(/%s/g, function() {
        return args[argIndex++];
      });
    if (typeof console !== 'undefined') {
      console.error(message);
    }
    try {
      // --- Welcome to debugging React ---
      // This error was thrown as a convenience so that you can use this stack
      // to find the callsite that caused this warning to fire.
      throw new Error(message);
    } catch (x) {}
  }

  warning = function(condition, format, args) {
    var len = arguments.length;
    args = new Array(len > 2 ? len - 2 : 0);
    for (var key = 2; key < len; key++) {
      args[key - 2] = arguments[key];
    }
    if (format === undefined) {
      throw new Error(
          '`warning(condition, format, ...args)` requires a warning ' +
          'message argument'
      );
    }
    if (!condition) {
      printWarning.apply(null, [format].concat(args));
    }
  };
}

module.exports = warning;


/***/ })

}]);