export default [
  {
    path: '/dataAdmin',
    redirect: '/dataAdmin/dashboard',
    // redirect: '/statistics/afGroupRecord',
  },
  {
    path: '/dataAdmin/dashboard',
    component: './dataAdmin/dashboard/index',
  },
  {
    path: '/dataAdmin/setting/afApplication',
    component: './dataAdmin/setting/afApplication/index',
  },
  {
    path: '/dataAdmin/setting/project',
    component: './dataAdmin/setting/project/index',
  },
  {
    path: '/dataAdmin/setting/launchPlatform',
    component: './dataAdmin/setting/launchPlatform/index',
  },
  {
    path: '/dataAdmin/setting/afCommand',
    component: './dataAdmin/setting/afCommand/index',
  },
  {
    path: '/dataAdmin/data/record',
    component: './dataAdmin/data/record/index',
  },
  {
    path: '/dataAdmin/data/afRecord',
    component: './dataAdmin/data/afRecord/index',
  },
  {
    path: '/dataAdmin/data/afLogRecord',
    component: './dataAdmin/data/afLogRecord/index',
  },
  {
    path: '/dataAdmin/data/jlRecord',
    component: './dataAdmin/data/jlRecord/index',
  },
  {
    path: '/dataAdmin/statistics/afGroupRecord',
    component: './dataAdmin/statistics/afGroupRecord/index',
  },
  {
    path: '/dataAdmin/statistics/jlGroupRecord',
    component: './dataAdmin/statistics/jlGroupRecord/index',
  },
];
