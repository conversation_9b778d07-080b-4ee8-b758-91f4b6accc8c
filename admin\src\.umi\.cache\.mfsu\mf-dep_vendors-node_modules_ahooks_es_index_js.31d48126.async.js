(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_ahooks_es_index_js"],{

/***/ "./node_modules/ahooks/es/createUpdateEffect/index.js":
/*!************************************************************!*\
  !*** ./node_modules/ahooks/es/createUpdateEffect/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "createUpdateEffect": function() { return /* binding */ createUpdateEffect; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

var createUpdateEffect = function createUpdateEffect(hook) {
  return function (effect, deps) {
    var isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false); // for react-refresh

    hook(function () {
      return function () {
        isMounted.current = false;
      };
    }, []);
    hook(function () {
      if (!isMounted.current) {
        isMounted.current = true;
      } else {
        return effect();
      }
    }, deps);
  };
};

/***/ }),

/***/ "./node_modules/ahooks/es/createUseStorageState/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/ahooks/es/createUseStorageState/index.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "createUseStorageState": function() { return /* binding */ createUseStorageState; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useUpdateEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useUpdateEffect */ "./node_modules/ahooks/es/useUpdateEffect/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};
/* eslint-disable no-empty */






function isFunction(obj) {
  return typeof obj === 'function';
}

function createUseStorageState(getStorage) {
  function useStorageState(key, options) {
    var storage; // https://github.com/alibaba/hooks/issues/800

    try {
      storage = getStorage();
    } catch (err) {
      console.error(err);
    }

    var serializer = function serializer(value) {
      if (options === null || options === void 0 ? void 0 : options.serializer) {
        return options === null || options === void 0 ? void 0 : options.serializer(value);
      }

      return JSON.stringify(value);
    };

    var deserializer = function deserializer(value) {
      if (options === null || options === void 0 ? void 0 : options.deserializer) {
        return options === null || options === void 0 ? void 0 : options.deserializer(value);
      }

      return JSON.parse(value);
    };

    function getStoredValue() {
      try {
        var raw = storage === null || storage === void 0 ? void 0 : storage.getItem(key);

        if (raw) {
          return deserializer(raw);
        }
      } catch (e) {
        console.error(e);
      }

      if (isFunction(options === null || options === void 0 ? void 0 : options.defaultValue)) {
        return options === null || options === void 0 ? void 0 : options.defaultValue();
      }

      return options === null || options === void 0 ? void 0 : options.defaultValue;
    }

    var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {
      return getStoredValue();
    }), 2),
        state = _a[0],
        setState = _a[1];

    (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
      setState(getStoredValue());
    }, [key]);

    var updateState = function updateState(value) {
      if (typeof value === 'undefined') {
        setState(undefined);
        storage === null || storage === void 0 ? void 0 : storage.removeItem(key);
      } else if (isFunction(value)) {
        var currentState = value(state);

        try {
          setState(currentState);
          storage === null || storage === void 0 ? void 0 : storage.setItem(key, serializer(currentState));
        } catch (e) {
          console.error(e);
        }
      } else {
        try {
          setState(value);
          storage === null || storage === void 0 ? void 0 : storage.setItem(key, serializer(value));
        } catch (e) {
          console.error(e);
        }
      }
    };

    return [state, (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_2__.default)(updateState)];
  }

  return useStorageState;
}

/***/ }),

/***/ "./node_modules/ahooks/es/index.js":
/*!*****************************************!*\
  !*** ./node_modules/ahooks/es/index.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "useRequest": function() { return /* reexport safe */ _useRequest__WEBPACK_IMPORTED_MODULE_0__.default; },
/* harmony export */   "useControllableValue": function() { return /* reexport safe */ _useControllableValue__WEBPACK_IMPORTED_MODULE_1__.default; },
/* harmony export */   "useDynamicList": function() { return /* reexport safe */ _useDynamicList__WEBPACK_IMPORTED_MODULE_2__.default; },
/* harmony export */   "useVirtualList": function() { return /* reexport safe */ _useVirtualList__WEBPACK_IMPORTED_MODULE_3__.default; },
/* harmony export */   "useResponsive": function() { return /* reexport safe */ _useResponsive__WEBPACK_IMPORTED_MODULE_4__.useResponsive; },
/* harmony export */   "useEventEmitter": function() { return /* reexport safe */ _useEventEmitter__WEBPACK_IMPORTED_MODULE_5__.default; },
/* harmony export */   "useLocalStorageState": function() { return /* reexport safe */ _useLocalStorageState__WEBPACK_IMPORTED_MODULE_6__.default; },
/* harmony export */   "useSessionStorageState": function() { return /* reexport safe */ _useSessionStorageState__WEBPACK_IMPORTED_MODULE_7__.default; },
/* harmony export */   "useSize": function() { return /* reexport safe */ _useSize__WEBPACK_IMPORTED_MODULE_8__.default; },
/* harmony export */   "configResponsive": function() { return /* reexport safe */ _useResponsive__WEBPACK_IMPORTED_MODULE_4__.configResponsive; },
/* harmony export */   "useUpdateEffect": function() { return /* reexport safe */ _useUpdateEffect__WEBPACK_IMPORTED_MODULE_9__.default; },
/* harmony export */   "useUpdateLayoutEffect": function() { return /* reexport safe */ _useUpdateLayoutEffect__WEBPACK_IMPORTED_MODULE_10__.default; },
/* harmony export */   "useBoolean": function() { return /* reexport safe */ _useBoolean__WEBPACK_IMPORTED_MODULE_11__.default; },
/* harmony export */   "useToggle": function() { return /* reexport safe */ _useToggle__WEBPACK_IMPORTED_MODULE_12__.default; },
/* harmony export */   "useDocumentVisibility": function() { return /* reexport safe */ _useDocumentVisibility__WEBPACK_IMPORTED_MODULE_13__.default; },
/* harmony export */   "useSelections": function() { return /* reexport safe */ _useSelections__WEBPACK_IMPORTED_MODULE_14__.default; },
/* harmony export */   "useThrottle": function() { return /* reexport safe */ _useThrottle__WEBPACK_IMPORTED_MODULE_15__.default; },
/* harmony export */   "useThrottleFn": function() { return /* reexport safe */ _useThrottleFn__WEBPACK_IMPORTED_MODULE_16__.default; },
/* harmony export */   "useThrottleEffect": function() { return /* reexport safe */ _useThrottleEffect__WEBPACK_IMPORTED_MODULE_17__.default; },
/* harmony export */   "useDebounce": function() { return /* reexport safe */ _useDebounce__WEBPACK_IMPORTED_MODULE_18__.default; },
/* harmony export */   "useDebounceFn": function() { return /* reexport safe */ _useDebounceFn__WEBPACK_IMPORTED_MODULE_19__.default; },
/* harmony export */   "useDebounceEffect": function() { return /* reexport safe */ _useDebounceEffect__WEBPACK_IMPORTED_MODULE_20__.default; },
/* harmony export */   "usePrevious": function() { return /* reexport safe */ _usePrevious__WEBPACK_IMPORTED_MODULE_21__.default; },
/* harmony export */   "useMouse": function() { return /* reexport safe */ _useMouse__WEBPACK_IMPORTED_MODULE_22__.default; },
/* harmony export */   "useScroll": function() { return /* reexport safe */ _useScroll__WEBPACK_IMPORTED_MODULE_23__.default; },
/* harmony export */   "useClickAway": function() { return /* reexport safe */ _useClickAway__WEBPACK_IMPORTED_MODULE_24__.default; },
/* harmony export */   "useFullscreen": function() { return /* reexport safe */ _useFullscreen__WEBPACK_IMPORTED_MODULE_25__.default; },
/* harmony export */   "useInViewport": function() { return /* reexport safe */ _useInViewport__WEBPACK_IMPORTED_MODULE_26__.default; },
/* harmony export */   "useKeyPress": function() { return /* reexport safe */ _useKeyPress__WEBPACK_IMPORTED_MODULE_27__.default; },
/* harmony export */   "useEventListener": function() { return /* reexport safe */ _useEventListener__WEBPACK_IMPORTED_MODULE_28__.default; },
/* harmony export */   "useHover": function() { return /* reexport safe */ _useHover__WEBPACK_IMPORTED_MODULE_29__.default; },
/* harmony export */   "useUnmount": function() { return /* reexport safe */ _useUnmount__WEBPACK_IMPORTED_MODULE_30__.default; },
/* harmony export */   "useSet": function() { return /* reexport safe */ _useSet__WEBPACK_IMPORTED_MODULE_31__.default; },
/* harmony export */   "useMemoizedFn": function() { return /* reexport safe */ _useMemoizedFn__WEBPACK_IMPORTED_MODULE_32__.default; },
/* harmony export */   "useMap": function() { return /* reexport safe */ _useMap__WEBPACK_IMPORTED_MODULE_33__.default; },
/* harmony export */   "useCreation": function() { return /* reexport safe */ _useCreation__WEBPACK_IMPORTED_MODULE_34__.default; },
/* harmony export */   "useDrag": function() { return /* reexport safe */ _useDrag__WEBPACK_IMPORTED_MODULE_35__.default; },
/* harmony export */   "useDrop": function() { return /* reexport safe */ _useDrop__WEBPACK_IMPORTED_MODULE_36__.default; },
/* harmony export */   "useMount": function() { return /* reexport safe */ _useMount__WEBPACK_IMPORTED_MODULE_37__.default; },
/* harmony export */   "useCounter": function() { return /* reexport safe */ _useCounter__WEBPACK_IMPORTED_MODULE_38__.default; },
/* harmony export */   "useUpdate": function() { return /* reexport safe */ _useUpdate__WEBPACK_IMPORTED_MODULE_39__.default; },
/* harmony export */   "useTextSelection": function() { return /* reexport safe */ _useTextSelection__WEBPACK_IMPORTED_MODULE_40__.default; },
/* harmony export */   "useEventTarget": function() { return /* reexport safe */ _useEventTarget__WEBPACK_IMPORTED_MODULE_41__.default; },
/* harmony export */   "useHistoryTravel": function() { return /* reexport safe */ _useHistoryTravel__WEBPACK_IMPORTED_MODULE_42__.default; },
/* harmony export */   "useCookieState": function() { return /* reexport safe */ _useCookieState__WEBPACK_IMPORTED_MODULE_43__.default; },
/* harmony export */   "useSetState": function() { return /* reexport safe */ _useSetState__WEBPACK_IMPORTED_MODULE_44__.default; },
/* harmony export */   "useInterval": function() { return /* reexport safe */ _useInterval__WEBPACK_IMPORTED_MODULE_45__.default; },
/* harmony export */   "useWhyDidYouUpdate": function() { return /* reexport safe */ _useWhyDidYouUpdate__WEBPACK_IMPORTED_MODULE_46__.default; },
/* harmony export */   "useTitle": function() { return /* reexport safe */ _useTitle__WEBPACK_IMPORTED_MODULE_47__.default; },
/* harmony export */   "useNetwork": function() { return /* reexport safe */ _useNetwork__WEBPACK_IMPORTED_MODULE_48__.default; },
/* harmony export */   "useTimeout": function() { return /* reexport safe */ _useTimeout__WEBPACK_IMPORTED_MODULE_49__.default; },
/* harmony export */   "useReactive": function() { return /* reexport safe */ _useReactive__WEBPACK_IMPORTED_MODULE_50__.default; },
/* harmony export */   "useFavicon": function() { return /* reexport safe */ _useFavicon__WEBPACK_IMPORTED_MODULE_51__.default; },
/* harmony export */   "useCountDown": function() { return /* reexport safe */ _useCountDown__WEBPACK_IMPORTED_MODULE_52__.default; },
/* harmony export */   "useWebSocket": function() { return /* reexport safe */ _useWebSocket__WEBPACK_IMPORTED_MODULE_53__.default; },
/* harmony export */   "useLockFn": function() { return /* reexport safe */ _useLockFn__WEBPACK_IMPORTED_MODULE_54__.default; },
/* harmony export */   "useUnmountedRef": function() { return /* reexport safe */ _useUnmountedRef__WEBPACK_IMPORTED_MODULE_55__.default; },
/* harmony export */   "useExternal": function() { return /* reexport safe */ _useExternal__WEBPACK_IMPORTED_MODULE_56__.default; },
/* harmony export */   "useSafeState": function() { return /* reexport safe */ _useSafeState__WEBPACK_IMPORTED_MODULE_57__.default; },
/* harmony export */   "useLatest": function() { return /* reexport safe */ _useLatest__WEBPACK_IMPORTED_MODULE_58__.default; },
/* harmony export */   "useIsomorphicLayoutEffect": function() { return /* reexport safe */ _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_59__.default; },
/* harmony export */   "useDeepCompareEffect": function() { return /* reexport safe */ _useDeepCompareEffect__WEBPACK_IMPORTED_MODULE_60__.default; },
/* harmony export */   "useAsyncEffect": function() { return /* reexport safe */ _useAsyncEffect__WEBPACK_IMPORTED_MODULE_61__.default; },
/* harmony export */   "useLongPress": function() { return /* reexport safe */ _useLongPress__WEBPACK_IMPORTED_MODULE_62__.default; },
/* harmony export */   "useRafState": function() { return /* reexport safe */ _useRafState__WEBPACK_IMPORTED_MODULE_63__.default; },
/* harmony export */   "useTrackedEffect": function() { return /* reexport safe */ _useTrackedEffect__WEBPACK_IMPORTED_MODULE_64__.default; },
/* harmony export */   "usePagination": function() { return /* reexport safe */ _usePagination__WEBPACK_IMPORTED_MODULE_65__.default; },
/* harmony export */   "useAntdTable": function() { return /* reexport safe */ _useAntdTable__WEBPACK_IMPORTED_MODULE_66__.default; },
/* harmony export */   "useFusionTable": function() { return /* reexport safe */ _useFusionTable__WEBPACK_IMPORTED_MODULE_67__.default; },
/* harmony export */   "useInfiniteScroll": function() { return /* reexport safe */ _useInfiniteScroll__WEBPACK_IMPORTED_MODULE_68__.default; },
/* harmony export */   "useGetState": function() { return /* reexport safe */ _useGetState__WEBPACK_IMPORTED_MODULE_69__.default; },
/* harmony export */   "clearCache": function() { return /* reexport safe */ _useRequest__WEBPACK_IMPORTED_MODULE_70__.clearCache; },
/* harmony export */   "useFocusWithin": function() { return /* reexport safe */ _useFocusWithin__WEBPACK_IMPORTED_MODULE_71__.default; },
/* harmony export */   "createUpdateEffect": function() { return /* reexport safe */ _createUpdateEffect__WEBPACK_IMPORTED_MODULE_72__.createUpdateEffect; },
/* harmony export */   "useRafInterval": function() { return /* reexport safe */ _useRafInterval__WEBPACK_IMPORTED_MODULE_73__.default; },
/* harmony export */   "useRafTimeout": function() { return /* reexport safe */ _useRafTimeout__WEBPACK_IMPORTED_MODULE_74__.default; }
/* harmony export */ });
/* harmony import */ var _useAntdTable__WEBPACK_IMPORTED_MODULE_66__ = __webpack_require__(/*! ./useAntdTable */ "./node_modules/ahooks/es/useAntdTable/index.js");
/* harmony import */ var _useAsyncEffect__WEBPACK_IMPORTED_MODULE_61__ = __webpack_require__(/*! ./useAsyncEffect */ "./node_modules/ahooks/es/useAsyncEffect/index.js");
/* harmony import */ var _useBoolean__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./useBoolean */ "./node_modules/ahooks/es/useBoolean/index.js");
/* harmony import */ var _useClickAway__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./useClickAway */ "./node_modules/ahooks/es/useClickAway/index.js");
/* harmony import */ var _useControllableValue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useControllableValue */ "./node_modules/ahooks/es/useControllableValue/index.js");
/* harmony import */ var _useCookieState__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! ./useCookieState */ "./node_modules/ahooks/es/useCookieState/index.js");
/* harmony import */ var _useCountDown__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! ./useCountDown */ "./node_modules/ahooks/es/useCountDown/index.js");
/* harmony import */ var _useCounter__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! ./useCounter */ "./node_modules/ahooks/es/useCounter/index.js");
/* harmony import */ var _useCreation__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./useCreation */ "./node_modules/ahooks/es/useCreation/index.js");
/* harmony import */ var _useDebounce__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./useDebounce */ "./node_modules/ahooks/es/useDebounce/index.js");
/* harmony import */ var _useDebounceEffect__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./useDebounceEffect */ "./node_modules/ahooks/es/useDebounceEffect/index.js");
/* harmony import */ var _useDebounceFn__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./useDebounceFn */ "./node_modules/ahooks/es/useDebounceFn/index.js");
/* harmony import */ var _useDeepCompareEffect__WEBPACK_IMPORTED_MODULE_60__ = __webpack_require__(/*! ./useDeepCompareEffect */ "./node_modules/ahooks/es/useDeepCompareEffect/index.js");
/* harmony import */ var _useDocumentVisibility__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./useDocumentVisibility */ "./node_modules/ahooks/es/useDocumentVisibility/index.js");
/* harmony import */ var _useDrag__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./useDrag */ "./node_modules/ahooks/es/useDrag/index.js");
/* harmony import */ var _useDrop__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./useDrop */ "./node_modules/ahooks/es/useDrop/index.js");
/* harmony import */ var _useDynamicList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useDynamicList */ "./node_modules/ahooks/es/useDynamicList/index.js");
/* harmony import */ var _useEventEmitter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useEventEmitter */ "./node_modules/ahooks/es/useEventEmitter/index.js");
/* harmony import */ var _useEventListener__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./useEventListener */ "./node_modules/ahooks/es/useEventListener/index.js");
/* harmony import */ var _useEventTarget__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! ./useEventTarget */ "./node_modules/ahooks/es/useEventTarget/index.js");
/* harmony import */ var _useExternal__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! ./useExternal */ "./node_modules/ahooks/es/useExternal/index.js");
/* harmony import */ var _useFavicon__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! ./useFavicon */ "./node_modules/ahooks/es/useFavicon/index.js");
/* harmony import */ var _useFocusWithin__WEBPACK_IMPORTED_MODULE_71__ = __webpack_require__(/*! ./useFocusWithin */ "./node_modules/ahooks/es/useFocusWithin/index.js");
/* harmony import */ var _useFullscreen__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./useFullscreen */ "./node_modules/ahooks/es/useFullscreen/index.js");
/* harmony import */ var _useFusionTable__WEBPACK_IMPORTED_MODULE_67__ = __webpack_require__(/*! ./useFusionTable */ "./node_modules/ahooks/es/useFusionTable/index.js");
/* harmony import */ var _useGetState__WEBPACK_IMPORTED_MODULE_69__ = __webpack_require__(/*! ./useGetState */ "./node_modules/ahooks/es/useGetState/index.js");
/* harmony import */ var _useHistoryTravel__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! ./useHistoryTravel */ "./node_modules/ahooks/es/useHistoryTravel/index.js");
/* harmony import */ var _useHover__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./useHover */ "./node_modules/ahooks/es/useHover/index.js");
/* harmony import */ var _useInfiniteScroll__WEBPACK_IMPORTED_MODULE_68__ = __webpack_require__(/*! ./useInfiniteScroll */ "./node_modules/ahooks/es/useInfiniteScroll/index.js");
/* harmony import */ var _useInterval__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! ./useInterval */ "./node_modules/ahooks/es/useInterval/index.js");
/* harmony import */ var _useInViewport__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./useInViewport */ "./node_modules/ahooks/es/useInViewport/index.js");
/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_59__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ "./node_modules/ahooks/es/useIsomorphicLayoutEffect/index.js");
/* harmony import */ var _useKeyPress__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./useKeyPress */ "./node_modules/ahooks/es/useKeyPress/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_58__ = __webpack_require__(/*! ./useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _useLocalStorageState__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./useLocalStorageState */ "./node_modules/ahooks/es/useLocalStorageState/index.js");
/* harmony import */ var _useLockFn__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! ./useLockFn */ "./node_modules/ahooks/es/useLockFn/index.js");
/* harmony import */ var _useLongPress__WEBPACK_IMPORTED_MODULE_62__ = __webpack_require__(/*! ./useLongPress */ "./node_modules/ahooks/es/useLongPress/index.js");
/* harmony import */ var _useMap__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./useMap */ "./node_modules/ahooks/es/useMap/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useMount__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./useMount */ "./node_modules/ahooks/es/useMount/index.js");
/* harmony import */ var _useMouse__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./useMouse */ "./node_modules/ahooks/es/useMouse/index.js");
/* harmony import */ var _useNetwork__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! ./useNetwork */ "./node_modules/ahooks/es/useNetwork/index.js");
/* harmony import */ var _usePagination__WEBPACK_IMPORTED_MODULE_65__ = __webpack_require__(/*! ./usePagination */ "./node_modules/ahooks/es/usePagination/index.js");
/* harmony import */ var _usePrevious__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./usePrevious */ "./node_modules/ahooks/es/usePrevious/index.js");
/* harmony import */ var _useRafState__WEBPACK_IMPORTED_MODULE_63__ = __webpack_require__(/*! ./useRafState */ "./node_modules/ahooks/es/useRafState/index.js");
/* harmony import */ var _useReactive__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! ./useReactive */ "./node_modules/ahooks/es/useReactive/index.js");
/* harmony import */ var _useRequest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useRequest */ "./node_modules/ahooks/es/useRequest/index.js");
/* harmony import */ var _useRequest__WEBPACK_IMPORTED_MODULE_70__ = __webpack_require__(/*! ./useRequest */ "./node_modules/ahooks/es/useRequest/src/utils/cache.js");
/* harmony import */ var _useResponsive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useResponsive */ "./node_modules/ahooks/es/useResponsive/index.js");
/* harmony import */ var _useSafeState__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(/*! ./useSafeState */ "./node_modules/ahooks/es/useSafeState/index.js");
/* harmony import */ var _useScroll__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./useScroll */ "./node_modules/ahooks/es/useScroll/index.js");
/* harmony import */ var _useSelections__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./useSelections */ "./node_modules/ahooks/es/useSelections/index.js");
/* harmony import */ var _useSessionStorageState__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useSessionStorageState */ "./node_modules/ahooks/es/useSessionStorageState/index.js");
/* harmony import */ var _useSet__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./useSet */ "./node_modules/ahooks/es/useSet/index.js");
/* harmony import */ var _useSetState__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! ./useSetState */ "./node_modules/ahooks/es/useSetState/index.js");
/* harmony import */ var _useSize__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useSize */ "./node_modules/ahooks/es/useSize/index.js");
/* harmony import */ var _useTextSelection__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! ./useTextSelection */ "./node_modules/ahooks/es/useTextSelection/index.js");
/* harmony import */ var _useThrottle__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./useThrottle */ "./node_modules/ahooks/es/useThrottle/index.js");
/* harmony import */ var _useThrottleEffect__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./useThrottleEffect */ "./node_modules/ahooks/es/useThrottleEffect/index.js");
/* harmony import */ var _useThrottleFn__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./useThrottleFn */ "./node_modules/ahooks/es/useThrottleFn/index.js");
/* harmony import */ var _useTimeout__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! ./useTimeout */ "./node_modules/ahooks/es/useTimeout/index.js");
/* harmony import */ var _useTitle__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! ./useTitle */ "./node_modules/ahooks/es/useTitle/index.js");
/* harmony import */ var _useToggle__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./useToggle */ "./node_modules/ahooks/es/useToggle/index.js");
/* harmony import */ var _useTrackedEffect__WEBPACK_IMPORTED_MODULE_64__ = __webpack_require__(/*! ./useTrackedEffect */ "./node_modules/ahooks/es/useTrackedEffect/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _useUnmountedRef__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! ./useUnmountedRef */ "./node_modules/ahooks/es/useUnmountedRef/index.js");
/* harmony import */ var _useUpdate__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ./useUpdate */ "./node_modules/ahooks/es/useUpdate/index.js");
/* harmony import */ var _useUpdateEffect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useUpdateEffect */ "./node_modules/ahooks/es/useUpdateEffect/index.js");
/* harmony import */ var _useUpdateLayoutEffect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useUpdateLayoutEffect */ "./node_modules/ahooks/es/useUpdateLayoutEffect/index.js");
/* harmony import */ var _useVirtualList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useVirtualList */ "./node_modules/ahooks/es/useVirtualList/index.js");
/* harmony import */ var _useWebSocket__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! ./useWebSocket */ "./node_modules/ahooks/es/useWebSocket/index.js");
/* harmony import */ var _useWhyDidYouUpdate__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! ./useWhyDidYouUpdate */ "./node_modules/ahooks/es/useWhyDidYouUpdate/index.js");
/* harmony import */ var _createUpdateEffect__WEBPACK_IMPORTED_MODULE_72__ = __webpack_require__(/*! ./createUpdateEffect */ "./node_modules/ahooks/es/createUpdateEffect/index.js");
/* harmony import */ var _useRafInterval__WEBPACK_IMPORTED_MODULE_73__ = __webpack_require__(/*! ./useRafInterval */ "./node_modules/ahooks/es/useRafInterval/index.js");
/* harmony import */ var _useRafTimeout__WEBPACK_IMPORTED_MODULE_74__ = __webpack_require__(/*! ./useRafTimeout */ "./node_modules/ahooks/es/useRafTimeout/index.js");












































































/***/ }),

/***/ "./node_modules/ahooks/es/useAntdTable/index.js":
/*!******************************************************!*\
  !*** ./node_modules/ahooks/es/useAntdTable/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _usePagination__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../usePagination */ "./node_modules/ahooks/es/usePagination/index.js");
/* harmony import */ var _useUpdateEffect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useUpdateEffect */ "./node_modules/ahooks/es/useUpdateEffect/index.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};






var useAntdTable = function useAntdTable(service, options) {
  var _a;

  if (options === void 0) {
    options = {};
  }

  var form = options.form,
      _b = options.defaultType,
      defaultType = _b === void 0 ? 'simple' : _b,
      defaultParams = options.defaultParams,
      _c = options.manual,
      manual = _c === void 0 ? false : _c,
      _d = options.refreshDeps,
      refreshDeps = _d === void 0 ? [] : _d,
      _e = options.ready,
      ready = _e === void 0 ? true : _e,
      rest = __rest(options, ["form", "defaultType", "defaultParams", "manual", "refreshDeps", "ready"]);

  var result = (0,_usePagination__WEBPACK_IMPORTED_MODULE_1__.default)(service, __assign({
    manual: true
  }, rest));
  var _f = result.params,
      params = _f === void 0 ? [] : _f,
      run = result.run;
  var cacheFormTableData = params[2] || {};

  var _g = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)((cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.type) || defaultType), 2),
      type = _g[0],
      setType = _g[1];

  var allFormDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});
  var isAntdV4 = !!(form === null || form === void 0 ? void 0 : form.getInternalHooks); // get current active field values

  var getActivetFieldValues = function getActivetFieldValues() {
    if (!form) {
      return {};
    } // antd 4


    if (isAntdV4) {
      return form.getFieldsValue(null, function () {
        return true;
      });
    } // antd 3


    var allFieldsValue = form.getFieldsValue();
    var activeFieldsValue = {};
    Object.keys(allFieldsValue).forEach(function (key) {
      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {
        activeFieldsValue[key] = allFieldsValue[key];
      }
    });
    return activeFieldsValue;
  };

  var validateFields = function validateFields() {
    if (!form) {
      return Promise.resolve({});
    }

    var activeFieldsValue = getActivetFieldValues();
    var fields = Object.keys(activeFieldsValue); // antd 4

    if (isAntdV4) {
      return form.validateFields(fields);
    } // antd 3


    return new Promise(function (resolve, reject) {
      form.validateFields(fields, function (errors, values) {
        if (errors) {
          reject(errors);
        } else {
          resolve(values);
        }
      });
    });
  };

  var restoreForm = function restoreForm() {
    if (!form) {
      return;
    } // antd v4


    if (isAntdV4) {
      return form.setFieldsValue(allFormDataRef.current);
    } // antd v3


    var activeFieldsValue = {};
    Object.keys(allFormDataRef.current).forEach(function (key) {
      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {
        activeFieldsValue[key] = allFormDataRef.current[key];
      }
    });
    form.setFieldsValue(activeFieldsValue);
  };

  var changeType = function changeType() {
    var activeFieldsValue = getActivetFieldValues();
    allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), activeFieldsValue);
    setType(function (t) {
      return t === 'simple' ? 'advance' : 'simple';
    });
  };

  var _submit = function _submit(initPagination) {
    if (!ready) {
      return;
    }

    setTimeout(function () {
      validateFields().then(function (values) {
        if (values === void 0) {
          values = {};
        }

        var pagination = initPagination || __assign(__assign({
          pageSize: options.defaultPageSize || 10
        }, (params === null || params === void 0 ? void 0 : params[0]) || {}), {
          current: 1
        });

        if (!form) {
          // @ts-ignore
          run(pagination);
          return;
        } // record all form data


        allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), values); // @ts-ignore

        run(pagination, values, {
          allFormData: allFormDataRef.current,
          type: type
        });
      })["catch"](function (err) {
        return err;
      });
    });
  };

  var reset = function reset() {
    if (form) {
      form.resetFields();
    }

    _submit();
  };

  var submit = function submit(e) {
    var _a;

    (_a = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);

    _submit();
  };

  var onTableChange = function onTableChange(pagination, filters, sorter) {
    var _a = __read(params || []),
        oldPaginationParams = _a[0],
        restParams = _a.slice(1);

    run.apply(void 0, __spread([__assign(__assign({}, oldPaginationParams), {
      current: pagination.current,
      pageSize: pagination.pageSize,
      filters: filters,
      sorter: sorter
    })], restParams));
  }; // init


  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    // if has cache, use cached params. ignore manual and ready.
    if (params.length > 0) {
      allFormDataRef.current = (cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.allFormData) || {};
      restoreForm(); // @ts-ignore

      run.apply(void 0, __spread(params));
      return;
    }

    if (!manual && ready) {
      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};
      restoreForm();

      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);
    }
  }, []); // change search type, restore form data

  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    if (!ready) {
      return;
    }

    restoreForm();
  }, [type]); // refresh & ready change on the same time

  var hasAutoRun = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  hasAutoRun.current = false;
  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    if (!manual && ready) {
      hasAutoRun.current = true;

      if (form) {
        form.resetFields();
      }

      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};
      restoreForm();

      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);
    }
  }, [ready]);
  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    if (hasAutoRun.current) {
      return;
    }

    if (!ready) {
      return;
    }

    if (!manual) {
      hasAutoRun.current = true;
      result.pagination.changeCurrent(1);
    }
  }, __spread(refreshDeps));
  return __assign(__assign({}, result), {
    tableProps: {
      dataSource: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.list) || [],
      loading: result.loading,
      onChange: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__.default)(onTableChange),
      pagination: {
        current: result.pagination.current,
        pageSize: result.pagination.pageSize,
        total: result.pagination.total
      }
    },
    search: {
      submit: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__.default)(submit),
      type: type,
      changeType: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__.default)(changeType),
      reset: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__.default)(reset)
    }
  });
};

/* harmony default export */ __webpack_exports__["default"] = (useAntdTable);

/***/ }),

/***/ "./node_modules/ahooks/es/useAsyncEffect/index.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useAsyncEffect/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __awaiter = undefined && undefined.__awaiter || function (thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function (resolve) {
      resolve(value);
    });
  }

  return new (P || (P = Promise))(function (resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }

    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }

    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }

    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};

var __generator = undefined && undefined.__generator || function (thisArg, body) {
  var _ = {
    label: 0,
    sent: function sent() {
      if (t[0] & 1) throw t[1];
      return t[1];
    },
    trys: [],
    ops: []
  },
      f,
      y,
      t,
      g;
  return g = {
    next: verb(0),
    "throw": verb(1),
    "return": verb(2)
  }, typeof Symbol === "function" && (g[Symbol.iterator] = function () {
    return this;
  }), g;

  function verb(n) {
    return function (v) {
      return step([n, v]);
    };
  }

  function step(op) {
    if (f) throw new TypeError("Generator is already executing.");

    while (_) {
      try {
        if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
        if (y = 0, t) op = [op[0] & 2, t.value];

        switch (op[0]) {
          case 0:
          case 1:
            t = op;
            break;

          case 4:
            _.label++;
            return {
              value: op[1],
              done: false
            };

          case 5:
            _.label++;
            y = op[1];
            op = [0];
            continue;

          case 7:
            op = _.ops.pop();

            _.trys.pop();

            continue;

          default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
              _ = 0;
              continue;
            }

            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }

            if (op[0] === 6 && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }

            if (t && _.label < t[2]) {
              _.label = t[2];

              _.ops.push(op);

              break;
            }

            if (t[2]) _.ops.pop();

            _.trys.pop();

            continue;
        }

        op = body.call(thisArg, _);
      } catch (e) {
        op = [6, e];
        y = 0;
      } finally {
        f = t = 0;
      }
    }

    if (op[0] & 5) throw op[1];
    return {
      value: op[0] ? op[1] : void 0,
      done: true
    };
  }
};



function useAsyncEffect(effect, deps) {
  function isGenerator(val) {
    return typeof val[Symbol.asyncIterator] === 'function';
  }

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var e = effect();
    var cancelled = false;

    function execute() {
      return __awaiter(this, void 0, void 0, function () {
        var result;
        return __generator(this, function (_a) {
          switch (_a.label) {
            case 0:
              if (!isGenerator(e)) return [3
              /*break*/
              , 4];
              _a.label = 1;

            case 1:
              if (false) {}
              return [4
              /*yield*/
              , e.next()];

            case 2:
              result = _a.sent();

              if (cancelled || result.done) {
                return [3
                /*break*/
                , 3];
              }

              return [3
              /*break*/
              , 1];

            case 3:
              return [3
              /*break*/
              , 6];

            case 4:
              return [4
              /*yield*/
              , e];

            case 5:
              _a.sent();

              _a.label = 6;

            case 6:
              return [2
              /*return*/
              ];
          }
        });
      });
    }

    execute();
    return function () {
      cancelled = true;
    };
  }, deps);
}

/* harmony default export */ __webpack_exports__["default"] = (useAsyncEffect);

/***/ }),

/***/ "./node_modules/ahooks/es/useBoolean/index.js":
/*!****************************************************!*\
  !*** ./node_modules/ahooks/es/useBoolean/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useBoolean; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useToggle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useToggle */ "./node_modules/ahooks/es/useToggle/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};



function useBoolean(defaultValue) {
  if (defaultValue === void 0) {
    defaultValue = false;
  }

  var _a = __read((0,_useToggle__WEBPACK_IMPORTED_MODULE_1__.default)(defaultValue), 2),
      state = _a[0],
      _b = _a[1],
      toggle = _b.toggle,
      _set = _b.set;

  var actions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    var setTrue = function setTrue() {
      return _set(true);
    };

    var setFalse = function setFalse() {
      return _set(false);
    };

    return {
      toggle: toggle,
      set: function set(v) {
        return _set(!!v);
      },
      setTrue: setTrue,
      setFalse: setFalse
    };
  }, []);
  return [state, actions];
}

/***/ }),

/***/ "./node_modules/ahooks/es/useClickAway/index.js":
/*!******************************************************!*\
  !*** ./node_modules/ahooks/es/useClickAway/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useClickAway; }
/* harmony export */ });
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");



function useClickAway(onClickAway, target, eventName) {
  if (eventName === void 0) {
    eventName = 'click';
  }

  var onClickAwayRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_0__.default)(onClickAway);
  (0,_utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    var handler = function handler(event) {
      var targets = Array.isArray(target) ? target : [target];

      if (targets.some(function (item) {
        var targetElement = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_2__.getTargetElement)(item);
        return !targetElement || (targetElement === null || targetElement === void 0 ? void 0 : targetElement.contains(event.target));
      })) {
        return;
      }

      onClickAwayRef.current(event);
    };

    var eventNames = Array.isArray(eventName) ? eventName : [eventName];
    eventNames.forEach(function (event) {
      return document.addEventListener(event, handler);
    });
    return function () {
      eventNames.forEach(function (event) {
        return document.removeEventListener(event, handler);
      });
    };
  }, Array.isArray(eventName) ? eventName : [eventName], target);
}

/***/ }),

/***/ "./node_modules/ahooks/es/useControllableValue/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/ahooks/es/useControllableValue/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useUpdate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useUpdate */ "./node_modules/ahooks/es/useUpdate/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};





function useControllableValue(props, options) {
  if (props === void 0) {
    props = {};
  }

  if (options === void 0) {
    options = {};
  }

  var defaultValue = options.defaultValue,
      _a = options.defaultValuePropName,
      defaultValuePropName = _a === void 0 ? 'defaultValue' : _a,
      _b = options.valuePropName,
      valuePropName = _b === void 0 ? 'value' : _b,
      _c = options.trigger,
      trigger = _c === void 0 ? 'onChange' : _c;
  var value = props[valuePropName];
  var isControlled = (valuePropName in props);
  var initialValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    if (isControlled) {
      return value;
    }

    if (defaultValuePropName in props) {
      return props[defaultValuePropName];
    }

    return defaultValue;
  }, []);
  var stateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(initialValue);

  if (isControlled) {
    stateRef.current = value;
  }

  var update = (0,_useUpdate__WEBPACK_IMPORTED_MODULE_1__.default)();

  var setState = function setState(v) {
    var args = [];

    for (var _i = 1; _i < arguments.length; _i++) {
      args[_i - 1] = arguments[_i];
    }

    if (!isControlled) {
      stateRef.current = v;
      update();
    }

    if (props[trigger]) {
      props[trigger].apply(props, __spread([v], args));
    }
  };

  return [stateRef.current, (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_2__.default)(setState)];
}

/* harmony default export */ __webpack_exports__["default"] = (useControllableValue);

/***/ }),

/***/ "./node_modules/ahooks/es/useCookieState/index.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useCookieState/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ "./node_modules/js-cookie/src/js.cookie.js");
/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(js_cookie__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils */ "./node_modules/ahooks/es/utils/index.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};






function useCookieState(cookieKey, options) {
  if (options === void 0) {
    options = {};
  }

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
    var cookieValue = js_cookie__WEBPACK_IMPORTED_MODULE_0___default().get(cookieKey);
    if (typeof cookieValue === 'string') return cookieValue;

    if ((0,_utils__WEBPACK_IMPORTED_MODULE_2__.isFunction)(options.defaultValue)) {
      return options.defaultValue();
    }

    return options.defaultValue;
  }), 2),
      state = _a[0],
      setState = _a[1];

  var updateState = (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__.default)(function (newValue, newOptions) {
    if (newOptions === void 0) {
      newOptions = {};
    }

    var _a = __assign(__assign({}, options), newOptions),
        defaultValue = _a.defaultValue,
        restOptions = __rest(_a, ["defaultValue"]);

    setState(function (prevState) {
      var value = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.isFunction)(newValue) ? newValue(prevState) : newValue;

      if (value === undefined) {
        js_cookie__WEBPACK_IMPORTED_MODULE_0___default().remove(cookieKey);
      } else {
        js_cookie__WEBPACK_IMPORTED_MODULE_0___default().set(cookieKey, value, restOptions);
      }

      return value;
    });
  });
  return [state, updateState];
}

/* harmony default export */ __webpack_exports__["default"] = (useCookieState);

/***/ }),

/***/ "./node_modules/ahooks/es/useCountDown/index.js":
/*!******************************************************!*\
  !*** ./node_modules/ahooks/es/useCountDown/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dayjs */ "./node_modules/ahooks/node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};





var calcLeft = function calcLeft(t) {
  if (!t) {
    return 0;
  } // https://stackoverflow.com/questions/4310953/invalid-date-in-safari


  var left = dayjs__WEBPACK_IMPORTED_MODULE_0___default()(t).valueOf() - new Date().getTime();

  if (left < 0) {
    return 0;
  }

  return left;
};

var parseMs = function parseMs(milliseconds) {
  return {
    days: Math.floor(milliseconds / 86400000),
    hours: Math.floor(milliseconds / 3600000) % 24,
    minutes: Math.floor(milliseconds / 60000) % 60,
    seconds: Math.floor(milliseconds / 1000) % 60,
    milliseconds: Math.floor(milliseconds) % 1000
  };
};

var useCountdown = function useCountdown(options) {
  var _a = options || {},
      targetDate = _a.targetDate,
      _b = _a.interval,
      interval = _b === void 0 ? 1000 : _b,
      onEnd = _a.onEnd;

  var _c = __read((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
    return calcLeft(targetDate);
  }), 2),
      timeLeft = _c[0],
      setTimeLeft = _c[1];

  var onEndRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_2__.default)(onEnd);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (!targetDate) {
      // for stop
      setTimeLeft(0);
      return;
    } // 立即执行一次


    setTimeLeft(calcLeft(targetDate));
    var timer = setInterval(function () {
      var _a;

      var targetLeft = calcLeft(targetDate);
      setTimeLeft(targetLeft);

      if (targetLeft === 0) {
        clearInterval(timer);
        (_a = onEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onEndRef);
      }
    }, interval);
    return function () {
      return clearInterval(timer);
    };
  }, [targetDate, interval]);
  var formattedRes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
    return parseMs(timeLeft);
  }, [timeLeft]);
  return [timeLeft, formattedRes];
};

/* harmony default export */ __webpack_exports__["default"] = (useCountdown);

/***/ }),

/***/ "./node_modules/ahooks/es/useCounter/index.js":
/*!****************************************************!*\
  !*** ./node_modules/ahooks/es/useCounter/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




function getTargetValue(val, options) {
  if (options === void 0) {
    options = {};
  }

  var min = options.min,
      max = options.max;
  var target = val;

  if (typeof max === 'number') {
    target = Math.min(max, target);
  }

  if (typeof min === 'number') {
    target = Math.max(min, target);
  }

  return target;
}

function useCounter(initialValue, options) {
  if (initialValue === void 0) {
    initialValue = 0;
  }

  if (options === void 0) {
    options = {};
  }

  var min = options.min,
      max = options.max;

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {
    return getTargetValue(initialValue, {
      min: min,
      max: max
    });
  }), 2),
      current = _a[0],
      setCurrent = _a[1];

  var setValue = function setValue(value) {
    setCurrent(function (c) {
      var target = typeof value === 'number' ? value : value(c);
      return getTargetValue(target, {
        max: max,
        min: min
      });
    });
  };

  var inc = function inc(delta) {
    if (delta === void 0) {
      delta = 1;
    }

    setValue(function (c) {
      return c + delta;
    });
  };

  var dec = function dec(delta) {
    if (delta === void 0) {
      delta = 1;
    }

    setValue(function (c) {
      return c - delta;
    });
  };

  var set = function set(value) {
    setValue(value);
  };

  var reset = function reset() {
    setValue(initialValue);
  };

  return [current, {
    inc: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(inc),
    dec: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(dec),
    set: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(set),
    reset: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(reset)
  }];
}

/* harmony default export */ __webpack_exports__["default"] = (useCounter);

/***/ }),

/***/ "./node_modules/ahooks/es/useCreation/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useCreation/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useCreation; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_depsAreSame__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/depsAreSame */ "./node_modules/ahooks/es/utils/depsAreSame.js");


function useCreation(factory, deps) {
  var current = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({
    deps: deps,
    obj: undefined,
    initialized: false
  }).current;

  if (current.initialized === false || !(0,_utils_depsAreSame__WEBPACK_IMPORTED_MODULE_1__.default)(current.deps, deps)) {
    current.deps = deps;
    current.obj = factory();
    current.initialized = true;
  }

  return current.obj;
}

/***/ }),

/***/ "./node_modules/ahooks/es/useDebounceEffect/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/ahooks/es/useDebounceEffect/index.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useDebounceFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDebounceFn */ "./node_modules/ahooks/es/useDebounceFn/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _useUpdateEffect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useUpdateEffect */ "./node_modules/ahooks/es/useUpdateEffect/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};






function useDebounceEffect(effect, deps, options) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}), 2),
      flag = _a[0],
      setFlag = _a[1];

  var _b = (0,_useDebounceFn__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    setFlag({});
  }, options),
      run = _b.run,
      cancel = _b.cancel;

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    return run();
  }, deps);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_2__.default)(cancel);
  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_3__.default)(effect, [flag]);
}

/* harmony default export */ __webpack_exports__["default"] = (useDebounceEffect);

/***/ }),

/***/ "./node_modules/ahooks/es/useDebounceFn/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/useDebounceFn/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/debounce */ "./node_modules/lodash/debounce.js");
/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};






function useDebounceFn(fn, options) {
  var _a;

  if (true) {
    if (typeof fn !== 'function') {
      console.error("useDebounceFn expected parameter is a function, got " + typeof fn);
    }
  }

  var fnRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_2__.default)(fn);
  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;
  var debounced = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
    return lodash_debounce__WEBPACK_IMPORTED_MODULE_0___default()(function () {
      var args = [];

      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }

      return fnRef.current.apply(fnRef, __spread(args));
    }, wait, options);
  }, []);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_3__.default)(function () {
    debounced.cancel();
  });
  return {
    run: debounced,
    cancel: debounced.cancel,
    flush: debounced.flush
  };
}

/* harmony default export */ __webpack_exports__["default"] = (useDebounceFn);

/***/ }),

/***/ "./node_modules/ahooks/es/useDebounce/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useDebounce/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useDebounceFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDebounceFn */ "./node_modules/ahooks/es/useDebounceFn/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




function useDebounce(value, options) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value), 2),
      debounced = _a[0],
      setDebounced = _a[1];

  var run = (0,_useDebounceFn__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    setDebounced(value);
  }, options).run;
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    run();
  }, [value]);
  return debounced;
}

/* harmony default export */ __webpack_exports__["default"] = (useDebounce);

/***/ }),

/***/ "./node_modules/ahooks/es/useDeepCompareEffect/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/ahooks/es/useDeepCompareEffect/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var lodash_isEqual__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/isEqual */ "./node_modules/lodash/isEqual.js");
/* harmony import */ var lodash_isEqual__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash_isEqual__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");



var depsEqual = function depsEqual(aDeps, bDeps) {
  if (bDeps === void 0) {
    bDeps = [];
  }

  return lodash_isEqual__WEBPACK_IMPORTED_MODULE_0___default()(aDeps, bDeps);
};

var useDeepCompareEffect = function useDeepCompareEffect(effect, deps) {
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
  var signalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);

  if (!depsEqual(deps, ref.current)) {
    ref.current = deps;
    signalRef.current += 1;
  }

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(effect, [signalRef.current]);
};

/* harmony default export */ __webpack_exports__["default"] = (useDeepCompareEffect);

/***/ }),

/***/ "./node_modules/ahooks/es/useDocumentVisibility/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/ahooks/es/useDocumentVisibility/index.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useEventListener__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useEventListener */ "./node_modules/ahooks/es/useEventListener/index.js");
/* harmony import */ var _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isBrowser */ "./node_modules/ahooks/es/utils/isBrowser.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};





var getVisibility = function getVisibility() {
  if (!_utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__.default) {
    return 'visible';
  }

  return document.visibilityState;
};

function useDocumentVisibility() {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {
    return getVisibility();
  }), 2),
      documentVisibility = _a[0],
      setDocumentVisibility = _a[1];

  (0,_useEventListener__WEBPACK_IMPORTED_MODULE_2__.default)('visibilitychange', function () {
    setDocumentVisibility(getVisibility());
  }, {
    target: function target() {
      return document;
    }
  });
  return documentVisibility;
}

/* harmony default export */ __webpack_exports__["default"] = (useDocumentVisibility);

/***/ }),

/***/ "./node_modules/ahooks/es/useDrag/index.js":
/*!*************************************************!*\
  !*** ./node_modules/ahooks/es/useDrag/index.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");




var useDrag = function useDrag(data, target, options) {
  if (options === void 0) {
    options = {};
  }

  var optionsRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_0__.default)(options);
  (0,_utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    var targetElement = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_2__.getTargetElement)(target);

    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {
      return;
    }

    var onDragStart = function onDragStart(event) {
      var _a, _b;

      (_b = (_a = optionsRef.current).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, event);
      event.dataTransfer.setData('custom', JSON.stringify(data));
    };

    var onDragEnd = function onDragEnd(event) {
      var _a, _b;

      (_b = (_a = optionsRef.current).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };

    targetElement.setAttribute('draggable', 'true');
    targetElement.addEventListener('dragstart', onDragStart);
    targetElement.addEventListener('dragend', onDragEnd);
    return function () {
      targetElement.removeEventListener('dragstart', onDragStart);
      targetElement.removeEventListener('dragend', onDragEnd);
    };
  }, [], target);
};

/* harmony default export */ __webpack_exports__["default"] = (useDrag);

/***/ }),

/***/ "./node_modules/ahooks/es/useDrop/index.js":
/*!*************************************************!*\
  !*** ./node_modules/ahooks/es/useDrop/index.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");





var useDrop = function useDrop(target, options) {
  if (options === void 0) {
    options = {};
  }

  var optionsRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(options); // https://stackoverflow.com/a/26459269

  var dragEnterTarget = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  (0,_utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    var targetElement = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(target);

    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {
      return;
    }

    var onData = function onData(dataTransfer, event) {
      var uri = dataTransfer.getData('text/uri-list');
      var dom = dataTransfer.getData('custom');

      if (dom && optionsRef.current.onDom) {
        var data = dom;

        try {
          data = JSON.parse(dom);
        } catch (e) {
          data = dom;
        }

        optionsRef.current.onDom(data, event);
        return;
      }

      if (uri && optionsRef.current.onUri) {
        optionsRef.current.onUri(uri, event);
        return;
      }

      if (dataTransfer.files && dataTransfer.files.length && optionsRef.current.onFiles) {
        optionsRef.current.onFiles(Array.from(dataTransfer.files), event);
        return;
      }

      if (dataTransfer.items && dataTransfer.items.length && optionsRef.current.onText) {
        dataTransfer.items[0].getAsString(function (text) {
          optionsRef.current.onText(text, event);
        });
      }
    };

    var onDragEnter = function onDragEnter(event) {
      var _a, _b;

      event.preventDefault();
      event.stopPropagation();
      dragEnterTarget.current = event.target;
      (_b = (_a = optionsRef.current).onDragEnter) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };

    var onDragOver = function onDragOver(event) {
      var _a, _b;

      event.preventDefault();
      (_b = (_a = optionsRef.current).onDragOver) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };

    var onDragLeave = function onDragLeave(event) {
      var _a, _b;

      if (event.target === dragEnterTarget.current) {
        (_b = (_a = optionsRef.current).onDragLeave) === null || _b === void 0 ? void 0 : _b.call(_a, event);
      }
    };

    var onDrop = function onDrop(event) {
      var _a, _b;

      event.preventDefault();
      onData(event.dataTransfer, event);
      (_b = (_a = optionsRef.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };

    var onPaste = function onPaste(event) {
      var _a, _b;

      onData(event.clipboardData, event);
      (_b = (_a = optionsRef.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };

    targetElement.addEventListener('dragenter', onDragEnter);
    targetElement.addEventListener('dragover', onDragOver);
    targetElement.addEventListener('dragleave', onDragLeave);
    targetElement.addEventListener('drop', onDrop);
    targetElement.addEventListener('paste', onPaste);
    return function () {
      targetElement.removeEventListener('dragenter', onDragEnter);
      targetElement.removeEventListener('dragover', onDragOver);
      targetElement.removeEventListener('dragleave', onDragLeave);
      targetElement.removeEventListener('drop', onDrop);
      targetElement.removeEventListener('paste', onPaste);
    };
  }, [], target);
};

/* harmony default export */ __webpack_exports__["default"] = (useDrop);

/***/ }),

/***/ "./node_modules/ahooks/es/useDynamicList/index.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useDynamicList/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};



var useDynamicList = function useDynamicList(initialList) {
  if (initialList === void 0) {
    initialList = [];
  }

  var counterRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(-1);
  var keyList = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);
  var setKey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (index) {
    counterRef.current += 1;
    keyList.current.splice(index, 0, counterRef.current);
  }, []);

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {
    initialList.forEach(function (_, index) {
      setKey(index);
    });
    return initialList;
  }), 2),
      list = _a[0],
      setList = _a[1];

  var resetList = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (newList) {
    keyList.current = [];
    setList(function () {
      newList.forEach(function (_, index) {
        setKey(index);
      });
      return newList;
    });
  }, []);
  var insert = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (index, item) {
    setList(function (l) {
      var temp = __spread(l);

      temp.splice(index, 0, item);
      setKey(index);
      return temp;
    });
  }, []);
  var getKey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (index) {
    return keyList.current[index];
  }, []);
  var getIndex = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (key) {
    return keyList.current.findIndex(function (ele) {
      return ele === key;
    });
  }, []);
  var merge = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (index, items) {
    setList(function (l) {
      var temp = __spread(l);

      items.forEach(function (_, i) {
        setKey(index + i);
      });
      temp.splice.apply(temp, __spread([index, 0], items));
      return temp;
    });
  }, []);
  var replace = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (index, item) {
    setList(function (l) {
      var temp = __spread(l);

      temp[index] = item;
      return temp;
    });
  }, []);
  var remove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (index) {
    setList(function (l) {
      var temp = __spread(l);

      temp.splice(index, 1); // remove keys if necessary

      try {
        keyList.current.splice(index, 1);
      } catch (e) {
        console.error(e);
      }

      return temp;
    });
  }, []);
  var move = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (oldIndex, newIndex) {
    if (oldIndex === newIndex) {
      return;
    }

    setList(function (l) {
      var newList = __spread(l);

      var temp = newList.filter(function (_, index) {
        return index !== oldIndex;
      });
      temp.splice(newIndex, 0, newList[oldIndex]); // move keys if necessary

      try {
        var keyTemp = keyList.current.filter(function (_, index) {
          return index !== oldIndex;
        });
        keyTemp.splice(newIndex, 0, keyList.current[oldIndex]);
        keyList.current = keyTemp;
      } catch (e) {
        console.error(e);
      }

      return temp;
    });
  }, []);
  var push = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (item) {
    setList(function (l) {
      setKey(l.length);
      return l.concat([item]);
    });
  }, []);
  var pop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    // remove keys if necessary
    try {
      keyList.current = keyList.current.slice(0, keyList.current.length - 1);
    } catch (e) {
      console.error(e);
    }

    setList(function (l) {
      return l.slice(0, l.length - 1);
    });
  }, []);
  var unshift = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (item) {
    setList(function (l) {
      setKey(0);
      return [item].concat(l);
    });
  }, []);
  var shift = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    // remove keys if necessary
    try {
      keyList.current = keyList.current.slice(1, keyList.current.length);
    } catch (e) {
      console.error(e);
    }

    setList(function (l) {
      return l.slice(1, l.length);
    });
  }, []);
  var sortList = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (result) {
    return result.map(function (item, index) {
      return {
        key: index,
        item: item
      };
    }) // add index into obj
    .sort(function (a, b) {
      return getIndex(a.key) - getIndex(b.key);
    }) // sort based on the index of table
    .filter(function (item) {
      return !!item.item;
    }) // remove undefined(s)
    .map(function (item) {
      return item.item;
    });
  }, // retrive the data
  []);
  return {
    list: list,
    insert: insert,
    merge: merge,
    replace: replace,
    remove: remove,
    getKey: getKey,
    getIndex: getIndex,
    move: move,
    push: push,
    pop: pop,
    unshift: unshift,
    shift: shift,
    sortList: sortList,
    resetList: resetList
  };
};

/* harmony default export */ __webpack_exports__["default"] = (useDynamicList);

/***/ }),

/***/ "./node_modules/ahooks/es/useEventEmitter/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/ahooks/es/useEventEmitter/index.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "EventEmitter": function() { return /* binding */ EventEmitter; },
/* harmony export */   "default": function() { return /* binding */ useEventEmitter; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __values = undefined && undefined.__values || function (o) {
  var s = typeof Symbol === "function" && Symbol.iterator,
      m = s && o[s],
      i = 0;
  if (m) return m.call(o);
  if (o && typeof o.length === "number") return {
    next: function next() {
      if (o && i >= o.length) o = void 0;
      return {
        value: o && o[i++],
        done: !o
      };
    }
  };
  throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};



var EventEmitter =
/** @class */
function () {
  function EventEmitter() {
    var _this = this;

    this.subscriptions = new Set();

    this.emit = function (val) {
      var e_1, _a;

      try {
        for (var _b = __values(_this.subscriptions), _c = _b.next(); !_c.done; _c = _b.next()) {
          var subscription = _c.value;
          subscription(val);
        }
      } catch (e_1_1) {
        e_1 = {
          error: e_1_1
        };
      } finally {
        try {
          if (_c && !_c.done && (_a = _b["return"])) _a.call(_b);
        } finally {
          if (e_1) throw e_1.error;
        }
      }
    };

    this.useSubscription = function (callback) {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      var callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
      callbackRef.current = callback; // eslint-disable-next-line react-hooks/rules-of-hooks

      (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
        function subscription(val) {
          if (callbackRef.current) {
            callbackRef.current(val);
          }
        }

        _this.subscriptions.add(subscription);

        return function () {
          _this.subscriptions["delete"](subscription);
        };
      }, []);
    };
  }

  return EventEmitter;
}();


function useEventEmitter() {
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();

  if (!ref.current) {
    ref.current = new EventEmitter();
  }

  return ref.current;
}

/***/ }),

/***/ "./node_modules/ahooks/es/useEventListener/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/ahooks/es/useEventListener/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");




function useEventListener(eventName, handler, options) {
  if (options === void 0) {
    options = {};
  }

  var handlerRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_0__.default)(handler);
  (0,_utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    var targetElement = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_2__.getTargetElement)(options.target, window);

    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {
      return;
    }

    var eventListener = function eventListener(event) {
      return handlerRef.current(event);
    };

    targetElement.addEventListener(eventName, eventListener, {
      capture: options.capture,
      once: options.once,
      passive: options.passive
    });
    return function () {
      targetElement.removeEventListener(eventName, eventListener, {
        capture: options.capture
      });
    };
  }, [eventName, options.capture, options.once, options.passive], options.target);
}

/* harmony default export */ __webpack_exports__["default"] = (useEventListener);

/***/ }),

/***/ "./node_modules/ahooks/es/useEventTarget/index.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useEventTarget/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




function useEventTarget(options) {
  var _a = options || {},
      initialValue = _a.initialValue,
      transformer = _a.transformer;

  var _b = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue), 2),
      value = _b[0],
      setValue = _b[1];

  var transformerRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(transformer);
  var reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    return setValue(initialValue);
  }, []);
  var onChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (e) {
    var _value = e.target.value;

    if (typeof transformerRef.current === 'function') {
      return setValue(transformerRef.current(_value));
    } // no transformer => U and T should be the same


    return setValue(_value);
  }, []);
  return [value, {
    onChange: onChange,
    reset: reset
  }];
}

/* harmony default export */ __webpack_exports__["default"] = (useEventTarget);

/***/ }),

/***/ "./node_modules/ahooks/es/useExternal/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useExternal/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

 // {[path]: count}
// remove external when no used

var EXTERNAL_USED_COUNT = {};

var loadScript = function loadScript(path, props) {
  if (props === void 0) {
    props = {};
  }

  var script = document.querySelector("script[src=\"" + path + "\"]");

  if (!script) {
    var newScript_1 = document.createElement('script');
    newScript_1.src = path;
    Object.keys(props).forEach(function (key) {
      newScript_1[key] = props[key];
    });
    newScript_1.setAttribute('data-status', 'loading');
    document.body.appendChild(newScript_1);
    return {
      ref: newScript_1,
      status: 'loading'
    };
  }

  return {
    ref: script,
    status: script.getAttribute('data-status') || 'ready'
  };
};

var loadCss = function loadCss(path, props) {
  if (props === void 0) {
    props = {};
  }

  var css = document.querySelector("link[href=\"" + path + "\"]");

  if (!css) {
    var newCss_1 = document.createElement('link');
    newCss_1.rel = 'stylesheet';
    newCss_1.href = path;
    Object.keys(props).forEach(function (key) {
      newCss_1[key] = props[key];
    }); // IE9+

    var isLegacyIECss = ('hideFocus' in newCss_1); // use preload in IE Edge (to detect load errors)

    if (isLegacyIECss && newCss_1.relList) {
      newCss_1.rel = 'preload';
      newCss_1.as = 'style';
    }

    newCss_1.setAttribute('data-status', 'loading');
    document.head.appendChild(newCss_1);
    return {
      ref: newCss_1,
      status: 'loading'
    };
  }

  return {
    ref: css,
    status: css.getAttribute('data-status') || 'ready'
  };
};

var useExternal = function useExternal(path, options) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(path ? 'loading' : 'unset'), 2),
      status = _a[0],
      setStatus = _a[1];

  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (!path) {
      setStatus('unset');
      return;
    }

    var pathname = path.replace(/[|#].*$/, '');

    if ((options === null || options === void 0 ? void 0 : options.type) === 'css' || !(options === null || options === void 0 ? void 0 : options.type) && /(^css!|\.css$)/.test(pathname)) {
      var result = loadCss(path, options === null || options === void 0 ? void 0 : options.css);
      ref.current = result.ref;
      setStatus(result.status);
    } else if ((options === null || options === void 0 ? void 0 : options.type) === 'js' || !(options === null || options === void 0 ? void 0 : options.type) && /(^js!|\.js$)/.test(pathname)) {
      var result = loadScript(path, options === null || options === void 0 ? void 0 : options.js);
      ref.current = result.ref;
      setStatus(result.status);
    } else {
      // do nothing
      console.error("Cannot infer the type of external resource, and please provide a type ('js' | 'css'). " + 'Refer to the https://ahooks.js.org/hooks/dom/use-external/#options');
    }

    if (!ref.current) {
      return;
    }

    if (EXTERNAL_USED_COUNT[path] === undefined) {
      EXTERNAL_USED_COUNT[path] = 1;
    } else {
      EXTERNAL_USED_COUNT[path] += 1;
    }

    var handler = function handler(event) {
      var _a;

      var targetStatus = event.type === 'load' ? 'ready' : 'error';
      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.setAttribute('data-status', targetStatus);
      setStatus(targetStatus);
    };

    ref.current.addEventListener('load', handler);
    ref.current.addEventListener('error', handler);
    return function () {
      var _a, _b, _c;

      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener('load', handler);
      (_b = ref.current) === null || _b === void 0 ? void 0 : _b.removeEventListener('error', handler);
      EXTERNAL_USED_COUNT[path] -= 1;

      if (EXTERNAL_USED_COUNT[path] === 0) {
        (_c = ref.current) === null || _c === void 0 ? void 0 : _c.remove();
      }

      ref.current = undefined;
    };
  }, [path]);
  return status;
};

/* harmony default export */ __webpack_exports__["default"] = (useExternal);

/***/ }),

/***/ "./node_modules/ahooks/es/useFavicon/index.js":
/*!****************************************************!*\
  !*** ./node_modules/ahooks/es/useFavicon/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

var ImgTypeMap = {
  SVG: 'image/svg+xml',
  ICO: 'image/x-icon',
  GIF: 'image/gif',
  PNG: 'image/png'
};

var useFavicon = function useFavicon(href) {
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (!href) return;
    var cutUrl = href.split('.');
    var imgSuffix = cutUrl[cutUrl.length - 1].toLocaleUpperCase();
    var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
    link.type = ImgTypeMap[imgSuffix];
    link.href = href;
    link.rel = 'shortcut icon';
    document.getElementsByTagName('head')[0].appendChild(link);
  }, [href]);
};

/* harmony default export */ __webpack_exports__["default"] = (useFavicon);

/***/ }),

/***/ "./node_modules/ahooks/es/useFocusWithin/index.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useFocusWithin/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useFocusWithin; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useEventListener__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useEventListener */ "./node_modules/ahooks/es/useEventListener/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};



function useFocusWithin(target, options) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), 2),
      isFocusWithin = _a[0],
      setIsFocusWithin = _a[1];

  var _b = options || {},
      onFocus = _b.onFocus,
      onBlur = _b.onBlur,
      onChange = _b.onChange;

  (0,_useEventListener__WEBPACK_IMPORTED_MODULE_1__.default)('focusin', function (e) {
    if (!isFocusWithin) {
      onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);
      onChange === null || onChange === void 0 ? void 0 : onChange(true);
      setIsFocusWithin(true);
    }
  }, {
    target: target
  });
  (0,_useEventListener__WEBPACK_IMPORTED_MODULE_1__.default)('focusout', function (e) {
    var _a, _b; // @ts-ignore


    if (isFocusWithin && !((_b = (_a = e.currentTarget) === null || _a === void 0 ? void 0 : _a.contains) === null || _b === void 0 ? void 0 : _b.call(_a, e.relatedTarget))) {
      onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);
      onChange === null || onChange === void 0 ? void 0 : onChange(false);
      setIsFocusWithin(false);
    }
  }, {
    target: target
  });
  return isFocusWithin;
}

/***/ }),

/***/ "./node_modules/ahooks/es/useFullscreen/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/useFullscreen/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var screenfull__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! screenfull */ "./node_modules/screenfull/dist/screenfull.js");
/* harmony import */ var screenfull__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(screenfull__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};








var useFullscreen = function useFullscreen(target, options) {
  var _a = options || {},
      onExit = _a.onExit,
      onEnter = _a.onEnter;

  var onExitRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_2__.default)(onExit);
  var onEnterRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_2__.default)(onEnter);

  var _b = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), 2),
      state = _b[0],
      setState = _b[1];

  var onChange = function onChange() {
    var _a, _b;

    if ((screenfull__WEBPACK_IMPORTED_MODULE_1___default().isEnabled)) {
      var isFullscreen = (screenfull__WEBPACK_IMPORTED_MODULE_1___default().isFullscreen);

      if (isFullscreen) {
        (_a = onEnterRef.current) === null || _a === void 0 ? void 0 : _a.call(onEnterRef);
      } else {
        screenfull__WEBPACK_IMPORTED_MODULE_1___default().off('change', onChange);
        (_b = onExitRef.current) === null || _b === void 0 ? void 0 : _b.call(onExitRef);
      }

      setState(isFullscreen);
    }
  };

  var enterFullscreen = function enterFullscreen() {
    var el = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(target);

    if (!el) {
      return;
    }

    if ((screenfull__WEBPACK_IMPORTED_MODULE_1___default().isEnabled)) {
      try {
        screenfull__WEBPACK_IMPORTED_MODULE_1___default().request(el);
        screenfull__WEBPACK_IMPORTED_MODULE_1___default().on('change', onChange);
      } catch (error) {
        console.error(error);
      }
    }
  };

  var exitFullscreen = function exitFullscreen() {
    if (!state) {
      return;
    }

    if ((screenfull__WEBPACK_IMPORTED_MODULE_1___default().isEnabled)) {
      screenfull__WEBPACK_IMPORTED_MODULE_1___default().exit();
    }
  };

  var toggleFullscreen = function toggleFullscreen() {
    if (state) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  };

  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_4__.default)(function () {
    if ((screenfull__WEBPACK_IMPORTED_MODULE_1___default().isEnabled)) {
      screenfull__WEBPACK_IMPORTED_MODULE_1___default().off('change', onChange);
    }
  });
  return [state, {
    enterFullscreen: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_5__.default)(enterFullscreen),
    exitFullscreen: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_5__.default)(exitFullscreen),
    toggleFullscreen: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_5__.default)(toggleFullscreen),
    isEnabled: (screenfull__WEBPACK_IMPORTED_MODULE_1___default().isEnabled)
  }];
};

/* harmony default export */ __webpack_exports__["default"] = (useFullscreen);

/***/ }),

/***/ "./node_modules/ahooks/es/useFusionTable/fusionAdapter.js":
/*!****************************************************************!*\
  !*** ./node_modules/ahooks/es/useFusionTable/fusionAdapter.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "fieldAdapter": function() { return /* binding */ fieldAdapter; },
/* harmony export */   "resultAdapter": function() { return /* binding */ resultAdapter; }
/* harmony export */ });
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var fieldAdapter = function fieldAdapter(field) {
  return {
    getFieldInstance: function getFieldInstance(name) {
      return field.getNames().includes(name);
    },
    setFieldsValue: field.setValues,
    getFieldsValue: field.getValues,
    resetFields: field.resetToDefault,
    validateFields: function validateFields(fields, callback) {
      field.validate(fields, callback);
    }
  };
};
var resultAdapter = function resultAdapter(result) {
  var tableProps = {
    dataSource: result.tableProps.dataSource,
    loading: result.tableProps.loading,
    onSort: function onSort(dataIndex, order) {
      var _a;

      result.tableProps.onChange({
        current: result.pagination.current,
        pageSize: result.pagination.pageSize
      }, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.filters, {
        field: dataIndex,
        order: order
      });
    },
    onFilter: function onFilter(filterParams) {
      var _a;

      result.tableProps.onChange({
        current: result.pagination.current,
        pageSize: result.pagination.pageSize
      }, filterParams, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.sorter);
    }
  };
  var paginationProps = {
    onChange: result.pagination.changeCurrent,
    onPageSizeChange: result.pagination.changePageSize,
    current: result.pagination.current,
    pageSize: result.pagination.pageSize,
    total: result.pagination.total
  };
  return __assign(__assign({}, result), {
    tableProps: tableProps,
    paginationProps: paginationProps
  });
};

/***/ }),

/***/ "./node_modules/ahooks/es/useFusionTable/index.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useFusionTable/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useAntdTable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useAntdTable */ "./node_modules/ahooks/es/useAntdTable/index.js");
/* harmony import */ var _fusionAdapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fusionAdapter */ "./node_modules/ahooks/es/useFusionTable/fusionAdapter.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};




var useFusionTable = function useFusionTable(service, options) {
  if (options === void 0) {
    options = {};
  }

  var ret = (0,_useAntdTable__WEBPACK_IMPORTED_MODULE_0__.default)(service, __assign(__assign({}, options), {
    form: options.field ? (0,_fusionAdapter__WEBPACK_IMPORTED_MODULE_1__.fieldAdapter)(options.field) : undefined
  }));
  return (0,_fusionAdapter__WEBPACK_IMPORTED_MODULE_1__.resultAdapter)(ret);
};

/* harmony default export */ __webpack_exports__["default"] = (useFusionTable);

/***/ }),

/***/ "./node_modules/ahooks/es/useGetState/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useGetState/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};



function useGetState(initialState) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState), 2),
      state = _a[0],
      setState = _a[1];

  var stateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(state);
  stateRef.current = state;
  var getState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    return stateRef.current;
  }, []);
  return [state, setState, getState];
}

/* harmony default export */ __webpack_exports__["default"] = (useGetState);

/***/ }),

/***/ "./node_modules/ahooks/es/useHistoryTravel/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/ahooks/es/useHistoryTravel/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useHistoryTravel; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};




var dumpIndex = function dumpIndex(step, arr) {
  var index = step > 0 ? step - 1 // move forward
  : arr.length + step; // move backward

  if (index >= arr.length - 1) {
    index = arr.length - 1;
  }

  if (index < 0) {
    index = 0;
  }

  return index;
};

var split = function split(step, targetArr) {
  var index = dumpIndex(step, targetArr);
  return {
    _current: targetArr[index],
    _before: targetArr.slice(0, index),
    _after: targetArr.slice(index + 1)
  };
};

function useHistoryTravel(initialValue) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({
    present: initialValue,
    past: [],
    future: []
  }), 2),
      history = _a[0],
      setHistory = _a[1];

  var present = history.present,
      past = history.past,
      future = history.future;
  var initialValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(initialValue);

  var reset = function reset() {
    var params = [];

    for (var _i = 0; _i < arguments.length; _i++) {
      params[_i] = arguments[_i];
    }

    var _initial = params.length > 0 ? params[0] : initialValueRef.current;

    initialValueRef.current = _initial;
    setHistory({
      present: _initial,
      future: [],
      past: []
    });
  };

  var updateValue = function updateValue(val) {
    setHistory({
      present: val,
      future: [],
      past: __spread(past, [present])
    });
  };

  var _forward = function _forward(step) {
    if (step === void 0) {
      step = 1;
    }

    if (future.length === 0) {
      return;
    }

    var _a = split(step, future),
        _before = _a._before,
        _current = _a._current,
        _after = _a._after;

    setHistory({
      past: __spread(past, [present], _before),
      present: _current,
      future: _after
    });
  };

  var _backward = function _backward(step) {
    if (step === void 0) {
      step = -1;
    }

    if (past.length === 0) {
      return;
    }

    var _a = split(step, past),
        _before = _a._before,
        _current = _a._current,
        _after = _a._after;

    setHistory({
      past: _before,
      present: _current,
      future: __spread(_after, [present], future)
    });
  };

  var go = function go(step) {
    var stepNum = typeof step === 'number' ? step : Number(step);

    if (stepNum === 0) {
      return;
    }

    if (stepNum > 0) {
      return _forward(stepNum);
    }

    _backward(stepNum);
  };

  return {
    value: present,
    backLength: past.length,
    forwardLength: future.length,
    setValue: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(updateValue),
    go: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(go),
    back: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
      go(-1);
    }),
    forward: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
      go(1);
    }),
    reset: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(reset)
  };
}

/***/ }),

/***/ "./node_modules/ahooks/es/useHover/index.js":
/*!**************************************************!*\
  !*** ./node_modules/ahooks/es/useHover/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useBoolean__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useBoolean */ "./node_modules/ahooks/es/useBoolean/index.js");
/* harmony import */ var _useEventListener__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useEventListener */ "./node_modules/ahooks/es/useEventListener/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};



/* harmony default export */ __webpack_exports__["default"] = (function (target, options) {
  var _a = options || {},
      onEnter = _a.onEnter,
      onLeave = _a.onLeave;

  var _b = __read((0,_useBoolean__WEBPACK_IMPORTED_MODULE_0__.default)(false), 2),
      state = _b[0],
      _c = _b[1],
      setTrue = _c.setTrue,
      setFalse = _c.setFalse;

  (0,_useEventListener__WEBPACK_IMPORTED_MODULE_1__.default)('mouseenter', function () {
    onEnter === null || onEnter === void 0 ? void 0 : onEnter();
    setTrue();
  }, {
    target: target
  });
  (0,_useEventListener__WEBPACK_IMPORTED_MODULE_1__.default)('mouseleave', function () {
    onLeave === null || onLeave === void 0 ? void 0 : onLeave();
    setFalse();
  }, {
    target: target
  });
  return state;
});

/***/ }),

/***/ "./node_modules/ahooks/es/useInViewport/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/useInViewport/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var intersection_observer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! intersection-observer */ "./node_modules/intersection-observer/intersection-observer.js");
/* harmony import */ var intersection_observer__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(intersection_observer__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __values = undefined && undefined.__values || function (o) {
  var s = typeof Symbol === "function" && Symbol.iterator,
      m = s && o[s],
      i = 0;
  if (m) return m.call(o);
  if (o && typeof o.length === "number") return {
    next: function next() {
      if (o && i >= o.length) o = void 0;
      return {
        value: o && o[i++],
        done: !o
      };
    }
  };
  throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};






function useInViewport(target, options) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), 2),
      state = _a[0],
      setState = _a[1];

  var _b = __read((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), 2),
      ratio = _b[0],
      setRatio = _b[1];

  (0,_utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    var el = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(target);

    if (!el) {
      return;
    }

    var observer = new IntersectionObserver(function (entries) {
      var e_1, _a;

      try {
        for (var entries_1 = __values(entries), entries_1_1 = entries_1.next(); !entries_1_1.done; entries_1_1 = entries_1.next()) {
          var entry = entries_1_1.value;
          setRatio(entry.intersectionRatio);

          if (entry.isIntersecting) {
            setState(true);
          } else {
            setState(false);
          }
        }
      } catch (e_1_1) {
        e_1 = {
          error: e_1_1
        };
      } finally {
        try {
          if (entries_1_1 && !entries_1_1.done && (_a = entries_1["return"])) _a.call(entries_1);
        } finally {
          if (e_1) throw e_1.error;
        }
      }
    }, __assign(__assign({}, options), {
      root: (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(options === null || options === void 0 ? void 0 : options.root)
    }));
    observer.observe(el);
    return function () {
      observer.disconnect();
    };
  }, [], target);
  return [state, ratio];
}

/* harmony default export */ __webpack_exports__["default"] = (useInViewport);

/***/ }),

/***/ "./node_modules/ahooks/es/useInfiniteScroll/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/ahooks/es/useInfiniteScroll/index.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useEventListener__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../useEventListener */ "./node_modules/ahooks/es/useEventListener/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useRequest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useRequest */ "./node_modules/ahooks/es/useRequest/index.js");
/* harmony import */ var _useUpdateEffect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../useUpdateEffect */ "./node_modules/ahooks/es/useUpdateEffect/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_rect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/rect */ "./node_modules/ahooks/es/utils/rect.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __awaiter = undefined && undefined.__awaiter || function (thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function (resolve) {
      resolve(value);
    });
  }

  return new (P || (P = Promise))(function (resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }

    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }

    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }

    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};

var __generator = undefined && undefined.__generator || function (thisArg, body) {
  var _ = {
    label: 0,
    sent: function sent() {
      if (t[0] & 1) throw t[1];
      return t[1];
    },
    trys: [],
    ops: []
  },
      f,
      y,
      t,
      g;
  return g = {
    next: verb(0),
    "throw": verb(1),
    "return": verb(2)
  }, typeof Symbol === "function" && (g[Symbol.iterator] = function () {
    return this;
  }), g;

  function verb(n) {
    return function (v) {
      return step([n, v]);
    };
  }

  function step(op) {
    if (f) throw new TypeError("Generator is already executing.");

    while (_) {
      try {
        if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
        if (y = 0, t) op = [op[0] & 2, t.value];

        switch (op[0]) {
          case 0:
          case 1:
            t = op;
            break;

          case 4:
            _.label++;
            return {
              value: op[1],
              done: false
            };

          case 5:
            _.label++;
            y = op[1];
            op = [0];
            continue;

          case 7:
            op = _.ops.pop();

            _.trys.pop();

            continue;

          default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
              _ = 0;
              continue;
            }

            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }

            if (op[0] === 6 && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }

            if (t && _.label < t[2]) {
              _.label = t[2];

              _.ops.push(op);

              break;
            }

            if (t[2]) _.ops.pop();

            _.trys.pop();

            continue;
        }

        op = body.call(thisArg, _);
      } catch (e) {
        op = [6, e];
        y = 0;
      } finally {
        f = t = 0;
      }
    }

    if (op[0] & 5) throw op[1];
    return {
      value: op[0] ? op[1] : void 0,
      done: true
    };
  }
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};









var useInfiniteScroll = function useInfiniteScroll(service, options) {
  if (options === void 0) {
    options = {};
  }

  var target = options.target,
      isNoMore = options.isNoMore,
      _a = options.threshold,
      threshold = _a === void 0 ? 100 : _a,
      _b = options.reloadDeps,
      reloadDeps = _b === void 0 ? [] : _b,
      manual = options.manual,
      _onBefore = options.onBefore,
      _onSuccess = options.onSuccess,
      _onError = options.onError,
      _onFinally = options.onFinally;

  var _c = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(), 2),
      finalData = _c[0],
      setFinalData = _c[1];

  var _d = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), 2),
      loadingMore = _d[0],
      setLoadingMore = _d[1];

  var noMore = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    if (!isNoMore) return false;
    return isNoMore(finalData);
  }, [finalData]);

  var _e = (0,_useRequest__WEBPACK_IMPORTED_MODULE_1__.default)(function (lastData) {
    return __awaiter(void 0, void 0, void 0, function () {
      var currentData;
      return __generator(this, function (_a) {
        switch (_a.label) {
          case 0:
            return [4
            /*yield*/
            , service(lastData)];

          case 1:
            currentData = _a.sent();

            if (!lastData) {
              setFinalData(currentData);
            } else {
              setFinalData(__assign(__assign({}, currentData), {
                // @ts-ignore
                list: __spread(lastData.list, currentData.list)
              }));
            }

            return [2
            /*return*/
            , currentData];
        }
      });
    });
  }, {
    manual: manual,
    onFinally: function onFinally(_, d, e) {
      setLoadingMore(false);
      _onFinally === null || _onFinally === void 0 ? void 0 : _onFinally(d, e);
    },
    onBefore: function onBefore() {
      return _onBefore === null || _onBefore === void 0 ? void 0 : _onBefore();
    },
    onSuccess: function onSuccess(d) {
      setTimeout(function () {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        scrollMethod();
      });
      _onSuccess === null || _onSuccess === void 0 ? void 0 : _onSuccess(d);
    },
    onError: function onError(e) {
      return _onError === null || _onError === void 0 ? void 0 : _onError(e);
    }
  }),
      loading = _e.loading,
      run = _e.run,
      runAsync = _e.runAsync,
      cancel = _e.cancel;

  var loadMore = function loadMore() {
    if (noMore) return;
    setLoadingMore(true);
    run(finalData);
  };

  var loadMoreAsync = function loadMoreAsync() {
    if (noMore) return;
    setLoadingMore(true);
    return runAsync(finalData);
  };

  var reload = function reload() {
    return run();
  };

  var reloadAsync = function reloadAsync() {
    return runAsync();
  };

  var scrollMethod = function scrollMethod() {
    var el = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_2__.getTargetElement)(target);

    if (!el) {
      return;
    }

    var scrollTop = (0,_utils_rect__WEBPACK_IMPORTED_MODULE_3__.getScrollTop)(el);
    var scrollHeight = (0,_utils_rect__WEBPACK_IMPORTED_MODULE_3__.getScrollHeight)(el);
    var clientHeight = (0,_utils_rect__WEBPACK_IMPORTED_MODULE_3__.getClientHeight)(el);

    if (scrollHeight - scrollTop <= clientHeight + threshold) {
      loadMore();
    }
  };

  (0,_useEventListener__WEBPACK_IMPORTED_MODULE_4__.default)('scroll', function () {
    if (loading || loadingMore) {
      return;
    }

    scrollMethod();
  }, {
    target: target
  });
  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_5__.default)(function () {
    run();
  }, __spread(reloadDeps));
  return {
    data: finalData,
    loading: !loadingMore && loading,
    loadingMore: loadingMore,
    noMore: noMore,
    loadMore: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(loadMore),
    loadMoreAsync: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(loadMoreAsync),
    reload: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(reload),
    reloadAsync: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(reloadAsync),
    mutate: setFinalData,
    cancel: cancel
  };
};

/* harmony default export */ __webpack_exports__["default"] = (useInfiniteScroll);

/***/ }),

/***/ "./node_modules/ahooks/es/useInterval/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useInterval/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");



function useInterval(fn, delay, options) {
  var immediate = options === null || options === void 0 ? void 0 : options.immediate;
  var fnRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(fn);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (typeof delay !== 'number' || delay < 0) return;

    if (immediate) {
      fnRef.current();
    }

    var timer = setInterval(function () {
      fnRef.current();
    }, delay);
    return function () {
      clearInterval(timer);
    };
  }, [delay]);
}

/* harmony default export */ __webpack_exports__["default"] = (useInterval);

/***/ }),

/***/ "./node_modules/ahooks/es/useIsomorphicLayoutEffect/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/ahooks/es/useIsomorphicLayoutEffect/index.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isBrowser */ "./node_modules/ahooks/es/utils/isBrowser.js");


var useIsomorphicLayoutEffect = _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__.default ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;
/* harmony default export */ __webpack_exports__["default"] = (useIsomorphicLayoutEffect);

/***/ }),

/***/ "./node_modules/ahooks/es/useKeyPress/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useKeyPress/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useDeepCompareWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/useDeepCompareWithTarget */ "./node_modules/ahooks/es/utils/useDeepCompareWithTarget.js");
var __values = undefined && undefined.__values || function (o) {
  var s = typeof Symbol === "function" && Symbol.iterator,
      m = s && o[s],
      i = 0;
  if (m) return m.call(o);
  if (o && typeof o.length === "number") return {
    next: function next() {
      if (o && i >= o.length) o = void 0;
      return {
        value: o && o[i++],
        done: !o
      };
    }
  };
  throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};



 // 键盘事件 keyCode 别名

var aliasKeyCodeMap = {
  '0': 48,
  '1': 49,
  '2': 50,
  '3': 51,
  '4': 52,
  '5': 53,
  '6': 54,
  '7': 55,
  '8': 56,
  '9': 57,
  backspace: 8,
  tab: 9,
  enter: 13,
  shift: 16,
  ctrl: 17,
  alt: 18,
  pausebreak: 19,
  capslock: 20,
  esc: 27,
  space: 32,
  pageup: 33,
  pagedown: 34,
  end: 35,
  home: 36,
  leftarrow: 37,
  uparrow: 38,
  rightarrow: 39,
  downarrow: 40,
  insert: 45,
  "delete": 46,
  a: 65,
  b: 66,
  c: 67,
  d: 68,
  e: 69,
  f: 70,
  g: 71,
  h: 72,
  i: 73,
  j: 74,
  k: 75,
  l: 76,
  m: 77,
  n: 78,
  o: 79,
  p: 80,
  q: 81,
  r: 82,
  s: 83,
  t: 84,
  u: 85,
  v: 86,
  w: 87,
  x: 88,
  y: 89,
  z: 90,
  leftwindowkey: 91,
  rightwindowkey: 92,
  selectkey: 93,
  numpad0: 96,
  numpad1: 97,
  numpad2: 98,
  numpad3: 99,
  numpad4: 100,
  numpad5: 101,
  numpad6: 102,
  numpad7: 103,
  numpad8: 104,
  numpad9: 105,
  multiply: 106,
  add: 107,
  subtract: 109,
  decimalpoint: 110,
  divide: 111,
  f1: 112,
  f2: 113,
  f3: 114,
  f4: 115,
  f5: 116,
  f6: 117,
  f7: 118,
  f8: 119,
  f9: 120,
  f10: 121,
  f11: 122,
  f12: 123,
  numlock: 144,
  scrolllock: 145,
  semicolon: 186,
  equalsign: 187,
  comma: 188,
  dash: 189,
  period: 190,
  forwardslash: 191,
  graveaccent: 192,
  openbracket: 219,
  backslash: 220,
  closebracket: 221,
  singlequote: 222
}; // 修饰键

var modifierKey = {
  ctrl: function ctrl(event) {
    return event.ctrlKey;
  },
  shift: function shift(event) {
    return event.shiftKey;
  },
  alt: function alt(event) {
    return event.altKey;
  },
  meta: function meta(event) {
    return event.metaKey;
  }
}; // 根据 event 计算激活键数量

function countKeyByEvent(event) {
  var countOfModifier = Object.keys(modifierKey).reduce(function (total, key) {
    if (modifierKey[key](event)) {
      return total + 1;
    }

    return total;
  }, 0); // 16 17 18 91 92 是修饰键的 keyCode，如果 keyCode 是修饰键，那么激活数量就是修饰键的数量，如果不是，那么就需要 +1

  return [16, 17, 18, 91, 92].includes(event.keyCode) ? countOfModifier : countOfModifier + 1;
}
/**
 * 判断按键是否激活
 * @param [event: KeyboardEvent]键盘事件
 * @param [keyFilter: any] 当前键
 * @returns Boolean
 */


function genFilterKey(event, keyFilter, exactMatch) {
  var e_1, _a; // 浏览器自动补全 input 的时候，会触发 keyDown、keyUp 事件，但此时 event.key 等为空


  if (!event.key) {
    return false;
  } // 数字类型直接匹配事件的 keyCode


  if (typeof keyFilter === 'number') {
    return event.keyCode === keyFilter;
  } // 字符串依次判断是否有组合键


  var genArr = keyFilter.split('.');
  var genLen = 0;

  try {
    for (var genArr_1 = __values(genArr), genArr_1_1 = genArr_1.next(); !genArr_1_1.done; genArr_1_1 = genArr_1.next()) {
      var key = genArr_1_1.value; // 组合键

      var genModifier = modifierKey[key]; // keyCode 别名

      var aliasKeyCode = aliasKeyCodeMap[key.toLowerCase()];

      if (genModifier && genModifier(event) || aliasKeyCode && aliasKeyCode === event.keyCode) {
        genLen++;
      }
    }
  } catch (e_1_1) {
    e_1 = {
      error: e_1_1
    };
  } finally {
    try {
      if (genArr_1_1 && !genArr_1_1.done && (_a = genArr_1["return"])) _a.call(genArr_1);
    } finally {
      if (e_1) throw e_1.error;
    }
  }
  /**
   * 需要判断触发的键位和监听的键位完全一致，判断方法就是触发的键位里有且等于监听的键位
   * genLen === genArr.length 能判断出来触发的键位里有监听的键位
   * countKeyByEvent(event) === genArr.length 判断出来触发的键位数量里有且等于监听的键位数量
   * 主要用来防止按组合键其子集也会触发的情况，例如监听 ctrl+a 会触发监听 ctrl 和 a 两个键的事件。
   */


  if (exactMatch) {
    return genLen === genArr.length && countKeyByEvent(event) === genArr.length;
  }

  return genLen === genArr.length;
}
/**
 * 键盘输入预处理方法
 * @param [keyFilter: any] 当前键
 * @returns () => Boolean
 */


function genKeyFormater(keyFilter, exactMatch) {
  if (typeof keyFilter === 'function') {
    return keyFilter;
  }

  if (typeof keyFilter === 'string' || typeof keyFilter === 'number') {
    return function (event) {
      return genFilterKey(event, keyFilter, exactMatch);
    };
  }

  if (Array.isArray(keyFilter)) {
    return function (event) {
      return keyFilter.some(function (item) {
        return genFilterKey(event, item, exactMatch);
      });
    };
  }

  return keyFilter ? function () {
    return true;
  } : function () {
    return false;
  };
}

var defaultEvents = ['keydown'];

function useKeyPress(keyFilter, eventHandler, option) {
  var _a = option || {},
      _b = _a.events,
      events = _b === void 0 ? defaultEvents : _b,
      target = _a.target,
      _c = _a.exactMatch,
      exactMatch = _c === void 0 ? false : _c;

  var eventHandlerRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_0__.default)(eventHandler);
  var keyFilterRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_0__.default)(keyFilter);
  (0,_utils_useDeepCompareWithTarget__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    var e_2, _a;

    var _b;

    var el = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_2__.getTargetElement)(target, window);

    if (!el) {
      return;
    }

    var callbackHandler = function callbackHandler(event) {
      var _a;

      var genGuard = genKeyFormater(keyFilterRef.current, exactMatch);

      if (genGuard(event)) {
        return (_a = eventHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(eventHandlerRef, event);
      }
    };

    try {
      for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {
        var eventName = events_1_1.value;
        (_b = el === null || el === void 0 ? void 0 : el.addEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler);
      }
    } catch (e_2_1) {
      e_2 = {
        error: e_2_1
      };
    } finally {
      try {
        if (events_1_1 && !events_1_1.done && (_a = events_1["return"])) _a.call(events_1);
      } finally {
        if (e_2) throw e_2.error;
      }
    }

    return function () {
      var e_3, _a;

      var _b;

      try {
        for (var events_2 = __values(events), events_2_1 = events_2.next(); !events_2_1.done; events_2_1 = events_2.next()) {
          var eventName = events_2_1.value;
          (_b = el === null || el === void 0 ? void 0 : el.removeEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler);
        }
      } catch (e_3_1) {
        e_3 = {
          error: e_3_1
        };
      } finally {
        try {
          if (events_2_1 && !events_2_1.done && (_a = events_2["return"])) _a.call(events_2);
        } finally {
          if (e_3) throw e_3.error;
        }
      }
    };
  }, [events], target);
}

/* harmony default export */ __webpack_exports__["default"] = (useKeyPress);

/***/ }),

/***/ "./node_modules/ahooks/es/useLatest/index.js":
/*!***************************************************!*\
  !*** ./node_modules/ahooks/es/useLatest/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


function useLatest(value) {
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);
  ref.current = value;
  return ref;
}

/* harmony default export */ __webpack_exports__["default"] = (useLatest);

/***/ }),

/***/ "./node_modules/ahooks/es/useLocalStorageState/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/ahooks/es/useLocalStorageState/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _createUseStorageState__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createUseStorageState */ "./node_modules/ahooks/es/createUseStorageState/index.js");
/* harmony import */ var _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isBrowser */ "./node_modules/ahooks/es/utils/isBrowser.js");


var useLocalStorageState = (0,_createUseStorageState__WEBPACK_IMPORTED_MODULE_0__.createUseStorageState)(function () {
  return _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__.default ? localStorage : undefined;
});
/* harmony default export */ __webpack_exports__["default"] = (useLocalStorageState);

/***/ }),

/***/ "./node_modules/ahooks/es/useLockFn/index.js":
/*!***************************************************!*\
  !*** ./node_modules/ahooks/es/useLockFn/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __awaiter = undefined && undefined.__awaiter || function (thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function (resolve) {
      resolve(value);
    });
  }

  return new (P || (P = Promise))(function (resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }

    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }

    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }

    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};

var __generator = undefined && undefined.__generator || function (thisArg, body) {
  var _ = {
    label: 0,
    sent: function sent() {
      if (t[0] & 1) throw t[1];
      return t[1];
    },
    trys: [],
    ops: []
  },
      f,
      y,
      t,
      g;
  return g = {
    next: verb(0),
    "throw": verb(1),
    "return": verb(2)
  }, typeof Symbol === "function" && (g[Symbol.iterator] = function () {
    return this;
  }), g;

  function verb(n) {
    return function (v) {
      return step([n, v]);
    };
  }

  function step(op) {
    if (f) throw new TypeError("Generator is already executing.");

    while (_) {
      try {
        if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
        if (y = 0, t) op = [op[0] & 2, t.value];

        switch (op[0]) {
          case 0:
          case 1:
            t = op;
            break;

          case 4:
            _.label++;
            return {
              value: op[1],
              done: false
            };

          case 5:
            _.label++;
            y = op[1];
            op = [0];
            continue;

          case 7:
            op = _.ops.pop();

            _.trys.pop();

            continue;

          default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
              _ = 0;
              continue;
            }

            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }

            if (op[0] === 6 && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }

            if (t && _.label < t[2]) {
              _.label = t[2];

              _.ops.push(op);

              break;
            }

            if (t[2]) _.ops.pop();

            _.trys.pop();

            continue;
        }

        op = body.call(thisArg, _);
      } catch (e) {
        op = [6, e];
        y = 0;
      } finally {
        f = t = 0;
      }
    }

    if (op[0] & 5) throw op[1];
    return {
      value: op[0] ? op[1] : void 0,
      done: true
    };
  }
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};



function useLockFn(fn) {
  var _this = this;

  var lockRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    var args = [];

    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }

    return __awaiter(_this, void 0, void 0, function () {
      var ret, e_1;
      return __generator(this, function (_a) {
        switch (_a.label) {
          case 0:
            if (lockRef.current) return [2
            /*return*/
            ];
            lockRef.current = true;
            _a.label = 1;

          case 1:
            _a.trys.push([1, 3,, 4]);

            return [4
            /*yield*/
            , fn.apply(void 0, __spread(args))];

          case 2:
            ret = _a.sent();
            lockRef.current = false;
            return [2
            /*return*/
            , ret];

          case 3:
            e_1 = _a.sent();
            lockRef.current = false;
            throw e_1;

          case 4:
            return [2
            /*return*/
            ];
        }
      });
    });
  }, [fn]);
}

/* harmony default export */ __webpack_exports__["default"] = (useLockFn);

/***/ }),

/***/ "./node_modules/ahooks/es/useLongPress/index.js":
/*!******************************************************!*\
  !*** ./node_modules/ahooks/es/useLongPress/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isBrowser */ "./node_modules/ahooks/es/utils/isBrowser.js");
/* harmony import */ var _utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");





var touchSupported = _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__.default && ( // @ts-ignore
'ontouchstart' in window || window.DocumentTouch && document instanceof DocumentTouch);

function useLongPress(onLongPress, target, _a) {
  var _b = _a === void 0 ? {} : _a,
      _c = _b.delay,
      delay = _c === void 0 ? 300 : _c,
      onClick = _b.onClick,
      onLongPressEnd = _b.onLongPressEnd;

  var onLongPressRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_2__.default)(onLongPress);
  var onClickRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_2__.default)(onClick);
  var onLongPressEndRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_2__.default)(onLongPressEnd);
  var timerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var isTriggeredRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  (0,_utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_3__.default)(function () {
    var targetElement = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_4__.getTargetElement)(target);

    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {
      return;
    }

    var onStart = function onStart(event) {
      timerRef.current = setTimeout(function () {
        onLongPressRef.current(event);
        isTriggeredRef.current = true;
      }, delay);
    };

    var onEnd = function onEnd(event, shouldTriggerClick) {
      var _a;

      if (shouldTriggerClick === void 0) {
        shouldTriggerClick = false;
      }

      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      if (isTriggeredRef.current) {
        (_a = onLongPressEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onLongPressEndRef, event);
      }

      if (shouldTriggerClick && !isTriggeredRef.current && onClickRef.current) {
        onClickRef.current(event);
      }

      isTriggeredRef.current = false;
    };

    var onEndWithClick = function onEndWithClick(event) {
      return onEnd(event, true);
    };

    if (!touchSupported) {
      targetElement.addEventListener('mousedown', onStart);
      targetElement.addEventListener('mouseup', onEndWithClick);
      targetElement.addEventListener('mouseleave', onEnd);
    } else {
      targetElement.addEventListener('touchstart', onStart);
      targetElement.addEventListener('touchend', onEndWithClick);
    }

    return function () {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        isTriggeredRef.current = false;
      }

      if (!touchSupported) {
        targetElement.removeEventListener('mousedown', onStart);
        targetElement.removeEventListener('mouseup', onEndWithClick);
        targetElement.removeEventListener('mouseleave', onEnd);
      } else {
        targetElement.removeEventListener('touchstart', onStart);
        targetElement.removeEventListener('touchend', onEndWithClick);
      }
    };
  }, [], target);
}

/* harmony default export */ __webpack_exports__["default"] = (useLongPress);

/***/ }),

/***/ "./node_modules/ahooks/es/useMap/index.js":
/*!************************************************!*\
  !*** ./node_modules/ahooks/es/useMap/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




function useMap(initialValue) {
  var getInitValue = function getInitValue() {
    return initialValue === undefined ? new Map() : new Map(initialValue);
  };

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {
    return getInitValue();
  }), 2),
      map = _a[0],
      setMap = _a[1];

  var set = function set(key, entry) {
    setMap(function (prev) {
      var temp = new Map(prev);
      temp.set(key, entry);
      return temp;
    });
  };

  var setAll = function setAll(newMap) {
    setMap(new Map(newMap));
  };

  var remove = function remove(key) {
    setMap(function (prev) {
      var temp = new Map(prev);
      temp["delete"](key);
      return temp;
    });
  };

  var reset = function reset() {
    return setMap(getInitValue());
  };

  var get = function get(key) {
    return map.get(key);
  };

  return [map, {
    set: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(set),
    setAll: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(setAll),
    remove: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(remove),
    reset: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(reset),
    get: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(get)
  }];
}

/* harmony default export */ __webpack_exports__["default"] = (useMap);

/***/ }),

/***/ "./node_modules/ahooks/es/useMemoizedFn/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/useMemoizedFn/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


function useMemoizedFn(fn) {
  if (true) {
    if (typeof fn !== 'function') {
      console.error("useMemoizedFn expected parameter is a function, got " + typeof fn);
    }
  }

  var fnRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fn); // why not write `fnRef.current = fn`?
  // https://github.com/alibaba/hooks/issues/728

  fnRef.current = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return fn;
  }, [fn]);
  var memoizedFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();

  if (!memoizedFn.current) {
    memoizedFn.current = function () {
      var args = [];

      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }

      return fnRef.current.apply(this, args);
    };
  }

  return memoizedFn.current;
}

/* harmony default export */ __webpack_exports__["default"] = (useMemoizedFn);

/***/ }),

/***/ "./node_modules/ahooks/es/useMount/index.js":
/*!**************************************************!*\
  !*** ./node_modules/ahooks/es/useMount/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


var useMount = function useMount(fn) {
  if (true) {
    if (typeof fn !== 'function') {
      console.error("useMount: parameter `fn` expected to be a function, but got \"" + typeof fn + "\".");
    }
  }

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    fn === null || fn === void 0 ? void 0 : fn();
  }, []);
};

/* harmony default export */ __webpack_exports__["default"] = (useMount);

/***/ }),

/***/ "./node_modules/ahooks/es/useMouse/index.js":
/*!**************************************************!*\
  !*** ./node_modules/ahooks/es/useMouse/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useRafState__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useRafState */ "./node_modules/ahooks/es/useRafState/index.js");
/* harmony import */ var _useEventListener__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useEventListener */ "./node_modules/ahooks/es/useEventListener/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




var initState = {
  screenX: NaN,
  screenY: NaN,
  clientX: NaN,
  clientY: NaN,
  pageX: NaN,
  pageY: NaN,
  elementX: NaN,
  elementY: NaN,
  elementH: NaN,
  elementW: NaN,
  elementPosX: NaN,
  elementPosY: NaN
};
/* harmony default export */ __webpack_exports__["default"] = (function (target) {
  var _a = __read((0,_useRafState__WEBPACK_IMPORTED_MODULE_0__.default)(initState), 2),
      state = _a[0],
      setState = _a[1];

  (0,_useEventListener__WEBPACK_IMPORTED_MODULE_1__.default)('mousemove', function (event) {
    var screenX = event.screenX,
        screenY = event.screenY,
        clientX = event.clientX,
        clientY = event.clientY,
        pageX = event.pageX,
        pageY = event.pageY;
    var newState = {
      screenX: screenX,
      screenY: screenY,
      clientX: clientX,
      clientY: clientY,
      pageX: pageX,
      pageY: pageY,
      elementX: NaN,
      elementY: NaN,
      elementH: NaN,
      elementW: NaN,
      elementPosX: NaN,
      elementPosY: NaN
    };
    var targetElement = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_2__.getTargetElement)(target);

    if (targetElement) {
      var _a = targetElement.getBoundingClientRect(),
          left = _a.left,
          top_1 = _a.top,
          width = _a.width,
          height = _a.height;

      newState.elementPosX = left + window.pageXOffset;
      newState.elementPosY = top_1 + window.pageYOffset;
      newState.elementX = pageX - newState.elementPosX;
      newState.elementY = pageY - newState.elementPosY;
      newState.elementW = width;
      newState.elementH = height;
    }

    setState(newState);
  }, {
    target: document
  });
  return state;
});

/***/ }),

/***/ "./node_modules/ahooks/es/useNetwork/index.js":
/*!****************************************************!*\
  !*** ./node_modules/ahooks/es/useNetwork/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};


var NetworkEventType;

(function (NetworkEventType) {
  NetworkEventType["ONLINE"] = "online";
  NetworkEventType["OFFLINE"] = "offline";
  NetworkEventType["CHANGE"] = "change";
})(NetworkEventType || (NetworkEventType = {}));

function getConnection() {
  var nav = navigator;
  if (typeof nav !== 'object') return null;
  return nav.connection || nav.mozConnection || nav.webkitConnection;
}

function getConnectionProperty() {
  var c = getConnection();
  if (!c) return {};
  return {
    rtt: c.rtt,
    type: c.type,
    saveData: c.saveData,
    downlink: c.downlink,
    downlinkMax: c.downlinkMax,
    effectiveType: c.effectiveType
  };
}

function useNetwork() {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {
    return __assign({
      since: undefined,
      online: navigator === null || navigator === void 0 ? void 0 : navigator.onLine
    }, getConnectionProperty());
  }), 2),
      state = _a[0],
      setState = _a[1];

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var onOnline = function onOnline() {
      setState(function (prevState) {
        return __assign(__assign({}, prevState), {
          online: true,
          since: new Date()
        });
      });
    };

    var onOffline = function onOffline() {
      setState(function (prevState) {
        return __assign(__assign({}, prevState), {
          online: false,
          since: new Date()
        });
      });
    };

    var onConnectionChange = function onConnectionChange() {
      setState(function (prevState) {
        return __assign(__assign({}, prevState), getConnectionProperty());
      });
    };

    window.addEventListener(NetworkEventType.ONLINE, onOnline);
    window.addEventListener(NetworkEventType.OFFLINE, onOffline);
    var connection = getConnection();
    connection === null || connection === void 0 ? void 0 : connection.addEventListener(NetworkEventType.CHANGE, onConnectionChange);
    return function () {
      window.removeEventListener(NetworkEventType.ONLINE, onOnline);
      window.removeEventListener(NetworkEventType.OFFLINE, onOffline);
      connection === null || connection === void 0 ? void 0 : connection.removeEventListener(NetworkEventType.CHANGE, onConnectionChange);
    };
  }, []);
  return state;
}

/* harmony default export */ __webpack_exports__["default"] = (useNetwork);

/***/ }),

/***/ "./node_modules/ahooks/es/usePagination/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/usePagination/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useRequest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useRequest */ "./node_modules/ahooks/es/useRequest/index.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};





var usePagination = function usePagination(service, options) {
  var _a;

  if (options === void 0) {
    options = {};
  }

  var _b = options.defaultPageSize,
      defaultPageSize = _b === void 0 ? 10 : _b,
      rest = __rest(options, ["defaultPageSize"]);

  var result = (0,_useRequest__WEBPACK_IMPORTED_MODULE_1__.default)(service, __assign({
    defaultParams: [{
      current: 1,
      pageSize: defaultPageSize
    }],
    refreshDepsAction: function refreshDepsAction() {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      changeCurrent(1);
    }
  }, rest));

  var _c = result.params[0] || {},
      _d = _c.current,
      current = _d === void 0 ? 1 : _d,
      _e = _c.pageSize,
      pageSize = _e === void 0 ? defaultPageSize : _e;

  var total = ((_a = result.data) === null || _a === void 0 ? void 0 : _a.total) || 0;
  var totalPage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return Math.ceil(total / pageSize);
  }, [pageSize, total]);

  var onChange = function onChange(c, p) {
    var toCurrent = c <= 0 ? 1 : c;
    var toPageSize = p <= 0 ? 1 : p;
    var tempTotalPage = Math.ceil(total / toPageSize);

    if (toCurrent > tempTotalPage) {
      toCurrent = Math.max(1, tempTotalPage);
    }

    var _a = __read(result.params || []),
        _b = _a[0],
        oldPaginationParams = _b === void 0 ? {} : _b,
        restParams = _a.slice(1);

    result.run.apply(result, __spread([__assign(__assign({}, oldPaginationParams), {
      current: toCurrent,
      pageSize: toPageSize
    })], restParams));
  };

  var changeCurrent = function changeCurrent(c) {
    onChange(c, pageSize);
  };

  var changePageSize = function changePageSize(p) {
    onChange(current, p);
  };

  return __assign(__assign({}, result), {
    pagination: {
      current: current,
      pageSize: pageSize,
      total: total,
      totalPage: totalPage,
      onChange: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_2__.default)(onChange),
      changeCurrent: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_2__.default)(changeCurrent),
      changePageSize: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_2__.default)(changePageSize)
    }
  });
};

/* harmony default export */ __webpack_exports__["default"] = (usePagination);

/***/ }),

/***/ "./node_modules/ahooks/es/usePrevious/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/usePrevious/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


var defaultShouldUpdate = function defaultShouldUpdate(a, b) {
  return a !== b;
};

function usePrevious(state, shouldUpdate) {
  if (shouldUpdate === void 0) {
    shouldUpdate = defaultShouldUpdate;
  }

  var prevRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var curRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();

  if (shouldUpdate(curRef.current, state)) {
    prevRef.current = curRef.current;
    curRef.current = state;
  }

  return prevRef.current;
}

/* harmony default export */ __webpack_exports__["default"] = (usePrevious);

/***/ }),

/***/ "./node_modules/ahooks/es/useRafInterval/index.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useRafInterval/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");



var setRafInterval = function setRafInterval(callback, delay) {
  if (delay === void 0) {
    delay = 0;
  }

  if (typeof requestAnimationFrame === typeof undefined) {
    return {
      id: setInterval(callback, delay)
    };
  }

  var start = new Date().getTime();
  var handle = {
    id: 0
  };

  var loop = function loop() {
    var current = new Date().getTime();

    if (current - start >= delay) {
      callback();
      start = new Date().getTime();
    }

    handle.id = requestAnimationFrame(loop);
  };

  handle.id = requestAnimationFrame(loop);
  return handle;
};

function cancelAnimationFrameIsNotDefined(t) {
  return typeof cancelAnimationFrame === typeof undefined;
}

var clearRafInterval = function clearRafInterval(handle) {
  if (cancelAnimationFrameIsNotDefined(handle.id)) {
    return clearInterval(handle.id);
  }

  cancelAnimationFrame(handle.id);
};

function useRafInterval(fn, delay, options) {
  var immediate = options === null || options === void 0 ? void 0 : options.immediate;
  var fnRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(fn);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (typeof delay !== 'number' || delay < 0) return;

    if (immediate) {
      fnRef.current();
    }

    var timer = setRafInterval(function () {
      fnRef.current();
    }, delay);
    return function () {
      clearRafInterval(timer);
    };
  }, [delay]);
}

/* harmony default export */ __webpack_exports__["default"] = (useRafInterval);

/***/ }),

/***/ "./node_modules/ahooks/es/useRafState/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useRafState/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




function useRafState(initialState) {
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState), 2),
      state = _a[0],
      setState = _a[1];

  var setRafState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (value) {
    cancelAnimationFrame(ref.current);
    ref.current = requestAnimationFrame(function () {
      setState(value);
    });
  }, []);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    cancelAnimationFrame(ref.current);
  });
  return [state, setRafState];
}

/* harmony default export */ __webpack_exports__["default"] = (useRafState);

/***/ }),

/***/ "./node_modules/ahooks/es/useRafTimeout/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/useRafTimeout/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");



var setRafTimeout = function setRafTimeout(callback, delay) {
  if (delay === void 0) {
    delay = 0;
  }

  if (typeof requestAnimationFrame === typeof undefined) {
    return {
      id: setTimeout(callback, delay)
    };
  }

  var handle = {
    id: 0
  };
  var startTime = new Date().getTime();

  var loop = function loop() {
    var current = new Date().getTime();

    if (current - startTime >= delay) {
      callback();
    } else {
      handle.id = requestAnimationFrame(loop);
    }
  };

  handle.id = requestAnimationFrame(loop);
  return handle;
};

function cancelAnimationFrameIsNotDefined(t) {
  return typeof cancelAnimationFrame === typeof undefined;
}

var clearRafTimeout = function clearRafTimeout(handle) {
  if (cancelAnimationFrameIsNotDefined(handle.id)) {
    return clearTimeout(handle.id);
  }

  cancelAnimationFrame(handle.id);
};

function useRafTimeout(fn, delay) {
  var fnRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(fn);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (typeof delay !== 'number' || delay < 0) return;
    var timer = setRafTimeout(function () {
      fnRef.current();
    }, delay);
    return function () {
      clearRafTimeout(timer);
    };
  }, [delay]);
}

/* harmony default export */ __webpack_exports__["default"] = (useRafTimeout);

/***/ }),

/***/ "./node_modules/ahooks/es/useReactive/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useReactive/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useCreation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useCreation */ "./node_modules/ahooks/es/useCreation/index.js");
/* harmony import */ var _useUpdate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useUpdate */ "./node_modules/ahooks/es/useUpdate/index.js");


 // k:v 原对象:代理过的对象

var proxyMap = new WeakMap(); // k:v 代理过的对象:原对象

var rawMap = new WeakMap();

function isObject(val) {
  return typeof val === 'object' && val !== null;
}

function observer(initialVal, cb) {
  var existingProxy = proxyMap.get(initialVal); // 添加缓存 防止重新构建proxy

  if (existingProxy) {
    return existingProxy;
  } // 防止代理已经代理过的对象
  // https://github.com/alibaba/hooks/issues/839


  if (rawMap.has(initialVal)) {
    return initialVal;
  }

  var proxy = new Proxy(initialVal, {
    get: function get(target, key, receiver) {
      var res = Reflect.get(target, key, receiver);
      return isObject(res) ? observer(res, cb) : Reflect.get(target, key);
    },
    set: function set(target, key, val) {
      var ret = Reflect.set(target, key, val);
      cb();
      return ret;
    },
    deleteProperty: function deleteProperty(target, key) {
      var ret = Reflect.deleteProperty(target, key);
      cb();
      return ret;
    }
  });
  proxyMap.set(initialVal, proxy);
  rawMap.set(proxy, initialVal);
  return proxy;
}

function useReactive(initialState) {
  var update = (0,_useUpdate__WEBPACK_IMPORTED_MODULE_1__.default)();
  var stateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(initialState);
  var state = (0,_useCreation__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    return observer(stateRef.current, function () {
      update();
    });
  }, []);
  return state;
}

/* harmony default export */ __webpack_exports__["default"] = (useReactive);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/index.js":
/*!****************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "clearCache": function() { return /* reexport safe */ _src_utils_cache__WEBPACK_IMPORTED_MODULE_0__.clearCache; }
/* harmony export */ });
/* harmony import */ var _src_useRequest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/useRequest */ "./node_modules/ahooks/es/useRequest/src/useRequest.js");
/* harmony import */ var _src_utils_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/utils/cache */ "./node_modules/ahooks/es/useRequest/src/utils/cache.js");



/* harmony default export */ __webpack_exports__["default"] = (_src_useRequest__WEBPACK_IMPORTED_MODULE_1__.default);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/Fetch.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/Fetch.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __awaiter = undefined && undefined.__awaiter || function (thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function (resolve) {
      resolve(value);
    });
  }

  return new (P || (P = Promise))(function (resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }

    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }

    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }

    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};

var __generator = undefined && undefined.__generator || function (thisArg, body) {
  var _ = {
    label: 0,
    sent: function sent() {
      if (t[0] & 1) throw t[1];
      return t[1];
    },
    trys: [],
    ops: []
  },
      f,
      y,
      t,
      g;
  return g = {
    next: verb(0),
    "throw": verb(1),
    "return": verb(2)
  }, typeof Symbol === "function" && (g[Symbol.iterator] = function () {
    return this;
  }), g;

  function verb(n) {
    return function (v) {
      return step([n, v]);
    };
  }

  function step(op) {
    if (f) throw new TypeError("Generator is already executing.");

    while (_) {
      try {
        if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
        if (y = 0, t) op = [op[0] & 2, t.value];

        switch (op[0]) {
          case 0:
          case 1:
            t = op;
            break;

          case 4:
            _.label++;
            return {
              value: op[1],
              done: false
            };

          case 5:
            _.label++;
            y = op[1];
            op = [0];
            continue;

          case 7:
            op = _.ops.pop();

            _.trys.pop();

            continue;

          default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
              _ = 0;
              continue;
            }

            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }

            if (op[0] === 6 && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }

            if (t && _.label < t[2]) {
              _.label = t[2];

              _.ops.push(op);

              break;
            }

            if (t[2]) _.ops.pop();

            _.trys.pop();

            continue;
        }

        op = body.call(thisArg, _);
      } catch (e) {
        op = [6, e];
        y = 0;
      } finally {
        f = t = 0;
      }
    }

    if (op[0] & 5) throw op[1];
    return {
      value: op[0] ? op[1] : void 0,
      done: true
    };
  }
};

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};

var Fetch =
/** @class */
function () {
  function Fetch(serviceRef, options, subscribe, initState) {
    if (initState === void 0) {
      initState = {};
    }

    this.serviceRef = serviceRef;
    this.options = options;
    this.subscribe = subscribe;
    this.initState = initState;
    this.count = 0;
    this.state = {
      loading: false,
      params: undefined,
      data: undefined,
      error: undefined
    };
    this.state = __assign(__assign(__assign({}, this.state), {
      loading: !options.manual
    }), initState);
  }

  Fetch.prototype.setState = function (s) {
    if (s === void 0) {
      s = {};
    }

    this.state = __assign(__assign({}, this.state), s);
    this.subscribe();
  };

  Fetch.prototype.runPluginHandler = function (event) {
    var rest = [];

    for (var _i = 1; _i < arguments.length; _i++) {
      rest[_i - 1] = arguments[_i];
    } // @ts-ignore


    var r = this.pluginImpls.map(function (i) {
      var _a;

      return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spread([i], rest));
    }).filter(Boolean);
    return Object.assign.apply(Object, __spread([{}], r));
  };

  Fetch.prototype.runAsync = function () {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;

    var params = [];

    for (var _i = 0; _i < arguments.length; _i++) {
      params[_i] = arguments[_i];
    }

    return __awaiter(this, void 0, void 0, function () {
      var currentCount, _l, _m, stopNow, _o, returnNow, state, servicePromise, res, error_1;

      var _p;

      return __generator(this, function (_q) {
        switch (_q.label) {
          case 0:
            this.count += 1;
            currentCount = this.count;
            _l = this.runPluginHandler('onBefore', params), _m = _l.stopNow, stopNow = _m === void 0 ? false : _m, _o = _l.returnNow, returnNow = _o === void 0 ? false : _o, state = __rest(_l, ["stopNow", "returnNow"]); // stop request

            if (stopNow) {
              return [2
              /*return*/
              , new Promise(function () {})];
            }

            this.setState(__assign({
              loading: true,
              params: params
            }, state)); // return now

            if (returnNow) {
              return [2
              /*return*/
              , Promise.resolve(state.data)];
            }

            (_b = (_a = this.options).onBefore) === null || _b === void 0 ? void 0 : _b.call(_a, params);
            _q.label = 1;

          case 1:
            _q.trys.push([1, 3,, 4]);

            servicePromise = this.runPluginHandler('onRequest', this.serviceRef.current, params).servicePromise;

            if (!servicePromise) {
              servicePromise = (_p = this.serviceRef).current.apply(_p, __spread(params));
            }

            return [4
            /*yield*/
            , servicePromise];

          case 2:
            res = _q.sent();

            if (currentCount !== this.count) {
              // prevent run.then when request is canceled
              return [2
              /*return*/
              , new Promise(function () {})];
            } // const formattedResult = this.options.formatResultRef.current ? this.options.formatResultRef.current(res) : res;


            this.setState({
              data: res,
              error: undefined,
              loading: false
            });
            (_d = (_c = this.options).onSuccess) === null || _d === void 0 ? void 0 : _d.call(_c, res, params);
            this.runPluginHandler('onSuccess', res, params);
            (_f = (_e = this.options).onFinally) === null || _f === void 0 ? void 0 : _f.call(_e, params, res, undefined);

            if (currentCount === this.count) {
              this.runPluginHandler('onFinally', params, res, undefined);
            }

            return [2
            /*return*/
            , res];

          case 3:
            error_1 = _q.sent();

            if (currentCount !== this.count) {
              // prevent run.then when request is canceled
              return [2
              /*return*/
              , new Promise(function () {})];
            }

            this.setState({
              error: error_1,
              loading: false
            });
            (_h = (_g = this.options).onError) === null || _h === void 0 ? void 0 : _h.call(_g, error_1, params);
            this.runPluginHandler('onError', error_1, params);
            (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, undefined, error_1);

            if (currentCount === this.count) {
              this.runPluginHandler('onFinally', params, undefined, error_1);
            }

            throw error_1;

          case 4:
            return [2
            /*return*/
            ];
        }
      });
    });
  };

  Fetch.prototype.run = function () {
    var _this = this;

    var params = [];

    for (var _i = 0; _i < arguments.length; _i++) {
      params[_i] = arguments[_i];
    }

    this.runAsync.apply(this, __spread(params))["catch"](function (error) {
      if (!_this.options.onError) {
        console.error(error);
      }
    });
  };

  Fetch.prototype.cancel = function () {
    this.count += 1;
    this.setState({
      loading: false
    });
    this.runPluginHandler('onCancel');
  };

  Fetch.prototype.refresh = function () {
    // @ts-ignore
    this.run.apply(this, __spread(this.state.params || []));
  };

  Fetch.prototype.refreshAsync = function () {
    // @ts-ignore
    return this.runAsync.apply(this, __spread(this.state.params || []));
  };

  Fetch.prototype.mutate = function (data) {
    var targetData;

    if (typeof data === 'function') {
      // @ts-ignore
      targetData = data(this.state.data);
    } else {
      targetData = data;
    }

    this.runPluginHandler('onMutate', targetData);
    this.setState({
      data: targetData
    });
  };

  return Fetch;
}();

/* harmony default export */ __webpack_exports__["default"] = (Fetch);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js":
/*!***************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useUpdateEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../useUpdateEffect */ "./node_modules/ahooks/es/useUpdateEffect/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};


 // support refreshDeps & ready

var useAutoRunPlugin = function useAutoRunPlugin(fetchInstance, _a) {
  var manual = _a.manual,
      _b = _a.ready,
      ready = _b === void 0 ? true : _b,
      _c = _a.defaultParams,
      defaultParams = _c === void 0 ? [] : _c,
      _d = _a.refreshDeps,
      refreshDeps = _d === void 0 ? [] : _d,
      refreshDepsAction = _a.refreshDepsAction;
  var hasAutoRun = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  hasAutoRun.current = false;
  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    if (!manual && ready) {
      hasAutoRun.current = true;
      fetchInstance.run.apply(fetchInstance, __spread(defaultParams));
    }
  }, [ready]);
  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    if (hasAutoRun.current) {
      return;
    }

    if (!manual) {
      hasAutoRun.current = true;

      if (refreshDepsAction) {
        refreshDepsAction();
      } else {
        fetchInstance.refresh();
      }
    }
  }, __spread(refreshDeps));
  return {
    onBefore: function onBefore() {
      if (!ready) {
        return {
          stopNow: true
        };
      }
    }
  };
};

useAutoRunPlugin.onInit = function (_a) {
  var _b = _a.ready,
      ready = _b === void 0 ? true : _b,
      manual = _a.manual;
  return {
    loading: !manual && ready
  };
};

/* harmony default export */ __webpack_exports__["default"] = (useAutoRunPlugin);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js":
/*!*************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useCreation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../useCreation */ "./node_modules/ahooks/es/useCreation/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _utils_cache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/cache */ "./node_modules/ahooks/es/useRequest/src/utils/cache.js");
/* harmony import */ var _utils_cachePromise__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/cachePromise */ "./node_modules/ahooks/es/useRequest/src/utils/cachePromise.js");
/* harmony import */ var _utils_cacheSubscribe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/cacheSubscribe */ "./node_modules/ahooks/es/useRequest/src/utils/cacheSubscribe.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};








var useCachePlugin = function useCachePlugin(fetchInstance, _a) {
  var cacheKey = _a.cacheKey,
      _b = _a.cacheTime,
      cacheTime = _b === void 0 ? 5 * 60 * 1000 : _b,
      _c = _a.staleTime,
      staleTime = _c === void 0 ? 0 : _c,
      customSetCache = _a.setCache,
      customGetCache = _a.getCache;
  var unSubscribeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var currentPromiseRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();

  var _setCache = function _setCache(key, cachedData) {
    if (customSetCache) {
      customSetCache(cachedData);
    } else {
      _utils_cache__WEBPACK_IMPORTED_MODULE_1__.setCache(key, cacheTime, cachedData);
    }

    _utils_cacheSubscribe__WEBPACK_IMPORTED_MODULE_2__.trigger(key, cachedData.data);
  };

  var _getCache = function _getCache(key, params) {
    if (params === void 0) {
      params = [];
    }

    if (customGetCache) {
      return customGetCache(params);
    }

    return _utils_cache__WEBPACK_IMPORTED_MODULE_1__.getCache(key);
  };

  (0,_useCreation__WEBPACK_IMPORTED_MODULE_3__.default)(function () {
    if (!cacheKey) {
      return;
    } // get data from cache when init


    var cacheData = _getCache(cacheKey);

    if (cacheData && Object.hasOwnProperty.call(cacheData, 'data')) {
      fetchInstance.state.data = cacheData.data;
      fetchInstance.state.params = cacheData.params;

      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {
        fetchInstance.state.loading = false;
      }
    } // subscribe same cachekey update, trigger update


    unSubscribeRef.current = _utils_cacheSubscribe__WEBPACK_IMPORTED_MODULE_2__.subscribe(cacheKey, function (data) {
      fetchInstance.setState({
        data: data
      });
    });
  }, []);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_4__.default)(function () {
    var _a;

    (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);
  });

  if (!cacheKey) {
    return {};
  }

  return {
    onBefore: function onBefore(params) {
      var cacheData = _getCache(cacheKey, params);

      if (!cacheData || !Object.hasOwnProperty.call(cacheData, 'data')) {
        return {};
      } // If the data is fresh, stop request


      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {
        return {
          loading: false,
          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,
          returnNow: true
        };
      } else {
        // If the data is stale, return data, and request continue
        return {
          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data
        };
      }
    },
    onRequest: function onRequest(service, args) {
      var servicePromise = _utils_cachePromise__WEBPACK_IMPORTED_MODULE_5__.getCachePromise(cacheKey); // If has servicePromise, and is not trigger by self, then use it

      if (servicePromise && servicePromise !== currentPromiseRef.current) {
        return {
          servicePromise: servicePromise
        };
      }

      servicePromise = service.apply(void 0, __spread(args));
      currentPromiseRef.current = servicePromise;
      _utils_cachePromise__WEBPACK_IMPORTED_MODULE_5__.setCachePromise(cacheKey, servicePromise);
      return {
        servicePromise: servicePromise
      };
    },
    onSuccess: function onSuccess(data, params) {
      var _a;

      if (cacheKey) {
        // cancel subscribe, avoid trgger self
        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);

        _setCache(cacheKey, {
          data: data,
          params: params,
          time: new Date().getTime()
        }); // resubscribe


        unSubscribeRef.current = _utils_cacheSubscribe__WEBPACK_IMPORTED_MODULE_2__.subscribe(cacheKey, function (d) {
          fetchInstance.setState({
            data: d
          });
        });
      }
    },
    onMutate: function onMutate(data) {
      var _a;

      if (cacheKey) {
        // cancel subscribe, avoid trgger self
        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);

        _setCache(cacheKey, {
          data: data,
          params: fetchInstance.state.params,
          time: new Date().getTime()
        }); // resubscribe


        unSubscribeRef.current = _utils_cacheSubscribe__WEBPACK_IMPORTED_MODULE_2__.subscribe(cacheKey, function (d) {
          fetchInstance.setState({
            data: d
          });
        });
      }
    }
  };
};

/* harmony default export */ __webpack_exports__["default"] = (useCachePlugin);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/plugins/useDebouncePlugin.js":
/*!****************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/plugins/useDebouncePlugin.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/debounce */ "./node_modules/lodash/debounce.js");
/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};




var useDebouncePlugin = function useDebouncePlugin(fetchInstance, _a) {
  var debounceWait = _a.debounceWait,
      debounceLeading = _a.debounceLeading,
      debounceTrailing = _a.debounceTrailing,
      debounceMaxWait = _a.debounceMaxWait;
  var debouncedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
  var options = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
    var ret = {};

    if (debounceLeading !== undefined) {
      ret.leading = debounceLeading;
    }

    if (debounceTrailing !== undefined) {
      ret.trailing = debounceTrailing;
    }

    if (debounceMaxWait !== undefined) {
      ret.maxWait = debounceMaxWait;
    }

    return ret;
  }, [debounceLeading, debounceTrailing, debounceMaxWait]);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (debounceWait) {
      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);

      debouncedRef.current = lodash_debounce__WEBPACK_IMPORTED_MODULE_0___default()(function (callback) {
        callback();
      }, debounceWait, options); // debounce runAsync should be promise
      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398

      fetchInstance.runAsync = function () {
        var args = [];

        for (var _i = 0; _i < arguments.length; _i++) {
          args[_i] = arguments[_i];
        }

        return new Promise(function (resolve, reject) {
          var _a;

          (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.call(debouncedRef, function () {
            _originRunAsync_1.apply(void 0, __spread(args)).then(resolve)["catch"](reject);
          });
        });
      };

      return function () {
        var _a;

        (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();
        fetchInstance.runAsync = _originRunAsync_1;
      };
    }
  }, [debounceWait, options]);

  if (!debounceWait) {
    return {};
  }

  return {
    onCancel: function onCancel() {
      var _a;

      (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();
    }
  };
};

/* harmony default export */ __webpack_exports__["default"] = (useDebouncePlugin);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/plugins/useLoadingDelayPlugin.js":
/*!********************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/plugins/useLoadingDelayPlugin.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


var useLoadingDelayPlugin = function useLoadingDelayPlugin(fetchInstance, _a) {
  var loadingDelay = _a.loadingDelay;
  var timerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();

  if (!loadingDelay) {
    return {};
  }

  var cancelTimeout = function cancelTimeout() {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
  };

  return {
    onBefore: function onBefore() {
      cancelTimeout();
      timerRef.current = setTimeout(function () {
        fetchInstance.setState({
          loading: true
        });
      }, loadingDelay);
      return {
        loading: false
      };
    },
    onFinally: function onFinally() {
      cancelTimeout();
    },
    onCancel: function onCancel() {
      cancelTimeout();
    }
  };
};

/* harmony default export */ __webpack_exports__["default"] = (useLoadingDelayPlugin);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js":
/*!***************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useUpdateEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../useUpdateEffect */ "./node_modules/ahooks/es/useUpdateEffect/index.js");
/* harmony import */ var _utils_isDocumentVisible__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/isDocumentVisible */ "./node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js");
/* harmony import */ var _utils_subscribeReVisible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/subscribeReVisible */ "./node_modules/ahooks/es/useRequest/src/utils/subscribeReVisible.js");





var usePollingPlugin = function usePollingPlugin(fetchInstance, _a) {
  var pollingInterval = _a.pollingInterval,
      _b = _a.pollingWhenHidden,
      pollingWhenHidden = _b === void 0 ? true : _b;
  var timerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var unsubscribeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();

  var stopPolling = function stopPolling() {
    var _a;

    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);
  };

  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    if (!pollingInterval) {
      stopPolling();
    }
  }, [pollingInterval]);

  if (!pollingInterval) {
    return {};
  }

  return {
    onBefore: function onBefore() {
      stopPolling();
    },
    onFinally: function onFinally() {
      // if pollingWhenHidden = false && document is hidden, then stop polling and subscribe revisible
      if (!pollingWhenHidden && !(0,_utils_isDocumentVisible__WEBPACK_IMPORTED_MODULE_2__.default)()) {
        unsubscribeRef.current = (0,_utils_subscribeReVisible__WEBPACK_IMPORTED_MODULE_3__.default)(function () {
          fetchInstance.refresh();
        });
        return;
      }

      timerRef.current = setTimeout(function () {
        fetchInstance.refresh();
      }, pollingInterval);
    },
    onCancel: function onCancel() {
      stopPolling();
    }
  };
};

/* harmony default export */ __webpack_exports__["default"] = (usePollingPlugin);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _utils_limit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/limit */ "./node_modules/ahooks/es/useRequest/src/utils/limit.js");
/* harmony import */ var _utils_subscribeFocus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/subscribeFocus */ "./node_modules/ahooks/es/useRequest/src/utils/subscribeFocus.js");





var useRefreshOnWindowFocusPlugin = function useRefreshOnWindowFocusPlugin(fetchInstance, _a) {
  var refreshOnWindowFocus = _a.refreshOnWindowFocus,
      _b = _a.focusTimespan,
      focusTimespan = _b === void 0 ? 5000 : _b;
  var unsubscribeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();

  var stopSubscribe = function stopSubscribe() {
    var _a;

    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);
  };

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (refreshOnWindowFocus) {
      var limitRefresh_1 = (0,_utils_limit__WEBPACK_IMPORTED_MODULE_1__.default)(fetchInstance.refresh.bind(fetchInstance), focusTimespan);
      unsubscribeRef.current = (0,_utils_subscribeFocus__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
        limitRefresh_1();
      });
    }

    return function () {
      stopSubscribe();
    };
  }, [refreshOnWindowFocus, focusTimespan]);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_3__.default)(function () {
    stopSubscribe();
  });
  return {};
};

/* harmony default export */ __webpack_exports__["default"] = (useRefreshOnWindowFocusPlugin);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/plugins/useRetryPlugin.js":
/*!*************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/plugins/useRetryPlugin.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


var useRetryPlugin = function useRetryPlugin(fetchInstance, _a) {
  var retryInterval = _a.retryInterval,
      retryCount = _a.retryCount;
  var timerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var countRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);
  var triggerByRetry = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);

  if (!retryCount) {
    return {};
  }

  return {
    onBefore: function onBefore() {
      if (!triggerByRetry.current) {
        countRef.current = 0;
      }

      triggerByRetry.current = false;

      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    },
    onSuccess: function onSuccess() {
      countRef.current = 0;
    },
    onError: function onError() {
      countRef.current += 1;

      if (retryCount === -1 || countRef.current <= retryCount) {
        // Exponential backoff
        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1000 * Math.pow(2, countRef.current), 30000);
        timerRef.current = setTimeout(function () {
          triggerByRetry.current = true;
          fetchInstance.refresh();
        }, timeout);
      } else {
        countRef.current = 0;
      }
    },
    onCancel: function onCancel() {
      countRef.current = 0;

      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    }
  };
};

/* harmony default export */ __webpack_exports__["default"] = (useRetryPlugin);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/plugins/useThrottlePlugin.js":
/*!****************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/plugins/useThrottlePlugin.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/throttle */ "./node_modules/lodash/throttle.js");
/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};




var useThrottlePlugin = function useThrottlePlugin(fetchInstance, _a) {
  var throttleWait = _a.throttleWait,
      throttleLeading = _a.throttleLeading,
      throttleTrailing = _a.throttleTrailing;
  var throttledRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
  var options = {};

  if (throttleLeading !== undefined) {
    options.leading = throttleLeading;
  }

  if (throttleTrailing !== undefined) {
    options.trailing = throttleTrailing;
  }

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (throttleWait) {
      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);

      throttledRef.current = lodash_throttle__WEBPACK_IMPORTED_MODULE_0___default()(function (callback) {
        callback();
      }, throttleWait, options); // throttle runAsync should be promise
      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398

      fetchInstance.runAsync = function () {
        var args = [];

        for (var _i = 0; _i < arguments.length; _i++) {
          args[_i] = arguments[_i];
        }

        return new Promise(function (resolve, reject) {
          var _a;

          (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.call(throttledRef, function () {
            _originRunAsync_1.apply(void 0, __spread(args)).then(resolve)["catch"](reject);
          });
        });
      };

      return function () {
        var _a;

        fetchInstance.runAsync = _originRunAsync_1;
        (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();
      };
    }
  }, [throttleWait, throttleLeading, throttleTrailing]);

  if (!throttleWait) {
    return {};
  }

  return {
    onCancel: function onCancel() {
      var _a;

      (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();
    }
  };
};

/* harmony default export */ __webpack_exports__["default"] = (useThrottlePlugin);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/useRequest.js":
/*!*************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/useRequest.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _plugins_useAutoRunPlugin__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./plugins/useAutoRunPlugin */ "./node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js");
/* harmony import */ var _plugins_useCachePlugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/useCachePlugin */ "./node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js");
/* harmony import */ var _plugins_useDebouncePlugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plugins/useDebouncePlugin */ "./node_modules/ahooks/es/useRequest/src/plugins/useDebouncePlugin.js");
/* harmony import */ var _plugins_useLoadingDelayPlugin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./plugins/useLoadingDelayPlugin */ "./node_modules/ahooks/es/useRequest/src/plugins/useLoadingDelayPlugin.js");
/* harmony import */ var _plugins_usePollingPlugin__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./plugins/usePollingPlugin */ "./node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js");
/* harmony import */ var _plugins_useRefreshOnWindowFocusPlugin__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./plugins/useRefreshOnWindowFocusPlugin */ "./node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js");
/* harmony import */ var _plugins_useRetryPlugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/useRetryPlugin */ "./node_modules/ahooks/es/useRequest/src/plugins/useRetryPlugin.js");
/* harmony import */ var _plugins_useThrottlePlugin__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./plugins/useThrottlePlugin */ "./node_modules/ahooks/es/useRequest/src/plugins/useThrottlePlugin.js");
/* harmony import */ var _useRequestImplement__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useRequestImplement */ "./node_modules/ahooks/es/useRequest/src/useRequestImplement.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};









 // function useRequest<TData, TParams extends any[], TFormated, TTFormated extends TFormated = any>(
//   service: Service<TData, TParams>,
//   options: OptionsWithFormat<TData, TParams, TFormated, TTFormated>,
//   plugins?: Plugin<TData, TParams>[],
// ): Result<TFormated, TParams>
// function useRequest<TData, TParams extends any[]>(
//   service: Service<TData, TParams>,
//   options?: OptionsWithoutFormat<TData, TParams>,
//   plugins?: Plugin<TData, TParams>[],
// ): Result<TData, TParams>

function useRequest(service, options, plugins) {
  return (0,_useRequestImplement__WEBPACK_IMPORTED_MODULE_0__.default)(service, options, __spread(plugins || [], [_plugins_useDebouncePlugin__WEBPACK_IMPORTED_MODULE_1__.default, _plugins_useLoadingDelayPlugin__WEBPACK_IMPORTED_MODULE_2__.default, _plugins_usePollingPlugin__WEBPACK_IMPORTED_MODULE_3__.default, _plugins_useRefreshOnWindowFocusPlugin__WEBPACK_IMPORTED_MODULE_4__.default, _plugins_useThrottlePlugin__WEBPACK_IMPORTED_MODULE_5__.default, _plugins_useAutoRunPlugin__WEBPACK_IMPORTED_MODULE_6__.default, _plugins_useCachePlugin__WEBPACK_IMPORTED_MODULE_7__.default, _plugins_useRetryPlugin__WEBPACK_IMPORTED_MODULE_8__.default]));
}

/* harmony default export */ __webpack_exports__["default"] = (useRequest);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/useRequestImplement.js":
/*!**********************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/useRequestImplement.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useCreation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../useCreation */ "./node_modules/ahooks/es/useCreation/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useMount__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../useMount */ "./node_modules/ahooks/es/useMount/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _useUpdate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../useUpdate */ "./node_modules/ahooks/es/useUpdate/index.js");
/* harmony import */ var _Fetch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Fetch */ "./node_modules/ahooks/es/useRequest/src/Fetch.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};









function useRequestImplement(service, options, plugins) {
  if (options === void 0) {
    options = {};
  }

  if (plugins === void 0) {
    plugins = [];
  }

  var _a = options.manual,
      manual = _a === void 0 ? false : _a,
      rest = __rest(options, ["manual"]);

  var fetchOptions = __assign({
    manual: manual
  }, rest);

  var serviceRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_0__.default)(service);
  var update = (0,_useUpdate__WEBPACK_IMPORTED_MODULE_1__.default)();
  var fetchInstance = (0,_useCreation__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    var initState = plugins.map(function (p) {
      var _a;

      return (_a = p === null || p === void 0 ? void 0 : p.onInit) === null || _a === void 0 ? void 0 : _a.call(p, fetchOptions);
    }).filter(Boolean);
    return new _Fetch__WEBPACK_IMPORTED_MODULE_3__.default(serviceRef, fetchOptions, update, Object.assign.apply(Object, __spread([{}], initState)));
  }, []);
  fetchInstance.options = fetchOptions; // run all plugins hooks

  fetchInstance.pluginImpls = plugins.map(function (p) {
    return p(fetchInstance, fetchOptions);
  });
  (0,_useMount__WEBPACK_IMPORTED_MODULE_4__.default)(function () {
    if (!manual) {
      // useCachePlugin can set fetchInstance.state.params from cache when init
      var params = fetchInstance.state.params || options.defaultParams || []; // @ts-ignore

      fetchInstance.run.apply(fetchInstance, __spread(params));
    }
  });
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_5__.default)(function () {
    fetchInstance.cancel();
  });
  return {
    loading: fetchInstance.state.loading,
    data: fetchInstance.state.data,
    error: fetchInstance.state.error,
    params: fetchInstance.state.params || [],
    cancel: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(fetchInstance.cancel.bind(fetchInstance)),
    refresh: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(fetchInstance.refresh.bind(fetchInstance)),
    refreshAsync: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(fetchInstance.refreshAsync.bind(fetchInstance)),
    run: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(fetchInstance.run.bind(fetchInstance)),
    runAsync: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(fetchInstance.runAsync.bind(fetchInstance)),
    mutate: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_6__.default)(fetchInstance.mutate.bind(fetchInstance))
  };
}

/* harmony default export */ __webpack_exports__["default"] = (useRequestImplement);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/utils/cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/utils/cache.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getCache": function() { return /* binding */ getCache; },
/* harmony export */   "setCache": function() { return /* binding */ setCache; },
/* harmony export */   "clearCache": function() { return /* binding */ clearCache; }
/* harmony export */ });
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var cache = new Map();

var setCache = function setCache(key, cacheTime, cachedData) {
  var currentCache = cache.get(key);

  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {
    clearTimeout(currentCache.timer);
  }

  var timer = undefined;

  if (cacheTime > -1) {
    // if cache out, clear it
    timer = setTimeout(function () {
      cache["delete"](key);
    }, cacheTime);
  }

  cache.set(key, __assign(__assign({}, cachedData), {
    timer: timer
  }));
};

var getCache = function getCache(key) {
  return cache.get(key);
};

var clearCache = function clearCache(key) {
  if (key) {
    var cacheKeys = Array.isArray(key) ? key : [key];
    cacheKeys.forEach(function (cacheKey) {
      return cache["delete"](cacheKey);
    });
  } else {
    cache.clear();
  }
};



/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/utils/cachePromise.js":
/*!*********************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/utils/cachePromise.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getCachePromise": function() { return /* binding */ getCachePromise; },
/* harmony export */   "setCachePromise": function() { return /* binding */ setCachePromise; }
/* harmony export */ });
var cachePromise = new Map();

var getCachePromise = function getCachePromise(cacheKey) {
  return cachePromise.get(cacheKey);
};

var setCachePromise = function setCachePromise(cacheKey, promise) {
  // Should cache the same promise, cannot be promise.finally
  // Because the promise.finally will change the reference of the promise
  cachePromise.set(cacheKey, promise); // no use promise.finally for compatibility

  promise.then(function (res) {
    cachePromise["delete"](cacheKey);
    return res;
  })["catch"](function () {
    cachePromise["delete"](cacheKey);
  });
};



/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/utils/cacheSubscribe.js":
/*!***********************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/utils/cacheSubscribe.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "trigger": function() { return /* binding */ trigger; },
/* harmony export */   "subscribe": function() { return /* binding */ subscribe; }
/* harmony export */ });
var listeners = {};

var trigger = function trigger(key, data) {
  if (listeners[key]) {
    listeners[key].forEach(function (item) {
      return item(data);
    });
  }
};

var subscribe = function subscribe(key, listener) {
  if (!listeners[key]) {
    listeners[key] = [];
  }

  listeners[key].push(listener);
  return function unsubscribe() {
    var index = listeners[key].indexOf(listener);
    listeners[key].splice(index, 1);
  };
};



/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js":
/*!**************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ isDocumentVisible; }
/* harmony export */ });
/* harmony import */ var _utils_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/canUseDom */ "./node_modules/ahooks/es/utils/canUseDom.js");

function isDocumentVisible() {
  if ((0,_utils_canUseDom__WEBPACK_IMPORTED_MODULE_0__.default)()) {
    return document.visibilityState !== 'hidden';
  }

  return true;
}

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/utils/isOnline.js":
/*!*****************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/utils/isOnline.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ isOnline; }
/* harmony export */ });
/* harmony import */ var _utils_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/canUseDom */ "./node_modules/ahooks/es/utils/canUseDom.js");

function isOnline() {
  if ((0,_utils_canUseDom__WEBPACK_IMPORTED_MODULE_0__.default)() && typeof navigator.onLine !== 'undefined') {
    return navigator.onLine;
  }

  return true;
}

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/utils/limit.js":
/*!**************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/utils/limit.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ limit; }
/* harmony export */ });
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};

function limit(fn, timespan) {
  var pending = false;
  return function () {
    var args = [];

    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }

    if (pending) return;
    pending = true;
    fn.apply(void 0, __spread(args));
    setTimeout(function () {
      pending = false;
    }, timespan);
  };
}

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/utils/subscribeFocus.js":
/*!***********************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/utils/subscribeFocus.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _utils_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/canUseDom */ "./node_modules/ahooks/es/utils/canUseDom.js");
/* harmony import */ var _isDocumentVisible__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isDocumentVisible */ "./node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js");
/* harmony import */ var _isOnline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isOnline */ "./node_modules/ahooks/es/useRequest/src/utils/isOnline.js");
// from swr



var listeners = [];

function subscribe(listener) {
  listeners.push(listener);
  return function unsubscribe() {
    var index = listeners.indexOf(listener);
    listeners.splice(index, 1);
  };
}

if ((0,_utils_canUseDom__WEBPACK_IMPORTED_MODULE_0__.default)()) {
  var revalidate = function revalidate() {
    if (!(0,_isDocumentVisible__WEBPACK_IMPORTED_MODULE_1__.default)() || !(0,_isOnline__WEBPACK_IMPORTED_MODULE_2__.default)()) return;

    for (var i = 0; i < listeners.length; i++) {
      var listener = listeners[i];
      listener();
    }
  };

  window.addEventListener('visibilitychange', revalidate, false);
  window.addEventListener('focus', revalidate, false);
}

/* harmony default export */ __webpack_exports__["default"] = (subscribe);

/***/ }),

/***/ "./node_modules/ahooks/es/useRequest/src/utils/subscribeReVisible.js":
/*!***************************************************************************!*\
  !*** ./node_modules/ahooks/es/useRequest/src/utils/subscribeReVisible.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _utils_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/canUseDom */ "./node_modules/ahooks/es/utils/canUseDom.js");
/* harmony import */ var _isDocumentVisible__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isDocumentVisible */ "./node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js");


var listeners = [];

function subscribe(listener) {
  listeners.push(listener);
  return function unsubscribe() {
    var index = listeners.indexOf(listener);
    listeners.splice(index, 1);
  };
}

if ((0,_utils_canUseDom__WEBPACK_IMPORTED_MODULE_0__.default)()) {
  var revalidate = function revalidate() {
    if (!(0,_isDocumentVisible__WEBPACK_IMPORTED_MODULE_1__.default)()) return;

    for (var i = 0; i < listeners.length; i++) {
      var listener = listeners[i];
      listener();
    }
  };

  window.addEventListener('visibilitychange', revalidate, false);
}

/* harmony default export */ __webpack_exports__["default"] = (subscribe);

/***/ }),

/***/ "./node_modules/ahooks/es/useResponsive/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/useResponsive/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "configResponsive": function() { return /* binding */ configResponsive; },
/* harmony export */   "useResponsive": function() { return /* binding */ useResponsive; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __values = undefined && undefined.__values || function (o) {
  var s = typeof Symbol === "function" && Symbol.iterator,
      m = s && o[s],
      i = 0;
  if (m) return m.call(o);
  if (o && typeof o.length === "number") return {
    next: function next() {
      if (o && i >= o.length) o = void 0;
      return {
        value: o && o[i++],
        done: !o
      };
    }
  };
  throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};


var subscribers = new Set();
var info;
var responsiveConfig = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200
};

function handleResize() {
  var e_1, _a;

  var oldInfo = info;
  calculate();
  if (oldInfo === info) return;

  try {
    for (var subscribers_1 = __values(subscribers), subscribers_1_1 = subscribers_1.next(); !subscribers_1_1.done; subscribers_1_1 = subscribers_1.next()) {
      var subscriber = subscribers_1_1.value;
      subscriber();
    }
  } catch (e_1_1) {
    e_1 = {
      error: e_1_1
    };
  } finally {
    try {
      if (subscribers_1_1 && !subscribers_1_1.done && (_a = subscribers_1["return"])) _a.call(subscribers_1);
    } finally {
      if (e_1) throw e_1.error;
    }
  }
}

var listening = false;

function calculate() {
  var e_2, _a;

  var width = window.innerWidth;
  var newInfo = {};
  var shouldUpdate = false;

  try {
    for (var _b = __values(Object.keys(responsiveConfig)), _c = _b.next(); !_c.done; _c = _b.next()) {
      var key = _c.value;
      newInfo[key] = width >= responsiveConfig[key];

      if (newInfo[key] !== info[key]) {
        shouldUpdate = true;
      }
    }
  } catch (e_2_1) {
    e_2 = {
      error: e_2_1
    };
  } finally {
    try {
      if (_c && !_c.done && (_a = _b["return"])) _a.call(_b);
    } finally {
      if (e_2) throw e_2.error;
    }
  }

  if (shouldUpdate) {
    info = newInfo;
  }
}

function configResponsive(config) {
  responsiveConfig = config;
  if (info) calculate();
}
function useResponsive() {
  var windowExists = typeof window !== 'undefined';

  if (windowExists && !listening) {
    info = {};
    calculate();
    window.addEventListener('resize', handleResize);
    listening = true;
  }

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(info), 2),
      state = _a[0],
      setState = _a[1];

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (!windowExists) return;

    var subscriber = function subscriber() {
      setState(info);
    };

    subscribers.add(subscriber);
    return function () {
      subscribers["delete"](subscriber);

      if (subscribers.size === 0) {
        window.removeEventListener('resize', handleResize);
        listening = false;
      }
    };
  }, []);
  return state;
}

/***/ }),

/***/ "./node_modules/ahooks/es/useSafeState/index.js":
/*!******************************************************!*\
  !*** ./node_modules/ahooks/es/useSafeState/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useUnmountedRef__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useUnmountedRef */ "./node_modules/ahooks/es/useUnmountedRef/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




function useSafeState(initialState) {
  var unmountedRef = (0,_useUnmountedRef__WEBPACK_IMPORTED_MODULE_1__.default)();

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState), 2),
      state = _a[0],
      setState = _a[1];

  var setCurrentState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (currentState) {
    /** if component is unmounted, stop update */
    if (unmountedRef.current) return;
    setState(currentState);
  }, []);
  return [state, setCurrentState];
}

/* harmony default export */ __webpack_exports__["default"] = (useSafeState);

/***/ }),

/***/ "./node_modules/ahooks/es/useScroll/index.js":
/*!***************************************************!*\
  !*** ./node_modules/ahooks/es/useScroll/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _useRafState__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useRafState */ "./node_modules/ahooks/es/useRafState/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};






function useScroll(target, shouldUpdate) {
  if (shouldUpdate === void 0) {
    shouldUpdate = function shouldUpdate() {
      return true;
    };
  }

  var _a = __read((0,_useRafState__WEBPACK_IMPORTED_MODULE_0__.default)(), 2),
      position = _a[0],
      setPosition = _a[1];

  var shouldUpdateRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(shouldUpdate);
  (0,_utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    var el = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(target, document);

    if (!el) {
      return;
    }

    var updatePosition = function updatePosition() {
      var newPosition;

      if (el === document) {
        if (document.scrollingElement) {
          newPosition = {
            left: document.scrollingElement.scrollLeft,
            top: document.scrollingElement.scrollTop
          };
        } else {
          // When in quirks mode, the scrollingElement attribute returns the HTML body element if it exists and is potentially scrollable, otherwise it returns null.
          // https://developer.mozilla.org/zh-CN/docs/Web/API/Document/scrollingElement
          // https://stackoverflow.com/questions/28633221/document-body-scrolltop-firefox-returns-0-only-js
          newPosition = {
            left: Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop),
            top: Math.max(window.pageXOffset, document.documentElement.scrollLeft, document.body.scrollLeft)
          };
        }
      } else {
        newPosition = {
          left: el.scrollLeft,
          top: el.scrollTop
        };
      }

      if (shouldUpdateRef.current(newPosition)) {
        setPosition(newPosition);
      }
    };

    updatePosition();
    el.addEventListener('scroll', updatePosition);
    return function () {
      el.removeEventListener('scroll', updatePosition);
    };
  }, [], target);
  return position;
}

/* harmony default export */ __webpack_exports__["default"] = (useScroll);

/***/ }),

/***/ "./node_modules/ahooks/es/useSelections/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/useSelections/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useSelections; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};



function useSelections(items, defaultSelected) {
  if (defaultSelected === void 0) {
    defaultSelected = [];
  }

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defaultSelected), 2),
      selected = _a[0],
      setSelected = _a[1];

  var selectedSet = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return new Set(selected);
  }, [selected]);

  var isSelected = function isSelected(item) {
    return selectedSet.has(item);
  };

  var select = function select(item) {
    selectedSet.add(item);
    return setSelected(Array.from(selectedSet));
  };

  var unSelect = function unSelect(item) {
    selectedSet["delete"](item);
    return setSelected(Array.from(selectedSet));
  };

  var toggle = function toggle(item) {
    if (isSelected(item)) {
      unSelect(item);
    } else {
      select(item);
    }
  };

  var selectAll = function selectAll() {
    items.forEach(function (o) {
      selectedSet.add(o);
    });
    setSelected(Array.from(selectedSet));
  };

  var unSelectAll = function unSelectAll() {
    items.forEach(function (o) {
      selectedSet["delete"](o);
    });
    setSelected(Array.from(selectedSet));
  };

  var noneSelected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return items.every(function (o) {
      return !selectedSet.has(o);
    });
  }, [items, selectedSet]);
  var allSelected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return items.every(function (o) {
      return selectedSet.has(o);
    }) && !noneSelected;
  }, [items, selectedSet, noneSelected]);
  var partiallySelected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return !noneSelected && !allSelected;
  }, [noneSelected, allSelected]);

  var toggleAll = function toggleAll() {
    return allSelected ? unSelectAll() : selectAll();
  };

  return {
    selected: selected,
    noneSelected: noneSelected,
    allSelected: allSelected,
    partiallySelected: partiallySelected,
    setSelected: setSelected,
    isSelected: isSelected,
    select: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(select),
    unSelect: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(unSelect),
    toggle: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(toggle),
    selectAll: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(selectAll),
    unSelectAll: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(unSelectAll),
    toggleAll: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(toggleAll)
  };
}

/***/ }),

/***/ "./node_modules/ahooks/es/useSessionStorageState/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/ahooks/es/useSessionStorageState/index.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _createUseStorageState__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createUseStorageState */ "./node_modules/ahooks/es/createUseStorageState/index.js");
/* harmony import */ var _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isBrowser */ "./node_modules/ahooks/es/utils/isBrowser.js");


var useSessionStorageState = (0,_createUseStorageState__WEBPACK_IMPORTED_MODULE_0__.createUseStorageState)(function () {
  return _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__.default ? sessionStorage : undefined;
});
/* harmony default export */ __webpack_exports__["default"] = (useSessionStorageState);

/***/ }),

/***/ "./node_modules/ahooks/es/useSetState/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useSetState/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ "./node_modules/ahooks/es/utils/index.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




var useSetState = function useSetState(initialState) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState), 2),
      state = _a[0],
      setState = _a[1];

  var setMergeState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (patch) {
    setState(function (prevState) {
      var newState = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isFunction)(patch) ? patch(prevState) : patch;
      return newState ? __assign(__assign({}, prevState), newState) : prevState;
    });
  }, []);
  return [state, setMergeState];
};

/* harmony default export */ __webpack_exports__["default"] = (useSetState);

/***/ }),

/***/ "./node_modules/ahooks/es/useSet/index.js":
/*!************************************************!*\
  !*** ./node_modules/ahooks/es/useSet/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




function useSet(initialValue) {
  var getInitValue = function getInitValue() {
    return initialValue === undefined ? new Set() : new Set(initialValue);
  };

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {
    return getInitValue();
  }), 2),
      set = _a[0],
      setSet = _a[1];

  var add = function add(key) {
    if (set.has(key)) {
      return;
    }

    setSet(function (prevSet) {
      var temp = new Set(prevSet);
      temp.add(key);
      return temp;
    });
  };

  var remove = function remove(key) {
    if (!set.has(key)) {
      return;
    }

    setSet(function (prevSet) {
      var temp = new Set(prevSet);
      temp["delete"](key);
      return temp;
    });
  };

  var reset = function reset() {
    return setSet(getInitValue());
  };

  return [set, {
    add: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(add),
    remove: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(remove),
    reset: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_1__.default)(reset)
  }];
}

/* harmony default export */ __webpack_exports__["default"] = (useSet);

/***/ }),

/***/ "./node_modules/ahooks/es/useSize/index.js":
/*!*************************************************!*\
  !*** ./node_modules/ahooks/es/useSize/index.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var resize_observer_polyfill__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resize-observer-polyfill */ "./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js");
/* harmony import */ var _useRafState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useRafState */ "./node_modules/ahooks/es/useRafState/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useIsomorphicLayoutEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/useIsomorphicLayoutEffectWithTarget */ "./node_modules/ahooks/es/utils/useIsomorphicLayoutEffectWithTarget.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};






function useSize(target) {
  var _a = __read((0,_useRafState__WEBPACK_IMPORTED_MODULE_1__.default)(), 2),
      state = _a[0],
      setState = _a[1];

  (0,_utils_useIsomorphicLayoutEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    var el = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(target);

    if (!el) {
      return;
    }

    var resizeObserver = new resize_observer_polyfill__WEBPACK_IMPORTED_MODULE_0__.default(function (entries) {
      entries.forEach(function (entry) {
        var _a = entry.target,
            clientWidth = _a.clientWidth,
            clientHeight = _a.clientHeight;
        setState({
          width: clientWidth,
          height: clientHeight
        });
      });
    });
    resizeObserver.observe(el);
    return function () {
      resizeObserver.disconnect();
    };
  }, [], target);
  return state;
}

/* harmony default export */ __webpack_exports__["default"] = (useSize);

/***/ }),

/***/ "./node_modules/ahooks/es/useTextSelection/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/ahooks/es/useTextSelection/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
/* harmony import */ var _utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};

var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




var initRect = {
  top: NaN,
  left: NaN,
  bottom: NaN,
  right: NaN,
  height: NaN,
  width: NaN
};

var initState = __assign({
  text: ''
}, initRect);

function getRectFromSelection(selection) {
  if (!selection) {
    return initRect;
  }

  if (selection.rangeCount < 1) {
    return initRect;
  }

  var range = selection.getRangeAt(0);

  var _a = range.getBoundingClientRect(),
      height = _a.height,
      width = _a.width,
      top = _a.top,
      left = _a.left,
      right = _a.right,
      bottom = _a.bottom;

  return {
    height: height,
    width: width,
    top: top,
    left: left,
    right: right,
    bottom: bottom
  };
}

function useTextSelection(target) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initState), 2),
      state = _a[0],
      setState = _a[1];

  var stateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(state);
  stateRef.current = state;
  (0,_utils_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    var el = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_2__.getTargetElement)(target, document);

    if (!el) {
      return;
    }

    var mouseupHandler = function mouseupHandler() {
      var selObj = null;
      var text = '';
      var rect = initRect;
      if (!window.getSelection) return;
      selObj = window.getSelection();
      text = selObj ? selObj.toString() : '';

      if (text) {
        rect = getRectFromSelection(selObj);
        setState(__assign(__assign(__assign({}, state), {
          text: text
        }), rect));
      }
    }; // 任意点击都需要清空之前的 range


    var mousedownHandler = function mousedownHandler() {
      if (!window.getSelection) return;

      if (stateRef.current.text) {
        setState(__assign({}, initState));
      }

      var selObj = window.getSelection();
      if (!selObj) return;
      selObj.removeAllRanges();
    };

    el.addEventListener('mouseup', mouseupHandler);
    document.addEventListener('mousedown', mousedownHandler);
    return function () {
      el.removeEventListener('mouseup', mouseupHandler);
      document.removeEventListener('mousedown', mousedownHandler);
    };
  }, [], target);
  return state;
}

/* harmony default export */ __webpack_exports__["default"] = (useTextSelection);

/***/ }),

/***/ "./node_modules/ahooks/es/useThrottleEffect/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/ahooks/es/useThrottleEffect/index.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useThrottleFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useThrottleFn */ "./node_modules/ahooks/es/useThrottleFn/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _useUpdateEffect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useUpdateEffect */ "./node_modules/ahooks/es/useUpdateEffect/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};






function useThrottleEffect(effect, deps, options) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}), 2),
      flag = _a[0],
      setFlag = _a[1];

  var _b = (0,_useThrottleFn__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    setFlag({});
  }, options),
      run = _b.run,
      cancel = _b.cancel;

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    return run();
  }, deps);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_2__.default)(cancel);
  (0,_useUpdateEffect__WEBPACK_IMPORTED_MODULE_3__.default)(effect, [flag]);
}

/* harmony default export */ __webpack_exports__["default"] = (useThrottleEffect);

/***/ }),

/***/ "./node_modules/ahooks/es/useThrottleFn/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/ahooks/es/useThrottleFn/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/throttle */ "./node_modules/lodash/throttle.js");
/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};

var __spread = undefined && undefined.__spread || function () {
  for (var ar = [], i = 0; i < arguments.length; i++) {
    ar = ar.concat(__read(arguments[i]));
  }

  return ar;
};






function useThrottleFn(fn, options) {
  var _a;

  if (true) {
    if (typeof fn !== 'function') {
      console.error("useThrottleFn expected parameter is a function, got " + typeof fn);
    }
  }

  var fnRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_2__.default)(fn);
  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;
  var throttled = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
    return lodash_throttle__WEBPACK_IMPORTED_MODULE_0___default()(function () {
      var args = [];

      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }

      return fnRef.current.apply(fnRef, __spread(args));
    }, wait, options);
  }, []);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_3__.default)(function () {
    throttled.cancel();
  });
  return {
    run: throttled,
    cancel: throttled.cancel,
    flush: throttled.flush
  };
}

/* harmony default export */ __webpack_exports__["default"] = (useThrottleFn);

/***/ }),

/***/ "./node_modules/ahooks/es/useThrottle/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/useThrottle/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useThrottleFn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useThrottleFn */ "./node_modules/ahooks/es/useThrottleFn/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};




function useThrottle(value, options) {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value), 2),
      throttled = _a[0],
      setThrottled = _a[1];

  var run = (0,_useThrottleFn__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    setThrottled(value);
  }, options).run;
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    run();
  }, [value]);
  return throttled;
}

/* harmony default export */ __webpack_exports__["default"] = (useThrottle);

/***/ }),

/***/ "./node_modules/ahooks/es/useTimeout/index.js":
/*!****************************************************!*\
  !*** ./node_modules/ahooks/es/useTimeout/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");



function useTimeout(fn, delay) {
  var fnRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(fn);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (typeof delay !== 'number' || delay < 0) return;
    var timer = setTimeout(function () {
      fnRef.current();
    }, delay);
    return function () {
      clearTimeout(timer);
    };
  }, [delay]);
}

/* harmony default export */ __webpack_exports__["default"] = (useTimeout);

/***/ }),

/***/ "./node_modules/ahooks/es/useTitle/index.js":
/*!**************************************************!*\
  !*** ./node_modules/ahooks/es/useTitle/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isBrowser */ "./node_modules/ahooks/es/utils/isBrowser.js");



var DEFAULT_OPTIONS = {
  restoreOnUnmount: false
};

function useTitle(title, options) {
  if (options === void 0) {
    options = DEFAULT_OPTIONS;
  }

  var titleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(_utils_isBrowser__WEBPACK_IMPORTED_MODULE_1__.default ? document.title : '');
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    document.title = title;
  }, [title]);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    if (options.restoreOnUnmount) {
      document.title = titleRef.current;
    }
  });
}

/* harmony default export */ __webpack_exports__["default"] = (useTitle);

/***/ }),

/***/ "./node_modules/ahooks/es/useToggle/index.js":
/*!***************************************************!*\
  !*** ./node_modules/ahooks/es/useToggle/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};



function useToggle(defaultValue, reverseValue) {
  if (defaultValue === void 0) {
    defaultValue = false;
  }

  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defaultValue), 2),
      state = _a[0],
      setState = _a[1];

  var actions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    var reverseValueOrigin = reverseValue === undefined ? !defaultValue : reverseValue;

    var toggle = function toggle() {
      return setState(function (s) {
        return s === defaultValue ? reverseValueOrigin : defaultValue;
      });
    };

    var set = function set(value) {
      return setState(value);
    };

    var setLeft = function setLeft() {
      return setState(defaultValue);
    };

    var setRight = function setRight() {
      return setState(reverseValueOrigin);
    };

    return {
      toggle: toggle,
      set: set,
      setLeft: setLeft,
      setRight: setRight
    }; // useToggle ignore value change
    // }, [defaultValue, reverseValue]);
  }, []);
  return [state, actions];
}

/* harmony default export */ __webpack_exports__["default"] = (useToggle);

/***/ }),

/***/ "./node_modules/ahooks/es/useTrackedEffect/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/ahooks/es/useTrackedEffect/index.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


var diffTwoDeps = function diffTwoDeps(deps1, deps2) {
  //Let's do a reference equality check on 2 dependency list.
  //If deps1 is defined, we iterate over deps1 and do comparison on each element with equivalent element from deps2
  //As this func is used only in this hook, we assume 2 deps always have same length.
  return deps1 ? deps1.map(function (_ele, idx) {
    return deps1[idx] !== (deps2 === null || deps2 === void 0 ? void 0 : deps2[idx]) ? idx : -1;
  }).filter(function (ele) {
    return ele >= 0;
  }) : deps2 ? deps2.map(function (_ele, idx) {
    return idx;
  }) : [];
};

var useTrackedEffect = function useTrackedEffect(effect, deps) {
  var previousDepsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var changes = diffTwoDeps(previousDepsRef.current, deps);
    var previousDeps = previousDepsRef.current;
    previousDepsRef.current = deps;
    return effect(changes, previousDeps, deps);
  }, deps);
};

/* harmony default export */ __webpack_exports__["default"] = (useTrackedEffect);

/***/ }),

/***/ "./node_modules/ahooks/es/useUnmount/index.js":
/*!****************************************************!*\
  !*** ./node_modules/ahooks/es/useUnmount/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");



var useUnmount = function useUnmount(fn) {
  if (true) {
    if (typeof fn !== 'function') {
      console.error("useUnmount expected parameter is a function, got " + typeof fn);
    }
  }

  var fnRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(fn);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    return function () {
      fnRef.current();
    };
  }, []);
};

/* harmony default export */ __webpack_exports__["default"] = (useUnmount);

/***/ }),

/***/ "./node_modules/ahooks/es/useUnmountedRef/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/ahooks/es/useUnmountedRef/index.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


var useUnmountedRef = function useUnmountedRef() {
  var unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    unmountedRef.current = false;
    return function () {
      unmountedRef.current = true;
    };
  }, []);
  return unmountedRef;
};

/* harmony default export */ __webpack_exports__["default"] = (useUnmountedRef);

/***/ }),

/***/ "./node_modules/ahooks/es/useUpdateEffect/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/ahooks/es/useUpdateEffect/index.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _createUpdateEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../createUpdateEffect */ "./node_modules/ahooks/es/createUpdateEffect/index.js");


/* harmony default export */ __webpack_exports__["default"] = ((0,_createUpdateEffect__WEBPACK_IMPORTED_MODULE_1__.createUpdateEffect)(react__WEBPACK_IMPORTED_MODULE_0__.useEffect));

/***/ }),

/***/ "./node_modules/ahooks/es/useUpdateLayoutEffect/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/ahooks/es/useUpdateLayoutEffect/index.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _createUpdateEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../createUpdateEffect */ "./node_modules/ahooks/es/createUpdateEffect/index.js");


/* harmony default export */ __webpack_exports__["default"] = ((0,_createUpdateEffect__WEBPACK_IMPORTED_MODULE_1__.createUpdateEffect)(react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect));

/***/ }),

/***/ "./node_modules/ahooks/es/useUpdate/index.js":
/*!***************************************************!*\
  !*** ./node_modules/ahooks/es/useUpdate/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};



var useUpdate = function useUpdate() {
  var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}), 2),
      setState = _a[1];

  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    return setState({});
  }, []);
};

/* harmony default export */ __webpack_exports__["default"] = (useUpdate);

/***/ }),

/***/ "./node_modules/ahooks/es/useVirtualList/index.js":
/*!********************************************************!*\
  !*** ./node_modules/ahooks/es/useVirtualList/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useEventListener__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../useEventListener */ "./node_modules/ahooks/es/useEventListener/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useSize__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useSize */ "./node_modules/ahooks/es/useSize/index.js");
/* harmony import */ var _utils_domTarget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};








var useVirtualList = function useVirtualList(list, options) {
  var containerTarget = options.containerTarget,
      wrapperTarget = options.wrapperTarget,
      itemHeight = options.itemHeight,
      _a = options.overscan,
      overscan = _a === void 0 ? 5 : _a;
  var itemHeightRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(itemHeight);
  var size = (0,_useSize__WEBPACK_IMPORTED_MODULE_2__.default)(containerTarget);
  var scrollTriggerByScrollToFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);

  var _b = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]), 2),
      targetList = _b[0],
      setTargetList = _b[1];

  var getVisibleCount = function getVisibleCount(containerHeight, fromIndex) {
    if (typeof itemHeightRef.current === 'number') {
      return Math.ceil(containerHeight / itemHeightRef.current);
    }

    var sum = 0;
    var endIndex = 0;

    for (var i = fromIndex; i < list.length; i++) {
      var height = itemHeightRef.current(i, list[i]);
      sum += height;
      endIndex = i;

      if (sum >= containerHeight) {
        break;
      }
    }

    return endIndex - fromIndex;
  };

  var getOffset = function getOffset(scrollTop) {
    if (typeof itemHeightRef.current === 'number') {
      return Math.floor(scrollTop / itemHeightRef.current) + 1;
    }

    var sum = 0;
    var offset = 0;

    for (var i = 0; i < list.length; i++) {
      var height = itemHeightRef.current(i, list[i]);
      sum += height;

      if (sum >= scrollTop) {
        offset = i;
        break;
      }
    }

    return offset + 1;
  }; // 获取上部高度


  var getDistanceTop = function getDistanceTop(index) {
    if (typeof itemHeightRef.current === 'number') {
      var height_1 = index * itemHeightRef.current;
      return height_1;
    }

    var height = list.slice(0, index) // @ts-ignore
    .reduce(function (sum, _, i) {
      return sum + itemHeightRef.current(i, list[index]);
    }, 0);
    return height;
  };

  var totalHeight = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    if (typeof itemHeightRef.current === 'number') {
      return list.length * itemHeightRef.current;
    } // @ts-ignore


    return list.reduce(function (sum, _, index) {
      return sum + itemHeightRef.current(index, list[index]);
    }, 0);
  }, [list]);

  var calculateRange = function calculateRange() {
    var container = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(containerTarget);
    var wrapper = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(wrapperTarget);

    if (container && wrapper) {
      var scrollTop = container.scrollTop,
          clientHeight = container.clientHeight;
      var offset = getOffset(scrollTop);
      var visibleCount = getVisibleCount(clientHeight, offset);
      var start_1 = Math.max(0, offset - overscan);
      var end = Math.min(list.length, offset + visibleCount + overscan);
      var offsetTop = getDistanceTop(start_1); // @ts-ignore

      wrapper.style.height = totalHeight - offsetTop + 'px'; // @ts-ignore

      wrapper.style.marginTop = offsetTop + 'px';
      setTargetList(list.slice(start_1, end).map(function (ele, index) {
        return {
          data: ele,
          index: index + start_1
        };
      }));
    }
  };

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (!(size === null || size === void 0 ? void 0 : size.width) || !(size === null || size === void 0 ? void 0 : size.height)) {
      return;
    }

    calculateRange();
  }, [size === null || size === void 0 ? void 0 : size.width, size === null || size === void 0 ? void 0 : size.height, list]);
  (0,_useEventListener__WEBPACK_IMPORTED_MODULE_4__.default)('scroll', function (e) {
    if (scrollTriggerByScrollToFunc.current) {
      scrollTriggerByScrollToFunc.current = false;
      return;
    }

    e.preventDefault();
    calculateRange();
  }, {
    target: containerTarget
  });

  var scrollTo = function scrollTo(index) {
    var container = (0,_utils_domTarget__WEBPACK_IMPORTED_MODULE_3__.getTargetElement)(containerTarget);

    if (container) {
      scrollTriggerByScrollToFunc.current = true;
      container.scrollTop = getDistanceTop(index);
      calculateRange();
    }
  };

  return [targetList, (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_5__.default)(scrollTo)];
};

/* harmony default export */ __webpack_exports__["default"] = (useVirtualList);

/***/ }),

/***/ "./node_modules/ahooks/es/useWebSocket/index.js":
/*!******************************************************!*\
  !*** ./node_modules/ahooks/es/useWebSocket/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ReadyState": function() { return /* binding */ ReadyState; },
/* harmony export */   "default": function() { return /* binding */ useWebSocket; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLatest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useLatest */ "./node_modules/ahooks/es/useLatest/index.js");
/* harmony import */ var _useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useMemoizedFn */ "./node_modules/ahooks/es/useMemoizedFn/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
var __read = undefined && undefined.__read || function (o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o),
      r,
      ar = [],
      e;

  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {
      ar.push(r.value);
    }
  } catch (error) {
    e = {
      error: error
    };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }

  return ar;
};





var ReadyState;

(function (ReadyState) {
  ReadyState[ReadyState["Connecting"] = 0] = "Connecting";
  ReadyState[ReadyState["Open"] = 1] = "Open";
  ReadyState[ReadyState["Closing"] = 2] = "Closing";
  ReadyState[ReadyState["Closed"] = 3] = "Closed";
})(ReadyState || (ReadyState = {}));

function useWebSocket(socketUrl, options) {
  if (options === void 0) {
    options = {};
  }

  var _a = options.reconnectLimit,
      reconnectLimit = _a === void 0 ? 3 : _a,
      _b = options.reconnectInterval,
      reconnectInterval = _b === void 0 ? 3 * 1000 : _b,
      _c = options.manual,
      manual = _c === void 0 ? false : _c,
      onOpen = options.onOpen,
      onClose = options.onClose,
      onMessage = options.onMessage,
      onError = options.onError,
      protocols = options.protocols;
  var onOpenRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(onOpen);
  var onCloseRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(onClose);
  var onMessageRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(onMessage);
  var onErrorRef = (0,_useLatest__WEBPACK_IMPORTED_MODULE_1__.default)(onError);
  var reconnectTimesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);
  var reconnectTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);

  var _d = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(), 2),
      latestMessage = _d[0],
      setLatestMessage = _d[1];

  var _e = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(ReadyState.Closed), 2),
      readyState = _e[0],
      setReadyState = _e[1];

  var reconnect = function reconnect() {
    var _a;

    if (reconnectTimesRef.current < reconnectLimit && ((_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.readyState) !== ReadyState.Open) {
      if (reconnectTimerRef.current) {
        clearTimeout(reconnectTimerRef.current);
      }

      reconnectTimerRef.current = setTimeout(function () {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        connectWs();
        reconnectTimesRef.current++;
      }, reconnectInterval);
    }
  };

  var connectWs = function connectWs() {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }

    if (websocketRef.current) {
      websocketRef.current.close();
    }

    var ws = new WebSocket(socketUrl, protocols);
    setReadyState(ReadyState.Connecting);

    ws.onerror = function (event) {
      var _a;

      if (unmountedRef.current) {
        return;
      }

      reconnect();
      (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, event, ws);
      setReadyState(ws.readyState || ReadyState.Closed);
    };

    ws.onopen = function (event) {
      var _a;

      if (unmountedRef.current) {
        return;
      }

      (_a = onOpenRef.current) === null || _a === void 0 ? void 0 : _a.call(onOpenRef, event, ws);
      reconnectTimesRef.current = 0;
      setReadyState(ws.readyState || ReadyState.Open);
    };

    ws.onmessage = function (message) {
      var _a;

      if (unmountedRef.current) {
        return;
      }

      (_a = onMessageRef.current) === null || _a === void 0 ? void 0 : _a.call(onMessageRef, message, ws);
      setLatestMessage(message);
    };

    ws.onclose = function (event) {
      var _a;

      if (unmountedRef.current) {
        return;
      }

      reconnect();
      (_a = onCloseRef.current) === null || _a === void 0 ? void 0 : _a.call(onCloseRef, event, ws);
      setReadyState(ws.readyState || ReadyState.Closed);
    };

    websocketRef.current = ws;
  };

  var sendMessage = function sendMessage(message) {
    var _a;

    if (readyState === ReadyState.Open) {
      (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.send(message);
    } else {
      throw new Error('WebSocket disconnected');
    }
  };

  var connect = function connect() {
    reconnectTimesRef.current = 0;
    connectWs();
  };

  var disconnect = function disconnect() {
    var _a;

    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }

    reconnectTimesRef.current = reconnectLimit;
    (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.close();
  };

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (!manual) {
      connect();
    }
  }, [socketUrl, manual]);
  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
    unmountedRef.current = true;
    disconnect();
  });
  return {
    latestMessage: latestMessage,
    sendMessage: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__.default)(sendMessage),
    connect: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__.default)(connect),
    disconnect: (0,_useMemoizedFn__WEBPACK_IMPORTED_MODULE_3__.default)(disconnect),
    readyState: readyState,
    webSocketIns: websocketRef.current
  };
}

/***/ }),

/***/ "./node_modules/ahooks/es/useWhyDidYouUpdate/index.js":
/*!************************************************************!*\
  !*** ./node_modules/ahooks/es/useWhyDidYouUpdate/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useWhyDidYouUpdate; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
var __assign = undefined && undefined.__assign || function () {
  __assign = Object.assign || function (t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];

      for (var p in s) {
        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
    }

    return t;
  };

  return __assign.apply(this, arguments);
};


function useWhyDidYouUpdate(componentName, props) {
  var prevProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (prevProps.current) {
      var allKeys = Object.keys(__assign(__assign({}, prevProps.current), props));
      var changedProps_1 = {};
      allKeys.forEach(function (key) {
        if (prevProps.current[key] !== props[key]) {
          changedProps_1[key] = {
            from: prevProps.current[key],
            to: props[key]
          };
        }
      });

      if (Object.keys(changedProps_1).length) {
        console.log('[why-did-you-update]', componentName, changedProps_1);
      }
    }

    prevProps.current = props;
  });
}

/***/ }),

/***/ "./node_modules/ahooks/es/utils/canUseDom.js":
/*!***************************************************!*\
  !*** ./node_modules/ahooks/es/utils/canUseDom.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ canUseDom; }
/* harmony export */ });
function canUseDom() {
  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);
}

/***/ }),

/***/ "./node_modules/ahooks/es/utils/createEffectWithTarget.js":
/*!****************************************************************!*\
  !*** ./node_modules/ahooks/es/utils/createEffectWithTarget.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useUnmount */ "./node_modules/ahooks/es/useUnmount/index.js");
/* harmony import */ var _depsAreSame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./depsAreSame */ "./node_modules/ahooks/es/utils/depsAreSame.js");
/* harmony import */ var _domTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./domTarget */ "./node_modules/ahooks/es/utils/domTarget.js");





var createEffectWithTarget = function createEffectWithTarget(useEffectType) {
  /**
   *
   * @param effect
   * @param deps
   * @param target target should compare ref.current vs ref.current, dom vs dom, ()=>dom vs ()=>dom
   */
  var useEffectWithTarget = function useEffectWithTarget(effect, deps, target) {
    var hasInitRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
    var lastElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);
    var lastDepsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);
    var unLoadRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
    useEffectType(function () {
      var _a;

      var targets = Array.isArray(target) ? target : [target];
      var els = targets.map(function (item) {
        return (0,_domTarget__WEBPACK_IMPORTED_MODULE_1__.getTargetElement)(item);
      }); // init run

      if (!hasInitRef.current) {
        hasInitRef.current = true;
        lastElementRef.current = els;
        lastDepsRef.current = deps;
        unLoadRef.current = effect();
        return;
      }

      if (els.length !== lastElementRef.current.length || !(0,_depsAreSame__WEBPACK_IMPORTED_MODULE_2__.default)(els, lastElementRef.current) || !(0,_depsAreSame__WEBPACK_IMPORTED_MODULE_2__.default)(deps, lastDepsRef.current)) {
        (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);
        lastElementRef.current = els;
        lastDepsRef.current = deps;
        unLoadRef.current = effect();
      }
    });
    (0,_useUnmount__WEBPACK_IMPORTED_MODULE_3__.default)(function () {
      var _a;

      (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef); // for react-refresh

      hasInitRef.current = false;
    });
  };

  return useEffectWithTarget;
};

/* harmony default export */ __webpack_exports__["default"] = (createEffectWithTarget);

/***/ }),

/***/ "./node_modules/ahooks/es/utils/depsAreSame.js":
/*!*****************************************************!*\
  !*** ./node_modules/ahooks/es/utils/depsAreSame.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ depsAreSame; }
/* harmony export */ });
function depsAreSame(oldDeps, deps) {
  if (oldDeps === deps) return true;

  for (var i = 0; i < oldDeps.length; i++) {
    if (!Object.is(oldDeps[i], deps[i])) return false;
  }

  return true;
}

/***/ }),

/***/ "./node_modules/ahooks/es/utils/domTarget.js":
/*!***************************************************!*\
  !*** ./node_modules/ahooks/es/utils/domTarget.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getTargetElement": function() { return /* binding */ getTargetElement; }
/* harmony export */ });
/* harmony import */ var _isBrowser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isBrowser */ "./node_modules/ahooks/es/utils/isBrowser.js");

function getTargetElement(target, defaultElement) {
  if (!_isBrowser__WEBPACK_IMPORTED_MODULE_0__.default) {
    return undefined;
  }

  if (!target) {
    return defaultElement;
  }

  var targetElement;

  if (typeof target === 'function') {
    targetElement = target();
  } else if ('current' in target) {
    targetElement = target.current;
  } else {
    targetElement = target;
  }

  return targetElement;
}

/***/ }),

/***/ "./node_modules/ahooks/es/utils/index.js":
/*!***********************************************!*\
  !*** ./node_modules/ahooks/es/utils/index.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "isFunction": function() { return /* binding */ isFunction; }
/* harmony export */ });
function isFunction(obj) {
  return typeof obj === 'function';
}

/***/ }),

/***/ "./node_modules/ahooks/es/utils/isBrowser.js":
/*!***************************************************!*\
  !*** ./node_modules/ahooks/es/utils/isBrowser.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
var isBrowser = !!(typeof window !== 'undefined' && window.document && window.document.createElement);
/* harmony default export */ __webpack_exports__["default"] = (isBrowser);

/***/ }),

/***/ "./node_modules/ahooks/es/utils/rect.js":
/*!**********************************************!*\
  !*** ./node_modules/ahooks/es/utils/rect.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getScrollTop": function() { return /* binding */ getScrollTop; },
/* harmony export */   "getScrollHeight": function() { return /* binding */ getScrollHeight; },
/* harmony export */   "getClientHeight": function() { return /* binding */ getClientHeight; }
/* harmony export */ });
var getScrollTop = function getScrollTop(el) {
  if (el === document || el === document.body) {
    return Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop);
  }

  return el.scrollTop;
};

var getScrollHeight = function getScrollHeight(el) {
  return el.scrollHeight || Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);
};

var getClientHeight = function getClientHeight(el) {
  return el.clientHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight);
};



/***/ }),

/***/ "./node_modules/ahooks/es/utils/useDeepCompareWithTarget.js":
/*!******************************************************************!*\
  !*** ./node_modules/ahooks/es/utils/useDeepCompareWithTarget.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var lodash_isEqual__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/isEqual */ "./node_modules/lodash/isEqual.js");
/* harmony import */ var lodash_isEqual__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash_isEqual__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");




var depsEqual = function depsEqual(aDeps, bDeps) {
  if (bDeps === void 0) {
    bDeps = [];
  }

  return lodash_isEqual__WEBPACK_IMPORTED_MODULE_0___default()(aDeps, bDeps);
};

var useDeepCompareEffectWithTarget = function useDeepCompareEffectWithTarget(effect, deps, target) {
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
  var signalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);

  if (!depsEqual(deps, ref.current)) {
    ref.current = deps;
    signalRef.current += 1;
  }

  (0,_useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__.default)(effect, [signalRef.current], target);
};

/* harmony default export */ __webpack_exports__["default"] = (useDeepCompareEffectWithTarget);

/***/ }),

/***/ "./node_modules/ahooks/es/utils/useEffectWithTarget.js":
/*!*************************************************************!*\
  !*** ./node_modules/ahooks/es/utils/useEffectWithTarget.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _createEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createEffectWithTarget */ "./node_modules/ahooks/es/utils/createEffectWithTarget.js");


var useEffectWithTarget = (0,_createEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__.default)(react__WEBPACK_IMPORTED_MODULE_0__.useEffect);
/* harmony default export */ __webpack_exports__["default"] = (useEffectWithTarget);

/***/ }),

/***/ "./node_modules/ahooks/es/utils/useIsomorphicLayoutEffectWithTarget.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/ahooks/es/utils/useIsomorphicLayoutEffectWithTarget.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _isBrowser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isBrowser */ "./node_modules/ahooks/es/utils/isBrowser.js");
/* harmony import */ var _useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useEffectWithTarget */ "./node_modules/ahooks/es/utils/useEffectWithTarget.js");
/* harmony import */ var _useLayoutEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useLayoutEffectWithTarget */ "./node_modules/ahooks/es/utils/useLayoutEffectWithTarget.js");



var useIsomorphicLayoutEffectWithTarget = _isBrowser__WEBPACK_IMPORTED_MODULE_0__.default ? _useLayoutEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__.default : _useEffectWithTarget__WEBPACK_IMPORTED_MODULE_2__.default;
/* harmony default export */ __webpack_exports__["default"] = (useIsomorphicLayoutEffectWithTarget);

/***/ }),

/***/ "./node_modules/ahooks/es/utils/useLayoutEffectWithTarget.js":
/*!*******************************************************************!*\
  !*** ./node_modules/ahooks/es/utils/useLayoutEffectWithTarget.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _createEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createEffectWithTarget */ "./node_modules/ahooks/es/utils/createEffectWithTarget.js");


var useEffectWithTarget = (0,_createEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__.default)(react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect);
/* harmony default export */ __webpack_exports__["default"] = (useEffectWithTarget);

/***/ }),

/***/ "./node_modules/ahooks/node_modules/dayjs/dayjs.min.js":
/*!*************************************************************!*\
  !*** ./node_modules/ahooks/node_modules/dayjs/dayjs.min.js ***!
  \*************************************************************/
/***/ (function(module) {

!function(t,e){ true?module.exports=e():0}(this,(function(){"use strict";var t=1e3,e=6e4,n=36e5,r="millisecond",i="second",s="minute",u="hour",a="day",o="week",f="month",h="quarter",c="year",d="date",$="Invalid Date",l=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,y=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,M={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},m=function(t,e,n){var r=String(t);return!r||r.length>=e?t:""+Array(e+1-r.length).join(n)+t},g={s:m,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),r=Math.floor(n/60),i=n%60;return(e<=0?"+":"-")+m(r,2,"0")+":"+m(i,2,"0")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var r=12*(n.year()-e.year())+(n.month()-e.month()),i=e.clone().add(r,f),s=n-i<0,u=e.clone().add(r+(s?-1:1),f);return+(-(r+(n-i)/(s?i-u:u-i))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:f,y:c,w:o,d:a,D:d,h:u,m:s,s:i,ms:r,Q:h}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},v="en",D={};D[v]=M;var p=function(t){return t instanceof _},S=function t(e,n,r){var i;if(!e)return v;if("string"==typeof e){var s=e.toLowerCase();D[s]&&(i=s),n&&(D[s]=n,i=s);var u=e.split("-");if(!i&&u.length>1)return t(u[0])}else{var a=e.name;D[a]=e,i=a}return!r&&i&&(v=i),i||!r&&v},w=function(t,e){if(p(t))return t.clone();var n="object"==typeof e?e:{};return n.date=t,n.args=arguments,new _(n)},O=g;O.l=S,O.i=p,O.w=function(t,e){return w(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var _=function(){function M(t){this.$L=S(t.locale,null,!0),this.parse(t)}var m=M.prototype;return m.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(O.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var r=e.match(l);if(r){var i=r[2]-1||0,s=(r[7]||"0").substring(0,3);return n?new Date(Date.UTC(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)):new Date(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},m.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},m.$utils=function(){return O},m.isValid=function(){return!(this.$d.toString()===$)},m.isSame=function(t,e){var n=w(t);return this.startOf(e)<=n&&n<=this.endOf(e)},m.isAfter=function(t,e){return w(t)<this.startOf(e)},m.isBefore=function(t,e){return this.endOf(e)<w(t)},m.$g=function(t,e,n){return O.u(t)?this[e]:this.set(n,t)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(t,e){var n=this,r=!!O.u(e)||e,h=O.p(t),$=function(t,e){var i=O.w(n.$u?Date.UTC(n.$y,e,t):new Date(n.$y,e,t),n);return r?i:i.endOf(a)},l=function(t,e){return O.w(n.toDate()[t].apply(n.toDate("s"),(r?[0,0,0,0]:[23,59,59,999]).slice(e)),n)},y=this.$W,M=this.$M,m=this.$D,g="set"+(this.$u?"UTC":"");switch(h){case c:return r?$(1,0):$(31,11);case f:return r?$(1,M):$(0,M+1);case o:var v=this.$locale().weekStart||0,D=(y<v?y+7:y)-v;return $(r?m-D:m+(6-D),M);case a:case d:return l(g+"Hours",0);case u:return l(g+"Minutes",1);case s:return l(g+"Seconds",2);case i:return l(g+"Milliseconds",3);default:return this.clone()}},m.endOf=function(t){return this.startOf(t,!1)},m.$set=function(t,e){var n,o=O.p(t),h="set"+(this.$u?"UTC":""),$=(n={},n[a]=h+"Date",n[d]=h+"Date",n[f]=h+"Month",n[c]=h+"FullYear",n[u]=h+"Hours",n[s]=h+"Minutes",n[i]=h+"Seconds",n[r]=h+"Milliseconds",n)[o],l=o===a?this.$D+(e-this.$W):e;if(o===f||o===c){var y=this.clone().set(d,1);y.$d[$](l),y.init(),this.$d=y.set(d,Math.min(this.$D,y.daysInMonth())).$d}else $&&this.$d[$](l);return this.init(),this},m.set=function(t,e){return this.clone().$set(t,e)},m.get=function(t){return this[O.p(t)]()},m.add=function(r,h){var d,$=this;r=Number(r);var l=O.p(h),y=function(t){var e=w($);return O.w(e.date(e.date()+Math.round(t*r)),$)};if(l===f)return this.set(f,this.$M+r);if(l===c)return this.set(c,this.$y+r);if(l===a)return y(1);if(l===o)return y(7);var M=(d={},d[s]=e,d[u]=n,d[i]=t,d)[l]||1,m=this.$d.getTime()+r*M;return O.w(m,this)},m.subtract=function(t,e){return this.add(-1*t,e)},m.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return n.invalidDate||$;var r=t||"YYYY-MM-DDTHH:mm:ssZ",i=O.z(this),s=this.$H,u=this.$m,a=this.$M,o=n.weekdays,f=n.months,h=function(t,n,i,s){return t&&(t[n]||t(e,r))||i[n].substr(0,s)},c=function(t){return O.s(s%12||12,t,"0")},d=n.meridiem||function(t,e,n){var r=t<12?"AM":"PM";return n?r.toLowerCase():r},l={YY:String(this.$y).slice(-2),YYYY:this.$y,M:a+1,MM:O.s(a+1,2,"0"),MMM:h(n.monthsShort,a,f,3),MMMM:h(f,a),D:this.$D,DD:O.s(this.$D,2,"0"),d:String(this.$W),dd:h(n.weekdaysMin,this.$W,o,2),ddd:h(n.weekdaysShort,this.$W,o,3),dddd:o[this.$W],H:String(s),HH:O.s(s,2,"0"),h:c(1),hh:c(2),a:d(s,u,!0),A:d(s,u,!1),m:String(u),mm:O.s(u,2,"0"),s:String(this.$s),ss:O.s(this.$s,2,"0"),SSS:O.s(this.$ms,3,"0"),Z:i};return r.replace(y,(function(t,e){return e||l[t]||i.replace(":","")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(r,d,$){var l,y=O.p(d),M=w(r),m=(M.utcOffset()-this.utcOffset())*e,g=this-M,v=O.m(this,M);return v=(l={},l[c]=v/12,l[f]=v,l[h]=v/3,l[o]=(g-m)/6048e5,l[a]=(g-m)/864e5,l[u]=g/n,l[s]=g/e,l[i]=g/t,l)[y]||g,$?v:O.a(v)},m.daysInMonth=function(){return this.endOf(f).$D},m.$locale=function(){return D[this.$L]},m.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),r=S(t,e,!0);return r&&(n.$L=r),n},m.clone=function(){return O.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},M}(),b=_.prototype;return w.prototype=b,[["$ms",r],["$s",i],["$m",s],["$H",u],["$W",a],["$M",f],["$y",c],["$D",d]].forEach((function(t){b[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),w.extend=function(t,e){return t.$i||(t(e,_,w),t.$i=!0),w},w.locale=S,w.isDayjs=p,w.unix=function(t){return w(1e3*t)},w.en=D[v],w.Ls=D,w.p={},w}));

/***/ }),

/***/ "./node_modules/intersection-observer/intersection-observer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/intersection-observer/intersection-observer.js ***!
  \*********************************************************************/
/***/ (function() {

/**
 * Copyright 2016 Google Inc. All Rights Reserved.
 *
 * Licensed under the W3C SOFTWARE AND DOCUMENT NOTICE AND LICENSE.
 *
 *  https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 */
(function() {
'use strict';

// Exit early if we're not running in a browser.
if (typeof window !== 'object') {
  return;
}

// Exit early if all IntersectionObserver and IntersectionObserverEntry
// features are natively supported.
if ('IntersectionObserver' in window &&
    'IntersectionObserverEntry' in window &&
    'intersectionRatio' in window.IntersectionObserverEntry.prototype) {

  // Minimal polyfill for Edge 15's lack of `isIntersecting`
  // See: https://github.com/w3c/IntersectionObserver/issues/211
  if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {
    Object.defineProperty(window.IntersectionObserverEntry.prototype,
      'isIntersecting', {
      get: function () {
        return this.intersectionRatio > 0;
      }
    });
  }
  return;
}

/**
 * Returns the embedding frame element, if any.
 * @param {!Document} doc
 * @return {!Element}
 */
function getFrameElement(doc) {
  try {
    return doc.defaultView && doc.defaultView.frameElement || null;
  } catch (e) {
    // Ignore the error.
    return null;
  }
}

/**
 * A local reference to the root document.
 */
var document = (function(startDoc) {
  var doc = startDoc;
  var frame = getFrameElement(doc);
  while (frame) {
    doc = frame.ownerDocument;
    frame = getFrameElement(doc);
  }
  return doc;
})(window.document);

/**
 * An IntersectionObserver registry. This registry exists to hold a strong
 * reference to IntersectionObserver instances currently observing a target
 * element. Without this registry, instances without another reference may be
 * garbage collected.
 */
var registry = [];

/**
 * The signal updater for cross-origin intersection. When not null, it means
 * that the polyfill is configured to work in a cross-origin mode.
 * @type {function(DOMRect|ClientRect, DOMRect|ClientRect)}
 */
var crossOriginUpdater = null;

/**
 * The current cross-origin intersection. Only used in the cross-origin mode.
 * @type {DOMRect|ClientRect}
 */
var crossOriginRect = null;


/**
 * Creates the global IntersectionObserverEntry constructor.
 * https://w3c.github.io/IntersectionObserver/#intersection-observer-entry
 * @param {Object} entry A dictionary of instance properties.
 * @constructor
 */
function IntersectionObserverEntry(entry) {
  this.time = entry.time;
  this.target = entry.target;
  this.rootBounds = ensureDOMRect(entry.rootBounds);
  this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);
  this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());
  this.isIntersecting = !!entry.intersectionRect;

  // Calculates the intersection ratio.
  var targetRect = this.boundingClientRect;
  var targetArea = targetRect.width * targetRect.height;
  var intersectionRect = this.intersectionRect;
  var intersectionArea = intersectionRect.width * intersectionRect.height;

  // Sets intersection ratio.
  if (targetArea) {
    // Round the intersection ratio to avoid floating point math issues:
    // https://github.com/w3c/IntersectionObserver/issues/324
    this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));
  } else {
    // If area is zero and is intersecting, sets to 1, otherwise to 0
    this.intersectionRatio = this.isIntersecting ? 1 : 0;
  }
}


/**
 * Creates the global IntersectionObserver constructor.
 * https://w3c.github.io/IntersectionObserver/#intersection-observer-interface
 * @param {Function} callback The function to be invoked after intersection
 *     changes have queued. The function is not invoked if the queue has
 *     been emptied by calling the `takeRecords` method.
 * @param {Object=} opt_options Optional configuration options.
 * @constructor
 */
function IntersectionObserver(callback, opt_options) {

  var options = opt_options || {};

  if (typeof callback != 'function') {
    throw new Error('callback must be a function');
  }

  if (
    options.root &&
    options.root.nodeType != 1 &&
    options.root.nodeType != 9
  ) {
    throw new Error('root must be a Document or Element');
  }

  // Binds and throttles `this._checkForIntersections`.
  this._checkForIntersections = throttle(
      this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT);

  // Private properties.
  this._callback = callback;
  this._observationTargets = [];
  this._queuedEntries = [];
  this._rootMarginValues = this._parseRootMargin(options.rootMargin);

  // Public properties.
  this.thresholds = this._initThresholds(options.threshold);
  this.root = options.root || null;
  this.rootMargin = this._rootMarginValues.map(function(margin) {
    return margin.value + margin.unit;
  }).join(' ');

  /** @private @const {!Array<!Document>} */
  this._monitoringDocuments = [];
  /** @private @const {!Array<function()>} */
  this._monitoringUnsubscribes = [];
}


/**
 * The minimum interval within which the document will be checked for
 * intersection changes.
 */
IntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;


/**
 * The frequency in which the polyfill polls for intersection changes.
 * this can be updated on a per instance basis and must be set prior to
 * calling `observe` on the first target.
 */
IntersectionObserver.prototype.POLL_INTERVAL = null;

/**
 * Use a mutation observer on the root element
 * to detect intersection changes.
 */
IntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;


/**
 * Sets up the polyfill in the cross-origin mode. The result is the
 * updater function that accepts two arguments: `boundingClientRect` and
 * `intersectionRect` - just as these fields would be available to the
 * parent via `IntersectionObserverEntry`. This function should be called
 * each time the iframe receives intersection information from the parent
 * window, e.g. via messaging.
 * @return {function(DOMRect|ClientRect, DOMRect|ClientRect)}
 */
IntersectionObserver._setupCrossOriginUpdater = function() {
  if (!crossOriginUpdater) {
    /**
     * @param {DOMRect|ClientRect} boundingClientRect
     * @param {DOMRect|ClientRect} intersectionRect
     */
    crossOriginUpdater = function(boundingClientRect, intersectionRect) {
      if (!boundingClientRect || !intersectionRect) {
        crossOriginRect = getEmptyRect();
      } else {
        crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);
      }
      registry.forEach(function(observer) {
        observer._checkForIntersections();
      });
    };
  }
  return crossOriginUpdater;
};


/**
 * Resets the cross-origin mode.
 */
IntersectionObserver._resetCrossOriginUpdater = function() {
  crossOriginUpdater = null;
  crossOriginRect = null;
};


/**
 * Starts observing a target element for intersection changes based on
 * the thresholds values.
 * @param {Element} target The DOM element to observe.
 */
IntersectionObserver.prototype.observe = function(target) {
  var isTargetAlreadyObserved = this._observationTargets.some(function(item) {
    return item.element == target;
  });

  if (isTargetAlreadyObserved) {
    return;
  }

  if (!(target && target.nodeType == 1)) {
    throw new Error('target must be an Element');
  }

  this._registerInstance();
  this._observationTargets.push({element: target, entry: null});
  this._monitorIntersections(target.ownerDocument);
  this._checkForIntersections();
};


/**
 * Stops observing a target element for intersection changes.
 * @param {Element} target The DOM element to observe.
 */
IntersectionObserver.prototype.unobserve = function(target) {
  this._observationTargets =
      this._observationTargets.filter(function(item) {
        return item.element != target;
      });
  this._unmonitorIntersections(target.ownerDocument);
  if (this._observationTargets.length == 0) {
    this._unregisterInstance();
  }
};


/**
 * Stops observing all target elements for intersection changes.
 */
IntersectionObserver.prototype.disconnect = function() {
  this._observationTargets = [];
  this._unmonitorAllIntersections();
  this._unregisterInstance();
};


/**
 * Returns any queue entries that have not yet been reported to the
 * callback and clears the queue. This can be used in conjunction with the
 * callback to obtain the absolute most up-to-date intersection information.
 * @return {Array} The currently queued entries.
 */
IntersectionObserver.prototype.takeRecords = function() {
  var records = this._queuedEntries.slice();
  this._queuedEntries = [];
  return records;
};


/**
 * Accepts the threshold value from the user configuration object and
 * returns a sorted array of unique threshold values. If a value is not
 * between 0 and 1 and error is thrown.
 * @private
 * @param {Array|number=} opt_threshold An optional threshold value or
 *     a list of threshold values, defaulting to [0].
 * @return {Array} A sorted list of unique and valid threshold values.
 */
IntersectionObserver.prototype._initThresholds = function(opt_threshold) {
  var threshold = opt_threshold || [0];
  if (!Array.isArray(threshold)) threshold = [threshold];

  return threshold.sort().filter(function(t, i, a) {
    if (typeof t != 'number' || isNaN(t) || t < 0 || t > 1) {
      throw new Error('threshold must be a number between 0 and 1 inclusively');
    }
    return t !== a[i - 1];
  });
};


/**
 * Accepts the rootMargin value from the user configuration object
 * and returns an array of the four margin values as an object containing
 * the value and unit properties. If any of the values are not properly
 * formatted or use a unit other than px or %, and error is thrown.
 * @private
 * @param {string=} opt_rootMargin An optional rootMargin value,
 *     defaulting to '0px'.
 * @return {Array<Object>} An array of margin objects with the keys
 *     value and unit.
 */
IntersectionObserver.prototype._parseRootMargin = function(opt_rootMargin) {
  var marginString = opt_rootMargin || '0px';
  var margins = marginString.split(/\s+/).map(function(margin) {
    var parts = /^(-?\d*\.?\d+)(px|%)$/.exec(margin);
    if (!parts) {
      throw new Error('rootMargin must be specified in pixels or percent');
    }
    return {value: parseFloat(parts[1]), unit: parts[2]};
  });

  // Handles shorthand.
  margins[1] = margins[1] || margins[0];
  margins[2] = margins[2] || margins[0];
  margins[3] = margins[3] || margins[1];

  return margins;
};


/**
 * Starts polling for intersection changes if the polling is not already
 * happening, and if the page's visibility state is visible.
 * @param {!Document} doc
 * @private
 */
IntersectionObserver.prototype._monitorIntersections = function(doc) {
  var win = doc.defaultView;
  if (!win) {
    // Already destroyed.
    return;
  }
  if (this._monitoringDocuments.indexOf(doc) != -1) {
    // Already monitoring.
    return;
  }

  // Private state for monitoring.
  var callback = this._checkForIntersections;
  var monitoringInterval = null;
  var domObserver = null;

  // If a poll interval is set, use polling instead of listening to
  // resize and scroll events or DOM mutations.
  if (this.POLL_INTERVAL) {
    monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);
  } else {
    addEvent(win, 'resize', callback, true);
    addEvent(doc, 'scroll', callback, true);
    if (this.USE_MUTATION_OBSERVER && 'MutationObserver' in win) {
      domObserver = new win.MutationObserver(callback);
      domObserver.observe(doc, {
        attributes: true,
        childList: true,
        characterData: true,
        subtree: true
      });
    }
  }

  this._monitoringDocuments.push(doc);
  this._monitoringUnsubscribes.push(function() {
    // Get the window object again. When a friendly iframe is destroyed, it
    // will be null.
    var win = doc.defaultView;

    if (win) {
      if (monitoringInterval) {
        win.clearInterval(monitoringInterval);
      }
      removeEvent(win, 'resize', callback, true);
    }

    removeEvent(doc, 'scroll', callback, true);
    if (domObserver) {
      domObserver.disconnect();
    }
  });

  // Also monitor the parent.
  var rootDoc =
    (this.root && (this.root.ownerDocument || this.root)) || document;
  if (doc != rootDoc) {
    var frame = getFrameElement(doc);
    if (frame) {
      this._monitorIntersections(frame.ownerDocument);
    }
  }
};


/**
 * Stops polling for intersection changes.
 * @param {!Document} doc
 * @private
 */
IntersectionObserver.prototype._unmonitorIntersections = function(doc) {
  var index = this._monitoringDocuments.indexOf(doc);
  if (index == -1) {
    return;
  }

  var rootDoc =
    (this.root && (this.root.ownerDocument || this.root)) || document;

  // Check if any dependent targets are still remaining.
  var hasDependentTargets =
      this._observationTargets.some(function(item) {
        var itemDoc = item.element.ownerDocument;
        // Target is in this context.
        if (itemDoc == doc) {
          return true;
        }
        // Target is nested in this context.
        while (itemDoc && itemDoc != rootDoc) {
          var frame = getFrameElement(itemDoc);
          itemDoc = frame && frame.ownerDocument;
          if (itemDoc == doc) {
            return true;
          }
        }
        return false;
      });
  if (hasDependentTargets) {
    return;
  }

  // Unsubscribe.
  var unsubscribe = this._monitoringUnsubscribes[index];
  this._monitoringDocuments.splice(index, 1);
  this._monitoringUnsubscribes.splice(index, 1);
  unsubscribe();

  // Also unmonitor the parent.
  if (doc != rootDoc) {
    var frame = getFrameElement(doc);
    if (frame) {
      this._unmonitorIntersections(frame.ownerDocument);
    }
  }
};


/**
 * Stops polling for intersection changes.
 * @param {!Document} doc
 * @private
 */
IntersectionObserver.prototype._unmonitorAllIntersections = function() {
  var unsubscribes = this._monitoringUnsubscribes.slice(0);
  this._monitoringDocuments.length = 0;
  this._monitoringUnsubscribes.length = 0;
  for (var i = 0; i < unsubscribes.length; i++) {
    unsubscribes[i]();
  }
};


/**
 * Scans each observation target for intersection changes and adds them
 * to the internal entries queue. If new entries are found, it
 * schedules the callback to be invoked.
 * @private
 */
IntersectionObserver.prototype._checkForIntersections = function() {
  if (!this.root && crossOriginUpdater && !crossOriginRect) {
    // Cross origin monitoring, but no initial data available yet.
    return;
  }

  var rootIsInDom = this._rootIsInDom();
  var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();

  this._observationTargets.forEach(function(item) {
    var target = item.element;
    var targetRect = getBoundingClientRect(target);
    var rootContainsTarget = this._rootContainsTarget(target);
    var oldEntry = item.entry;
    var intersectionRect = rootIsInDom && rootContainsTarget &&
        this._computeTargetAndRootIntersection(target, targetRect, rootRect);

    var rootBounds = null;
    if (!this._rootContainsTarget(target)) {
      rootBounds = getEmptyRect();
    } else if (!crossOriginUpdater || this.root) {
      rootBounds = rootRect;
    }

    var newEntry = item.entry = new IntersectionObserverEntry({
      time: now(),
      target: target,
      boundingClientRect: targetRect,
      rootBounds: rootBounds,
      intersectionRect: intersectionRect
    });

    if (!oldEntry) {
      this._queuedEntries.push(newEntry);
    } else if (rootIsInDom && rootContainsTarget) {
      // If the new entry intersection ratio has crossed any of the
      // thresholds, add a new entry.
      if (this._hasCrossedThreshold(oldEntry, newEntry)) {
        this._queuedEntries.push(newEntry);
      }
    } else {
      // If the root is not in the DOM or target is not contained within
      // root but the previous entry for this target had an intersection,
      // add a new record indicating removal.
      if (oldEntry && oldEntry.isIntersecting) {
        this._queuedEntries.push(newEntry);
      }
    }
  }, this);

  if (this._queuedEntries.length) {
    this._callback(this.takeRecords(), this);
  }
};


/**
 * Accepts a target and root rect computes the intersection between then
 * following the algorithm in the spec.
 * TODO(philipwalton): at this time clip-path is not considered.
 * https://w3c.github.io/IntersectionObserver/#calculate-intersection-rect-algo
 * @param {Element} target The target DOM element
 * @param {Object} targetRect The bounding rect of the target.
 * @param {Object} rootRect The bounding rect of the root after being
 *     expanded by the rootMargin value.
 * @return {?Object} The final intersection rect object or undefined if no
 *     intersection is found.
 * @private
 */
IntersectionObserver.prototype._computeTargetAndRootIntersection =
    function(target, targetRect, rootRect) {
  // If the element isn't displayed, an intersection can't happen.
  if (window.getComputedStyle(target).display == 'none') return;

  var intersectionRect = targetRect;
  var parent = getParentNode(target);
  var atRoot = false;

  while (!atRoot && parent) {
    var parentRect = null;
    var parentComputedStyle = parent.nodeType == 1 ?
        window.getComputedStyle(parent) : {};

    // If the parent isn't displayed, an intersection can't happen.
    if (parentComputedStyle.display == 'none') return null;

    if (parent == this.root || parent.nodeType == /* DOCUMENT */ 9) {
      atRoot = true;
      if (parent == this.root || parent == document) {
        if (crossOriginUpdater && !this.root) {
          if (!crossOriginRect ||
              crossOriginRect.width == 0 && crossOriginRect.height == 0) {
            // A 0-size cross-origin intersection means no-intersection.
            parent = null;
            parentRect = null;
            intersectionRect = null;
          } else {
            parentRect = crossOriginRect;
          }
        } else {
          parentRect = rootRect;
        }
      } else {
        // Check if there's a frame that can be navigated to.
        var frame = getParentNode(parent);
        var frameRect = frame && getBoundingClientRect(frame);
        var frameIntersect =
            frame &&
            this._computeTargetAndRootIntersection(frame, frameRect, rootRect);
        if (frameRect && frameIntersect) {
          parent = frame;
          parentRect = convertFromParentRect(frameRect, frameIntersect);
        } else {
          parent = null;
          intersectionRect = null;
        }
      }
    } else {
      // If the element has a non-visible overflow, and it's not the <body>
      // or <html> element, update the intersection rect.
      // Note: <body> and <html> cannot be clipped to a rect that's not also
      // the document rect, so no need to compute a new intersection.
      var doc = parent.ownerDocument;
      if (parent != doc.body &&
          parent != doc.documentElement &&
          parentComputedStyle.overflow != 'visible') {
        parentRect = getBoundingClientRect(parent);
      }
    }

    // If either of the above conditionals set a new parentRect,
    // calculate new intersection data.
    if (parentRect) {
      intersectionRect = computeRectIntersection(parentRect, intersectionRect);
    }
    if (!intersectionRect) break;
    parent = parent && getParentNode(parent);
  }
  return intersectionRect;
};


/**
 * Returns the root rect after being expanded by the rootMargin value.
 * @return {ClientRect} The expanded root rect.
 * @private
 */
IntersectionObserver.prototype._getRootRect = function() {
  var rootRect;
  if (this.root && !isDoc(this.root)) {
    rootRect = getBoundingClientRect(this.root);
  } else {
    // Use <html>/<body> instead of window since scroll bars affect size.
    var doc = isDoc(this.root) ? this.root : document;
    var html = doc.documentElement;
    var body = doc.body;
    rootRect = {
      top: 0,
      left: 0,
      right: html.clientWidth || body.clientWidth,
      width: html.clientWidth || body.clientWidth,
      bottom: html.clientHeight || body.clientHeight,
      height: html.clientHeight || body.clientHeight
    };
  }
  return this._expandRectByRootMargin(rootRect);
};


/**
 * Accepts a rect and expands it by the rootMargin value.
 * @param {DOMRect|ClientRect} rect The rect object to expand.
 * @return {ClientRect} The expanded rect.
 * @private
 */
IntersectionObserver.prototype._expandRectByRootMargin = function(rect) {
  var margins = this._rootMarginValues.map(function(margin, i) {
    return margin.unit == 'px' ? margin.value :
        margin.value * (i % 2 ? rect.width : rect.height) / 100;
  });
  var newRect = {
    top: rect.top - margins[0],
    right: rect.right + margins[1],
    bottom: rect.bottom + margins[2],
    left: rect.left - margins[3]
  };
  newRect.width = newRect.right - newRect.left;
  newRect.height = newRect.bottom - newRect.top;

  return newRect;
};


/**
 * Accepts an old and new entry and returns true if at least one of the
 * threshold values has been crossed.
 * @param {?IntersectionObserverEntry} oldEntry The previous entry for a
 *    particular target element or null if no previous entry exists.
 * @param {IntersectionObserverEntry} newEntry The current entry for a
 *    particular target element.
 * @return {boolean} Returns true if a any threshold has been crossed.
 * @private
 */
IntersectionObserver.prototype._hasCrossedThreshold =
    function(oldEntry, newEntry) {

  // To make comparing easier, an entry that has a ratio of 0
  // but does not actually intersect is given a value of -1
  var oldRatio = oldEntry && oldEntry.isIntersecting ?
      oldEntry.intersectionRatio || 0 : -1;
  var newRatio = newEntry.isIntersecting ?
      newEntry.intersectionRatio || 0 : -1;

  // Ignore unchanged ratios
  if (oldRatio === newRatio) return;

  for (var i = 0; i < this.thresholds.length; i++) {
    var threshold = this.thresholds[i];

    // Return true if an entry matches a threshold or if the new ratio
    // and the old ratio are on the opposite sides of a threshold.
    if (threshold == oldRatio || threshold == newRatio ||
        threshold < oldRatio !== threshold < newRatio) {
      return true;
    }
  }
};


/**
 * Returns whether or not the root element is an element and is in the DOM.
 * @return {boolean} True if the root element is an element and is in the DOM.
 * @private
 */
IntersectionObserver.prototype._rootIsInDom = function() {
  return !this.root || containsDeep(document, this.root);
};


/**
 * Returns whether or not the target element is a child of root.
 * @param {Element} target The target element to check.
 * @return {boolean} True if the target element is a child of root.
 * @private
 */
IntersectionObserver.prototype._rootContainsTarget = function(target) {
  var rootDoc =
    (this.root && (this.root.ownerDocument || this.root)) || document;
  return (
    containsDeep(rootDoc, target) &&
    (!this.root || rootDoc == target.ownerDocument)
  );
};


/**
 * Adds the instance to the global IntersectionObserver registry if it isn't
 * already present.
 * @private
 */
IntersectionObserver.prototype._registerInstance = function() {
  if (registry.indexOf(this) < 0) {
    registry.push(this);
  }
};


/**
 * Removes the instance from the global IntersectionObserver registry.
 * @private
 */
IntersectionObserver.prototype._unregisterInstance = function() {
  var index = registry.indexOf(this);
  if (index != -1) registry.splice(index, 1);
};


/**
 * Returns the result of the performance.now() method or null in browsers
 * that don't support the API.
 * @return {number} The elapsed time since the page was requested.
 */
function now() {
  return window.performance && performance.now && performance.now();
}


/**
 * Throttles a function and delays its execution, so it's only called at most
 * once within a given time period.
 * @param {Function} fn The function to throttle.
 * @param {number} timeout The amount of time that must pass before the
 *     function can be called again.
 * @return {Function} The throttled function.
 */
function throttle(fn, timeout) {
  var timer = null;
  return function () {
    if (!timer) {
      timer = setTimeout(function() {
        fn();
        timer = null;
      }, timeout);
    }
  };
}


/**
 * Adds an event handler to a DOM node ensuring cross-browser compatibility.
 * @param {Node} node The DOM node to add the event handler to.
 * @param {string} event The event name.
 * @param {Function} fn The event handler to add.
 * @param {boolean} opt_useCapture Optionally adds the even to the capture
 *     phase. Note: this only works in modern browsers.
 */
function addEvent(node, event, fn, opt_useCapture) {
  if (typeof node.addEventListener == 'function') {
    node.addEventListener(event, fn, opt_useCapture || false);
  }
  else if (typeof node.attachEvent == 'function') {
    node.attachEvent('on' + event, fn);
  }
}


/**
 * Removes a previously added event handler from a DOM node.
 * @param {Node} node The DOM node to remove the event handler from.
 * @param {string} event The event name.
 * @param {Function} fn The event handler to remove.
 * @param {boolean} opt_useCapture If the event handler was added with this
 *     flag set to true, it should be set to true here in order to remove it.
 */
function removeEvent(node, event, fn, opt_useCapture) {
  if (typeof node.removeEventListener == 'function') {
    node.removeEventListener(event, fn, opt_useCapture || false);
  }
  else if (typeof node.detatchEvent == 'function') {
    node.detatchEvent('on' + event, fn);
  }
}


/**
 * Returns the intersection between two rect objects.
 * @param {Object} rect1 The first rect.
 * @param {Object} rect2 The second rect.
 * @return {?Object|?ClientRect} The intersection rect or undefined if no
 *     intersection is found.
 */
function computeRectIntersection(rect1, rect2) {
  var top = Math.max(rect1.top, rect2.top);
  var bottom = Math.min(rect1.bottom, rect2.bottom);
  var left = Math.max(rect1.left, rect2.left);
  var right = Math.min(rect1.right, rect2.right);
  var width = right - left;
  var height = bottom - top;

  return (width >= 0 && height >= 0) && {
    top: top,
    bottom: bottom,
    left: left,
    right: right,
    width: width,
    height: height
  } || null;
}


/**
 * Shims the native getBoundingClientRect for compatibility with older IE.
 * @param {Element} el The element whose bounding rect to get.
 * @return {DOMRect|ClientRect} The (possibly shimmed) rect of the element.
 */
function getBoundingClientRect(el) {
  var rect;

  try {
    rect = el.getBoundingClientRect();
  } catch (err) {
    // Ignore Windows 7 IE11 "Unspecified error"
    // https://github.com/w3c/IntersectionObserver/pull/205
  }

  if (!rect) return getEmptyRect();

  // Older IE
  if (!(rect.width && rect.height)) {
    rect = {
      top: rect.top,
      right: rect.right,
      bottom: rect.bottom,
      left: rect.left,
      width: rect.right - rect.left,
      height: rect.bottom - rect.top
    };
  }
  return rect;
}


/**
 * Returns an empty rect object. An empty rect is returned when an element
 * is not in the DOM.
 * @return {ClientRect} The empty rect.
 */
function getEmptyRect() {
  return {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    width: 0,
    height: 0
  };
}


/**
 * Ensure that the result has all of the necessary fields of the DOMRect.
 * Specifically this ensures that `x` and `y` fields are set.
 *
 * @param {?DOMRect|?ClientRect} rect
 * @return {?DOMRect}
 */
function ensureDOMRect(rect) {
  // A `DOMRect` object has `x` and `y` fields.
  if (!rect || 'x' in rect) {
    return rect;
  }
  // A IE's `ClientRect` type does not have `x` and `y`. The same is the case
  // for internally calculated Rect objects. For the purposes of
  // `IntersectionObserver`, it's sufficient to simply mirror `left` and `top`
  // for these fields.
  return {
    top: rect.top,
    y: rect.top,
    bottom: rect.bottom,
    left: rect.left,
    x: rect.left,
    right: rect.right,
    width: rect.width,
    height: rect.height
  };
}


/**
 * Inverts the intersection and bounding rect from the parent (frame) BCR to
 * the local BCR space.
 * @param {DOMRect|ClientRect} parentBoundingRect The parent's bound client rect.
 * @param {DOMRect|ClientRect} parentIntersectionRect The parent's own intersection rect.
 * @return {ClientRect} The local root bounding rect for the parent's children.
 */
function convertFromParentRect(parentBoundingRect, parentIntersectionRect) {
  var top = parentIntersectionRect.top - parentBoundingRect.top;
  var left = parentIntersectionRect.left - parentBoundingRect.left;
  return {
    top: top,
    left: left,
    height: parentIntersectionRect.height,
    width: parentIntersectionRect.width,
    bottom: top + parentIntersectionRect.height,
    right: left + parentIntersectionRect.width
  };
}


/**
 * Checks to see if a parent element contains a child element (including inside
 * shadow DOM).
 * @param {Node} parent The parent element.
 * @param {Node} child The child element.
 * @return {boolean} True if the parent node contains the child node.
 */
function containsDeep(parent, child) {
  var node = child;
  while (node) {
    if (node == parent) return true;

    node = getParentNode(node);
  }
  return false;
}


/**
 * Gets the parent node of an element or its host element if the parent node
 * is a shadow root.
 * @param {Node} node The node whose parent to get.
 * @return {Node|null} The parent node or null if no parent exists.
 */
function getParentNode(node) {
  var parent = node.parentNode;

  if (node.nodeType == /* DOCUMENT */ 9 && node != document) {
    // If this node is a document node, look for the embedding frame.
    return getFrameElement(node);
  }

  // If the parent has element that is assigned through shadow root slot
  if (parent && parent.assignedSlot) {
    parent = parent.assignedSlot.parentNode
  }

  if (parent && parent.nodeType == 11 && parent.host) {
    // If the parent is a shadow root, return the host element.
    return parent.host;
  }

  return parent;
}

/**
 * Returns true if `node` is a Document.
 * @param {!Node} node
 * @returns {boolean}
 */
function isDoc(node) {
  return node && node.nodeType === 9;
}


// Exposes the constructors globally.
window.IntersectionObserver = IntersectionObserver;
window.IntersectionObserverEntry = IntersectionObserverEntry;

}());


/***/ }),

/***/ "./node_modules/js-cookie/src/js.cookie.js":
/*!*************************************************!*\
  !*** ./node_modules/js-cookie/src/js.cookie.js ***!
  \*************************************************/
/***/ (function(module, exports, __webpack_require__) {

var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_RESULT__;/*!
 * JavaScript Cookie v2.2.1
 * https://github.com/js-cookie/js-cookie
 *
 * Copyright 2006, 2015 Klaus Hartl & Fagner Brack
 * Released under the MIT license
 */
;(function (factory) {
	var registeredInModuleLoader;
	if (true) {
		!(__WEBPACK_AMD_DEFINE_FACTORY__ = (factory),
		__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
		(__WEBPACK_AMD_DEFINE_FACTORY__.call(exports, __webpack_require__, exports, module)) :
		__WEBPACK_AMD_DEFINE_FACTORY__),
		__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));
		registeredInModuleLoader = true;
	}
	if (true) {
		module.exports = factory();
		registeredInModuleLoader = true;
	}
	if (!registeredInModuleLoader) {
		var OldCookies = window.Cookies;
		var api = window.Cookies = factory();
		api.noConflict = function () {
			window.Cookies = OldCookies;
			return api;
		};
	}
}(function () {
	function extend () {
		var i = 0;
		var result = {};
		for (; i < arguments.length; i++) {
			var attributes = arguments[ i ];
			for (var key in attributes) {
				result[key] = attributes[key];
			}
		}
		return result;
	}

	function decode (s) {
		return s.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent);
	}

	function init (converter) {
		function api() {}

		function set (key, value, attributes) {
			if (typeof document === 'undefined') {
				return;
			}

			attributes = extend({
				path: '/'
			}, api.defaults, attributes);

			if (typeof attributes.expires === 'number') {
				attributes.expires = new Date(new Date() * 1 + attributes.expires * 864e+5);
			}

			// We're using "expires" because "max-age" is not supported by IE
			attributes.expires = attributes.expires ? attributes.expires.toUTCString() : '';

			try {
				var result = JSON.stringify(value);
				if (/^[\{\[]/.test(result)) {
					value = result;
				}
			} catch (e) {}

			value = converter.write ?
				converter.write(value, key) :
				encodeURIComponent(String(value))
					.replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent);

			key = encodeURIComponent(String(key))
				.replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent)
				.replace(/[\(\)]/g, escape);

			var stringifiedAttributes = '';
			for (var attributeName in attributes) {
				if (!attributes[attributeName]) {
					continue;
				}
				stringifiedAttributes += '; ' + attributeName;
				if (attributes[attributeName] === true) {
					continue;
				}

				// Considers RFC 6265 section 5.2:
				// ...
				// 3.  If the remaining unparsed-attributes contains a %x3B (";")
				//     character:
				// Consume the characters of the unparsed-attributes up to,
				// not including, the first %x3B (";") character.
				// ...
				stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];
			}

			return (document.cookie = key + '=' + value + stringifiedAttributes);
		}

		function get (key, json) {
			if (typeof document === 'undefined') {
				return;
			}

			var jar = {};
			// To prevent the for loop in the first place assign an empty array
			// in case there are no cookies at all.
			var cookies = document.cookie ? document.cookie.split('; ') : [];
			var i = 0;

			for (; i < cookies.length; i++) {
				var parts = cookies[i].split('=');
				var cookie = parts.slice(1).join('=');

				if (!json && cookie.charAt(0) === '"') {
					cookie = cookie.slice(1, -1);
				}

				try {
					var name = decode(parts[0]);
					cookie = (converter.read || converter)(cookie, name) ||
						decode(cookie);

					if (json) {
						try {
							cookie = JSON.parse(cookie);
						} catch (e) {}
					}

					jar[name] = cookie;

					if (key === name) {
						break;
					}
				} catch (e) {}
			}

			return key ? jar[key] : jar;
		}

		api.set = set;
		api.get = function (key) {
			return get(key, false /* read as raw */);
		};
		api.getJSON = function (key) {
			return get(key, true /* read as json */);
		};
		api.remove = function (key, attributes) {
			set(key, '', extend(attributes, {
				expires: -1
			}));
		};

		api.defaults = {};

		api.withConverter = init;

		return api;
	}

	return init(function () {});
}));


/***/ }),

/***/ "./node_modules/lodash/_baseTrim.js":
/*!******************************************!*\
  !*** ./node_modules/lodash/_baseTrim.js ***!
  \******************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var trimmedEndIndex = __webpack_require__(/*! ./_trimmedEndIndex */ "./node_modules/lodash/_trimmedEndIndex.js");

/** Used to match leading whitespace. */
var reTrimStart = /^\s+/;

/**
 * The base implementation of `_.trim`.
 *
 * @private
 * @param {string} string The string to trim.
 * @returns {string} Returns the trimmed string.
 */
function baseTrim(string) {
  return string
    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')
    : string;
}

module.exports = baseTrim;


/***/ }),

/***/ "./node_modules/lodash/_trimmedEndIndex.js":
/*!*************************************************!*\
  !*** ./node_modules/lodash/_trimmedEndIndex.js ***!
  \*************************************************/
/***/ (function(module) {

/** Used to match a single whitespace character. */
var reWhitespace = /\s/;

/**
 * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace
 * character of `string`.
 *
 * @private
 * @param {string} string The string to inspect.
 * @returns {number} Returns the index of the last non-whitespace character.
 */
function trimmedEndIndex(string) {
  var index = string.length;

  while (index-- && reWhitespace.test(string.charAt(index))) {}
  return index;
}

module.exports = trimmedEndIndex;


/***/ }),

/***/ "./node_modules/lodash/debounce.js":
/*!*****************************************!*\
  !*** ./node_modules/lodash/debounce.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var isObject = __webpack_require__(/*! ./isObject */ "./node_modules/lodash/isObject.js"),
    now = __webpack_require__(/*! ./now */ "./node_modules/lodash/now.js"),
    toNumber = __webpack_require__(/*! ./toNumber */ "./node_modules/lodash/toNumber.js");

/** Error message constants. */
var FUNC_ERROR_TEXT = 'Expected a function';

/* Built-in method references for those with the same name as other `lodash` methods. */
var nativeMax = Math.max,
    nativeMin = Math.min;

/**
 * Creates a debounced function that delays invoking `func` until after `wait`
 * milliseconds have elapsed since the last time the debounced function was
 * invoked. The debounced function comes with a `cancel` method to cancel
 * delayed `func` invocations and a `flush` method to immediately invoke them.
 * Provide `options` to indicate whether `func` should be invoked on the
 * leading and/or trailing edge of the `wait` timeout. The `func` is invoked
 * with the last arguments provided to the debounced function. Subsequent
 * calls to the debounced function return the result of the last `func`
 * invocation.
 *
 * **Note:** If `leading` and `trailing` options are `true`, `func` is
 * invoked on the trailing edge of the timeout only if the debounced function
 * is invoked more than once during the `wait` timeout.
 *
 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
 *
 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
 * for details over the differences between `_.debounce` and `_.throttle`.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Function
 * @param {Function} func The function to debounce.
 * @param {number} [wait=0] The number of milliseconds to delay.
 * @param {Object} [options={}] The options object.
 * @param {boolean} [options.leading=false]
 *  Specify invoking on the leading edge of the timeout.
 * @param {number} [options.maxWait]
 *  The maximum time `func` is allowed to be delayed before it's invoked.
 * @param {boolean} [options.trailing=true]
 *  Specify invoking on the trailing edge of the timeout.
 * @returns {Function} Returns the new debounced function.
 * @example
 *
 * // Avoid costly calculations while the window size is in flux.
 * jQuery(window).on('resize', _.debounce(calculateLayout, 150));
 *
 * // Invoke `sendMail` when clicked, debouncing subsequent calls.
 * jQuery(element).on('click', _.debounce(sendMail, 300, {
 *   'leading': true,
 *   'trailing': false
 * }));
 *
 * // Ensure `batchLog` is invoked once after 1 second of debounced calls.
 * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });
 * var source = new EventSource('/stream');
 * jQuery(source).on('message', debounced);
 *
 * // Cancel the trailing debounced invocation.
 * jQuery(window).on('popstate', debounced.cancel);
 */
function debounce(func, wait, options) {
  var lastArgs,
      lastThis,
      maxWait,
      result,
      timerId,
      lastCallTime,
      lastInvokeTime = 0,
      leading = false,
      maxing = false,
      trailing = true;

  if (typeof func != 'function') {
    throw new TypeError(FUNC_ERROR_TEXT);
  }
  wait = toNumber(wait) || 0;
  if (isObject(options)) {
    leading = !!options.leading;
    maxing = 'maxWait' in options;
    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
    trailing = 'trailing' in options ? !!options.trailing : trailing;
  }

  function invokeFunc(time) {
    var args = lastArgs,
        thisArg = lastThis;

    lastArgs = lastThis = undefined;
    lastInvokeTime = time;
    result = func.apply(thisArg, args);
    return result;
  }

  function leadingEdge(time) {
    // Reset any `maxWait` timer.
    lastInvokeTime = time;
    // Start the timer for the trailing edge.
    timerId = setTimeout(timerExpired, wait);
    // Invoke the leading edge.
    return leading ? invokeFunc(time) : result;
  }

  function remainingWait(time) {
    var timeSinceLastCall = time - lastCallTime,
        timeSinceLastInvoke = time - lastInvokeTime,
        timeWaiting = wait - timeSinceLastCall;

    return maxing
      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting;
  }

  function shouldInvoke(time) {
    var timeSinceLastCall = time - lastCallTime,
        timeSinceLastInvoke = time - lastInvokeTime;

    // Either this is the first call, activity has stopped and we're at the
    // trailing edge, the system time has gone backwards and we're treating
    // it as the trailing edge, or we've hit the `maxWait` limit.
    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||
      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));
  }

  function timerExpired() {
    var time = now();
    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }
    // Restart the timer.
    timerId = setTimeout(timerExpired, remainingWait(time));
  }

  function trailingEdge(time) {
    timerId = undefined;

    // Only invoke if we have `lastArgs` which means `func` has been
    // debounced at least once.
    if (trailing && lastArgs) {
      return invokeFunc(time);
    }
    lastArgs = lastThis = undefined;
    return result;
  }

  function cancel() {
    if (timerId !== undefined) {
      clearTimeout(timerId);
    }
    lastInvokeTime = 0;
    lastArgs = lastCallTime = lastThis = timerId = undefined;
  }

  function flush() {
    return timerId === undefined ? result : trailingEdge(now());
  }

  function debounced() {
    var time = now(),
        isInvoking = shouldInvoke(time);

    lastArgs = arguments;
    lastThis = this;
    lastCallTime = time;

    if (isInvoking) {
      if (timerId === undefined) {
        return leadingEdge(lastCallTime);
      }
      if (maxing) {
        // Handle invocations in a tight loop.
        clearTimeout(timerId);
        timerId = setTimeout(timerExpired, wait);
        return invokeFunc(lastCallTime);
      }
    }
    if (timerId === undefined) {
      timerId = setTimeout(timerExpired, wait);
    }
    return result;
  }
  debounced.cancel = cancel;
  debounced.flush = flush;
  return debounced;
}

module.exports = debounce;


/***/ }),

/***/ "./node_modules/lodash/isEqual.js":
/*!****************************************!*\
  !*** ./node_modules/lodash/isEqual.js ***!
  \****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var baseIsEqual = __webpack_require__(/*! ./_baseIsEqual */ "./node_modules/lodash/_baseIsEqual.js");

/**
 * Performs a deep comparison between two values to determine if they are
 * equivalent.
 *
 * **Note:** This method supports comparing arrays, array buffers, booleans,
 * date objects, error objects, maps, numbers, `Object` objects, regexes,
 * sets, strings, symbols, and typed arrays. `Object` objects are compared
 * by their own, not inherited, enumerable properties. Functions and DOM
 * nodes are compared by strict equality, i.e. `===`.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 * @example
 *
 * var object = { 'a': 1 };
 * var other = { 'a': 1 };
 *
 * _.isEqual(object, other);
 * // => true
 *
 * object === other;
 * // => false
 */
function isEqual(value, other) {
  return baseIsEqual(value, other);
}

module.exports = isEqual;


/***/ }),

/***/ "./node_modules/lodash/isSymbol.js":
/*!*****************************************!*\
  !*** ./node_modules/lodash/isSymbol.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var baseGetTag = __webpack_require__(/*! ./_baseGetTag */ "./node_modules/lodash/_baseGetTag.js"),
    isObjectLike = __webpack_require__(/*! ./isObjectLike */ "./node_modules/lodash/isObjectLike.js");

/** `Object#toString` result references. */
var symbolTag = '[object Symbol]';

/**
 * Checks if `value` is classified as a `Symbol` primitive or object.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
 * @example
 *
 * _.isSymbol(Symbol.iterator);
 * // => true
 *
 * _.isSymbol('abc');
 * // => false
 */
function isSymbol(value) {
  return typeof value == 'symbol' ||
    (isObjectLike(value) && baseGetTag(value) == symbolTag);
}

module.exports = isSymbol;


/***/ }),

/***/ "./node_modules/lodash/now.js":
/*!************************************!*\
  !*** ./node_modules/lodash/now.js ***!
  \************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var root = __webpack_require__(/*! ./_root */ "./node_modules/lodash/_root.js");

/**
 * Gets the timestamp of the number of milliseconds that have elapsed since
 * the Unix epoch (1 January 1970 00:00:00 UTC).
 *
 * @static
 * @memberOf _
 * @since 2.4.0
 * @category Date
 * @returns {number} Returns the timestamp.
 * @example
 *
 * _.defer(function(stamp) {
 *   console.log(_.now() - stamp);
 * }, _.now());
 * // => Logs the number of milliseconds it took for the deferred invocation.
 */
var now = function() {
  return root.Date.now();
};

module.exports = now;


/***/ }),

/***/ "./node_modules/lodash/throttle.js":
/*!*****************************************!*\
  !*** ./node_modules/lodash/throttle.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var debounce = __webpack_require__(/*! ./debounce */ "./node_modules/lodash/debounce.js"),
    isObject = __webpack_require__(/*! ./isObject */ "./node_modules/lodash/isObject.js");

/** Error message constants. */
var FUNC_ERROR_TEXT = 'Expected a function';

/**
 * Creates a throttled function that only invokes `func` at most once per
 * every `wait` milliseconds. The throttled function comes with a `cancel`
 * method to cancel delayed `func` invocations and a `flush` method to
 * immediately invoke them. Provide `options` to indicate whether `func`
 * should be invoked on the leading and/or trailing edge of the `wait`
 * timeout. The `func` is invoked with the last arguments provided to the
 * throttled function. Subsequent calls to the throttled function return the
 * result of the last `func` invocation.
 *
 * **Note:** If `leading` and `trailing` options are `true`, `func` is
 * invoked on the trailing edge of the timeout only if the throttled function
 * is invoked more than once during the `wait` timeout.
 *
 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
 *
 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
 * for details over the differences between `_.throttle` and `_.debounce`.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Function
 * @param {Function} func The function to throttle.
 * @param {number} [wait=0] The number of milliseconds to throttle invocations to.
 * @param {Object} [options={}] The options object.
 * @param {boolean} [options.leading=true]
 *  Specify invoking on the leading edge of the timeout.
 * @param {boolean} [options.trailing=true]
 *  Specify invoking on the trailing edge of the timeout.
 * @returns {Function} Returns the new throttled function.
 * @example
 *
 * // Avoid excessively updating the position while scrolling.
 * jQuery(window).on('scroll', _.throttle(updatePosition, 100));
 *
 * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.
 * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });
 * jQuery(element).on('click', throttled);
 *
 * // Cancel the trailing throttled invocation.
 * jQuery(window).on('popstate', throttled.cancel);
 */
function throttle(func, wait, options) {
  var leading = true,
      trailing = true;

  if (typeof func != 'function') {
    throw new TypeError(FUNC_ERROR_TEXT);
  }
  if (isObject(options)) {
    leading = 'leading' in options ? !!options.leading : leading;
    trailing = 'trailing' in options ? !!options.trailing : trailing;
  }
  return debounce(func, wait, {
    'leading': leading,
    'maxWait': wait,
    'trailing': trailing
  });
}

module.exports = throttle;


/***/ }),

/***/ "./node_modules/lodash/toNumber.js":
/*!*****************************************!*\
  !*** ./node_modules/lodash/toNumber.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var baseTrim = __webpack_require__(/*! ./_baseTrim */ "./node_modules/lodash/_baseTrim.js"),
    isObject = __webpack_require__(/*! ./isObject */ "./node_modules/lodash/isObject.js"),
    isSymbol = __webpack_require__(/*! ./isSymbol */ "./node_modules/lodash/isSymbol.js");

/** Used as references for various `Number` constants. */
var NAN = 0 / 0;

/** Used to detect bad signed hexadecimal string values. */
var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;

/** Used to detect binary string values. */
var reIsBinary = /^0b[01]+$/i;

/** Used to detect octal string values. */
var reIsOctal = /^0o[0-7]+$/i;

/** Built-in method references without a dependency on `root`. */
var freeParseInt = parseInt;

/**
 * Converts `value` to a number.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to process.
 * @returns {number} Returns the number.
 * @example
 *
 * _.toNumber(3.2);
 * // => 3.2
 *
 * _.toNumber(Number.MIN_VALUE);
 * // => 5e-324
 *
 * _.toNumber(Infinity);
 * // => Infinity
 *
 * _.toNumber('3.2');
 * // => 3.2
 */
function toNumber(value) {
  if (typeof value == 'number') {
    return value;
  }
  if (isSymbol(value)) {
    return NAN;
  }
  if (isObject(value)) {
    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;
    value = isObject(other) ? (other + '') : other;
  }
  if (typeof value != 'string') {
    return value === 0 ? value : +value;
  }
  value = baseTrim(value);
  var isBinary = reIsBinary.test(value);
  return (isBinary || reIsOctal.test(value))
    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)
    : (reIsBadHex.test(value) ? NAN : +value);
}

module.exports = toNumber;


/***/ }),

/***/ "./node_modules/screenfull/dist/screenfull.js":
/*!****************************************************!*\
  !*** ./node_modules/screenfull/dist/screenfull.js ***!
  \****************************************************/
/***/ (function(module) {

/*!
* screenfull
* v5.2.0 - 2021-11-03
* (c) Sindre Sorhus; MIT License
*/
(function () {
	'use strict';

	var document = typeof window !== 'undefined' && typeof window.document !== 'undefined' ? window.document : {};
	var isCommonjs =  true && module.exports;

	var fn = (function () {
		var val;

		var fnMap = [
			[
				'requestFullscreen',
				'exitFullscreen',
				'fullscreenElement',
				'fullscreenEnabled',
				'fullscreenchange',
				'fullscreenerror'
			],
			// New WebKit
			[
				'webkitRequestFullscreen',
				'webkitExitFullscreen',
				'webkitFullscreenElement',
				'webkitFullscreenEnabled',
				'webkitfullscreenchange',
				'webkitfullscreenerror'

			],
			// Old WebKit
			[
				'webkitRequestFullScreen',
				'webkitCancelFullScreen',
				'webkitCurrentFullScreenElement',
				'webkitCancelFullScreen',
				'webkitfullscreenchange',
				'webkitfullscreenerror'

			],
			[
				'mozRequestFullScreen',
				'mozCancelFullScreen',
				'mozFullScreenElement',
				'mozFullScreenEnabled',
				'mozfullscreenchange',
				'mozfullscreenerror'
			],
			[
				'msRequestFullscreen',
				'msExitFullscreen',
				'msFullscreenElement',
				'msFullscreenEnabled',
				'MSFullscreenChange',
				'MSFullscreenError'
			]
		];

		var i = 0;
		var l = fnMap.length;
		var ret = {};

		for (; i < l; i++) {
			val = fnMap[i];
			if (val && val[1] in document) {
				for (i = 0; i < val.length; i++) {
					ret[fnMap[0][i]] = val[i];
				}
				return ret;
			}
		}

		return false;
	})();

	var eventNameMap = {
		change: fn.fullscreenchange,
		error: fn.fullscreenerror
	};

	var screenfull = {
		request: function (element, options) {
			return new Promise(function (resolve, reject) {
				var onFullScreenEntered = function () {
					this.off('change', onFullScreenEntered);
					resolve();
				}.bind(this);

				this.on('change', onFullScreenEntered);

				element = element || document.documentElement;

				var returnPromise = element[fn.requestFullscreen](options);

				if (returnPromise instanceof Promise) {
					returnPromise.then(onFullScreenEntered).catch(reject);
				}
			}.bind(this));
		},
		exit: function () {
			return new Promise(function (resolve, reject) {
				if (!this.isFullscreen) {
					resolve();
					return;
				}

				var onFullScreenExit = function () {
					this.off('change', onFullScreenExit);
					resolve();
				}.bind(this);

				this.on('change', onFullScreenExit);

				var returnPromise = document[fn.exitFullscreen]();

				if (returnPromise instanceof Promise) {
					returnPromise.then(onFullScreenExit).catch(reject);
				}
			}.bind(this));
		},
		toggle: function (element, options) {
			return this.isFullscreen ? this.exit() : this.request(element, options);
		},
		onchange: function (callback) {
			this.on('change', callback);
		},
		onerror: function (callback) {
			this.on('error', callback);
		},
		on: function (event, callback) {
			var eventName = eventNameMap[event];
			if (eventName) {
				document.addEventListener(eventName, callback, false);
			}
		},
		off: function (event, callback) {
			var eventName = eventNameMap[event];
			if (eventName) {
				document.removeEventListener(eventName, callback, false);
			}
		},
		raw: fn
	};

	if (!fn) {
		if (isCommonjs) {
			module.exports = {isEnabled: false};
		} else {
			window.screenfull = {isEnabled: false};
		}

		return;
	}

	Object.defineProperties(screenfull, {
		isFullscreen: {
			get: function () {
				return Boolean(document[fn.fullscreenElement]);
			}
		},
		element: {
			enumerable: true,
			get: function () {
				return document[fn.fullscreenElement];
			}
		},
		isEnabled: {
			enumerable: true,
			get: function () {
				// Coerce to boolean in case of old WebKit
				return Boolean(document[fn.fullscreenEnabled]);
			}
		}
	});

	if (isCommonjs) {
		module.exports = screenfull;
	} else {
		window.screenfull = screenfull;
	}
})();


/***/ })

}]);