(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1844],{64335:function(ge,H,s){"use strict";var te=s(67294),k=(0,te.createContext)({});H.Z=k},21349:function(ge,H,s){"use strict";var te=s(84305),k=s(69224),w=s(53645),Z=s.n(w),I=s(67294),re=s(94184),ne=s.n(re),ae=s(64335),oe=function(D){var T=(0,I.useContext)(ae.Z),ie=D.children,U=D.contentWidth,M=D.className,X=D.style,Y=(0,I.useContext)(k.ZP.ConfigContext),le=Y.getPrefixCls,z=D.prefixCls||le("pro"),V=U||T.contentWidth,ce="".concat(z,"-grid-content");return I.createElement("div",{className:ne()(ce,M,{wide:V==="Fixed"}),style:X},I.createElement("div",{className:"".concat(z,"-grid-content-children")},ie))};H.Z=oe},8292:function(ge,H,s){"use strict";s.d(H,{ZP:function(){return $t}});var te=s(65056),k=s(70883),w=s(22122),Z=s(96156),I=s(6610),re=s(5991),ne=s(10379),ae=s(54070),oe=s(90484),i=s(67294),D=s(94184),T=s.n(D),ie=s(10366),U=s(48717),M=s(65632),X=s(85061),Y=s(96523);function le(n){var e,t=function(o){return function(){e=null,n.apply(void 0,(0,X.Z)(o))}},r=function(){if(e==null){for(var o=arguments.length,f=new Array(o),l=0;l<o;l++)f[l]=arguments[l];e=(0,Y.Z)(t(f))}};return r.cancel=function(){return Y.Z.cancel(e)},r}function z(){return function(e,t,r){var a=r.value,o=!1;return{configurable:!0,get:function(){if(o||this===e.prototype||this.hasOwnProperty(t))return a;var l=le(a.bind(this));return o=!0,Object.defineProperty(this,t,{value:l,configurable:!0,writable:!0}),o=!1,l}}}}var V=s(73935);function ce(n,e,t,r){var a=V.unstable_batchedUpdates?function(f){V.unstable_batchedUpdates(t,f)}:t;return n.addEventListener&&n.addEventListener(e,a,r),{remove:function(){n.removeEventListener&&n.removeEventListener(e,a)}}}function J(n){return n!==window?n.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function he(n,e,t){if(t!==void 0&&e.top>n.top-t)return t+e.top}function pe(n,e,t){if(t!==void 0&&e.bottom<n.bottom+t){var r=window.innerHeight-e.bottom;return t+r}}var be=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"],F=[];function nr(){return F}function ye(n,e){if(!!n){var t=F.find(function(r){return r.target===n});t?t.affixList.push(e):(t={target:n,affixList:[e],eventHandlers:{}},F.push(t),be.forEach(function(r){t.eventHandlers[r]=ce(n,r,function(){t.affixList.forEach(function(a){a.lazyUpdatePosition()})})}))}}function Pe(n){var e=F.find(function(t){var r=t.affixList.some(function(a){return a===n});return r&&(t.affixList=t.affixList.filter(function(a){return a!==n})),r});e&&e.affixList.length===0&&(F=F.filter(function(t){return t!==e}),be.forEach(function(t){var r=e.eventHandlers[t];r&&r.remove&&r.remove()}))}var Ce=function(n,e,t,r){var a=arguments.length,o=a<3?e:r===null?r=Object.getOwnPropertyDescriptor(e,t):r,f;if((typeof Reflect=="undefined"?"undefined":(0,oe.Z)(Reflect))==="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(n,e,t,r);else for(var l=n.length-1;l>=0;l--)(f=n[l])&&(o=(a<3?f(o):a>3?f(e,t,o):f(e,t))||o);return a>3&&o&&Object.defineProperty(e,t,o),o};function ze(){return typeof window!="undefined"?window:null}var L;(function(n){n[n.None=0]="None",n[n.Prepare=1]="Prepare"})(L||(L={}));var Q=function(n){(0,ne.Z)(t,n);var e=(0,ae.Z)(t);function t(){var r;return(0,I.Z)(this,t),r=e.apply(this,arguments),r.state={status:L.None,lastAffix:!1,prevTarget:null},r.getOffsetTop=function(){var a=r.props.offsetBottom,o=r.props.offsetTop;return a===void 0&&o===void 0&&(o=0),o},r.getOffsetBottom=function(){return r.props.offsetBottom},r.savePlaceholderNode=function(a){r.placeholderNode=a},r.saveFixedNode=function(a){r.fixedNode=a},r.measure=function(){var a=r.state,o=a.status,f=a.lastAffix,l=r.props.onChange,u=r.getTargetFunc();if(!(o!==L.Prepare||!r.fixedNode||!r.placeholderNode||!u)){var g=r.getOffsetTop(),v=r.getOffsetBottom(),m=u();if(!!m){var c={status:L.None},d=J(m),h=J(r.placeholderNode),y=he(h,d,g),p=pe(h,d,v);y!==void 0?(c.affixStyle={position:"fixed",top:y,width:h.width,height:h.height},c.placeholderStyle={width:h.width,height:h.height}):p!==void 0&&(c.affixStyle={position:"fixed",bottom:p,width:h.width,height:h.height},c.placeholderStyle={width:h.width,height:h.height}),c.lastAffix=!!c.affixStyle,l&&f!==c.lastAffix&&l(c.lastAffix),r.setState(c)}}},r.prepareMeasure=function(){if(r.setState({status:L.Prepare,affixStyle:void 0,placeholderStyle:void 0}),!1)var a},r.render=function(){var a=r.context.getPrefixCls,o=r.state,f=o.affixStyle,l=o.placeholderStyle,u=r.props,g=u.prefixCls,v=u.children,m=T()((0,Z.Z)({},a("affix",g),!!f)),c=(0,ie.Z)(r.props,["prefixCls","offsetTop","offsetBottom","target","onChange"]);return i.createElement(U.Z,{onResize:function(){r.updatePosition()}},i.createElement("div",(0,w.Z)({},c,{ref:r.savePlaceholderNode}),f&&i.createElement("div",{style:l,"aria-hidden":"true"}),i.createElement("div",{className:m,ref:r.saveFixedNode,style:f},i.createElement(U.Z,{onResize:function(){r.updatePosition()}},v))))},r}return(0,re.Z)(t,[{key:"getTargetFunc",value:function(){var a=this.context.getTargetContainer,o=this.props.target;return o!==void 0?o:a||ze}},{key:"componentDidMount",value:function(){var a=this,o=this.getTargetFunc();o&&(this.timeout=setTimeout(function(){ye(o(),a),a.updatePosition()}))}},{key:"componentDidUpdate",value:function(a){var o=this.state.prevTarget,f=this.getTargetFunc(),l=null;f&&(l=f()||null),o!==l&&(Pe(this),l&&(ye(l,this),this.updatePosition()),this.setState({prevTarget:l})),(a.offsetTop!==this.props.offsetTop||a.offsetBottom!==this.props.offsetBottom)&&this.updatePosition(),this.measure()}},{key:"componentWillUnmount",value:function(){clearTimeout(this.timeout),Pe(this),this.updatePosition.cancel(),this.lazyUpdatePosition.cancel()}},{key:"updatePosition",value:function(){this.prepareMeasure()}},{key:"lazyUpdatePosition",value:function(){var a=this.getTargetFunc(),o=this.state.affixStyle;if(a&&o){var f=this.getOffsetTop(),l=this.getOffsetBottom(),u=a();if(u&&this.placeholderNode){var g=J(u),v=J(this.placeholderNode),m=he(v,g,f),c=pe(v,g,l);if(m!==void 0&&o.top===m||c!==void 0&&o.bottom===c)return}}this.prepareMeasure()}}]),t}(i.Component);Q.contextType=M.E_,Ce([z()],Q.prototype,"updatePosition",null),Ce([z()],Q.prototype,"lazyUpdatePosition",null);var $e=Q,ar=s(84305),se=s(69224),or=s(59903),ir=s(81262),lr=s(30887),cr=s(59250),sr=s(94233),Ke=s(28481),Ge=s(6700),ke=s(93488),Xe=s(37419),Ye=s(57254),Ve=s(81555),Je=function(n,e){var t={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(n);a<r.length;a++)e.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(n,r[a])&&(t[r[a]]=n[r[a]]);return t},xe=function(e){var t=e.prefixCls,r=e.separator,a=r===void 0?"/":r,o=e.children,f=e.overlay,l=e.dropdownProps,u=Je(e,["prefixCls","separator","children","overlay","dropdownProps"]),g=i.useContext(M.E_),v=g.getPrefixCls,m=v("breadcrumb",t),c=function(y){return f?i.createElement(Ve.Z,(0,w.Z)({overlay:f,placement:"bottomCenter"},l),i.createElement("span",{className:"".concat(m,"-overlay-link")},y,i.createElement(Ye.Z,null))):y},d;return"href"in u?d=i.createElement("a",(0,w.Z)({className:"".concat(m,"-link")},u),o):d=i.createElement("span",(0,w.Z)({className:"".concat(m,"-link")},u),o),d=c(d),o?i.createElement("span",null,d,a&&i.createElement("span",{className:"".concat(m,"-separator")},a)):null};xe.__ANT_BREADCRUMB_ITEM=!0;var Ee=xe,Oe=function(e){var t=e.children,r=i.useContext(M.E_),a=r.getPrefixCls,o=a("breadcrumb");return i.createElement("span",{className:"".concat(o,"-separator")},t||"/")};Oe.__ANT_BREADCRUMB_SEPARATOR=!0;var Qe=Oe,Ne=s(99210),qe=s(21687),_e=s(96159),et=function(n,e){var t={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(n);a<r.length;a++)e.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(n,r[a])&&(t[r[a]]=n[r[a]]);return t};function tt(n,e){if(!n.breadcrumbName)return null;var t=Object.keys(e).join("|"),r=n.breadcrumbName.replace(new RegExp(":(".concat(t,")"),"g"),function(a,o){return e[o]||a});return r}function rt(n,e,t,r){var a=t.indexOf(n)===t.length-1,o=tt(n,e);return a?i.createElement("span",null,o):i.createElement("a",{href:"#/".concat(r.join("/"))},o)}var Te=function(e,t){return e=(e||"").replace(/^\//,""),Object.keys(t).forEach(function(r){e=e.replace(":".concat(r),t[r])}),e},nt=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",r=arguments.length>2?arguments[2]:void 0,a=(0,X.Z)(e),o=Te(t,r);return o&&a.push(o),a},fe=function(e){var t=e.prefixCls,r=e.separator,a=r===void 0?"/":r,o=e.style,f=e.className,l=e.routes,u=e.children,g=e.itemRender,v=g===void 0?rt:g,m=e.params,c=m===void 0?{}:m,d=et(e,["prefixCls","separator","style","className","routes","children","itemRender","params"]),h=i.useContext(M.E_),y=h.getPrefixCls,p=h.direction,b,N=y("breadcrumb",t);if(l&&l.length>0){var x=[];b=l.map(function(P){var E=Te(P.path,c);E&&x.push(E);var O;return P.children&&P.children.length&&(O=i.createElement(Ne.Z,null,P.children.map(function(S){return i.createElement(Ne.Z.Item,{key:S.path||S.breadcrumbName},v(S,c,l,nt(x,S.path,c)))}))),i.createElement(Ee,{overlay:O,separator:a,key:E||P.breadcrumbName},v(P,c,l,x))})}else u&&(b=(0,Xe.Z)(u).map(function(P,E){return P&&((0,qe.Z)(P.type&&(P.type.__ANT_BREADCRUMB_ITEM===!0||P.type.__ANT_BREADCRUMB_SEPARATOR===!0),"Breadcrumb","Only accepts Breadcrumb.Item and Breadcrumb.Separator as it's children"),(0,_e.Tm)(P,{separator:a,key:E}))}));var C=T()(N,(0,Z.Z)({},"".concat(N,"-rtl"),p==="rtl"),f);return i.createElement("div",(0,w.Z)({className:C,style:o},d),b)};fe.Item=Ee,fe.Separator=Qe;var at=fe,ot=at,it=s(51890),lt=s(34952),ct=s(42051),st=function(e,t,r){return!t||!r?null:i.createElement(ct.Z,{componentName:"PageHeader"},function(a){var o=a.back;return i.createElement("div",{className:"".concat(e,"-back")},i.createElement(lt.Z,{onClick:function(l){r==null||r(l)},className:"".concat(e,"-back-button"),"aria-label":o},t))})},ft=function(e){return i.createElement(ot,e)},ut=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"ltr";return e.backIcon!==void 0?e.backIcon:t==="rtl"?i.createElement(ke.Z,null):i.createElement(Ge.Z,null)},dt=function(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"ltr",a=t.title,o=t.avatar,f=t.subTitle,l=t.tags,u=t.extra,g=t.onBack,v="".concat(e,"-heading"),m=a||f||l||u;if(!m)return null;var c=ut(t,r),d=st(e,c,g),h=d||o||m;return i.createElement("div",{className:v},h&&i.createElement("div",{className:"".concat(v,"-left")},d,o&&i.createElement(it.C,o),a&&i.createElement("span",{className:"".concat(v,"-title"),title:typeof a=="string"?a:void 0},a),f&&i.createElement("span",{className:"".concat(v,"-sub-title"),title:typeof f=="string"?f:void 0},f),l&&i.createElement("span",{className:"".concat(v,"-tags")},l)),u&&i.createElement("span",{className:"".concat(v,"-extra")},u))},vt=function(e,t){return t?i.createElement("div",{className:"".concat(e,"-footer")},t):null},mt=function(e,t){return i.createElement("div",{className:"".concat(e,"-content")},t)},gt=function(e){var t=i.useState(!1),r=(0,Ke.Z)(t,2),a=r[0],o=r[1],f=function(u){var g=u.width;o(g<768)};return i.createElement(M.C,null,function(l){var u,g=l.getPrefixCls,v=l.pageHeader,m=l.direction,c=e.prefixCls,d=e.style,h=e.footer,y=e.children,p=e.breadcrumb,b=e.breadcrumbRender,N=e.className,x=!0;"ghost"in e?x=e.ghost:v&&"ghost"in v&&(x=v.ghost);var C=g("page-header",c),P=function(){var W;return((W=p)===null||W===void 0?void 0:W.routes)?ft(p):null},E=P(),O=p&&"props"in p,S=(b==null?void 0:b(e,E))||E,$=O?p:S,ee=T()(C,N,(u={"has-breadcrumb":!!$,"has-footer":!!h},(0,Z.Z)(u,"".concat(C,"-ghost"),x),(0,Z.Z)(u,"".concat(C,"-rtl"),m==="rtl"),(0,Z.Z)(u,"".concat(C,"-compact"),a),u));return i.createElement(U.Z,{onResize:f},i.createElement("div",{className:ee,style:d},$,dt(C,e,m),y&&mt(C,y),vt(C,h)))})},ht=gt,fr=s(18106),Re=s(51752),ue=s(64335),pt=s(21349),bt=s(97435),ur=s(56264),yt=["children","className","extra","style","renderContent"];function de(){return de=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},de.apply(this,arguments)}function Se(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),t.push.apply(t,r)}return t}function q(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Se(Object(t),!0).forEach(function(r){Pt(n,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):Se(Object(t)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(t,r))})}return n}function Pt(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Ct(n,e){if(n==null)return{};var t=xt(n,e),r,a;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(a=0;a<o.length;a++)r=o[a],!(e.indexOf(r)>=0)&&(!Object.prototype.propertyIsEnumerable.call(n,r)||(t[r]=n[r]))}return t}function xt(n,e){if(n==null)return{};var t={},r=Object.keys(n),a,o;for(o=0;o<r.length;o++)a=r[o],!(e.indexOf(a)>=0)&&(t[a]=n[a]);return t}var Et=function(e){var t=e.children,r=e.className,a=e.extra,o=e.style,f=e.renderContent,l=Ct(e,yt),u=(0,i.useContext)(se.ZP.ConfigContext),g=u.getPrefixCls,v=e.prefixCls||g("pro"),m="".concat(v,"-footer-bar"),c=(0,i.useContext)(ue.Z),d=(0,i.useMemo)(function(){var y=c.hasSiderMenu,p=c.isMobile,b=c.siderWidth;if(!!y)return b?p?"100%":"calc(100% - ".concat(b,"px)"):"100%"},[c.collapsed,c.hasSiderMenu,c.isMobile,c.siderWidth]),h=i.createElement(i.Fragment,null,i.createElement("div",{className:"".concat(m,"-left")},a),i.createElement("div",{className:"".concat(m,"-right")},t));return(0,i.useEffect)(function(){return!c||!(c==null?void 0:c.setHasFooterToolbar)?function(){}:(c==null||c.setHasFooterToolbar(!0),function(){var y;c==null||(y=c.setHasFooterToolbar)===null||y===void 0||y.call(c,!1)})},[]),i.createElement("div",de({className:T()(r,"".concat(m)),style:q({width:d},o)},(0,bt.Z)(l,["prefixCls"])),f?f(q(q(q({},e),c),{},{leftWidth:d}),h):h)},Ot=Et,dr=s(12395),Nt=s(83832);function Be(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),t.push.apply(t,r)}return t}function je(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Be(Object(t),!0).forEach(function(r){Tt(n,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):Be(Object(t)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(t,r))})}return n}function Tt(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Rt(n,e){return wt(n)||jt(n,e)||Bt(n,e)||St()}function St(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Bt(n,e){if(!!n){if(typeof n=="string")return we(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);if(t==="Object"&&n.constructor&&(t=n.constructor.name),t==="Map"||t==="Set")return Array.from(n);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return we(n,e)}}function we(n,e){(e==null||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function jt(n,e){var t=n==null?null:typeof Symbol!="undefined"&&n[Symbol.iterator]||n["@@iterator"];if(t!=null){var r=[],a=!0,o=!1,f,l;try{for(t=t.call(n);!(a=(f=t.next()).done)&&(r.push(f.value),!(e&&r.length===e));a=!0);}catch(u){o=!0,l=u}finally{try{!a&&t.return!=null&&t.return()}finally{if(o)throw l}}return r}}function wt(n){if(Array.isArray(n))return n}var Dt=function(e){if(!e)return 1;var t=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/t},Mt=function(e){var t=e.children,r=e.style,a=e.className,o=e.markStyle,f=e.markClassName,l=e.zIndex,u=l===void 0?9:l,g=e.gapX,v=g===void 0?212:g,m=e.gapY,c=m===void 0?222:m,d=e.width,h=d===void 0?120:d,y=e.height,p=y===void 0?64:y,b=e.rotate,N=b===void 0?-22:b,x=e.image,C=e.content,P=e.offsetLeft,E=e.offsetTop,O=e.fontStyle,S=O===void 0?"normal":O,$=e.fontWeight,ee=$===void 0?"normal":$,me=e.fontColor,W=me===void 0?"rgba(0,0,0,.15)":me,Ae=e.fontSize,Ze=Ae===void 0?16:Ae,Ie=e.fontFamily,Fe=Ie===void 0?"sans-serif":Ie,Kt=e.prefixCls,Gt=(0,i.useContext)(se.ZP.ConfigContext),kt=Gt.getPrefixCls,Le=kt("pro-layout-watermark",Kt),Xt=T()("".concat(Le,"-wrapper"),a),Yt=T()(Le,f),Vt=(0,i.useState)(""),We=Rt(Vt,2),Jt=We[0],He=We[1];return(0,i.useEffect)(function(){var K=document.createElement("canvas"),j=K.getContext("2d"),A=Dt(j),Qt="".concat((v+h)*A,"px"),qt="".concat((c+p)*A,"px"),_t=P||v/2,er=E||c/2;if(K.setAttribute("width",Qt),K.setAttribute("height",qt),j){j.translate(_t*A,er*A),j.rotate(Math.PI/180*Number(N));var tr=h*A,Ue=p*A;if(x){var G=new Image;G.crossOrigin="anonymous",G.referrerPolicy="no-referrer",G.src=x,G.onload=function(){j.drawImage(G,0,0,tr,Ue),He(K.toDataURL())}}else if(C){var rr=Number(Ze)*A;j.font="".concat(S," normal ").concat(ee," ").concat(rr,"px/").concat(Ue,"px ").concat(Fe),j.fillStyle=W,j.fillText(C,0,0),He(K.toDataURL())}}else console.error("\u5F53\u524D\u73AF\u5883\u4E0D\u652F\u6301Canvas")},[v,c,P,E,N,S,ee,h,p,Fe,W,x,C,Ze]),i.createElement("div",{style:je({position:"relative"},r),className:Xt},t,i.createElement("div",{className:Yt,style:je({zIndex:u,position:"absolute",left:0,top:0,width:"100%",height:"100%",backgroundSize:"".concat(v+h,"px"),pointerEvents:"none",backgroundRepeat:"repeat",backgroundImage:"url('".concat(Jt,"')")},o)}))},At=Mt,Zt=["title","content","pageHeaderRender","header","prefixedClassName","extraContent","style","prefixCls","breadcrumbRender"],It=["children","loading","className","style","footer","affixProps","ghost","fixedHeader"];function De(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),t.push.apply(t,r)}return t}function R(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?De(Object(t),!0).forEach(function(r){ve(n,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):De(Object(t)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(t,r))})}return n}function ve(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Me(n,e){if(n==null)return{};var t=Ft(n,e),r,a;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(a=0;a<o.length;a++)r=o[a],!(e.indexOf(r)>=0)&&(!Object.prototype.propertyIsEnumerable.call(n,r)||(t[r]=n[r]))}return t}function Ft(n,e){if(n==null)return{};var t={},r=Object.keys(n),a,o;for(o=0;o<r.length;o++)a=r[o],!(e.indexOf(a)>=0)&&(t[a]=n[a]);return t}function B(){return B=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},B.apply(this,arguments)}function _(n){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_=function(t){return typeof t}:_=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(n)}function Lt(n){return _(n)==="object"?n:{spinning:n}}var Wt=function(e){var t=e.tabList,r=e.tabActiveKey,a=e.onTabChange,o=e.tabBarExtraContent,f=e.tabProps,l=e.prefixedClassName;return t&&t.length||o?i.createElement(Re.Z,B({className:"".concat(l,"-tabs"),activeKey:r,onChange:function(g){a&&a(g)},tabBarExtraContent:o},f),t==null?void 0:t.map(function(u,g){return i.createElement(Re.Z.TabPane,B({},u,{tab:u.tab,key:u.key||g}))})):null},Ht=function(e,t,r){return!e&&!t?null:i.createElement("div",{className:"".concat(r,"-detail")},i.createElement("div",{className:"".concat(r,"-main")},i.createElement("div",{className:"".concat(r,"-row")},e&&i.createElement("div",{className:"".concat(r,"-content")},e),t&&i.createElement("div",{className:"".concat(r,"-extraContent")},t))))},vr=function(e){var t=useContext(RouteContext);return React.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center"}},React.createElement(_Breadcrumb,B({},t==null?void 0:t.breadcrumb,t==null?void 0:t.breadcrumbProps,e)))},Ut=function(e){var t,r=(0,i.useContext)(ue.Z),a=e.title,o=e.content,f=e.pageHeaderRender,l=e.header,u=e.prefixedClassName,g=e.extraContent,v=e.style,m=e.prefixCls,c=e.breadcrumbRender,d=Me(e,Zt),h=(0,i.useMemo)(function(){if(!!c)return c},[c]);if(f===!1)return null;if(f)return i.createElement(i.Fragment,null," ",f(R(R({},e),r)));var y=a;!a&&a!==!1&&(y=r.title);var p=R(R(R({},r),{},{title:y},d),{},{footer:Wt(R(R({},d),{},{breadcrumbRender:c,prefixedClassName:u}))},l),b=p.breadcrumb,N=(!b||!(b==null?void 0:b.itemRender)&&!(b==null||(t=b.routes)===null||t===void 0?void 0:t.length))&&!c;return["title","subTitle","extra","tags","footer","avatar","backIcon"].every(function(x){return!p[x]})&&N&&!o&&!g?null:i.createElement("div",{className:"".concat(u,"-warp")},i.createElement(ht,B({},p,{breadcrumb:c===!1?void 0:R(R({},p.breadcrumb),r.breadcrumbProps),breadcrumbRender:h,prefixCls:m}),(l==null?void 0:l.children)||Ht(o,g,u)))},zt=function(e){var t,r=e.children,a=e.loading,o=a===void 0?!1:a,f=e.className,l=e.style,u=e.footer,g=e.affixProps,v=e.ghost,m=e.fixedHeader,c=Me(e,It),d=(0,i.useContext)(ue.Z),h=(0,i.useContext)(se.ZP.ConfigContext),y=h.getPrefixCls,p=e.prefixCls||y("pro"),b="".concat(p,"-page-container"),N=T()(b,f,(t={},ve(t,"".concat(p,"-page-container-ghost"),v),ve(t,"".concat(p,"-page-container-with-footer"),u),t)),x=(0,i.useMemo)(function(){return r?i.createElement(i.Fragment,null,i.createElement("div",{className:"".concat(b,"-children-content")},r),d.hasFooterToolbar&&i.createElement("div",{style:{height:48,marginTop:24}})):null},[r,b,d.hasFooterToolbar]),C=i.createElement(Ut,B({},c,{ghost:v,prefixCls:void 0,prefixedClassName:b})),P=(0,i.useMemo)(function(){if(i.isValidElement(o))return o;if(typeof o=="boolean"&&!o)return null;var O=Lt(o);return i.createElement(Nt.Z,O)},[o]),E=(0,i.useMemo)(function(){var O=P||x;return e.waterMarkProps||d.waterMarkProps?i.createElement(At,e.waterMarkProps||d.waterMarkProps,O):O},[e.waterMarkProps,d.waterMarkProps,P,x]);return i.createElement("div",{style:l,className:N},m&&C?i.createElement($e,B({offsetTop:d.hasHeader&&d.fixedHeader?d.headerHeight:0},g),C):C,E&&i.createElement(pt.Z,null,E),u&&i.createElement(Ot,{prefixCls:p},u))},$t=zt},56264:function(){},53645:function(){},12395:function(){},70883:function(){},81262:function(){},59903:function(){}}]);
