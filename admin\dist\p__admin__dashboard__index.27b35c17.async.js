(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[55],{22232:function(V){V.exports={activitiesList:"activitiesList___1e8Gq",username:"username___1T1dn",event:"event___1rbHZ",pageHeaderContent:"pageHeaderContent___3Dnjj",avatar:"avatar___M_TyC",content:"content___21tcu",contentTitle:"contentTitle___13ZLH",extraContent:"extraContent___2llzF",statItem:"statItem___WuGIN",members:"members___1XBXw",member:"member___23KdI",projectList:"projectList___1SFK3",cardTitle:"cardTitle___3Gbwz",projectGrid:"projectGrid___1Ai9L",projectItemContent:"projectItemContent___1wjHZ",datetime:"datetime___2nBL9",activeCard:"activeCard___1wJ0e"}},57719:function(){},54344:function(V,Q,n){"use strict";n.r(Q),n.d(Q,{default:function(){return Zt}});var Vt=n(13062),pt=n(71230),Qt=n(89032),gt=n(15746),kt=n(65056),qt=n(57719),_t=n(13254),te=n(20228),ee=n(14781),ae=n(6999),k=n(85061),j=n(22122),f=n(96156),q=n(28481),vt=n(90484),i=n(67294),ht=n(94184),w=n.n(ht),ft=n(11382),yt=n(25378),_=n(24308),K=n(65632),xt=n(19866),Ct=n(92820),bt=n(21584),jt=n(96159),tt=function(r,o){var l={};for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&o.indexOf(e)<0&&(l[e]=r[e]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var t=0,e=Object.getOwnPropertySymbols(r);t<e.length;t++)o.indexOf(e[t])<0&&Object.prototype.propertyIsEnumerable.call(r,e[t])&&(l[e[t]]=r[e[t]]);return l},At=function(o){var l=o.prefixCls,e=o.className,t=o.avatar,c=o.title,A=o.description,S=tt(o,["prefixCls","className","avatar","title","description"]),L=i.useContext(K.E_),I=L.getPrefixCls,g=I("list",l),P=w()("".concat(g,"-item-meta"),e),N=i.createElement("div",{className:"".concat(g,"-item-meta-content")},c&&i.createElement("h4",{className:"".concat(g,"-item-meta-title")},c),A&&i.createElement("div",{className:"".concat(g,"-item-meta-description")},A));return i.createElement("div",(0,j.Z)({},S,{className:P}),t&&i.createElement("div",{className:"".concat(g,"-item-meta-avatar")},t),(c||A)&&N)},et=function(o){var l=o.prefixCls,e=o.children,t=o.actions,c=o.extra,A=o.className,S=o.colStyle,L=tt(o,["prefixCls","children","actions","extra","className","colStyle"]),I=i.useContext(J),g=I.grid,P=I.itemLayout,N=i.useContext(K.E_),p=N.getPrefixCls,O=function(){var h;return i.Children.forEach(e,function(H){typeof H=="string"&&(h=!0)}),h&&i.Children.count(e)>1},E=function(){return P==="vertical"?!!c:!O()},x=p("list",l),z=t&&t.length>0&&i.createElement("ul",{className:"".concat(x,"-item-action"),key:"actions"},t.map(function(B,h){return i.createElement("li",{key:"".concat(x,"-item-action-").concat(h)},B,h!==t.length-1&&i.createElement("em",{className:"".concat(x,"-item-action-split")}))})),F=g?"div":"li",M=i.createElement(F,(0,j.Z)({},L,{className:w()("".concat(x,"-item"),(0,f.Z)({},"".concat(x,"-item-no-flex"),!E()),A)}),P==="vertical"&&c?[i.createElement("div",{className:"".concat(x,"-item-main"),key:"content"},e,z),i.createElement("div",{className:"".concat(x,"-item-extra"),key:"extra"},c)]:[e,z,(0,jt.Tm)(c,{key:"extra"})]);return g?i.createElement(bt.Z,{flex:1,style:S},M):M};et.Meta=At;var Pt=et,St=function(r,o){var l={};for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&o.indexOf(e)<0&&(l[e]=r[e]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var t=0,e=Object.getOwnPropertySymbols(r);t<e.length;t++)o.indexOf(e[t])<0&&Object.prototype.propertyIsEnumerable.call(r,e[t])&&(l[e[t]]=r[e[t]]);return l},J=i.createContext({}),re=J.Consumer;function at(r){var o,l=r.pagination,e=l===void 0?!1:l,t=r.prefixCls,c=r.bordered,A=c===void 0?!1:c,S=r.split,L=S===void 0?!0:S,I=r.className,g=r.children,P=r.itemLayout,N=r.loadMore,p=r.grid,O=r.dataSource,E=O===void 0?[]:O,x=r.size,z=r.header,F=r.footer,M=r.loading,B=M===void 0?!1:M,h=r.rowKey,H=r.renderItem,rt=r.locale,Lt=St(r,["pagination","prefixCls","bordered","split","className","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]),nt=e&&(0,vt.Z)(e)==="object"?e:{},zt=i.useState(nt.defaultCurrent||1),it=(0,q.Z)(zt,2),Ft=it[0],Mt=it[1],Bt=i.useState(nt.defaultPageSize||10),ot=(0,q.Z)(Bt,2),Dt=ot[0],Ot=ot[1],$=i.useContext(K.E_),Ht=$.getPrefixCls,Rt=$.renderEmpty,Gt=$.direction,Tt={current:1,total:0},st={},ct=function(d){return function(b,C){Mt(b),Ot(C),e&&e[d]&&e[d](b,C)}},wt=ct("onChange"),Kt=ct("onShowSizeChange"),Jt=function(d,b){if(!H)return null;var C;return typeof h=="function"?C=h(d):typeof h=="string"?C=d[h]:C=d.key,C||(C="list-item-".concat(b)),st[b]=C,H(d,b)},Wt=function(){return!!(N||e||F)},$t=function(d,b){return i.createElement("div",{className:"".concat(d,"-empty-text")},rt&&rt.emptyText||b("List"))},m=Ht("list",t),Z=B;typeof Z=="boolean"&&(Z={spinning:Z});var X=Z&&Z.spinning,R="";switch(x){case"large":R="lg";break;case"small":R="sm";break;default:break}var Xt=w()(m,(o={},(0,f.Z)(o,"".concat(m,"-vertical"),P==="vertical"),(0,f.Z)(o,"".concat(m,"-").concat(R),R),(0,f.Z)(o,"".concat(m,"-split"),L),(0,f.Z)(o,"".concat(m,"-bordered"),A),(0,f.Z)(o,"".concat(m,"-loading"),X),(0,f.Z)(o,"".concat(m,"-grid"),!!p),(0,f.Z)(o,"".concat(m,"-something-after-last-item"),Wt()),(0,f.Z)(o,"".concat(m,"-rtl"),Gt==="rtl"),o),I),v=(0,j.Z)((0,j.Z)((0,j.Z)({},Tt),{total:E.length,current:Ft,pageSize:Dt}),e||{}),lt=Math.ceil(v.total/v.pageSize);v.current>lt&&(v.current=lt);var dt=e?i.createElement("div",{className:"".concat(m,"-pagination")},i.createElement(xt.Z,(0,j.Z)({},v,{onChange:wt,onShowSizeChange:Kt}))):null,U=(0,k.Z)(E);e&&E.length>(v.current-1)*v.pageSize&&(U=(0,k.Z)(E).splice((v.current-1)*v.pageSize,v.pageSize));var ut=(0,yt.Z)(),G=i.useMemo(function(){for(var u=0;u<_.c4.length;u+=1){var d=_.c4[u];if(ut[d])return d}},[ut]),Ut=i.useMemo(function(){if(!!p){var u=G&&p[G]?p[G]:p.column;if(u)return{width:"".concat(100/u,"%"),maxWidth:"".concat(100/u,"%")}}},[p==null?void 0:p.column,G]),Y=X&&i.createElement("div",{style:{minHeight:53}});if(U.length>0){var mt=U.map(function(u,d){return Jt(u,d)}),Yt=i.Children.map(mt,function(u,d){return i.createElement("div",{key:st[d],style:Ut},u)});Y=p?i.createElement(Ct.Z,{gutter:p.gutter},Yt):i.createElement("ul",{className:"".concat(m,"-items")},mt)}else!g&&!X&&(Y=$t(m,Rt));var T=v.position||"bottom";return i.createElement(J.Provider,{value:{grid:p,itemLayout:P}},i.createElement("div",(0,j.Z)({className:Xt},Lt),(T==="top"||T==="both")&&dt,z&&i.createElement("div",{className:"".concat(m,"-header")},z),i.createElement(ft.Z,Z,Y,g),F&&i.createElement("div",{className:"".concat(m,"-footer")},F),N||(T==="bottom"||T==="both")&&dt))}at.Item=Pt;var W=at,ne=n(58024),y=n(39144),ie=n(94233),D=n(51890),It=n(8292),Nt=n(22232),s=n.n(Nt),a=n(85893),Et=function(){var o=[{id:1,title:"PHP 7.3",logo:"https://www.php.net/images/logos/php-logo.svg",description:"PHP \u662F\u670D\u52A1\u5668\u7AEF\u811A\u672C\u8BED\u8A00\u3002",updatedAt:"2022-01-07",member:"Rasmus Lerdorf",href:"https://www.php.net/"},{id:2,title:"Laravel 8.5",logo:"https://cdn.learnku.com//uploads/communities/WtC3cPLHzMbKRSZnagU9.png!/both/44x44",description:"Laravel \u662F\u4E00\u4E2A\u57FA\u4E8E PHP \u7684 Web \u5E94\u7528\u6846\u67B6\uFF0C\u6709\u7740\u8868\u73B0\u529B\u5F3A\u3001\u8BED\u6CD5\u4F18\u96C5\u7684\u7279\u70B9\u3002",updatedAt:"2022-01-07",member:"Taylor Otwell",href:"https://learnku.com/docs/laravel/8.5"},{id:3,title:"laravel-modules 8.2.0",logo:"https://avatars.githubusercontent.com/u/882397?s=48&v=4",description:"\u7528\u4E8E\u4F7F\u7528\u6A21\u5757\u7BA1\u7406\u5927\u578B Laravel \u5E94\u7528\u7A0B\u5E8F\u3002\u6A21\u5757\u5C31\u50CF\u4E00\u4E2A Laravel \u5305\uFF0C\u5B83\u6709\u4E00\u4E9B\u89C6\u56FE\u3001\u63A7\u5236\u5668\u6216\u6A21\u578B\u3002",updatedAt:"2022-01-07",member:"Nicolas Widart",href:"https://github.com/nWidart/laravel-modules"},{id:4,title:"laravel-wechat 5.1.0",logo:"https://easywechat.vercel.app/w7team.jpg",description:"\u4E00\u4E2A\u5F00\u6E90\u7684 \u5FAE\u4FE1 \u975E\u5B98\u65B9 SDK\u3002",updatedAt:"2022-01-07",member:"Overtrue",href:"https://easywechat.vercel.app/5.x/"},{id:5,title:"jwt-auth 1.0.2",logo:"https://avatars.githubusercontent.com/u/5436950?s=48&v=4",description:"\u4E00\u4E2A\u597D\u7528\u7684API\u751F\u6210Token\u9A8C\u8BC1\u63D2\u4EF6\u3002",updatedAt:"2022-01-07",member:"Adam Hanna",href:"https://github.com/adam-hanna/jwt-auth"},{id:6,title:"laravel-schema-extend 1.4.1",logo:"https://avatars.githubusercontent.com/u/8280666?s=48&v=4",description:"\u57FA\u4E8ELaravel\u6570\u636E\u5E93\u8FC1\u79FB\u751F\u6210\u8868\u6CE8\u91CA\u63D2\u4EF6\u3002",updatedAt:"2022-01-07",member:"Zedisdog",href:"https://github.com/zedisdog/laravel-schema-extend"},{id:7,title:"qiniu-laravel-storage 0.10.4",logo:"https://avatars.githubusercontent.com/u/312404?s=48&v=4",description:"\u57FA\u4E8ELaravel\u5BF9\u4E03\u725B\u4E91API\u8BF7\u6C42\u7684\u63D2\u4EF6\u3002",updatedAt:"2022-01-07",member:"Zgldh",href:"https://github.com/zgldh/qiniu-laravel-storage"},{id:8,title:"ali-oss-storage 2.1.0",logo:"https://avatars.githubusercontent.com/u/906128?s=48&v=4",description:"\u57FA\u4E8ELaravel\u5BF9\u963F\u91CC\u4E91OSS\u7684API\u8BF7\u6C42\u7684\u63D2\u4EF6\u3002",updatedAt:"2022-01-07",member:"Jacobcyl",href:"https://github.com/jacobcyl/Aliyun-oss-storage"},{id:9,title:"l5-swagger 8.0",logo:"https://avatars.githubusercontent.com/u/761541?s=48&v=4",description:"Laravel \u9879\u76EE\u7684 OpenApi \u6216 Swagger \u89C4\u8303\u53D8\u5F97\u7B80\u5355\u3002",updatedAt:"2022-01-07",member:"Ashish Khokhar",href:"https://github.com/DarkaOnLine/L5-Swagger"},{id:10,title:"predis 1.1.10",logo:"https://avatars.githubusercontent.com/u/17923?s=48&v=4",description:"\u4E00\u4E2A\u7075\u6D3B\u4E14\u529F\u80FD\u9F50\u5168\u7684 PHP Redis \u63D2\u4EF6\u3002",updatedAt:"2022-01-07",member:"Daniele Alessandri",href:"https://github.com/predis/predis/tree/documentation"},{id:11,title:"log-viewer 8.2.0",logo:"https://avatars.githubusercontent.com/u/3282340?s=48&v=4",description:"laravel\u65E5\u5FD7\u7BA1\u7406\u5DE5\u5177\u3002",updatedAt:"2022-01-07",member:"Maroc",href:"https://github.com/ARCANEDEV/LogViewer"},{id:12,title:"volc-sdk-php 1.0",logo:"https://avatars.githubusercontent.com/u/67365215?s=200&v=4",description:"\u706B\u5C71\u5F15\u64CESDK for PHP\u3002",updatedAt:"2022-01-07",member:"Volcengine",href:"https://github.com/volcengine/volc-sdk-php"},{id:12,title:"zipstream-php 2.1",logo:"https://avatars.githubusercontent.com/u/333918?v=4",description:"\u4E00\u4E2A\u5FEB\u901F\u7B80\u5355\u7684 PHP \u6D41\u5F0F zip \u6587\u4EF6\u4E0B\u8F7D\u5668\u3002\u4F7F\u7528\u8FD9\u4E2A\u5E93\u5C06\u4F7F\u60A8\u4E0D\u5FC5\u5C06 Zip \u5199\u5165\u78C1\u76D8\u3002\u60A8\u53EF\u4EE5\u76F4\u63A5\u5C06\u5176\u53D1\u9001\u7ED9\u7528\u6237\uFF0C\u8FD9\u8981\u5FEB\u5F97\u591A\u3002\u5B83\u53EF\u4EE5\u4E0E S3 \u5B58\u50A8\u6876\u6216\u4EFB\u4F55 PSR7 \u6D41\u4E00\u8D77\u4F7F\u7528\u3002",updatedAt:"2022-01-07",member:"Jonatan M\xE4nnchen",href:"https://github.com/maennchen/ZipStream-PHP"}],l=[{id:1,title:"Ant Design Pro 5.0",logo:"https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg",description:"\u5F00\u7BB1\u5373\u7528\u7684\u4E2D\u53F0\u524D\u7AEF/\u8BBE\u8BA1\u89E3\u51B3\u65B9\u6848",updatedAt:"2022-01-07",member:"\u963F\u91CC",href:"https://pro.ant.design/zh-CN"},{id:2,title:"React 17.0.0",logo:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii0xMS41IC0xMC4yMzE3NCAyMyAyMC40NjM0OCI+CiAgPHRpdGxlPlJlYWN0IExvZ288L3RpdGxlPgogIDxjaXJjbGUgY3g9IjAiIGN5PSIwIiByPSIyLjA1IiBmaWxsPSIjNjFkYWZiIi8+CiAgPGcgc3Ryb2tlPSIjNjFkYWZiIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiPgogICAgPGVsbGlwc2Ugcng9IjExIiByeT0iNC4yIi8+CiAgICA8ZWxsaXBzZSByeD0iMTEiIHJ5PSI0LjIiIHRyYW5zZm9ybT0icm90YXRlKDYwKSIvPgogICAgPGVsbGlwc2Ugcng9IjExIiByeT0iNC4yIiB0cmFuc2Zvcm09InJvdGF0ZSgxMjApIi8+CiAgPC9nPgo8L3N2Zz4K",description:"\u7528\u4E8E\u6784\u5EFA\u7528\u6237\u754C\u9762\u7684 JavaScript \u5E93",updatedAt:"2022-01-07",member:"Facebook",href:"https://react.docschina.org/"},{id:3,title:"TypeScript",logo:"	https://typescript.bootcss.com/images/typescript-icon.svg",description:"TypeScript\u5177\u6709\u7C7B\u578B\u7CFB\u7EDF\uFF0C\u4E14\u662FJavaScript\u7684\u8D85\u96C6\u3002 \u5B83\u53EF\u4EE5\u7F16\u8BD1\u6210\u666E\u901A\u7684JavaScript\u4EE3\u7801\u3002",updatedAt:"2022-01-07",member:"Microsoft",href:"https://typescript.bootcss.com/"},{id:4,title:"antd 4.16.13",logo:"https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg",description:"antd \u662F\u57FA\u4E8E Ant Design \u8BBE\u8BA1\u4F53\u7CFB\u7684 React UI \u7EC4\u4EF6\u5E93\uFF0C\u4E3B\u8981\u7528\u4E8E\u7814\u53D1\u4F01\u4E1A\u7EA7\u4E2D\u540E\u53F0\u4EA7\u54C1\u3002",updatedAt:"2022-01-07",member:"\u963F\u91CC",href:"https://ant.design/index-cn"},{id:5,title:"UmiJS 3.5.0",logo:"https://img.alicdn.com/tfs/TB1zomHwxv1gK0jSZFFXXb0sXXa-200-200.png",description:"\u63D2\u4EF6\u5316\u7684\u4F01\u4E1A\u7EA7\u524D\u7AEF\u5E94\u7528\u6846\u67B6\u3002",updatedAt:"2022-01-07",member:"\u963F\u91CC",href:"https://umijs.org/zh-CN"},{id:6,title:"Pro Components",logo:"https://gw.alipayobjects.com/zos/antfincdn/upvrAjAPQX/Logo_Tech%252520UI.svg",description:"ProComponents \u662F\u57FA\u4E8E Ant Design \u800C\u5F00\u53D1\u7684\u6A21\u677F\u7EC4\u4EF6\uFF0C\u63D0\u4F9B\u4E86\u66F4\u9AD8\u7EA7\u522B\u7684\u62BD\u8C61\u652F\u6301\uFF0C\u5F00\u7BB1\u5373\u7528\u3002",updatedAt:"2022-01-07",member:"\u963F\u91CC",href:"https://procomponents.ant.design/"},{id:7,title:"react-sortable-hoc 2.0.0",logo:"https://avatars.githubusercontent.com/u/1416436?s=48&v=4",description:"\u62D6\u62FD\u63D2\u4EF6\uFF0C\u4E00\u7EC4\u9AD8\u9636\u7EC4\u4EF6\uFF0C\u53EF\u5C06\u4EFB\u4F55\u5217\u8868\u8F6C\u6362\u4E3A\u52A8\u753B\u3001\u53EF\u8BBF\u95EE\u4E14\u89E6\u6478\u53CB\u597D\u7684\u53EF\u6392\u5E8F\u5217\u8868",updatedAt:"2022-01-07",member:"Shopify",href:"https://github.com/clauderic/react-sortable-hoc"},{id:8,title:"braft-editor 2.3.9",logo:"https://avatars.githubusercontent.com/u/18643034?s=48&v=4",description:"\u5173\u4E8E\u7F16\u8F91\u6613\u7528\u7684React\u5BCC\u6587\u672C\u7F16\u8F91\u5668\u5668\uFF0C\u57FA\u4E8Edraft-js\u5F00\u53D1",updatedAt:"2022-01-07",member:"Psaren",href:"https://github.com/margox/braft-editor"},{id:9,title:"react-json-view 1.21.3",logo:"https://avatars.githubusercontent.com/u/4097374?s=48&v=4",description:"\u4E00\u4E2A\u7528\u4E8E\u663E\u793A\u548C\u7F16\u8F91 javascript\u6570\u7EC4\u548CJSON \u5BF9\u8C61\u7684 React \u7EC4\u4EF6\u3002",updatedAt:"2022-01-07",member:"Mac Gainor",href:"https://github.com/mac-s-g/react-json-view"},{id:10,title:"react-monaco-editor 0.48.0",logo:"https://avatars.githubusercontent.com/u/49051982?s=200&v=4",description:"React \u7684 Monaco \u7F16\u8F91\u5668\u3002",updatedAt:"2022-01-07",member:"react-monaco-editor",href:"https://github.com/react-monaco-editor/react-monaco-editor"}],e=[{id:1,logo:"/logo.png",description:"\u540E\u53F0\u7CFB\u7EDF\u7BA1\u7406",status:1,updatedAt:"2022-1-10"},{id:2,logo:"/logo.png",description:"\u6570\u636E\u4E2D\u5FC3",status:1,updatedAt:"2022-3-10"},{id:3,logo:"/logo.png",description:"\u5FEB\u5E94\u7528\u7BA1\u7406",status:1,updatedAt:"2022-3-20"},{id:3,logo:"/logo.png",description:"\u5176\u4ED6\u6570\u636E",status:1,updatedAt:"2022-3-30"}];return(0,a.jsx)(It.ZP,{title:!1,content:(0,a.jsxs)("div",{className:s().pageHeaderContent,children:[(0,a.jsx)("div",{className:s().avatar,children:(0,a.jsx)(D.C,{size:"large",src:"/logo.png"})}),(0,a.jsxs)("div",{className:s().content,children:[(0,a.jsx)("div",{className:s().contentTitle,children:"\u60A8\u597D \uFF0C\u6B22\u8FCE\u6765\u5230\u5929\u5947\u626C\u540E\u53F0\u7BA1\u7406\uFF0C\u795D\u4F60\u5F00\u5FC3\u6BCF\u4E00\u5929\uFF01"}),(0,a.jsx)("div",{children:"\u5929\u5947\u626C\u540E\u53F0\u7BA1\u7406\u662F\u57FA\u4E8E\u6700\u65B0Laravel 8.5\u6846\u67B6\u548CAnt Design Pro 5.0\u7684\u540E\u53F0\u7BA1\u7406\u7CFB\u7EDF\u3002\u521B\u7ACB\u4E8E2022\u5E74\u521D\u3002"})]})]}),children:(0,a.jsx)(pt.Z,{gutter:24,children:(0,a.jsxs)(gt.Z,{xl:24,lg:24,md:24,sm:24,xs:24,children:[(0,a.jsx)(y.Z,{className:s().projectList,style:{marginBottom:24},title:"\u524D\u7AEF\u6846\u67B6/\u63D2\u4EF6",bordered:!1,bodyStyle:{padding:0},children:l.map(function(t){return(0,a.jsx)(y.Z.Grid,{className:s().projectGrid,children:(0,a.jsxs)(y.Z,{bodyStyle:{padding:0},bordered:!1,children:[(0,a.jsx)(y.Z.Meta,{title:(0,a.jsxs)("div",{className:s().cardTitle,children:[(0,a.jsx)(D.C,{size:"small",src:t.logo}),(0,a.jsx)("a",{href:t.href,target:"_blank",children:t.title})]}),description:t.description}),(0,a.jsxs)("div",{className:s().projectItemContent,children:[(0,a.jsx)("a",{children:t.member}),(0,a.jsx)("span",{className:s().datetime,title:t.updatedAt,children:t.updatedAt})]})]})},t.id)})}),(0,a.jsx)(y.Z,{className:s().projectList,style:{marginBottom:24},title:"\u540E\u7AEF\u8BED\u8A00/\u6846\u67B6/\u63D2\u4EF6",bordered:!1,bodyStyle:{padding:0},children:o.map(function(t){return(0,a.jsx)(y.Z.Grid,{className:s().projectGrid,children:(0,a.jsxs)(y.Z,{bodyStyle:{padding:0},bordered:!1,children:[(0,a.jsx)(y.Z.Meta,{title:(0,a.jsxs)("div",{className:s().cardTitle,children:[(0,a.jsx)(D.C,{size:"small",src:t.logo}),(0,a.jsx)("a",{href:t.href,target:"_blank",children:t.title})]}),description:t.description}),(0,a.jsxs)("div",{className:s().projectItemContent,children:[(0,a.jsx)("a",{children:t.member}),(0,a.jsx)("span",{className:s().datetime,title:t.updatedAt,children:t.updatedAt})]})]})},t.id)})}),(0,a.jsx)(y.Z,{bodyStyle:{padding:0},bordered:!1,className:s().activeCard,title:"\u52A8\u6001",children:(0,a.jsx)(W,{renderItem:function(c){return(0,a.jsxs)(W.Item,{children:[(0,a.jsx)(W.Item.Meta,{avatar:(0,a.jsx)(D.C,{src:c.logo}),title:(0,a.jsx)("span",{className:s().event,children:c.description}),description:(0,a.jsx)("span",{className:s().datetime,title:c.updatedAt,children:c.updatedAt})}),c.status===1?(0,a.jsx)("div",{children:"\u5DF2\u5B8C\u6210"}):(0,a.jsx)("div",{children:"\u5F00\u53D1\u4E2D"})]},c.id)},dataSource:e,className:s().activitiesList,size:"large"})})]})})})},Zt=Et}}]);
