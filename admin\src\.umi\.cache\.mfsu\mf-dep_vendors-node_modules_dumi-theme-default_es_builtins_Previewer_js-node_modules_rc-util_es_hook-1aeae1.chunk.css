.__dumi-default-previewer {
  background-color: #fff;
  border: 1px solid #ebedf1;
  border-radius: 1px;
}
[data-prefers-color=dark] .__dumi-default-previewer {
  background-color: #141414;
  border-color: #6b6c6d;
}
.__dumi-default-previewer[data-debug] {
  margin-top: 32px;
  border-color: #ffcb00;
}
.__dumi-default-previewer[data-debug]::before {
  content: 'DEV ONLY';
  float: left;
  margin-left: -1px;
  margin-top: -18px;
  padding: 3px 6px;
  font-size: 12px;
  line-height: 1;
  background-color: #ffcb00;
  color: #735600;
  text-shadow: 0.5px 0.5px 0 rgba(255, 255, 255, 0.5);
  border-top-left-radius: 1px;
  border-top-right-radius: 1px;
}
.__dumi-default-previewer[data-iframe] .__dumi-default-previewer-browser-nav {
  padding: 2px 6px;
  background-color: #ebedf1;
}
.__dumi-default-previewer[data-iframe] .__dumi-default-previewer-browser-nav::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #fd6458;
  box-shadow: 20px 0 0 #ffbf2b, 40px 0 0 #24cc3d;
}
.__dumi-default-previewer[data-iframe] .__dumi-default-previewer-demo > iframe {
  border: 0;
  width: 100%;
  height: 300px;
}
.__dumi-default-previewer + .__dumi-default-previewer {
  margin-top: 32px;
}
.__dumi-default-previewer-demo {
  padding: 40px 24px;
}
.__dumi-default-previewer-target {
  border-color: rgba(69, 105, 212, 0.5);
  box-shadow: 0 0 0 5px rgba(69, 105, 212, 0.05);
}
.__dumi-default-previewer-desc > div:last-child {
  padding: 1.2em 1em 1em;
  color: #454d64;
  border-top: 1px solid #ebedf1;
}
[data-prefers-color=dark] .__dumi-default-previewer-desc > div:last-child {
  border-color: #6b6c6d;
}
.__dumi-default-previewer-desc .markdown > p:first-child {
  margin-top: 0;
}
.__dumi-default-previewer-desc .markdown > p:last-child {
  margin-bottom: 0;
}
.__dumi-default-previewer-desc[data-title] {
  position: relative;
}
.__dumi-default-previewer-desc[data-title] > a:first-child {
  position: absolute;
  top: 0;
  left: 1em;
  margin-left: -4px;
  padding: 0 4px;
  color: #454d64;
  font-size: inherit;
  font-weight: 500;
  background: linear-gradient(to top, #ffffff, #ffffff 50%, rgba(255, 255, 255, 0)) 100%;
  transform: translateY(-50%);
  pointer-events: auto;
  cursor: pointer;
}
[data-prefers-color=dark] .__dumi-default-previewer-desc[data-title] > a:first-child {
  color: rgba(255, 255, 255, 0.85);
  background: linear-gradient(to top, #141414, #141414 50%, rgba(255, 255, 255, 0)) 100%;
}
.__dumi-default-previewer-desc[data-title]:empty {
  padding-top: 0;
}
.__dumi-default-previewer-desc[data-title]:empty + .__dumi-default-previewer-actions {
  height: 46px;
  border-top-style: solid;
}
.__dumi-default-previewer-actions {
  display: flex;
  height: 40px;
  padding: 0 1em;
  align-items: center;
  border-top: 1px dashed #ebedf1;
}
[data-prefers-color=dark] .__dumi-default-previewer-actions {
  border-color: #6b6c6d;
}
.__dumi-default-previewer-actions > a:not(:last-child),
.__dumi-default-previewer-actions > button:not(:last-child) {
  margin-right: 8px;
}
.__dumi-default-previewer-actions > a {
  display: flex;
}
.__dumi-default-previewer-actions button {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  cursor: pointer;
  opacity: 0.6;
  outline: none;
  transition: opacity 0.2s, background 0.2s;
}
[data-prefers-color=dark] .__dumi-default-previewer-actions button {
  opacity: 1;
}
.__dumi-default-previewer-actions button::after {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
}
.__dumi-default-previewer-actions button:hover {
  opacity: 0.8;
}
.__dumi-default-previewer-actions button:active {
  opacity: 0.9;
}
.__dumi-default-previewer-actions button:disabled {
  opacity: 0.2;
  cursor: not-allowed;
}
.__dumi-default-previewer-actions button[role='codesandbox'] {
  background-position: -18px 0;
}
.__dumi-default-previewer-actions button[role='codepen'] {
  background-position: -36px 0;
}
.__dumi-default-previewer-actions button[role='source'] {
  background-position: -72px 0;
}
.__dumi-default-previewer-actions button[role='change-jsx'] {
  background-position: -90px 0;
}
.__dumi-default-previewer-actions button[role='change-tsx'] {
  background-position: -108px 0;
}
.__dumi-default-previewer-actions button[role='open-demo'] {
  background-position: -126px 0;
}
.__dumi-default-previewer-actions button[role='motions'] {
  background-position: -162px 0;
}
.__dumi-default-previewer-actions button[role='sketch-component'] {
  background-position: -182px 0;
}
.__dumi-default-previewer-actions button[role='sketch-group'] {
  background-position: -200px 0;
}
.__dumi-default-previewer-actions button[role='copy'][data-status='ready'] {
  background-position: -54px 0;
}
.__dumi-default-previewer-actions button[role='copy'][data-status='copied'] {
  pointer-events: none;
  background-position: -54px -16px;
}
.__dumi-default-previewer-actions button[role='refresh'] {
  background-position-x: -144px;
}
.__dumi-default-previewer-actions > span {
  flex: 1 1;
  display: inline-block;
}
.__dumi-default-previewer-source {
  border-top: 1px dashed #ebedf1;
}
[data-prefers-color=dark] .__dumi-default-previewer-source {
  border-color: #6b6c6d;
}
.__dumi-default-previewer-source-tab {
  border-top: 1px dashed #ebedf1;
}
[data-prefers-color=dark] .__dumi-default-previewer-source-tab {
  border-color: #6b6c6d;
}
.__dumi-default-previewer-source-tab .__dumi-default-tabs-tab-btn {
  position: relative;
  padding-left: 32px;
}
.__dumi-default-previewer-source-tab .__dumi-default-tabs-tab-btn::before,
.__dumi-default-previewer-source-tab .__dumi-default-tabs-tab-btn::after {
  content: '';
  position: absolute;
  margin-right: 4px;
  display: inline-block;
  box-sizing: border-box;
}
.__dumi-default-previewer-source-tab .__dumi-default-tabs-tab-btn::before {
  left: 16px;
  top: 50%;
  margin-top: -6px;
  width: 10px;
  height: 12px;
  border: 1px solid #717484;
}
.__dumi-default-previewer-source-tab .__dumi-default-tabs-tab-btn::after {
  top: 50%;
  left: 23px;
  margin-top: -7px;
  width: 4px;
  height: 4px;
  background: #fff;
  border-bottom: 1px solid #717484;
  transform: rotate(45deg);
}
[data-prefers-color=dark] .__dumi-default-previewer-source-tab .__dumi-default-tabs-tab-btn::after {
  background: #141414;
}
.__dumi-default-tabs {
  overflow: hidden;
}
.__dumi-default-tabs.__dumi-default-tabs-top {
  flex-direction: column;
}
.__dumi-default-tabs.__dumi-default-tabs-top .__dumi-default-tabs-ink-bar {
  bottom: 0;
}
.__dumi-default-tabs-nav {
  display: flex;
}
.__dumi-default-tabs-nav-wrap {
  display: flex;
  white-space: nowrap;
  overflow: hidden;
}
.__dumi-default-tabs-nav-wrap.__dumi-default-tabs-nav-wrap-ping-left {
  box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1) inset;
}
.__dumi-default-tabs-nav-wrap.__dumi-default-tabs-nav-wrap-ping-right ~ * > .__dumi-default-tabs-nav-more {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
.__dumi-default-tabs-nav-list {
  position: relative;
  display: flex;
  transition: transform 0.2s;
}
.__dumi-default-tabs-nav-more {
  height: 100%;
  cursor: pointer;
  background: none;
  border: 0;
  transition: box-shadow 0.2s;
}
.__dumi-default-tabs-tab {
  display: flex;
}
.__dumi-default-tabs-tab-btn {
  padding: 0 16px;
  font-size: 14px;
  line-height: 36px;
  border: 0;
  outline: none;
  background: transparent;
  box-sizing: border-box;
  cursor: pointer;
}
.__dumi-default-tabs-tab-btn:hover {
  color: #4569d4;
}
.__dumi-default-tabs-ink-bar {
  position: absolute;
  height: 2px;
  background: #4569d4;
  transition: left 0.2s, width 0.2s;
  pointer-events: none;
}
[data-prefers-color=dark] .__dumi-default-tabs-ink-bar {
  background: #7395f7;
}
.__dumi-default-tabs-dropdown {
  position: absolute;
  background: #fff;
  border: 1px solid #ebedf1;
  max-height: 200px;
  overflow: auto;
}
.__dumi-default-tabs-dropdown > ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.__dumi-default-tabs-dropdown > ul > li {
  padding: 4px 12px;
  font-size: 14px;
  cursor: pointer;
}
.__dumi-default-tabs-dropdown > ul > li:hover {
  color: #4569d4;
}
.__dumi-default-tabs-dropdown > ul > li:not(:last-child) {
  border-bottom: 1px dashed #ebedf1;
}
.__dumi-default-tabs-dropdown-hidden {
  display: none;
}

/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
/* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
.ant-message {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: 'tnum', "tnum";
  position: fixed;
  top: 8px;
  left: 0;
  z-index: 1010;
  width: 100%;
  pointer-events: none;
}
.ant-message-notice {
  padding: 8px;
  text-align: center;
}
.ant-message-notice-content {
  display: inline-block;
  padding: 10px 16px;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: all;
}
.ant-message-success .anticon {
  color: #52c41a;
}
.ant-message-error .anticon {
  color: #ff4d4f;
}
.ant-message-warning .anticon {
  color: #faad14;
}
.ant-message-info .anticon,
.ant-message-loading .anticon {
  color: #1890ff;
}
.ant-message .anticon {
  position: relative;
  top: 1px;
  margin-right: 8px;
  font-size: 16px;
}
.ant-message-notice.ant-move-up-leave.ant-move-up-leave-active {
  animation-name: MessageMoveOut;
  animation-duration: 0.3s;
}
@keyframes MessageMoveOut {
  0% {
    max-height: 150px;
    padding: 8px;
    opacity: 1;
  }
  100% {
    max-height: 0;
    padding: 0;
    opacity: 0;
  }
}
.ant-message-rtl {
  direction: rtl;
}
.ant-message-rtl span {
  direction: rtl;
}
.ant-message-rtl .anticon {
  margin-right: 0;
  margin-left: 8px;
}

/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
/* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
.ant-notification {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: 'tnum', "tnum";
  position: fixed;
  z-index: 1010;
  margin-right: 24px;
}
.ant-notification-topLeft,
.ant-notification-bottomLeft {
  margin-right: 0;
  margin-left: 24px;
}
.ant-notification-topLeft .ant-notification-fade-enter.ant-notification-fade-enter-active,
.ant-notification-bottomLeft .ant-notification-fade-enter.ant-notification-fade-enter-active,
.ant-notification-topLeft .ant-notification-fade-appear.ant-notification-fade-appear-active,
.ant-notification-bottomLeft .ant-notification-fade-appear.ant-notification-fade-appear-active {
  animation-name: NotificationLeftFadeIn;
}
.ant-notification-close-icon {
  font-size: 14px;
  cursor: pointer;
}
.ant-notification-hook-holder {
  position: relative;
}
.ant-notification-notice {
  position: relative;
  width: 384px;
  max-width: calc(100vw - 24px * 2);
  margin-bottom: 16px;
  margin-left: auto;
  padding: 16px 24px;
  overflow: hidden;
  line-height: 1.5715;
  word-wrap: break-word;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}
.ant-notification-topLeft .ant-notification-notice,
.ant-notification-bottomLeft .ant-notification-notice {
  margin-right: auto;
  margin-left: 0;
}
.ant-notification-notice-message {
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  line-height: 24px;
}
.ant-notification-notice-message-single-line-auto-margin {
  display: block;
  width: calc(384px - 24px * 2 - 24px - 48px - 100%);
  max-width: 4px;
  background-color: transparent;
  pointer-events: none;
}
.ant-notification-notice-message-single-line-auto-margin::before {
  display: block;
  content: '';
}
.ant-notification-notice-description {
  font-size: 14px;
}
.ant-notification-notice-closable .ant-notification-notice-message {
  padding-right: 24px;
}
.ant-notification-notice-with-icon .ant-notification-notice-message {
  margin-bottom: 4px;
  margin-left: 48px;
  font-size: 16px;
}
.ant-notification-notice-with-icon .ant-notification-notice-description {
  margin-left: 48px;
  font-size: 14px;
}
.ant-notification-notice-icon {
  position: absolute;
  margin-left: 4px;
  font-size: 24px;
  line-height: 24px;
}
.anticon.ant-notification-notice-icon-success {
  color: #52c41a;
}
.anticon.ant-notification-notice-icon-info {
  color: #1890ff;
}
.anticon.ant-notification-notice-icon-warning {
  color: #faad14;
}
.anticon.ant-notification-notice-icon-error {
  color: #ff4d4f;
}
.ant-notification-notice-close {
  position: absolute;
  top: 16px;
  right: 22px;
  color: rgba(0, 0, 0, 0.45);
  outline: none;
}
.ant-notification-notice-close:hover {
  color: rgba(0, 0, 0, 0.67);
}
.ant-notification-notice-btn {
  float: right;
  margin-top: 16px;
}
.ant-notification .notification-fade-effect {
  animation-duration: 0.24s;
  animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  animation-fill-mode: both;
}
.ant-notification-fade-enter,
.ant-notification-fade-appear {
  animation-duration: 0.24s;
  animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  animation-fill-mode: both;
  opacity: 0;
  animation-play-state: paused;
}
.ant-notification-fade-leave {
  animation-duration: 0.24s;
  animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  animation-fill-mode: both;
  animation-duration: 0.2s;
  animation-play-state: paused;
}
.ant-notification-fade-enter.ant-notification-fade-enter-active,
.ant-notification-fade-appear.ant-notification-fade-appear-active {
  animation-name: NotificationFadeIn;
  animation-play-state: running;
}
.ant-notification-fade-leave.ant-notification-fade-leave-active {
  animation-name: NotificationFadeOut;
  animation-play-state: running;
}
@keyframes NotificationFadeIn {
  0% {
    left: 384px;
    opacity: 0;
  }
  100% {
    left: 0;
    opacity: 1;
  }
}
@keyframes NotificationLeftFadeIn {
  0% {
    right: 384px;
    opacity: 0;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}
@keyframes NotificationFadeOut {
  0% {
    max-height: 150px;
    margin-bottom: 16px;
    opacity: 1;
  }
  100% {
    max-height: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
  }
}
.ant-notification-rtl {
  direction: rtl;
}
.ant-notification-rtl .ant-notification-notice-closable .ant-notification-notice-message {
  padding-right: 0;
  padding-left: 24px;
}
.ant-notification-rtl .ant-notification-notice-with-icon .ant-notification-notice-message {
  margin-right: 48px;
  margin-left: 0;
}
.ant-notification-rtl .ant-notification-notice-with-icon .ant-notification-notice-description {
  margin-right: 48px;
  margin-left: 0;
}
.ant-notification-rtl .ant-notification-notice-icon {
  margin-right: 4px;
  margin-left: 0;
}
.ant-notification-rtl .ant-notification-notice-close {
  right: auto;
  left: 22px;
}
.ant-notification-rtl .ant-notification-notice-btn {
  float: left;
}

/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou
 */

code[class*="language-"],
pre[class*="language-"] {
	color: black;
	background: none;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
	font-size: 1em;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	word-wrap: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
	white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.token.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
	color: #9a6e3a;
	/* This background color was intended by the author of this theme. */
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function,
.token.class-name {
	color: #DD4A68;
}

.token.regex,
.token.important,
.token.variable {
	color: #e90;
}

.token.important,
.token.bold {
	font-weight: bold;
}
.token.italic {
	font-style: italic;
}

.token.entity {
	cursor: help;
}

.__dumi-default-code-block {
  position: relative;
  font-size: 14px;
  background-color: #f9fafb;
}
[data-prefers-color=dark] .__dumi-default-code-block {
  color: rgba(255, 255, 255, 0.85);
  background: #262626;
}
.__dumi-default-code-block + .__dumi-default-code-block,
.__dumi-default-code-block + table {
  margin-top: 16px;
}
.__dumi-default-code-block > pre[class*='language-'] {
  margin: 0;
  background: transparent;
}
.__dumi-default-code-block > pre[class*='language-'] .token-line:not(:last-child) .plain:empty {
  display: inline-block;
  min-height: 1em;
}
.__dumi-default-code-block-copy-btn {
  position: absolute;
  top: 1.1em;
  right: 1em;
  display: inline-block;
  width: 16px;
  height: 16px;
  padding: 0;
  border: 0;
  outline: none;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s, background 0.2s;
}
.__dumi-default-code-block-copy-btn:hover {
  opacity: 0.8;
}
.__dumi-default-code-block-copy-btn:active {
  opacity: 0.9;
}
.__dumi-default-code-block-copy-btn[data-status='ready'] {
  background-position: -54px 0;
}
.__dumi-default-code-block-copy-btn[data-status='copied'] {
  opacity: 1;
  pointer-events: none;
  background-position: -54px -16px;
}
.__dumi-default-code-block:not(:hover) .__dumi-default-code-block-copy-btn {
  visibility: hidden;
  opacity: 0;
}
code[class*="language-"]::selection,
pre[class*="language-"]::selection,
code[class*="language-"] ::selection,
pre[class*="language-"] ::selection {
  color: inherit;
}
[data-prefers-color=dark] code[class*="language-"],
[data-prefers-color=dark] pre[class*="language-"] {
  color: rgba(255, 255, 255, 0.85);
  text-shadow: 0 1px #000;
}
[data-prefers-color=dark] code[class*="language-"]::selection,
[data-prefers-color=dark] pre[class*="language-"]::selection,
[data-prefers-color=dark] code[class*="language-"] ::selection,
[data-prefers-color=dark] pre[class*="language-"] ::selection {
  background-color: #364a63;
}
[data-prefers-color=dark] .token.operator,
[data-prefers-color=dark] .token.entity,
[data-prefers-color=dark] .token.url,
[data-prefers-color=dark] .language-css .token.string,
[data-prefers-color=dark] .style .token.string {
  background: transparent;
}
[data-prefers-color=dark] .token.property,
[data-prefers-color=dark] .token.tag,
[data-prefers-color=dark] .token.constant,
[data-prefers-color=dark] .token.symbol,
[data-prefers-color=dark] .token.deleted {
  color: #f92672;
}
[data-prefers-color=dark] .token.selector,
[data-prefers-color=dark] .token.attr-name,
[data-prefers-color=dark] .token.string,
[data-prefers-color=dark] .token.char,
[data-prefers-color=dark] .token.builtin,
[data-prefers-color=dark] .token.inserted {
  color: #a6e22e;
}
[data-prefers-color=dark] .token.atrule,
[data-prefers-color=dark] .token.attr-value,
[data-prefers-color=dark] .token.keyword {
  color: #e6db74;
}
[data-prefers-color=dark] .token.punctuation {
  color: rgba(255, 255, 255, 0.85);
}
[data-prefers-color=dark] .token.keyword {
  color: #66d9ef;
}
[data-prefers-color=dark] .token.boolean,
[data-prefers-color=dark] .token.number {
  color: #ae81ff;
}

