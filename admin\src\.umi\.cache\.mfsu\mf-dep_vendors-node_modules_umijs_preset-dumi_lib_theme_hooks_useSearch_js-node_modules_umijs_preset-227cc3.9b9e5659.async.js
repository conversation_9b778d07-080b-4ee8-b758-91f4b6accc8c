(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_umijs_preset-dumi_lib_theme_hooks_useSearch_js-node_modules_umijs_preset-227cc3"],{

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/components/AnchorLink.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/components/AnchorLink.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "./node_modules/react/index.js"));

var _runtime = __webpack_require__(/*! @umijs/runtime */ "./node_modules/@umijs/runtime/dist/index.esm.js");

var _lodash = _interopRequireDefault(__webpack_require__(/*! lodash.throttle */ "./node_modules/lodash.throttle/index.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

var anchorWatcher = new ( /*#__PURE__*/function () {
  function _class2() {
    _classCallCheck(this, _class2);

    this.anchors = [];
    this.listeners = [];
    this.listener = void 0;
    this.listener = (0, _lodash.default)(this._matchActiveAnchor.bind(this), 200);
  }
  /**
   * get active anchor by position
   */


  _createClass(_class2, [{
    key: "_matchActiveAnchor",
    value: function _matchActiveAnchor() {
      var _this = this;

      // find the first element which close the top of viewport
      var closestElmIndex = this.anchors.findIndex(function (elm, i) {
        return elm.getBoundingClientRect().top > 128 || i === _this.anchors.length - 1;
      });
      var currentElm = this.anchors[Math.max(0, closestElmIndex - 1)];
      var anchorVal = currentElm.parentElement.id; // trigger listeners

      this.listeners.forEach(function (fn) {
        return fn(anchorVal);
      });
    }
    /**
     * watch position for specific element
     * @param elm element
     */

  }, {
    key: "watch",
    value: function watch(elm) {
      if (this.anchors.length === 0 && typeof window !== 'undefined') {
        window.addEventListener('scroll', this.listener);
      }

      this.anchors.push(elm); // match immediately to get initial active anchor

      this.listener();
    }
    /**
     * unwatch position for specific element
     * @param elm element
     */

  }, {
    key: "unwatch",
    value: function unwatch(elm) {
      this.anchors.splice(this.anchors.findIndex(function (anchor) {
        return anchor === elm;
      }), 1);

      if (this.anchors.length === 0 && typeof window !== 'undefined') {
        window.removeEventListener('scroll', this.listener);
      }
    }
    /**
     * listen active anchor change
     * @param fn callback
     */

  }, {
    key: "listen",
    value: function listen(fn) {
      this.listeners.push(fn);
    }
    /**
     * unlisten active anchor change
     * @param fn callback
     */

  }, {
    key: "unlisten",
    value: function unlisten(fn) {
      this.listeners.splice(this.listeners.findIndex(function (f) {
        return f === fn;
      }), 1);
    }
  }]);

  return _class2;
}())();

function getElmScrollPosition(elm) {
  return elm.offsetTop + (elm.offsetParent ? getElmScrollPosition(elm.offsetParent) : 0);
}

var AnchorLink = function AnchorLink(props) {
  var _props$to$match;

  var hash = ((_props$to$match = props.to.match(/(#[^&?]*)/)) === null || _props$to$match === void 0 ? void 0 : _props$to$match[1]) || '';
  var ref = (0, _react.useRef)(null);

  var _useState = (0, _react.useState)(false),
      _useState2 = _slicedToArray(_useState, 2),
      _isActive = _useState2[0],
      setIsActive = _useState2[1];

  (0, _react.useEffect)(function () {
    var _ref$current, _ref$current$parentEl;

    if ( // only collect 3-levels title anchors, see also: SlugList.tsx
    ['H1', 'H2', 'H3'].includes((_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : (_ref$current$parentEl = _ref$current.parentElement) === null || _ref$current$parentEl === void 0 ? void 0 : _ref$current$parentEl.tagName) && ref.current.parentElement.id) {
      // only listen anchors within content area, mark by tranformer/remark/link.ts
      var elm = ref.current; // push element to list

      anchorWatcher.watch(elm);
      return function () {
        // release element from list
        anchorWatcher.unwatch(elm);
      };
    } // listen active anchor change for non-title anchor links


    var fn = function fn(anchorVal) {
      setIsActive(hash === "#".concat(anchorVal));
    };

    anchorWatcher.listen(fn);
    return function () {
      return anchorWatcher.unlisten(fn);
    };
  }, []);
  return /*#__PURE__*/_react.default.createElement(_runtime.NavLink, _extends({}, props, {
    ref: ref,
    onClick: function onClick() {
      return AnchorLink.scrollToAnchor(hash.substring(1));
    },
    isActive: function isActive() {
      return _isActive;
    }
  }));
};

AnchorLink.scrollToAnchor = function (anchor) {
  // wait for dom update
  window.requestAnimationFrame(function () {
    var elm = document.getElementById(decodeURIComponent(anchor));

    if (elm) {
      // compatible in Edge
      window.scrollTo(0, getElmScrollPosition(elm) - 100);
    }
  });
};

var _default = AnchorLink;
exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/components/Link.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/components/Link.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = exports.LinkWrapper = void 0;

var _react = _interopRequireDefault(__webpack_require__(/*! react */ "./node_modules/react/index.js"));

var _runtime = __webpack_require__(/*! @umijs/runtime */ "./node_modules/@umijs/runtime/dist/index.esm.js");

var _excluded = ["to"];

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

/**
 * Link component wrapper for render external link
 * @param Component   original Link component
 */
var LinkWrapper = function LinkWrapper(Component) {
  return function (_ref) {
    var to = _ref.to,
        props = _objectWithoutProperties(_ref, _excluded);

    var isExternal = /^(\w+:)?\/\/|^(mailto|tel):/.test(to) || !to;

    var hasComplexChildren = /*#__PURE__*/_react.default.isValidElement(props.children);

    return /*#__PURE__*/_react.default.createElement(Component, _extends({
      to: to || '',
      component: isExternal ? function () {
        return /*#__PURE__*/_react.default.createElement("a", {
          target: "_blank",
          rel: "noopener noreferrer",
          href: to
        }, props.children, to && !hasComplexChildren && /*#__PURE__*/_react.default.createElement("svg", {
          xmlns: "http://www.w3.org/2000/svg",
          "aria-hidden": "true",
          x: "0px",
          y: "0px",
          viewBox: "0 0 100 100",
          width: "15",
          height: "15",
          className: "__dumi-default-external-link-icon"
        }, /*#__PURE__*/_react.default.createElement("path", {
          fill: "currentColor",
          d: "M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"
        }), /*#__PURE__*/_react.default.createElement("polygon", {
          fill: "currentColor",
          points: "45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"
        })));
      } : undefined
    }, props, isExternal ? {} : {
      // scroll to top while change url
      onClick: function onClick() {
        var _props$onClick;

        window.scrollTo({
          top: 0
        });

        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }

        (_props$onClick = props.onClick) === null || _props$onClick === void 0 ? void 0 : _props$onClick.apply(this, args);
      }
    }));
  };
};

exports.LinkWrapper = LinkWrapper;

var _default = LinkWrapper(_runtime.Link);

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/components/NavLink.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/components/NavLink.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

var _runtime = __webpack_require__(/*! @umijs/runtime */ "./node_modules/@umijs/runtime/dist/index.esm.js");

var _Link = __webpack_require__(/*! ./Link */ "./node_modules/@umijs/preset-dumi/lib/theme/components/Link.js");

var _default = (0, _Link.LinkWrapper)(_runtime.NavLink);

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/context.js":
/*!**************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/context.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = _interopRequireDefault(__webpack_require__(/*! react */ "./node_modules/react/index.js"));

  _react = function _react() {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var _default = _react().default.createContext({
  config: {
    mode: 'doc',
    title: '',
    navs: {},
    menus: {},
    locales: [],
    repository: {
      branch: 'master'
    },
    theme: {}
  },
  meta: {
    title: ''
  },
  menu: [],
  nav: [],
  base: '',
  routes: [],
  apis: {},
  demos: {}
});

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useApiData.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useApiData.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

var _context = _interopRequireDefault(__webpack_require__(/*! ../context */ "./node_modules/@umijs/preset-dumi/lib/theme/context.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

/**
 * get API data
 * @param identifier      component name
 * @param locale          current locale
 * @param isDefaultLocale default locale flag
 */
function getApiData(apis, identifier, locale, isDefaultLocale) {
  return Object.entries(apis[identifier]).reduce((expts, [expt, rows]) => {
    expts[expt] = rows.map(props => {
      // copy original data
      const result = Object.assign({}, props);
      Object.keys(props).forEach(prop => {
        // discard useless locale property
        if (/^description(\.|$)/.test(prop)) {
          const _prop$match = prop.match(/^description\.?(.*)$/),
                _prop$match2 = _slicedToArray(_prop$match, 2),
                propLocale = _prop$match2[1];

          if (propLocale && propLocale !== locale || !propLocale && !isDefaultLocale) {
            delete result[prop];
          } else {
            result.description = result[prop];
          }
        }
      });
      return result;
    });
    return expts;
  }, {});
}
/**
 * use api data by identifier
 * @note  identifier is component name or component path
 */


var _default = identifier => {
  const _useContext = (0, _react().useContext)(_context.default),
        locale = _useContext.locale,
        locales = _useContext.config.locales,
        apis = _useContext.apis;

  const isDefaultLocale = !locales.length || locales[0].name === locale;

  const _useState = (0, _react().useState)(getApiData(apis, identifier, locale, isDefaultLocale)),
        _useState2 = _slicedToArray(_useState, 2),
        data = _useState2[0],
        setData = _useState2[1];

  (0, _react().useEffect)(() => {
    setData(getApiData(apis, identifier, locale, isDefaultLocale));
  }, [apis, identifier, locale, isDefaultLocale]);
  return data;
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useCodeSandbox.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useCodeSandbox.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

function _lzString() {
  const data = _interopRequireDefault(__webpack_require__(/*! lz-string */ "./node_modules/lz-string/libs/lz-string.js"));

  _lzString = function _lzString() {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

const CSB_API_ENDPOINT = 'https://codesandbox.io/api/v1/sandboxes/define'; // ref: https://github.com/codesandbox/codesandbox-importers/blob/master/packages/import-utils/src/api/define.ts

function serialize(data) {
  return _lzString().default.compressToBase64(JSON.stringify(data)).replace(/\+/g, '-') // Convert '+' to '-'
  .replace(/\//g, '_') // Convert '/' to '_'
  .replace(/=+$/, ''); // Remove ending '='
}

function getTextContent(raw) {
  const elm = document.createElement('span');
  elm.innerHTML = raw;
  const text = elm.textContent;
  elm.remove();
  return text;
}
/**
 * get serialized data that use to submit to codesandbox.io
 * @param opts  previewer props
 */


function getCSBData(opts) {
  const isTSX = Boolean(opts.sources._.tsx);
  const ext = isTSX ? '.tsx' : '.jsx';
  const files = {};
  const deps = {};
  const CSSDeps = Object.values(opts.dependencies).filter(dep => dep.css);
  const appFileName = `App${ext}`;
  const entryFileName = `index${ext}`; // generate dependencies

  Object.entries(opts.dependencies).forEach(([dep, {
    version
  }]) => {
    deps[dep] = version;
  }); // add react-dom dependency

  if (!deps['react-dom']) {
    deps['react-dom'] = deps.react || 'latest';
  } // append sandbox.config.json


  files['sandbox.config.json'] = {
    content: JSON.stringify({
      template: isTSX ? 'create-react-app-typescript' : 'create-react-app'
    }, null, 2)
  }; // append package.json

  files['package.json'] = {
    content: JSON.stringify({
      name: opts.title,
      description: getTextContent(opts.description) || 'An auto-generated demo by dumi',
      main: entryFileName,
      dependencies: deps,
      // add TypeScript dependency if required, must in devDeps to avoid csb compile error
      devDependencies: isTSX ? {
        typescript: '^3'
      } : {}
    }, null, 2)
  }; // append index.html

  files['index.html'] = {
    content: '<div style="margin: 16px;" id="root"></div>'
  }; // append entry file

  files[entryFileName] = {
    content: `/**
* This is an auto-generated demo by dumi
* if you think it is not working as expected,
* please report the issue at
* https://github.com/umijs/dumi/issues
**/

import React from 'react';
import ReactDOM from 'react-dom';
${CSSDeps.map(({
      css
    }) => `import '${css}';`).join('\n')}
import App from './App';

ReactDOM.render(
  <App />,
  document.getElementById('root'),
);`
  }; // append other imported local files

  Object.entries(opts.sources).forEach(([filename, {
    tsx,
    jsx,
    content
  }]) => {
    // handle primary content
    files[filename === '_' ? appFileName : filename] = {
      content: tsx || jsx || content
    };
  });
  return serialize({
    files
  });
}
/**
 * use CodeSandbox.io
 * @param opts  previewer opts
 * @note  return a open function for open demo on codesandbox.io
 */


var _default = (opts, api = CSB_API_ENDPOINT) => {
  const _useState = (0, _react().useState)(),
        _useState2 = _slicedToArray(_useState, 2),
        handler = _useState2[0],
        setHandler = _useState2[1];

  (0, _react().useEffect)(() => {
    if (opts) {
      const form = document.createElement('form');
      const input = document.createElement('input');
      const data = getCSBData(opts);
      form.method = 'POST';
      form.target = '_blank';
      form.style.display = 'none';
      form.action = api;
      form.appendChild(input);
      form.setAttribute('data-demo', opts.title || '');
      input.name = 'parameters';
      input.value = data;
      document.body.appendChild(form);
      setHandler(() => () => form.submit());
      return () => form.remove();
    }
  }, [opts]);
  return handler;
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useCopy.js":
/*!********************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useCopy.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

function _copyTextToClipboard() {
  const data = _interopRequireDefault(__webpack_require__(/*! copy-text-to-clipboard */ "./node_modules/copy-text-to-clipboard/index.js"));

  _copyTextToClipboard = function _copyTextToClipboard() {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

/**
 * use to copy text into clipboard
 */
var _default = () => {
  const _useState = (0, _react().useState)(),
        _useState2 = _slicedToArray(_useState, 2),
        timer = _useState2[0],
        setTimer = _useState2[1];

  const _useState3 = (0, _react().useState)('ready'),
        _useState4 = _slicedToArray(_useState3, 2),
        status = _useState4[0],
        setStatus = _useState4[1];

  const handler = (0, _react().useCallback)(text => {
    (0, _copyTextToClipboard().default)(text);
    setStatus('copied'); // reset status after 2000ms

    clearTimeout(timer);
    setTimer(setTimeout(() => {
      setStatus('ready');
    }, 2000));
  }, []);
  return [handler, status];
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useDemoUrl.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useDemoUrl.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.getDemoUrl = exports.getDemoRouteName = exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

var _context = _interopRequireDefault(__webpack_require__(/*! ../context */ "./node_modules/@umijs/preset-dumi/lib/theme/context.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

// functional for testing
function isBMW() {
  return ({"NODE_ENV":"development"}).PLATFORM_TYPE === 'BASEMENT';
}
/**
 * get demo route name
 * @note  also use this function in CLI, do not use BOM inside
 */


const getDemoRouteName = () => {
  return isBMW() ? `_demos` : `~demos`;
};
/**
 * get single demo url
 * @param demoId  demo identifier
 * @param htmlSuffix true when `exportStatic: { htmlSuffix: true }`
 */


exports.getDemoRouteName = getDemoRouteName;

const getDemoUrl = (demoId, htmlSuffix) => {
  var _window2;

  const _window = window,
        _window$location = _window.location,
        href = _window$location.href,
        origin = _window$location.origin;

  const _href$split = href.split(/#\//),
        _href$split2 = _slicedToArray(_href$split, 2),
        base = _href$split2[0],
        hashRoute = _href$split2[1];

  const isHashRoute = typeof hashRoute === 'string';
  return [isHashRoute ? `${base}#` : origin, // compatible with (empty), /base & /base/
  `${((_window2 = window) === null || _window2 === void 0 ? void 0 : _window2.routerBase) || ''}/`.replace(/\/\/$/, '/'), getDemoRouteName(), '/', demoId, `${htmlSuffix ? '.html' : ''}`].join('');
};
/**
 * hooks for get single demo url
 */


exports.getDemoUrl = getDemoUrl;

var _default = demoId => {
  const _useContext = (0, _react().useContext)(_context.default),
        config = _useContext.config;

  const _useState = (0, _react().useState)(''),
        _useState2 = _slicedToArray(_useState, 2),
        url = _useState2[0],
        setUrl = _useState2[1];

  (0, _react().useEffect)(() => {
    setUrl(demoId ? getDemoUrl(demoId, config.exportStatic && config.exportStatic.htmlSuffix) : null);
  }, [demoId, config]);
  return url;
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useLocaleProps.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useLocaleProps.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

/**
 * transform props by current locale
 * @note  such as title.zh-CN => title
 */
var _default = (locale, props) => {
  const processor = (...args) => {
    const result = {};
    Object.keys(args[1]).forEach(key => {
      const _slice = (key.match(/^(.+)\.([^_]+)$/) || []).slice(1),
            _slice2 = _slicedToArray(_slice, 2),
            name = _slice2[0],
            keyLocale = _slice2[1];

      if (!keyLocale || keyLocale === args[0]) {
        result[name || key] = args[1][key];
      }
    });
    return result;
  };

  const _useState = (0, _react().useState)(processor(locale, props)),
        _useState2 = _slicedToArray(_useState, 2),
        localeProps = _useState2[0],
        setLocaleProps = _useState2[1];

  (0, _react().useEffect)(() => {
    setLocaleProps(processor(locale, props));
  }, [locale, props]);
  return localeProps;
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useMotions.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useMotions.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

/**
 * execute motions
 * @param wrapper element wrapper
 * @param motions motion data
 * @param cb      callback
 * @param index   current motion index
 */
function runner(wrapper, motions, cb, index = 0) {
  var _container$querySelec;

  if (index < motions.length) {
    const current = motions[index];

    const next = () => runner(wrapper, motions, cb, index + 1);

    const _ref = current.match(/^([^:]+):?(.*)$/) || [],
          _ref2 = _slicedToArray(_ref, 3),
          type = _ref2[1],
          value = _ref2[2];

    switch (type) {
      // controls
      case 'autoplay':
        next();
        break;
      // actions

      case 'click':
        // eslint-disable-next-line no-case-declarations
        const _ref3 = value.match(/^(global\()?(.+?)\)?$/) || [],
              _ref4 = _slicedToArray(_ref3, 3),
              isGlobal = _ref4[1],
              selector = _ref4[2]; // eslint-disable-next-line no-case-declarations


        const container = isGlobal ? document : wrapper; // @ts-ignore

        (_container$querySelec = container.querySelector(selector)) === null || _container$querySelec === void 0 ? void 0 : _container$querySelec.click();
        next();
        break;

      case 'timeout':
        setTimeout(next, Number(value));
        break;
      // boardcasts

      case 'capture':
        window.postMessage({
          type: 'dumi:capture-element',
          value
        }, '*');
        next();
        break;

      default:
        console.warn(`[dumi: motion] unknown motion '${current}', skip.`);
        next();
    }
  } else {
    cb();
  }
}
/**
 * hook for execute dumi motions
 */


var _default = (motions, wrapper) => {
  const _useState = (0, _react().useState)(false),
        _useState2 = _slicedToArray(_useState, 2),
        isRunning = _useState2[0],
        setIsRunning = _useState2[1];

  const handler = (0, _react().useCallback)(() => {
    if (!isRunning) {
      runner(wrapper, motions, () => {
        setIsRunning(false);
      });
      setIsRunning(true);
    }
  }, [motions, wrapper, isRunning]);
  (0, _react().useEffect)(() => {
    if (motions[0] === 'autoplay' && wrapper) {
      handler();
    }
  }, [motions, wrapper]);
  return [handler, isRunning];
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/usePrefersColor.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/usePrefersColor.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

const COLOR_ATTR_NAME = 'data-prefers-color';
const COLOR_LS_NAME = 'dumi:prefers-color';
let colorChanger;

class ColorChanger {
  /**
   * current color
   * @note  initial value from head script in src/plugins/theme.ts
   */

  /**
   * color change callbacks
   */
  constructor() {
    this.color = void 0;
    this.callbacks = [];
    this.color = localStorage.getItem(COLOR_LS_NAME) || document.documentElement.getAttribute(COLOR_ATTR_NAME); // listen prefers color change

    ['light', 'dark'].forEach(color => {
      const mediaQueryList = this.getColorMedia(color);

      const handler = ev => {
        // only apply media prefers color in auto mode
        if (ev.matches && this.color === 'auto') {
          document.documentElement.setAttribute(COLOR_ATTR_NAME, color);
          this.applyCallbacks();
        }
      }; // compatible with Safari 13-

      /* istanbul ignore else */


      if (mediaQueryList.addEventListener) {
        mediaQueryList.addEventListener('change', handler);
      } else if (mediaQueryList.addListener) {
        mediaQueryList.addListener(handler);
      }
    });
  }
  /**
   * get media instance for prefers color
   * @param color   prefers color
   */


  getColorMedia(color) {
    return window.matchMedia(`(prefers-color-scheme: ${color})`);
  }
  /**
   * detect color whether matches current color mode
   * @param color   expected color
   */


  isColorMode(color) {
    return this.getColorMedia(color).matches;
  }
  /**
   * apply all event change callbacks
   */


  applyCallbacks() {
    this.callbacks.forEach(cb => cb(this.color));
  }
  /**
   * listen color change
   * @param cb  callback
   */


  listen(cb) {
    this.callbacks.push(cb);
  }
  /**
   * unlisten color change
   * @param cb  callback
   */


  unlisten(cb) {
    this.callbacks.splice(this.callbacks.indexOf(cb), 1);
  }
  /**
   * set prefers color
   */


  set(color) {
    this.color = color;
    localStorage.setItem(COLOR_LS_NAME, color);
    this.applyCallbacks();

    if (color === 'auto') {
      document.documentElement.setAttribute(COLOR_ATTR_NAME, this.isColorMode('dark') ? 'dark' : 'light');
    } else {
      document.documentElement.setAttribute(COLOR_ATTR_NAME, color);
    }

    return color;
  }

}
/**
 * hook for get/set prefers-color-schema, use to control color mode for theme package
 * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-color-scheme
 */


var _default = () => {
  const _useState = (0, _react().useState)(),
        _useState2 = _slicedToArray(_useState, 2),
        color = _useState2[0],
        setColor = _useState2[1];

  const changeColor = (0, _react().useCallback)(val => {
    colorChanger.set(val);
  }, []);
  (0, _react().useEffect)(() => {
    // lazy initialize, for SSR
    colorChanger = colorChanger || new ColorChanger();
    colorChanger.listen(setColor);
    setColor(colorChanger.color);
    return () => colorChanger.unlisten(setColor);
  }, []);
  return [color, changeColor];
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useRiddle.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useRiddle.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

const RIDDLE_API_ENDPOINT = 'https://riddle.alibaba-inc.com/riddles/define';
let isInternalNetwork;

const useInternalNet = () => {
  const _useState = (0, _react().useState)(Boolean(isInternalNetwork)),
        _useState2 = _slicedToArray(_useState, 2),
        isInternal = _useState2[0],
        setIsInternal = _useState2[1];

  (0, _react().useEffect)(() => {
    if (isInternalNetwork === undefined) {
      // detect network via img request
      const img = document.createElement('img'); // interrupt image pending after 200ms

      setTimeout(() => {
        img.src = '';
        img.remove();
      }, 200);

      img.onload = () => {
        isInternalNetwork = true;
        setIsInternal(true);
        img.remove();
      };

      img.src = 'https://private-alipayobjects.alipay.com/alipay-rmsdeploy-image/rmsportal/RKuAiriJqrUhyqW.png';
    }
  }, []);
  return isInternal;
};
/**
 * get js code for Riddle
 * @param opts  previewer props
 */


function getRiddleAppCode(opts) {
  var _dependencies$react;

  const dependencies = opts.dependencies;
  let result = opts.sources._.tsx || opts.sources._.jsx; // convert export default to ReactDOM.render for riddle

  result = result.replace(/^/, `import ReactDOM from 'react-dom@${((_dependencies$react = dependencies.react) === null || _dependencies$react === void 0 ? void 0 : _dependencies$react.version) || 'latest'}';\n`).replace('export default', 'const DumiDemo =').concat('\nReactDOM.render(<DumiDemo />, mountNode);'); // add version for dependencies

  result = result.replace(/(from ')((?:@[^/'"]+)?[^/'"]+)/g, (_, $1, $2) => {
    let dep = `${$1}${$2}`;

    if (dependencies[$2]) {
      dep += `@${dependencies[$2].version}`;
    }

    return dep;
  });
  return result;
}

var _default = opts => {
  const _useState3 = (0, _react().useState)(),
        _useState4 = _slicedToArray(_useState3, 2),
        handler = _useState4[0],
        setHandler = _useState4[1];

  const isInternal = useInternalNet();
  (0, _react().useEffect)(() => {
    if (opts && isInternal && // TODO: riddle is not support multiple files for currently
    Object.keys(opts.sources).length === 1) {
      const form = document.createElement('form');
      const input = document.createElement('input');
      form.method = 'POST';
      form.target = '_blank';
      form.style.display = 'none';
      form.action = RIDDLE_API_ENDPOINT;
      form.appendChild(input);
      form.setAttribute('data-demo', opts.title || '');
      input.name = 'data'; // create riddle data

      input.value = JSON.stringify({
        title: opts.titlle,
        js: getRiddleAppCode(opts),
        css: Object.entries(opts.dependencies).filter(([, dep]) => dep.css).map(([name, {
          version,
          css
        }]) => // generate to @import '~pkg@version/path/to/css' format
        `@import '~${css.replace(new RegExp(`^(${name})`), `$1@${version}`)}';`).join('\n')
      });
      document.body.appendChild(form);
      setHandler(() => () => form.submit());
      return () => form.remove();
    }
  }, [opts, isInternal]);
  return handler;
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useSearch.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useSearch.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

var _ = __webpack_require__(/*! .. */ "./node_modules/@umijs/preset-dumi/lib/theme/index.js");

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

/**
 * hooks for get search result by keywords (builtin search feature)
 * @param keywords  search keywords
 */
const useBuiltinSearch = keywords => {
  const _useContext = (0, _react().useContext)(_.context),
        locale = _useContext.locale,
        routes = _useContext.routes,
        locales = _useContext.config.locales;

  const _useState = (0, _react().useState)([]),
        _useState2 = _slicedToArray(_useState, 2),
        metas = _useState2[0],
        setMetas = _useState2[1];

  const _useState3 = (0, _react().useState)([]),
        _useState4 = _slicedToArray(_useState3, 2),
        items = _useState4[0],
        setItems = _useState4[1];

  (0, _react().useEffect)(() => {
    setMetas(routes.filter(({
      title,
      meta
    }) => {
      const isValidLocaleRoute = (meta === null || meta === void 0 ? void 0 : meta.locale) === locale;
      const isValidDefaultLocaleRoute = // route locale euqal default locale
      (meta === null || meta === void 0 ? void 0 : meta.locale) === locales[0].name || // missing locale and there has no locale or global locale equal default locale
      !(meta === null || meta === void 0 ? void 0 : meta.locale) && (!locales.length || locale === locales[0].name);
      return title && (isValidDefaultLocaleRoute || isValidLocaleRoute);
    }).reduce((result, route) => {
      var _route$meta, _route$meta2, _route$meta3;

      const routeMetaItem = {
        title: ((_route$meta = route.meta) === null || _route$meta === void 0 ? void 0 : _route$meta.title) || route.title,
        path: route.path
      };

      if ((_route$meta2 = route.meta) === null || _route$meta2 === void 0 ? void 0 : _route$meta2.group) {
        routeMetaItem.parent = route.meta.group;
      }

      result.push(routeMetaItem);
      result.push(...(((_route$meta3 = route.meta) === null || _route$meta3 === void 0 ? void 0 : _route$meta3.slugs) || []).filter(({
        value
      }) => {
        var _route$meta4;

        return value !== (((_route$meta4 = route.meta) === null || _route$meta4 === void 0 ? void 0 : _route$meta4.title) || route.title);
      }).map(slug => ({
        title: slug.value,
        path: `${route.path}#${slug.heading}`,
        parent: routeMetaItem
      })));
      return result;
    }, []));
  }, [routes.length, locale]);
  (0, _react().useEffect)(() => {
    const val = keywords === null || keywords === void 0 ? void 0 : keywords.trim().toUpperCase();

    if (val) {
      const result = [];

      for (let i = 0; i < metas.length; i += 1) {
        if (metas[i].title.toUpperCase().indexOf(val) > -1) {
          result.push(metas[i]);
        }
      }

      setItems(result);
    } else {
      setItems([]);
    }
  }, [keywords, metas.length]);
  return items;
};
/**
 * hooks for bind Algolia search feature
 */


const useAlgoliaSearch = () => {
  const _useContext2 = (0, _react().useContext)(_.context),
        algolia = _useContext2.config.algolia;

  const binder = (0, _react().useCallback)(selector => {
    window.docsearch(_objectSpread({
      inputSelector: selector
    }, algolia));
  }, [algolia]);
  return binder;
};
/**
 * use to bind algolia or return search result by keywords
 */


var _default = keywords => {
  const _useContext3 = (0, _react().useContext)(_.context),
        config = _useContext3.config;

  const builtin = useBuiltinSearch(keywords);
  const algolia = useAlgoliaSearch();
  return config.algolia ? algolia : builtin;
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useTSPlaygroundUrl.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/hooks/useTSPlaygroundUrl.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.default = void 0;

function _react() {
  const data = __webpack_require__(/*! react */ "./node_modules/react/index.js");

  _react = function _react() {
    return data;
  };

  return data;
}

function _lzString() {
  const data = _interopRequireDefault(__webpack_require__(/*! lz-string */ "./node_modules/lz-string/libs/lz-string.js"));

  _lzString = function _lzString() {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

const API_ENDPOINTS = {
  'zh-CN': 'https://www.typescriptlang.org/zh/play',
  'en-US': 'https://www.typescriptlang.org/play'
};
/**
 * hooks for generate TypeScript playground url for tsx? code
 */

var _default = (locale, code) => {
  const processor = (...args) => {
    const api = /^zh|cn$/.test(args[0]) ? API_ENDPOINTS['zh-CN'] : API_ENDPOINTS['en-US'];
    return `${api}?skipLibCheck=true&jsx=1#code/${_lzString().default.compressToEncodedURIComponent(args[1])}`;
  };

  const _useState = (0, _react().useState)(processor(locale, code)),
        _useState2 = _slicedToArray(_useState, 2),
        url = _useState2[0],
        setUrl = _useState2[1];

  (0, _react().useEffect)(() => {
    setUrl(processor(locale, code));
  }, [locale, code]);
  return url;
};

exports.default = _default;

/***/ }),

/***/ "./node_modules/@umijs/preset-dumi/lib/theme/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@umijs/preset-dumi/lib/theme/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
Object.defineProperty(exports, "AnchorLink", ({
  enumerable: true,
  get: function get() {
    return _AnchorLink.default;
  }
}));
Object.defineProperty(exports, "Link", ({
  enumerable: true,
  get: function get() {
    return _Link.default;
  }
}));
Object.defineProperty(exports, "NavLink", ({
  enumerable: true,
  get: function get() {
    return _NavLink.default;
  }
}));
Object.defineProperty(exports, "context", ({
  enumerable: true,
  get: function get() {
    return _context.default;
  }
}));
Object.defineProperty(exports, "useApiData", ({
  enumerable: true,
  get: function get() {
    return _useApiData.default;
  }
}));
Object.defineProperty(exports, "useCodeSandbox", ({
  enumerable: true,
  get: function get() {
    return _useCodeSandbox.default;
  }
}));
Object.defineProperty(exports, "useCopy", ({
  enumerable: true,
  get: function get() {
    return _useCopy.default;
  }
}));
Object.defineProperty(exports, "useDemoUrl", ({
  enumerable: true,
  get: function get() {
    return _useDemoUrl.default;
  }
}));
Object.defineProperty(exports, "useLocaleProps", ({
  enumerable: true,
  get: function get() {
    return _useLocaleProps.default;
  }
}));
Object.defineProperty(exports, "useMotions", ({
  enumerable: true,
  get: function get() {
    return _useMotions.default;
  }
}));
Object.defineProperty(exports, "usePrefersColor", ({
  enumerable: true,
  get: function get() {
    return _usePrefersColor.default;
  }
}));
Object.defineProperty(exports, "useRiddle", ({
  enumerable: true,
  get: function get() {
    return _useRiddle.default;
  }
}));
Object.defineProperty(exports, "useSearch", ({
  enumerable: true,
  get: function get() {
    return _useSearch.default;
  }
}));
Object.defineProperty(exports, "useTSPlaygroundUrl", ({
  enumerable: true,
  get: function get() {
    return _useTSPlaygroundUrl.default;
  }
}));

var _context = _interopRequireDefault(__webpack_require__(/*! ./context */ "./node_modules/@umijs/preset-dumi/lib/theme/context.js"));

var _Link = _interopRequireDefault(__webpack_require__(/*! ./components/Link */ "./node_modules/@umijs/preset-dumi/lib/theme/components/Link.js"));

var _NavLink = _interopRequireDefault(__webpack_require__(/*! ./components/NavLink */ "./node_modules/@umijs/preset-dumi/lib/theme/components/NavLink.js"));

var _AnchorLink = _interopRequireDefault(__webpack_require__(/*! ./components/AnchorLink */ "./node_modules/@umijs/preset-dumi/lib/theme/components/AnchorLink.js"));

var _useSearch = _interopRequireDefault(__webpack_require__(/*! ./hooks/useSearch */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useSearch.js"));

var _useCopy = _interopRequireDefault(__webpack_require__(/*! ./hooks/useCopy */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useCopy.js"));

var _useRiddle = _interopRequireDefault(__webpack_require__(/*! ./hooks/useRiddle */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useRiddle.js"));

var _useMotions = _interopRequireDefault(__webpack_require__(/*! ./hooks/useMotions */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useMotions.js"));

var _useCodeSandbox = _interopRequireDefault(__webpack_require__(/*! ./hooks/useCodeSandbox */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useCodeSandbox.js"));

var _useLocaleProps = _interopRequireDefault(__webpack_require__(/*! ./hooks/useLocaleProps */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useLocaleProps.js"));

var _useDemoUrl = _interopRequireDefault(__webpack_require__(/*! ./hooks/useDemoUrl */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useDemoUrl.js"));

var _useApiData = _interopRequireDefault(__webpack_require__(/*! ./hooks/useApiData */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useApiData.js"));

var _useTSPlaygroundUrl = _interopRequireDefault(__webpack_require__(/*! ./hooks/useTSPlaygroundUrl */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/useTSPlaygroundUrl.js"));

var _usePrefersColor = _interopRequireDefault(__webpack_require__(/*! ./hooks/usePrefersColor */ "./node_modules/@umijs/preset-dumi/lib/theme/hooks/usePrefersColor.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/***/ }),

/***/ "./node_modules/copy-text-to-clipboard/index.js":
/*!******************************************************!*\
  !*** ./node_modules/copy-text-to-clipboard/index.js ***!
  \******************************************************/
/***/ (function(module) {

"use strict";


const copyTextToClipboard = (input, {target = document.body} = {}) => {
	const element = document.createElement('textarea');
	const previouslyFocusedElement = document.activeElement;

	element.value = input;

	// Prevent keyboard from showing on mobile
	element.setAttribute('readonly', '');

	element.style.contain = 'strict';
	element.style.position = 'absolute';
	element.style.left = '-9999px';
	element.style.fontSize = '12pt'; // Prevent zooming on iOS

	const selection = document.getSelection();
	let originalRange = false;
	if (selection.rangeCount > 0) {
		originalRange = selection.getRangeAt(0);
	}

	target.append(element);
	element.select();

	// Explicit selection workaround for iOS
	element.selectionStart = 0;
	element.selectionEnd = input.length;

	let isSuccess = false;
	try {
		isSuccess = document.execCommand('copy');
	} catch (_) {}

	element.remove();

	if (originalRange) {
		selection.removeAllRanges();
		selection.addRange(originalRange);
	}

	// Get the focus back on the previously focused element, if any
	if (previouslyFocusedElement) {
		previouslyFocusedElement.focus();
	}

	return isSuccess;
};

module.exports = copyTextToClipboard;
// TODO: Remove this for the next major release
module.exports.default = copyTextToClipboard;


/***/ }),

/***/ "./node_modules/lz-string/libs/lz-string.js":
/*!**************************************************!*\
  !*** ./node_modules/lz-string/libs/lz-string.js ***!
  \**************************************************/
/***/ (function(module, exports, __webpack_require__) {

var __WEBPACK_AMD_DEFINE_RESULT__;// Copyright (c) 2013 Pieroxy <<EMAIL>>
// This work is free. You can redistribute it and/or modify it
// under the terms of the WTFPL, Version 2
// For more information see LICENSE.txt or http://www.wtfpl.net/
//
// For more information, the home page:
// http://pieroxy.net/blog/pages/lz-string/testing.html
//
// LZ-based compression algorithm, version 1.4.4
var LZString = (function() {

// private property
var f = String.fromCharCode;
var keyStrBase64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
var keyStrUriSafe = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$";
var baseReverseDic = {};

function getBaseValue(alphabet, character) {
  if (!baseReverseDic[alphabet]) {
    baseReverseDic[alphabet] = {};
    for (var i=0 ; i<alphabet.length ; i++) {
      baseReverseDic[alphabet][alphabet.charAt(i)] = i;
    }
  }
  return baseReverseDic[alphabet][character];
}

var LZString = {
  compressToBase64 : function (input) {
    if (input == null) return "";
    var res = LZString._compress(input, 6, function(a){return keyStrBase64.charAt(a);});
    switch (res.length % 4) { // To produce valid Base64
    default: // When could this happen ?
    case 0 : return res;
    case 1 : return res+"===";
    case 2 : return res+"==";
    case 3 : return res+"=";
    }
  },

  decompressFromBase64 : function (input) {
    if (input == null) return "";
    if (input == "") return null;
    return LZString._decompress(input.length, 32, function(index) { return getBaseValue(keyStrBase64, input.charAt(index)); });
  },

  compressToUTF16 : function (input) {
    if (input == null) return "";
    return LZString._compress(input, 15, function(a){return f(a+32);}) + " ";
  },

  decompressFromUTF16: function (compressed) {
    if (compressed == null) return "";
    if (compressed == "") return null;
    return LZString._decompress(compressed.length, 16384, function(index) { return compressed.charCodeAt(index) - 32; });
  },

  //compress into uint8array (UCS-2 big endian format)
  compressToUint8Array: function (uncompressed) {
    var compressed = LZString.compress(uncompressed);
    var buf=new Uint8Array(compressed.length*2); // 2 bytes per character

    for (var i=0, TotalLen=compressed.length; i<TotalLen; i++) {
      var current_value = compressed.charCodeAt(i);
      buf[i*2] = current_value >>> 8;
      buf[i*2+1] = current_value % 256;
    }
    return buf;
  },

  //decompress from uint8array (UCS-2 big endian format)
  decompressFromUint8Array:function (compressed) {
    if (compressed===null || compressed===undefined){
        return LZString.decompress(compressed);
    } else {
        var buf=new Array(compressed.length/2); // 2 bytes per character
        for (var i=0, TotalLen=buf.length; i<TotalLen; i++) {
          buf[i]=compressed[i*2]*256+compressed[i*2+1];
        }

        var result = [];
        buf.forEach(function (c) {
          result.push(f(c));
        });
        return LZString.decompress(result.join(''));

    }

  },


  //compress into a string that is already URI encoded
  compressToEncodedURIComponent: function (input) {
    if (input == null) return "";
    return LZString._compress(input, 6, function(a){return keyStrUriSafe.charAt(a);});
  },

  //decompress from an output of compressToEncodedURIComponent
  decompressFromEncodedURIComponent:function (input) {
    if (input == null) return "";
    if (input == "") return null;
    input = input.replace(/ /g, "+");
    return LZString._decompress(input.length, 32, function(index) { return getBaseValue(keyStrUriSafe, input.charAt(index)); });
  },

  compress: function (uncompressed) {
    return LZString._compress(uncompressed, 16, function(a){return f(a);});
  },
  _compress: function (uncompressed, bitsPerChar, getCharFromInt) {
    if (uncompressed == null) return "";
    var i, value,
        context_dictionary= {},
        context_dictionaryToCreate= {},
        context_c="",
        context_wc="",
        context_w="",
        context_enlargeIn= 2, // Compensate for the first entry which should not count
        context_dictSize= 3,
        context_numBits= 2,
        context_data=[],
        context_data_val=0,
        context_data_position=0,
        ii;

    for (ii = 0; ii < uncompressed.length; ii += 1) {
      context_c = uncompressed.charAt(ii);
      if (!Object.prototype.hasOwnProperty.call(context_dictionary,context_c)) {
        context_dictionary[context_c] = context_dictSize++;
        context_dictionaryToCreate[context_c] = true;
      }

      context_wc = context_w + context_c;
      if (Object.prototype.hasOwnProperty.call(context_dictionary,context_wc)) {
        context_w = context_wc;
      } else {
        if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate,context_w)) {
          if (context_w.charCodeAt(0)<256) {
            for (i=0 ; i<context_numBits ; i++) {
              context_data_val = (context_data_val << 1);
              if (context_data_position == bitsPerChar-1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
            }
            value = context_w.charCodeAt(0);
            for (i=0 ; i<8 ; i++) {
              context_data_val = (context_data_val << 1) | (value&1);
              if (context_data_position == bitsPerChar-1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = value >> 1;
            }
          } else {
            value = 1;
            for (i=0 ; i<context_numBits ; i++) {
              context_data_val = (context_data_val << 1) | value;
              if (context_data_position ==bitsPerChar-1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = 0;
            }
            value = context_w.charCodeAt(0);
            for (i=0 ; i<16 ; i++) {
              context_data_val = (context_data_val << 1) | (value&1);
              if (context_data_position == bitsPerChar-1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = value >> 1;
            }
          }
          context_enlargeIn--;
          if (context_enlargeIn == 0) {
            context_enlargeIn = Math.pow(2, context_numBits);
            context_numBits++;
          }
          delete context_dictionaryToCreate[context_w];
        } else {
          value = context_dictionary[context_w];
          for (i=0 ; i<context_numBits ; i++) {
            context_data_val = (context_data_val << 1) | (value&1);
            if (context_data_position == bitsPerChar-1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }


        }
        context_enlargeIn--;
        if (context_enlargeIn == 0) {
          context_enlargeIn = Math.pow(2, context_numBits);
          context_numBits++;
        }
        // Add wc to the dictionary.
        context_dictionary[context_wc] = context_dictSize++;
        context_w = String(context_c);
      }
    }

    // Output the code for w.
    if (context_w !== "") {
      if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate,context_w)) {
        if (context_w.charCodeAt(0)<256) {
          for (i=0 ; i<context_numBits ; i++) {
            context_data_val = (context_data_val << 1);
            if (context_data_position == bitsPerChar-1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
          }
          value = context_w.charCodeAt(0);
          for (i=0 ; i<8 ; i++) {
            context_data_val = (context_data_val << 1) | (value&1);
            if (context_data_position == bitsPerChar-1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }
        } else {
          value = 1;
          for (i=0 ; i<context_numBits ; i++) {
            context_data_val = (context_data_val << 1) | value;
            if (context_data_position == bitsPerChar-1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = 0;
          }
          value = context_w.charCodeAt(0);
          for (i=0 ; i<16 ; i++) {
            context_data_val = (context_data_val << 1) | (value&1);
            if (context_data_position == bitsPerChar-1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }
        }
        context_enlargeIn--;
        if (context_enlargeIn == 0) {
          context_enlargeIn = Math.pow(2, context_numBits);
          context_numBits++;
        }
        delete context_dictionaryToCreate[context_w];
      } else {
        value = context_dictionary[context_w];
        for (i=0 ; i<context_numBits ; i++) {
          context_data_val = (context_data_val << 1) | (value&1);
          if (context_data_position == bitsPerChar-1) {
            context_data_position = 0;
            context_data.push(getCharFromInt(context_data_val));
            context_data_val = 0;
          } else {
            context_data_position++;
          }
          value = value >> 1;
        }


      }
      context_enlargeIn--;
      if (context_enlargeIn == 0) {
        context_enlargeIn = Math.pow(2, context_numBits);
        context_numBits++;
      }
    }

    // Mark the end of the stream
    value = 2;
    for (i=0 ; i<context_numBits ; i++) {
      context_data_val = (context_data_val << 1) | (value&1);
      if (context_data_position == bitsPerChar-1) {
        context_data_position = 0;
        context_data.push(getCharFromInt(context_data_val));
        context_data_val = 0;
      } else {
        context_data_position++;
      }
      value = value >> 1;
    }

    // Flush the last char
    while (true) {
      context_data_val = (context_data_val << 1);
      if (context_data_position == bitsPerChar-1) {
        context_data.push(getCharFromInt(context_data_val));
        break;
      }
      else context_data_position++;
    }
    return context_data.join('');
  },

  decompress: function (compressed) {
    if (compressed == null) return "";
    if (compressed == "") return null;
    return LZString._decompress(compressed.length, 32768, function(index) { return compressed.charCodeAt(index); });
  },

  _decompress: function (length, resetValue, getNextValue) {
    var dictionary = [],
        next,
        enlargeIn = 4,
        dictSize = 4,
        numBits = 3,
        entry = "",
        result = [],
        i,
        w,
        bits, resb, maxpower, power,
        c,
        data = {val:getNextValue(0), position:resetValue, index:1};

    for (i = 0; i < 3; i += 1) {
      dictionary[i] = i;
    }

    bits = 0;
    maxpower = Math.pow(2,2);
    power=1;
    while (power!=maxpower) {
      resb = data.val & data.position;
      data.position >>= 1;
      if (data.position == 0) {
        data.position = resetValue;
        data.val = getNextValue(data.index++);
      }
      bits |= (resb>0 ? 1 : 0) * power;
      power <<= 1;
    }

    switch (next = bits) {
      case 0:
          bits = 0;
          maxpower = Math.pow(2,8);
          power=1;
          while (power!=maxpower) {
            resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
              data.position = resetValue;
              data.val = getNextValue(data.index++);
            }
            bits |= (resb>0 ? 1 : 0) * power;
            power <<= 1;
          }
        c = f(bits);
        break;
      case 1:
          bits = 0;
          maxpower = Math.pow(2,16);
          power=1;
          while (power!=maxpower) {
            resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
              data.position = resetValue;
              data.val = getNextValue(data.index++);
            }
            bits |= (resb>0 ? 1 : 0) * power;
            power <<= 1;
          }
        c = f(bits);
        break;
      case 2:
        return "";
    }
    dictionary[3] = c;
    w = c;
    result.push(c);
    while (true) {
      if (data.index > length) {
        return "";
      }

      bits = 0;
      maxpower = Math.pow(2,numBits);
      power=1;
      while (power!=maxpower) {
        resb = data.val & data.position;
        data.position >>= 1;
        if (data.position == 0) {
          data.position = resetValue;
          data.val = getNextValue(data.index++);
        }
        bits |= (resb>0 ? 1 : 0) * power;
        power <<= 1;
      }

      switch (c = bits) {
        case 0:
          bits = 0;
          maxpower = Math.pow(2,8);
          power=1;
          while (power!=maxpower) {
            resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
              data.position = resetValue;
              data.val = getNextValue(data.index++);
            }
            bits |= (resb>0 ? 1 : 0) * power;
            power <<= 1;
          }

          dictionary[dictSize++] = f(bits);
          c = dictSize-1;
          enlargeIn--;
          break;
        case 1:
          bits = 0;
          maxpower = Math.pow(2,16);
          power=1;
          while (power!=maxpower) {
            resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
              data.position = resetValue;
              data.val = getNextValue(data.index++);
            }
            bits |= (resb>0 ? 1 : 0) * power;
            power <<= 1;
          }
          dictionary[dictSize++] = f(bits);
          c = dictSize-1;
          enlargeIn--;
          break;
        case 2:
          return result.join('');
      }

      if (enlargeIn == 0) {
        enlargeIn = Math.pow(2, numBits);
        numBits++;
      }

      if (dictionary[c]) {
        entry = dictionary[c];
      } else {
        if (c === dictSize) {
          entry = w + w.charAt(0);
        } else {
          return null;
        }
      }
      result.push(entry);

      // Add w+entry[0] to the dictionary.
      dictionary[dictSize++] = w + entry.charAt(0);
      enlargeIn--;

      w = entry;

      if (enlargeIn == 0) {
        enlargeIn = Math.pow(2, numBits);
        numBits++;
      }

    }
  }
};
  return LZString;
})();

if (true) {
  !(__WEBPACK_AMD_DEFINE_RESULT__ = (function () { return LZString; }).call(exports, __webpack_require__, exports, module),
		__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));
} else {}


/***/ })

}]);