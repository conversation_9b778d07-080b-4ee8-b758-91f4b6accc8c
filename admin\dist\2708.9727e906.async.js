(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2708],{68179:function(){},27049:function(v,n,e){"use strict";var s=e(22122),a=e(96156),u=e(67294),p=e(94184),g=e.n(p),T=e(65632),h=function(y,f){var E={};for(var m in y)Object.prototype.hasOwnProperty.call(y,m)&&f.indexOf(m)<0&&(E[m]=y[m]);if(y!=null&&typeof Object.getOwnPropertySymbols=="function")for(var M=0,m=Object.getOwnPropertySymbols(y);M<m.length;M++)f.indexOf(m[M])<0&&Object.prototype.propertyIsEnumerable.call(y,m[M])&&(E[m[M]]=y[m[M]]);return E},d=function(f){return u.createElement(T.C,null,function(E){var m,M=E.getPrefixCls,N=E.direction,S=f.prefixCls,b=f.type,P=b===void 0?"horizontal":b,i=f.orientation,t=i===void 0?"center":i,l=f.className,r=f.children,o=f.dashed,c=f.plain,C=h(f,["prefixCls","type","orientation","className","children","dashed","plain"]),R=M("divider",S),O=t.length>0?"-".concat(t):t,A=!!r,F=g()(R,"".concat(R,"-").concat(P),(m={},(0,a.Z)(m,"".concat(R,"-with-text"),A),(0,a.Z)(m,"".concat(R,"-with-text").concat(O),A),(0,a.Z)(m,"".concat(R,"-dashed"),!!o),(0,a.Z)(m,"".concat(R,"-plain"),!!c),(0,a.Z)(m,"".concat(R,"-rtl"),N==="rtl"),m),l);return u.createElement("div",(0,s.Z)({className:F},C,{role:"separator"}),r&&u.createElement("span",{className:"".concat(R,"-inner-text")},r))})};n.Z=d},48736:function(v,n,e){"use strict";var s=e(65056),a=e.n(s),u=e(68179),p=e.n(u)},14890:function(v,n,e){"use strict";e.r(n),e.d(n,{__DO_NOT_USE__ActionTypes:function(){return g},applyMiddleware:function(){return l},bindActionCreators:function(){return i},combineReducers:function(){return b},compose:function(){return t},createStore:function(){return m}});var s=e(28991);function a(o){return"Minified Redux error #"+o+"; visit https://redux.js.org/Errors?code="+o+" for the full message or use the non-minified dev environment for full errors. "}var u=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),p=function(){return Math.random().toString(36).substring(7).split("").join(".")},g={INIT:"@@redux/INIT"+p(),REPLACE:"@@redux/REPLACE"+p(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+p()}};function T(o){if(typeof o!="object"||o===null)return!1;for(var c=o;Object.getPrototypeOf(c)!==null;)c=Object.getPrototypeOf(c);return Object.getPrototypeOf(o)===c}function h(o){if(o===void 0)return"undefined";if(o===null)return"null";var c=typeof o;switch(c){case"boolean":case"string":case"number":case"symbol":case"function":return c}if(Array.isArray(o))return"array";if(f(o))return"date";if(y(o))return"error";var C=d(o);switch(C){case"Symbol":case"Promise":case"WeakMap":case"WeakSet":case"Map":case"Set":return C}return c.slice(8,-1).toLowerCase().replace(/\s/g,"")}function d(o){return typeof o.constructor=="function"?o.constructor.name:null}function y(o){return o instanceof Error||typeof o.message=="string"&&o.constructor&&typeof o.constructor.stackTraceLimit=="number"}function f(o){return o instanceof Date?!0:typeof o.toDateString=="function"&&typeof o.getDate=="function"&&typeof o.setDate=="function"}function E(o){var c=typeof o;return c}function m(o,c,C){var R;if(typeof c=="function"&&typeof C=="function"||typeof C=="function"&&typeof arguments[3]=="function")throw new Error(a(0));if(typeof c=="function"&&typeof C=="undefined"&&(C=c,c=void 0),typeof C!="undefined"){if(typeof C!="function")throw new Error(a(1));return C(m)(o,c)}if(typeof o!="function")throw new Error(a(2));var O=o,A=c,F=[],B=F,U=!1;function j(){B===F&&(B=F.slice())}function I(){if(U)throw new Error(a(3));return A}function D(K){if(typeof K!="function")throw new Error(a(4));if(U)throw new Error(a(5));var H=!0;return j(),B.push(K),function(){if(!!H){if(U)throw new Error(a(6));H=!1,j();var $=B.indexOf(K);B.splice($,1),F=null}}}function k(K){if(!T(K))throw new Error(a(7));if(typeof K.type=="undefined")throw new Error(a(8));if(U)throw new Error(a(9));try{U=!0,A=O(A,K)}finally{U=!1}for(var H=F=B,V=0;V<H.length;V++){var $=H[V];$()}return K}function x(K){if(typeof K!="function")throw new Error(a(10));O=K,k({type:g.REPLACE})}function W(){var K,H=D;return K={subscribe:function($){if(typeof $!="object"||$===null)throw new Error(a(11));function z(){$.next&&$.next(I())}z();var G=H(z);return{unsubscribe:G}}},K[u]=function(){return this},K}return k({type:g.INIT}),R={dispatch:k,subscribe:D,getState:I,replaceReducer:x},R[u]=W,R}function M(o){typeof console!="undefined"&&typeof console.error=="function"&&console.error(o);try{throw new Error(o)}catch(c){}}function N(o,c,C,R){var O=Object.keys(c),A=C&&C.type===g.INIT?"preloadedState argument passed to createStore":"previous state received by the reducer";if(O.length===0)return"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.";if(!T(o))return"The "+A+' has unexpected type of "'+E(o)+'". Expected argument to be an object with the following '+('keys: "'+O.join('", "')+'"');var F=Object.keys(o).filter(function(B){return!c.hasOwnProperty(B)&&!R[B]});if(F.forEach(function(B){R[B]=!0}),!(C&&C.type===g.REPLACE)&&F.length>0)return"Unexpected "+(F.length>1?"keys":"key")+" "+('"'+F.join('", "')+'" found in '+A+". ")+"Expected to find one of the known reducer keys instead: "+('"'+O.join('", "')+'". Unexpected keys will be ignored.')}function S(o){Object.keys(o).forEach(function(c){var C=o[c],R=C(void 0,{type:g.INIT});if(typeof R=="undefined")throw new Error(a(12));if(typeof C(void 0,{type:g.PROBE_UNKNOWN_ACTION()})=="undefined")throw new Error(a(13))})}function b(o){for(var c=Object.keys(o),C={},R=0;R<c.length;R++){var O=c[R];typeof o[O]=="function"&&(C[O]=o[O])}var A=Object.keys(C),F,B;try{S(C)}catch(U){B=U}return function(j,I){if(j===void 0&&(j={}),B)throw B;if(!1)var D;for(var k=!1,x={},W=0;W<A.length;W++){var K=A[W],H=C[K],V=j[K],$=H(V,I);if(typeof $=="undefined"){var z=I&&I.type;throw new Error(a(14))}x[K]=$,k=k||$!==V}return k=k||A.length!==Object.keys(j).length,k?x:j}}function P(o,c){return function(){return c(o.apply(this,arguments))}}function i(o,c){if(typeof o=="function")return P(o,c);if(typeof o!="object"||o===null)throw new Error(a(16));var C={};for(var R in o){var O=o[R];typeof O=="function"&&(C[R]=P(O,c))}return C}function t(){for(var o=arguments.length,c=new Array(o),C=0;C<o;C++)c[C]=arguments[C];return c.length===0?function(R){return R}:c.length===1?c[0]:c.reduce(function(R,O){return function(){return R(O.apply(void 0,arguments))}})}function l(){for(var o=arguments.length,c=new Array(o),C=0;C<o;C++)c[C]=arguments[C];return function(R){return function(){var O=R.apply(void 0,arguments),A=function(){throw new Error(a(15))},F={getState:O.getState,dispatch:function(){return A.apply(void 0,arguments)}},B=c.map(function(U){return U(F)});return A=t.apply(void 0,B)(O.dispatch),(0,s.Z)((0,s.Z)({},O),{},{dispatch:A})}}}function r(){}},45501:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(8e3)),p=a(e(98785)),g=a(e(56385)),T=e(14890),h=a(e(99610)),d=s(e(52234)),y=s(e(38359)),f=function(){function E(m){(0,p.default)(this,E),this.store=m||(0,T.createStore)(h.default),this.video=null,this.rootElement=null}return(0,g.default)(E,[{key:"getActions",value:function(){var M=this,N=this.store.dispatch,S=(0,u.default)({},d,y);function b(P){return function(){var t=P.apply(M,arguments);typeof t!="undefined"&&N(t)}}return Object.keys(S).filter(function(P){return typeof S[P]=="function"}).reduce(function(P,i){return P[i]=b(S[i]),P},{})}},{key:"getState",value:function(){return this.store.getState()}},{key:"subscribeToStateChange",value:function(M,N){N||(N=this.getState.bind(this));var S=N(),b=function(){var i=N();if(i!==S){var t=S;S=i,M(i,t)}};return this.store.subscribe(b)}},{key:"subscribeToOperationStateChange",value:function(M){var N=this;return this.subscribeToStateChange(M,function(){return N.getState().operation})}},{key:"subscribeToPlayerStateChange",value:function(M){var N=this;return this.subscribeToStateChange(M,function(){return N.getState().player})}}]),E}();n.default=f},52234:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.handleFullscreenChange=h,n.activate=d,n.userActivate=y,n.play=f,n.pause=E,n.togglePlay=m,n.seek=M,n.forward=N,n.replay=S,n.changeRate=b,n.changeVolume=P,n.mute=i,n.toggleFullscreen=t,n.USER_ACTIVATE=n.PLAYER_ACTIVATE=n.FULLSCREEN_CHANGE=n.OPERATE=void 0;var a=s(e(3387)),u="video-react/OPERATE";n.OPERATE=u;var p="video-react/FULLSCREEN_CHANGE";n.FULLSCREEN_CHANGE=p;var g="video-react/PLAYER_ACTIVATE";n.PLAYER_ACTIVATE=g;var T="video-react/USER_ACTIVATE";n.USER_ACTIVATE=T;function h(l){return{type:p,isFullscreen:l}}function d(l){return{type:g,activity:l}}function y(l){return{type:T,activity:l}}function f(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{action:"play",source:""};return this.video.play(),{type:u,operation:l}}function E(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{action:"pause",source:""};return this.video.pause(),{type:u,operation:l}}function m(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{action:"toggle-play",source:""};return this.video.togglePlay(),{type:u,operation:l}}function M(l){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{action:"seek",source:""};return this.video.seek(l),{type:u,operation:r}}function N(l){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{action:"forward-".concat(l),source:""};return this.video.forward(l),{type:u,operation:r}}function S(l){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{action:"replay-".concat(l),source:""};return this.video.replay(l),{type:u,operation:r}}function b(l){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{action:"change-rate",source:""};return this.video.playbackRate=l,{type:u,operation:r}}function P(l){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{action:"change-volume",source:""},o=l;return l<0&&(o=0),l>1&&(o=1),this.video.volume=o,{type:u,operation:r}}function i(l){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{action:l?"muted":"unmuted",source:""};return this.video.muted=l,{type:u,operation:r}}function t(l){return a.default.enabled?(a.default.isFullscreen?a.default.exit():a.default.request(this.rootElement),{type:u,operation:{action:"toggle-fullscreen",source:""}}):{type:p,isFullscreen:!l.isFullscreen}}},38359:function(v,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.handleLoadStart=O,n.handleCanPlay=A,n.handleWaiting=F,n.handleCanPlayThrough=B,n.handlePlaying=U,n.handlePlay=j,n.handlePause=I,n.handleEnd=D,n.handleSeeking=k,n.handleSeeked=x,n.handleDurationChange=W,n.handleTimeUpdate=K,n.handleVolumeChange=H,n.handleProgressChange=V,n.handleRateChange=$,n.handleSuspend=z,n.handleAbort=G,n.handleEmptied=Y,n.handleStalled=Z,n.handleLoadedMetaData=X,n.handleLoadedData=J,n.handleResize=Q,n.handleError=w,n.handleSeekingTime=q,n.handleEndSeeking=_,n.activateTextTrack=ee,n.ACTIVATE_TEXT_TRACK=n.ERROR=n.RESIZE=n.LOADED_DATA=n.LOADED_META_DATA=n.STALLED=n.EMPTIED=n.ABORT=n.SUSPEND=n.RATE_CHANGE=n.PROGRESS_CHANGE=n.VOLUME_CHANGE=n.TIME_UPDATE=n.DURATION_CHANGE=n.END_SEEKING=n.SEEKING_TIME=n.SEEKED=n.SEEKING=n.END=n.PAUSE=n.PLAY=n.PLAYING=n.CAN_PLAY_THROUGH=n.WAITING=n.CAN_PLAY=n.LOAD_START=void 0;var e="video-react/LOAD_START";n.LOAD_START=e;var s="video-react/CAN_PLAY";n.CAN_PLAY=s;var a="video-react/WAITING";n.WAITING=a;var u="video-react/CAN_PLAY_THROUGH";n.CAN_PLAY_THROUGH=u;var p="video-react/PLAYING";n.PLAYING=p;var g="video-react/PLAY";n.PLAY=g;var T="video-react/PAUSE";n.PAUSE=T;var h="video-react/END";n.END=h;var d="video-react/SEEKING";n.SEEKING=d;var y="video-react/SEEKED";n.SEEKED=y;var f="video-react/SEEKING_TIME";n.SEEKING_TIME=f;var E="video-react/END_SEEKING";n.END_SEEKING=E;var m="video-react/DURATION_CHANGE";n.DURATION_CHANGE=m;var M="video-react/TIME_UPDATE";n.TIME_UPDATE=M;var N="video-react/VOLUME_CHANGE";n.VOLUME_CHANGE=N;var S="video-react/PROGRESS_CHANGE";n.PROGRESS_CHANGE=S;var b="video-react/RATE_CHANGE";n.RATE_CHANGE=b;var P="video-react/SUSPEND";n.SUSPEND=P;var i="video-react/ABORT";n.ABORT=i;var t="video-react/EMPTIED";n.EMPTIED=t;var l="video-react/STALLED";n.STALLED=l;var r="video-react/LOADED_META_DATA";n.LOADED_META_DATA=r;var o="video-react/LOADED_DATA";n.LOADED_DATA=o;var c="video-react/RESIZE";n.RESIZE=c;var C="video-react/ERROR";n.ERROR=C;var R="video-react/ACTIVATE_TEXT_TRACK";n.ACTIVATE_TEXT_TRACK=R;function O(L){return{type:e,videoProps:L}}function A(L){return{type:s,videoProps:L}}function F(L){return{type:a,videoProps:L}}function B(L){return{type:u,videoProps:L}}function U(L){return{type:p,videoProps:L}}function j(L){return{type:g,videoProps:L}}function I(L){return{type:T,videoProps:L}}function D(L){return{type:h,videoProps:L}}function k(L){return{type:d,videoProps:L}}function x(L){return{type:y,videoProps:L}}function W(L){return{type:m,videoProps:L}}function K(L){return{type:M,videoProps:L}}function H(L){return{type:N,videoProps:L}}function V(L){return{type:S,videoProps:L}}function $(L){return{type:b,videoProps:L}}function z(L){return{type:P,videoProps:L}}function G(L){return{type:i,videoProps:L}}function Y(L){return{type:t,videoProps:L}}function Z(L){return{type:l,videoProps:L}}function X(L){return{type:r,videoProps:L}}function J(L){return{type:o,videoProps:L}}function Q(L){return{type:c,videoProps:L}}function w(L){return{type:C,videoProps:L}}function q(L){return{type:f,time:L}}function _(L){return{type:E,time:L}}function ee(L){return{type:R,textTrack:L}}},77423:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m={manager:y.default.object,className:y.default.string},M=function(N){(0,d.default)(S,N);function S(b,P){var i;return(0,u.default)(this,S),i=(0,g.default)(this,(0,T.default)(S).call(this,b,P)),i.timer=null,b.manager.subscribeToOperationStateChange(i.handleStateChange.bind((0,h.default)(i))),i.state={hidden:!0,operation:{}},i}return(0,p.default)(S,[{key:"handleStateChange",value:function(P,i){var t=this;P.count!==i.count&&P.operation.source==="shortcut"&&(this.timer&&(clearTimeout(this.timer),this.timer=null),this.setState({hidden:!1,count:P.count,operation:P.operation}),this.timer=setTimeout(function(){t.setState({hidden:!0}),t.timer=null},500))}},{key:"render",value:function(){if(this.state.operation.source!=="shortcut")return null;var P=this.state.hidden?{display:"none"}:null;return f.default.createElement("div",{className:(0,E.default)({"video-react-bezel":!0,"video-react-bezel-animation":this.state.count%2==0,"video-react-bezel-animation-alt":this.state.count%2==1},this.props.className),style:P,role:"status","aria-label":this.state.operation.action},f.default.createElement("div",{className:(0,E.default)("video-react-bezel-icon","video-react-bezel-icon-".concat(this.state.operation.action))}))}}]),S}(f.Component);n.default=M,M.propTypes=m,M.displayName="Bezel"},989:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m={actions:y.default.object,player:y.default.object,position:y.default.string,className:y.default.string},M={position:"left"},N=function(S){(0,d.default)(b,S);function b(P,i){var t;return(0,u.default)(this,b),t=(0,g.default)(this,(0,T.default)(b).call(this,P,i)),t.handleClick=t.handleClick.bind((0,h.default)(t)),t}return(0,p.default)(b,[{key:"componentDidMount",value:function(){}},{key:"handleClick",value:function(){var i=this.props.actions;i.play()}},{key:"render",value:function(){var i=this.props,t=i.player,l=i.position;return f.default.createElement("button",{className:(0,E.default)("video-react-button","video-react-big-play-button","video-react-big-play-button-".concat(l),this.props.className,{"big-play-button-hide":t.hasStarted||!t.currentSrc}),type:"button","aria-live":"polite",tabIndex:"0",onClick:this.handleClick},f.default.createElement("span",{className:"video-react-control-text"},"Play Video"))}}]),b}(f.Component);n.default=N,N.propTypes=m,N.defaultProps=M,N.displayName="BigPlayButton"},17191:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(45740)),p=a(e(8e3)),g=a(e(98785)),T=a(e(56385)),h=a(e(49781)),d=a(e(56156)),y=a(e(85049)),f=a(e(50535)),E=a(e(45697)),m=s(e(67294)),M=a(e(94184)),N={tagName:E.default.string,onClick:E.default.func.isRequired,onFocus:E.default.func,onBlur:E.default.func,className:E.default.string},S={tagName:"div"},b=function(P){(0,f.default)(i,P);function i(t,l){var r;return(0,g.default)(this,i),r=(0,h.default)(this,(0,d.default)(i).call(this,t,l)),r.handleClick=r.handleClick.bind((0,y.default)(r)),r.handleFocus=r.handleFocus.bind((0,y.default)(r)),r.handleBlur=r.handleBlur.bind((0,y.default)(r)),r.handleKeypress=r.handleKeypress.bind((0,y.default)(r)),r}return(0,T.default)(i,[{key:"componentWillUnmount",value:function(l){this.handleBlur(l)}},{key:"handleKeypress",value:function(l){(l.which===32||l.which===13)&&(l.preventDefault(),this.handleClick(l))}},{key:"handleClick",value:function(l){var r=this.props.onClick;r(l)}},{key:"handleFocus",value:function(l){document.addEventListener("keydown",this.handleKeypress),this.props.onFocus&&this.props.onFocus(l)}},{key:"handleBlur",value:function(l){document.removeEventListener("keydown",this.handleKeypress),this.props.onBlur&&this.props.onBlur(l)}},{key:"render",value:function(){var l=this.props.tagName,r=(0,p.default)({},this.props);return delete r.tagName,delete r.className,m.default.createElement(l,(0,u.default)({className:(0,M.default)(this.props.className),role:"button",tabIndex:"0",onClick:this.handleClick,onFocus:this.handleFocus,onBlur:this.handleBlur},r))}}]),i}(m.Component);n.default=b,b.propTypes=N,b.defaultProps=S,b.displayName="ClickableComponent"},6238:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=T;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g={player:a.default.object,className:a.default.string};function T(h){var d=h.player,y=h.className;return d.error?null:u.default.createElement("div",{className:(0,p.default)("video-react-loading-spinner",y)})}T.propTypes=g,T.displayName="LoadingSpinner"},97617:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(8e3)),p=a(e(610)),g=a(e(67701)),T=a(e(98785)),h=a(e(56385)),d=a(e(49781)),y=a(e(56156)),f=a(e(85049)),E=a(e(50535)),m=a(e(45697)),M=s(e(67294)),N=a(e(94184)),S=a(e(45501)),b=a(e(989)),P=a(e(6238)),i=a(e(78814)),t=a(e(20122)),l=a(e(77423)),r=a(e(19097)),o=a(e(7473)),c=s(e(87152)),C=e(6021),R=e(43453),O=a(e(3387)),A={children:m.default.any,width:m.default.oneOfType([m.default.string,m.default.number]),height:m.default.oneOfType([m.default.string,m.default.number]),fluid:m.default.bool,muted:m.default.bool,playsInline:m.default.bool,aspectRatio:m.default.string,className:m.default.string,videoId:m.default.string,startTime:m.default.number,loop:m.default.bool,autoPlay:m.default.bool,src:m.default.string,poster:m.default.string,preload:m.default.oneOf(["auto","metadata","none"]),onLoadStart:m.default.func,onWaiting:m.default.func,onCanPlay:m.default.func,onCanPlayThrough:m.default.func,onPlaying:m.default.func,onEnded:m.default.func,onSeeking:m.default.func,onSeeked:m.default.func,onPlay:m.default.func,onPause:m.default.func,onProgress:m.default.func,onDurationChange:m.default.func,onError:m.default.func,onSuspend:m.default.func,onAbort:m.default.func,onEmptied:m.default.func,onStalled:m.default.func,onLoadedMetadata:m.default.func,onLoadedData:m.default.func,onTimeUpdate:m.default.func,onRateChange:m.default.func,onVolumeChange:m.default.func,store:m.default.object},F={fluid:!0,muted:!1,playsInline:!1,preload:"auto",aspectRatio:"auto"},B=function(U){(0,E.default)(j,U);function j(I){var D;return(0,T.default)(this,j),D=(0,d.default)(this,(0,y.default)(j).call(this,I)),D.controlsHideTimer=null,D.video=null,D.manager=new S.default(I.store),D.actions=D.manager.getActions(),D.manager.subscribeToPlayerStateChange(D.handleStateChange.bind((0,f.default)(D))),D.getStyle=D.getStyle.bind((0,f.default)(D)),D.handleResize=D.handleResize.bind((0,f.default)(D)),D.getChildren=D.getChildren.bind((0,f.default)(D)),D.handleMouseMove=(0,R.throttle)(D.handleMouseMove.bind((0,f.default)(D)),250),D.handleMouseDown=D.handleMouseDown.bind((0,f.default)(D)),D.startControlsTimer=D.startControlsTimer.bind((0,f.default)(D)),D.handleFullScreenChange=D.handleFullScreenChange.bind((0,f.default)(D)),D.handleKeyDown=D.handleKeyDown.bind((0,f.default)(D)),D.handleFocus=D.handleFocus.bind((0,f.default)(D)),D.handleBlur=D.handleBlur.bind((0,f.default)(D)),D}return(0,h.default)(j,[{key:"componentDidMount",value:function(){this.handleResize(),window.addEventListener("resize",this.handleResize),O.default.addEventListener(this.handleFullScreenChange)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.handleResize),O.default.removeEventListener(this.handleFullScreenChange),this.controlsHideTimer&&window.clearTimeout(this.controlsHideTimer)}},{key:"getDefaultChildren",value:function(D){var k=this;return[M.default.createElement(t.default,{ref:function(W){k.video=W,k.manager.video=k.video},key:"video",order:0},D),M.default.createElement(i.default,{key:"poster-image",order:1}),M.default.createElement(P.default,{key:"loading-spinner",order:2}),M.default.createElement(l.default,{key:"bezel",order:3}),M.default.createElement(b.default,{key:"big-play-button",order:4}),M.default.createElement(o.default,{key:"control-bar",order:5}),M.default.createElement(r.default,{key:"shortcut",order:99})]}},{key:"getChildren",value:function(D){var k=D.className,x=D.children,W=(0,g.default)(D,["className","children"]),K=M.default.Children.toArray(this.props.children).filter(function(V){return!(0,R.isVideoChild)(V)}),H=this.getDefaultChildren(x);return(0,R.mergeAndSortChildren)(H,K,W)}},{key:"setWidthOrHeight",value:function(D,k,x){var W;typeof x=="string"?x==="auto"?W="auto":x.match(/\d+%/)&&(W=x):typeof x=="number"&&(W="".concat(x,"px")),Object.assign(D,(0,p.default)({},k,W))}},{key:"getStyle",value:function(){var D=this.props,k=D.fluid,x=D.aspectRatio,W=D.height,K=D.width,H=this.manager.getState(),V=H.player,$={},z,G,Y;x!==void 0&&x!=="auto"?Y=x:V.videoWidth?Y="".concat(V.videoWidth,":").concat(V.videoHeight):Y="16:9";var Z=Y.split(":"),X=Z[1]/Z[0];return K!==void 0?z=K:W!==void 0?z=W/X:z=V.videoWidth||400,W!==void 0?G=W:G=z*X,k?$.paddingTop="".concat(X*100,"%"):(this.setWidthOrHeight($,"width",z),this.setWidthOrHeight($,"height",G)),$}},{key:"getState",value:function(){return this.manager.getState()}},{key:"play",value:function(){this.video.play()}},{key:"pause",value:function(){this.video.pause()}},{key:"load",value:function(){this.video.load()}},{key:"addTextTrack",value:function(){var D;(D=this.video).addTextTrack.apply(D,arguments)}},{key:"canPlayType",value:function(){var D;(D=this.video).canPlayType.apply(D,arguments)}},{key:"seek",value:function(D){this.video.seek(D)}},{key:"forward",value:function(D){this.video.forward(D)}},{key:"replay",value:function(D){this.video.replay(D)}},{key:"toggleFullscreen",value:function(){this.video.toggleFullscreen()}},{key:"subscribeToStateChange",value:function(D){return this.manager.subscribeToPlayerStateChange(D)}},{key:"handleResize",value:function(){}},{key:"handleFullScreenChange",value:function(D){D.target===this.manager.rootElement&&this.actions.handleFullscreenChange(O.default.isFullscreen)}},{key:"handleMouseDown",value:function(){this.startControlsTimer()}},{key:"handleMouseMove",value:function(){this.startControlsTimer()}},{key:"handleKeyDown",value:function(){this.startControlsTimer()}},{key:"startControlsTimer",value:function(){var D=this,k=3e3;M.default.Children.forEach(this.props.children,function(x){if(!(!M.default.isValidElement(x)||x.type!==o.default)){var W=x.props.autoHideTime;typeof W=="number"&&(k=W)}}),this.actions.userActivate(!0),clearTimeout(this.controlsHideTimer),this.controlsHideTimer=setTimeout(function(){D.actions.userActivate(!1)},k)}},{key:"handleStateChange",value:function(D,k){D.isFullscreen!==k.isFullscreen&&(this.handleResize(),(0,C.focusNode)(this.manager.rootElement)),this.forceUpdate()}},{key:"handleFocus",value:function(){this.actions.activate(!0)}},{key:"handleBlur",value:function(){this.actions.activate(!1)}},{key:"render",value:function(){var D=this,k=this.props.fluid,x=this.manager.getState(),W=x.player,K=W.paused,H=W.hasStarted,V=W.waiting,$=W.seeking,z=W.isFullscreen,G=W.userActivity,Y=(0,u.default)({},this.props,{player:W,actions:this.actions,manager:this.manager,store:this.manager.store,video:this.video?this.video.video:null}),Z=this.getChildren(Y);return M.default.createElement("div",{className:(0,N.default)({"video-react-controls-enabled":!0,"video-react-has-started":H,"video-react-paused":K,"video-react-playing":!K,"video-react-waiting":V,"video-react-seeking":$,"video-react-fluid":k,"video-react-fullscreen":z,"video-react-user-inactive":!G,"video-react-user-active":G,"video-react-workinghover":!c.IS_IOS},"video-react",this.props.className),style:this.getStyle(),ref:function(J){D.manager.rootElement=J},role:"region",onTouchStart:this.handleMouseDown,onMouseDown:this.handleMouseDown,onTouchMove:this.handleMouseMove,onMouseMove:this.handleMouseMove,onKeyDown:this.handleKeyDown,onFocus:this.handleFocus,onBlur:this.handleBlur,tabIndex:"-1"},Z)}},{key:"playbackRate",get:function(){return this.video.playbackRate},set:function(D){this.video.playbackRate=D}},{key:"muted",get:function(){return this.video.muted},set:function(D){this.video.muted=D}},{key:"volume",get:function(){return this.video.volume},set:function(D){this.video.volume=D}},{key:"videoWidth",get:function(){return this.video.videoWidth}},{key:"videoHeight",get:function(){return this.video.videoHeight}}]),j}(M.Component);n.default=B,B.contextTypes={store:m.default.object},B.propTypes=A,B.defaultProps=F,B.displayName="Player"},78814:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g={poster:a.default.string,player:a.default.object,actions:a.default.object,className:a.default.string};function T(d){var y=d.poster,f=d.player,E=d.actions,m=d.className;return!y||f.hasStarted?null:u.default.createElement("div",{className:(0,p.default)("video-react-poster",m),style:{backgroundImage:'url("'.concat(y,'")')},onClick:function(){f.paused&&E.play()}})}T.propTypes=g,T.displayName="PosterImage";var h=T;n.default=h},19097:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(610)),u=s(e(76440)),p=s(e(98785)),g=s(e(56385)),T=s(e(49781)),h=s(e(56156)),d=s(e(85049)),y=s(e(50535)),f=e(67294),E=s(e(45697)),m=e(6021),M={clickable:E.default.bool,dblclickable:E.default.bool,manager:E.default.object,actions:E.default.object,player:E.default.object,shortcuts:E.default.array},N={clickable:!0,dblclickable:!0},S=function(b){(0,y.default)(P,b);function P(i,t){var l;return(0,p.default)(this,P),l=(0,T.default)(this,(0,h.default)(P).call(this,i,t)),l.defaultShortcuts=[{keyCode:32,handle:l.togglePlay},{keyCode:75,handle:l.togglePlay},{keyCode:70,handle:l.toggleFullscreen},{keyCode:37,handle:function(o,c){!o.hasStarted||c.replay(5,{action:"replay-5",source:"shortcut"})}},{keyCode:74,handle:function(o,c){!o.hasStarted||c.replay(10,{action:"replay-10",source:"shortcut"})}},{keyCode:39,handle:function(o,c){!o.hasStarted||c.forward(5,{action:"forward-5",source:"shortcut"})}},{keyCode:76,handle:function(o,c){!o.hasStarted||c.forward(10,{action:"forward-10",source:"shortcut"})}},{keyCode:36,handle:function(o,c){!o.hasStarted||c.seek(0)}},{keyCode:35,handle:function(o,c){!o.hasStarted||c.seek(o.duration)}},{keyCode:38,handle:function(o,c){var C=o.volume+.05;C>1&&(C=1),c.changeVolume(C,{action:"volume-up",source:"shortcut"})}},{keyCode:40,handle:function(o,c){var C=o.volume-.05;C<0&&(C=0);var R=C>0?"volume-down":"volume-off";c.changeVolume(C,{action:R,source:"shortcut"})}},{keyCode:190,shift:!0,handle:function(o,c){var C=o.playbackRate;C>=1.5?C=2:C>=1.25?C=1.5:C>=1?C=1.25:C>=.5?C=1:C>=.25?C=.5:C>=0&&(C=.25),c.changeRate(C,{action:"fast-forward",source:"shortcut"})}},{keyCode:188,shift:!0,handle:function(o,c){var C=o.playbackRate;C<=.5?C=.25:C<=1?C=.5:C<=1.25?C=1:C<=1.5?C=1.25:C<=2&&(C=1.5),c.changeRate(C,{action:"fast-rewind",source:"shortcut"})}}],l.shortcuts=(0,u.default)(l.defaultShortcuts),l.mergeShortcuts=l.mergeShortcuts.bind((0,d.default)(l)),l.handleKeyPress=l.handleKeyPress.bind((0,d.default)(l)),l.handleClick=l.handleClick.bind((0,d.default)(l)),l.handleDoubleClick=l.handleDoubleClick.bind((0,d.default)(l)),l}return(0,g.default)(P,[{key:"componentDidMount",value:function(){this.mergeShortcuts(),document.addEventListener("keydown",this.handleKeyPress),document.addEventListener("click",this.handleClick),document.addEventListener("dblclick",this.handleDoubleClick)}},{key:"componentDidUpdate",value:function(t){t.shortcuts!==this.props.shortcuts&&this.mergeShortcuts()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyPress),document.removeEventListener("click",this.handleClick),document.removeEventListener("dblclick",this.handleDoubleClick)}},{key:"mergeShortcuts",value:function(){var t=function(C){var R=C.keyCode,O=R===void 0?0:R,A=C.ctrl,F=A===void 0?!1:A,B=C.shift,U=B===void 0?!1:B,j=C.alt,I=j===void 0?!1:j;return"".concat(O,":").concat(F,":").concat(U,":").concat(I)},l=this.defaultShortcuts.reduce(function(c,C){return Object.assign(c,(0,a.default)({},t(C),C))},{}),r=(this.props.shortcuts||[]).reduce(function(c,C){var R=C.keyCode,O=C.handle;return R&&typeof O=="function"?Object.assign(c,(0,a.default)({},t(C),C)):c},l),o=function(C){var R=0,O=["ctrl","shift","alt"];return O.forEach(function(A){C[A]&&R++}),R};this.shortcuts=Object.keys(r).map(function(c){return r[c]}).sort(function(c,C){return o(C)-o(c)})}},{key:"togglePlay",value:function(t,l){t.paused?l.play({action:"play",source:"shortcut"}):l.pause({action:"pause",source:"shortcut"})}},{key:"toggleFullscreen",value:function(t,l){l.toggleFullscreen(t)}},{key:"handleKeyPress",value:function(t){var l=this.props,r=l.player,o=l.actions;if(!!r.isActive&&!(document.activeElement&&((0,m.hasClass)(document.activeElement,"video-react-control")||(0,m.hasClass)(document.activeElement,"video-react-menu-button-active")||(0,m.hasClass)(document.activeElement,"video-react-big-play-button")))){var c=t.keyCode||t.which,C=t.ctrlKey||t.metaKey,R=t.shiftKey,O=t.altKey,A=this.shortcuts.filter(function(F){return!(!F.keyCode||F.keyCode-c!=0||F.ctrl!==void 0&&F.ctrl!==C||F.shift!==void 0&&F.shift!==R||F.alt!==void 0&&F.alt!==O)})[0];A&&(A.handle(r,o),t.preventDefault())}}},{key:"canBeClicked",value:function(t,l){return!(!t.isActive||l.target.nodeName!=="VIDEO"||t.readyState!==4)}},{key:"handleClick",value:function(t){var l=this.props,r=l.player,o=l.actions,c=l.clickable;!this.canBeClicked(r,t)||!c||this.togglePlay(r,o)}},{key:"handleDoubleClick",value:function(t){var l=this.props,r=l.player,o=l.actions,c=l.dblclickable;!this.canBeClicked(r,t)||!c||this.toggleFullscreen(r,o)}},{key:"render",value:function(){return null}}]),P}(f.Component);n.default=S,S.propTypes=M,S.defaultProps=N,S.displayName="Shortcut"},21303:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m=s(e(6021)),M={className:y.default.string,onMouseDown:y.default.func,onMouseMove:y.default.func,stepForward:y.default.func,stepBack:y.default.func,sliderActive:y.default.func,sliderInactive:y.default.func,onMouseUp:y.default.func,onFocus:y.default.func,onBlur:y.default.func,onClick:y.default.func,getPercent:y.default.func,vertical:y.default.bool,children:y.default.node,label:y.default.string,valuenow:y.default.string,valuetext:y.default.string},N=function(S){(0,d.default)(b,S);function b(P,i){var t;return(0,u.default)(this,b),t=(0,g.default)(this,(0,T.default)(b).call(this,P,i)),t.handleMouseDown=t.handleMouseDown.bind((0,h.default)(t)),t.handleMouseMove=t.handleMouseMove.bind((0,h.default)(t)),t.handleMouseUp=t.handleMouseUp.bind((0,h.default)(t)),t.handleFocus=t.handleFocus.bind((0,h.default)(t)),t.handleBlur=t.handleBlur.bind((0,h.default)(t)),t.handleClick=t.handleClick.bind((0,h.default)(t)),t.handleKeyPress=t.handleKeyPress.bind((0,h.default)(t)),t.stepForward=t.stepForward.bind((0,h.default)(t)),t.stepBack=t.stepBack.bind((0,h.default)(t)),t.calculateDistance=t.calculateDistance.bind((0,h.default)(t)),t.getProgress=t.getProgress.bind((0,h.default)(t)),t.renderChildren=t.renderChildren.bind((0,h.default)(t)),t.state={active:!1},t}return(0,p.default)(b,[{key:"componentWillUnmount",value:function(){document.removeEventListener("mousemove",this.handleMouseMove,!0),document.removeEventListener("mouseup",this.handleMouseUp,!0),document.removeEventListener("touchmove",this.handleMouseMove,!0),document.removeEventListener("touchend",this.handleMouseUp,!0),document.removeEventListener("keydown",this.handleKeyPress,!0)}},{key:"getProgress",value:function(){var i=this.props.getPercent;if(!i)return 0;var t=i();return(typeof t!="number"||t<0||t===Infinity)&&(t=0),t}},{key:"handleMouseDown",value:function(i){var t=this.props.onMouseDown;document.addEventListener("mousemove",this.handleMouseMove,!0),document.addEventListener("mouseup",this.handleMouseUp,!0),document.addEventListener("touchmove",this.handleMouseMove,!0),document.addEventListener("touchend",this.handleMouseUp,!0),this.setState({active:!0}),this.props.sliderActive&&this.props.sliderActive(i),this.handleMouseMove(i),t&&t(i)}},{key:"handleMouseMove",value:function(i){var t=this.props.onMouseMove;t&&t(i)}},{key:"handleMouseUp",value:function(i){i.preventDefault();var t=this.props.onMouseUp;document.removeEventListener("mousemove",this.handleMouseMove,!0),document.removeEventListener("mouseup",this.handleMouseUp,!0),document.removeEventListener("touchmove",this.handleMouseMove,!0),document.removeEventListener("touchend",this.handleMouseUp,!0),this.setState({active:!1}),this.props.sliderInactive&&this.props.sliderInactive(i),t&&t(i)}},{key:"handleFocus",value:function(i){document.addEventListener("keydown",this.handleKeyPress,!0),this.props.onFocus&&this.props.onFocus(i)}},{key:"handleBlur",value:function(i){document.removeEventListener("keydown",this.handleKeyPress,!0),this.props.onBlur&&this.props.onBlur(i)}},{key:"handleClick",value:function(i){i.preventDefault(),this.props.onClick&&this.props.onClick(i)}},{key:"handleKeyPress",value:function(i){i.which===37||i.which===40?(i.preventDefault(),i.stopPropagation(),this.stepBack()):(i.which===38||i.which===39)&&(i.preventDefault(),i.stopPropagation(),this.stepForward())}},{key:"stepForward",value:function(){this.props.stepForward&&this.props.stepForward()}},{key:"stepBack",value:function(){this.props.stepBack&&this.props.stepBack()}},{key:"calculateDistance",value:function(i){var t=this.slider,l=m.getPointerPosition(t,i);return this.props.vertical?l.y:l.x}},{key:"renderChildren",value:function(){var i=this.getProgress(),t="".concat((i*100).toFixed(2),"%");return f.default.Children.map(this.props.children,function(l){return f.default.cloneElement(l,{progress:i,percentage:t})})}},{key:"render",value:function(){var i=this,t=this.props,l=t.vertical,r=t.label,o=t.valuenow,c=t.valuetext;return f.default.createElement("div",{className:(0,E.default)(this.props.className,{"video-react-slider-vertical":l,"video-react-slider-horizontal":!l,"video-react-sliding":this.state.active},"video-react-slider"),ref:function(R){i.slider=R},tabIndex:"0",role:"slider",onMouseDown:this.handleMouseDown,onTouchStart:this.handleMouseDown,onFocus:this.handleFocus,onBlur:this.handleBlur,onClick:this.handleClick,"aria-label":r||"","aria-valuenow":o||"","aria-valuetext":c||"","aria-valuemin":0,"aria-valuemax":100},this.renderChildren())}}]),b}(f.Component);n.default=N,N.propTypes=M,N.displayName="Slider"},20122:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(8e3)),p=a(e(98785)),g=a(e(56385)),T=a(e(49781)),h=a(e(56156)),d=a(e(85049)),y=a(e(50535)),f=a(e(45697)),E=s(e(67294)),m=a(e(94184)),M=e(43453),N={actions:f.default.object,player:f.default.object,children:f.default.any,startTime:f.default.number,loop:f.default.bool,muted:f.default.bool,autoPlay:f.default.bool,playsInline:f.default.bool,src:f.default.string,poster:f.default.string,className:f.default.string,preload:f.default.oneOf(["auto","metadata","none"]),crossOrigin:f.default.string,onLoadStart:f.default.func,onWaiting:f.default.func,onCanPlay:f.default.func,onCanPlayThrough:f.default.func,onPlaying:f.default.func,onEnded:f.default.func,onSeeking:f.default.func,onSeeked:f.default.func,onPlay:f.default.func,onPause:f.default.func,onProgress:f.default.func,onDurationChange:f.default.func,onError:f.default.func,onSuspend:f.default.func,onAbort:f.default.func,onEmptied:f.default.func,onStalled:f.default.func,onLoadedMetadata:f.default.func,onLoadedData:f.default.func,onTimeUpdate:f.default.func,onRateChange:f.default.func,onVolumeChange:f.default.func,onResize:f.default.func},S=function(b){(0,y.default)(P,b);function P(i){var t;return(0,p.default)(this,P),t=(0,T.default)(this,(0,h.default)(P).call(this,i)),t.video=null,t.play=t.play.bind((0,d.default)(t)),t.pause=t.pause.bind((0,d.default)(t)),t.seek=t.seek.bind((0,d.default)(t)),t.forward=t.forward.bind((0,d.default)(t)),t.replay=t.replay.bind((0,d.default)(t)),t.toggleFullscreen=t.toggleFullscreen.bind((0,d.default)(t)),t.getProperties=t.getProperties.bind((0,d.default)(t)),t.renderChildren=t.renderChildren.bind((0,d.default)(t)),t.handleLoadStart=t.handleLoadStart.bind((0,d.default)(t)),t.handleCanPlay=t.handleCanPlay.bind((0,d.default)(t)),t.handleCanPlayThrough=t.handleCanPlayThrough.bind((0,d.default)(t)),t.handlePlay=t.handlePlay.bind((0,d.default)(t)),t.handlePlaying=t.handlePlaying.bind((0,d.default)(t)),t.handlePause=t.handlePause.bind((0,d.default)(t)),t.handleEnded=t.handleEnded.bind((0,d.default)(t)),t.handleWaiting=t.handleWaiting.bind((0,d.default)(t)),t.handleSeeking=t.handleSeeking.bind((0,d.default)(t)),t.handleSeeked=t.handleSeeked.bind((0,d.default)(t)),t.handleFullscreenChange=t.handleFullscreenChange.bind((0,d.default)(t)),t.handleError=t.handleError.bind((0,d.default)(t)),t.handleSuspend=t.handleSuspend.bind((0,d.default)(t)),t.handleAbort=t.handleAbort.bind((0,d.default)(t)),t.handleEmptied=t.handleEmptied.bind((0,d.default)(t)),t.handleStalled=t.handleStalled.bind((0,d.default)(t)),t.handleLoadedMetaData=t.handleLoadedMetaData.bind((0,d.default)(t)),t.handleLoadedData=t.handleLoadedData.bind((0,d.default)(t)),t.handleTimeUpdate=t.handleTimeUpdate.bind((0,d.default)(t)),t.handleRateChange=t.handleRateChange.bind((0,d.default)(t)),t.handleVolumeChange=t.handleVolumeChange.bind((0,d.default)(t)),t.handleDurationChange=t.handleDurationChange.bind((0,d.default)(t)),t.handleProgress=(0,M.throttle)(t.handleProgress.bind((0,d.default)(t)),250),t.handleKeypress=t.handleKeypress.bind((0,d.default)(t)),t.handleTextTrackChange=t.handleTextTrackChange.bind((0,d.default)(t)),t}return(0,g.default)(P,[{key:"componentDidMount",value:function(){this.forceUpdate(),this.video&&this.video.textTracks&&(this.video.textTracks.onaddtrack=this.handleTextTrackChange,this.video.textTracks.onremovetrack=this.handleTextTrackChange)}},{key:"getProperties",value:function(){var t=this;return this.video?M.mediaProperties.reduce(function(l,r){return l[r]=t.video[r],l},{}):null}},{key:"handleTextTrackChange",value:function(){var t=this.props,l=t.actions,r=t.player;if(this.video&&this.video.textTracks){var o=Array.from(this.video.textTracks).find(function(c){return c.mode==="showing"});o!==r.activeTextTrack&&l.activateTextTrack(o)}}},{key:"play",value:function(){var t=this.video.play();t!==void 0&&t.catch(function(){}).then(function(){})}},{key:"pause",value:function(){var t=this.video.pause();t!==void 0&&t.catch(function(){}).then(function(){})}},{key:"load",value:function(){this.video.load()}},{key:"addTextTrack",value:function(){var t;(t=this.video).addTextTrack.apply(t,arguments)}},{key:"canPlayType",value:function(){var t;(t=this.video).canPlayType.apply(t,arguments)}},{key:"togglePlay",value:function(){this.video.paused?this.play():this.pause()}},{key:"seek",value:function(t){try{this.video.currentTime=t}catch(l){}}},{key:"forward",value:function(t){this.seek(this.video.currentTime+t)}},{key:"replay",value:function(t){this.forward(-t)}},{key:"toggleFullscreen",value:function(){var t=this.props,l=t.player,r=t.actions;r.toggleFullscreen(l)}},{key:"handleLoadStart",value:function(){var t=this.props,l=t.actions,r=t.onLoadStart;l.handleLoadStart(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleCanPlay",value:function(){var t=this.props,l=t.actions,r=t.onCanPlay;l.handleCanPlay(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleCanPlayThrough",value:function(){var t=this.props,l=t.actions,r=t.onCanPlayThrough;l.handleCanPlayThrough(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handlePlaying",value:function(){var t=this.props,l=t.actions,r=t.onPlaying;l.handlePlaying(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handlePlay",value:function(){var t=this.props,l=t.actions,r=t.onPlay;l.handlePlay(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handlePause",value:function(){var t=this.props,l=t.actions,r=t.onPause;l.handlePause(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleDurationChange",value:function(){var t=this.props,l=t.actions,r=t.onDurationChange;l.handleDurationChange(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleProgress",value:function(){var t=this.props,l=t.actions,r=t.onProgress;this.video&&l.handleProgressChange(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleEnded",value:function(){var t=this.props,l=t.loop,r=t.player,o=t.actions,c=t.onEnded;l?(this.seek(0),this.play()):r.paused||this.pause(),o.handleEnd(this.getProperties()),c&&c.apply(void 0,arguments)}},{key:"handleWaiting",value:function(){var t=this.props,l=t.actions,r=t.onWaiting;l.handleWaiting(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleSeeking",value:function(){var t=this.props,l=t.actions,r=t.onSeeking;l.handleSeeking(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleSeeked",value:function(){var t=this.props,l=t.actions,r=t.onSeeked;l.handleSeeked(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleFullscreenChange",value:function(){}},{key:"handleSuspend",value:function(){var t=this.props,l=t.actions,r=t.onSuspend;l.handleSuspend(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleAbort",value:function(){var t=this.props,l=t.actions,r=t.onAbort;l.handleAbort(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleEmptied",value:function(){var t=this.props,l=t.actions,r=t.onEmptied;l.handleEmptied(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleStalled",value:function(){var t=this.props,l=t.actions,r=t.onStalled;l.handleStalled(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleLoadedMetaData",value:function(){var t=this.props,l=t.actions,r=t.onLoadedMetadata,o=t.startTime;o&&o>0&&(this.video.currentTime=o),l.handleLoadedMetaData(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleLoadedData",value:function(){var t=this.props,l=t.actions,r=t.onLoadedData;l.handleLoadedData(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleTimeUpdate",value:function(){var t=this.props,l=t.actions,r=t.onTimeUpdate;l.handleTimeUpdate(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleRateChange",value:function(){var t=this.props,l=t.actions,r=t.onRateChange;l.handleRateChange(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleVolumeChange",value:function(){var t=this.props,l=t.actions,r=t.onVolumeChange;l.handleVolumeChange(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleError",value:function(){var t=this.props,l=t.actions,r=t.onError;l.handleError(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleResize",value:function(){var t=this.props,l=t.actions,r=t.onResize;l.handleResize(this.getProperties()),r&&r.apply(void 0,arguments)}},{key:"handleKeypress",value:function(){}},{key:"renderChildren",value:function(){var t=this,l=(0,u.default)({},this.props,{video:this.video});return this.video?E.default.Children.toArray(this.props.children).filter(M.isVideoChild).map(function(r){var o;if(typeof r.type=="string"){if(r.type==="source"){o=(0,u.default)({},r.props);var c=o.onError;o.onError=function(){c&&c.apply(void 0,arguments),t.handleError.apply(t,arguments)}}}else o=l;return E.default.cloneElement(r,o)}):null}},{key:"render",value:function(){var t=this,l=this.props,r=l.loop,o=l.poster,c=l.preload,C=l.src,R=l.autoPlay,O=l.playsInline,A=l.muted,F=l.crossOrigin,B=l.videoId;return E.default.createElement("video",{className:(0,m.default)("video-react-video",this.props.className),id:B,crossOrigin:F,ref:function(j){t.video=j},muted:A,preload:c,loop:r,playsInline:O,autoPlay:R,poster:o,src:C,onLoadStart:this.handleLoadStart,onWaiting:this.handleWaiting,onCanPlay:this.handleCanPlay,onCanPlayThrough:this.handleCanPlayThrough,onPlaying:this.handlePlaying,onEnded:this.handleEnded,onSeeking:this.handleSeeking,onSeeked:this.handleSeeked,onPlay:this.handlePlay,onPause:this.handlePause,onProgress:this.handleProgress,onDurationChange:this.handleDurationChange,onError:this.handleError,onSuspend:this.handleSuspend,onAbort:this.handleAbort,onEmptied:this.handleEmptied,onStalled:this.handleStalled,onLoadedMetadata:this.handleLoadedMetaData,onLoadedData:this.handleLoadedData,onTimeUpdate:this.handleTimeUpdate,onRateChange:this.handleRateChange,onVolumeChange:this.handleVolumeChange,tabIndex:"-1"},this.renderChildren())}},{key:"playbackRate",get:function(){return this.video.playbackRate},set:function(t){this.video.playbackRate=t}},{key:"muted",get:function(){return this.video.muted},set:function(t){this.video.muted=t}},{key:"volume",get:function(){return this.video.volume},set:function(t){t>1&&(t=1),t<0&&(t=0),this.video.volume=t}},{key:"videoWidth",get:function(){return this.video.videoWidth}},{key:"videoHeight",get:function(){return this.video.videoHeight}}]),P}(E.Component);n.default=S,S.propTypes=N,S.displayName="Video"},77557:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m=a(e(9077)),M={player:y.default.object,actions:y.default.object,className:y.default.string,offMenuText:y.default.string,showOffMenu:y.default.bool,kinds:y.default.array},N={offMenuText:"Off",showOffMenu:!0,kinds:["captions","subtitles"]},S=function(P){(0,d.default)(i,P);function i(t,l){var r;return(0,u.default)(this,i),r=(0,g.default)(this,(0,T.default)(i).call(this,t,l)),r.getTextTrackItems=r.getTextTrackItems.bind((0,h.default)(r)),r.updateState=r.updateState.bind((0,h.default)(r)),r.handleSelectItem=r.handleSelectItem.bind((0,h.default)(r)),r.state=r.getTextTrackItems(),r}return(0,p.default)(i,[{key:"componentDidUpdate",value:function(){this.updateState()}},{key:"getTextTrackItems",value:function(){var l=this.props,r=l.kinds,o=l.player,c=l.offMenuText,C=l.showOffMenu,R=o.textTracks,O=o.activeTextTrack,A={items:[],selectedIndex:0},F=Array.from(R||[]);return F.length===0||(C&&A.items.push({label:c||"Off",value:null}),F.forEach(function(B){r.length&&!r.includes(B.kind)||A.items.push({label:B.label,value:B.language})}),A.selectedIndex=A.items.findIndex(function(B){return O&&O.language===B.value}),A.selectedIndex===-1&&(A.selectedIndex=0)),A}},{key:"updateState",value:function(){var l=this.getTextTrackItems();(l.selectedIndex!==this.state.selectedIndex||!this.textTrackItemsAreEqual(l.items,this.state.items))&&this.setState(l)}},{key:"textTrackItemsAreEqual",value:function(l,r){if(l.length!==r.length)return!1;for(var o=0;o<l.length;o++)if(!r[o]||l[o].label!==r[o].label||l[o].value!==r[o].value)return!1;return!0}},{key:"handleSelectItem",value:function(l){var r=this.props,o=r.player,c=r.actions,C=r.showOffMenu,R=o.textTracks;Array.from(R).forEach(function(O,A){l===(C?A+1:A)?(O.mode="showing",c.activateTextTrack(O)):O.mode="hidden"})}},{key:"render",value:function(){var l=this.state,r=l.items,o=l.selectedIndex;return f.default.createElement(m.default,{className:(0,E.default)("video-react-closed-caption",this.props.className),onSelectItem:this.handleSelectItem,items:r,selectedIndex:o},f.default.createElement("span",{className:"video-react-control-text"},"Closed Caption"))}}]),i}(f.Component);S.propTypes=M,S.defaultProps=N,S.displayName="ClosedCaptionButton";var b=S;n.default=b},7473:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(67701)),p=a(e(98785)),g=a(e(56385)),T=a(e(49781)),h=a(e(56156)),d=a(e(85049)),y=a(e(50535)),f=a(e(45697)),E=s(e(67294)),m=a(e(94184)),M=a(e(54850)),N=a(e(17058)),S=a(e(50183)),b=a(e(9356)),P=a(e(93282)),i=a(e(61866)),t=a(e(66008)),l=a(e(29999)),r=a(e(24609)),o=a(e(90228)),c=a(e(44223)),C=e(43453),R={children:f.default.any,autoHide:f.default.bool,autoHideTime:f.default.number,disableDefaultControls:f.default.bool,disableCompletely:f.default.bool,className:f.default.string},O={autoHide:!0,disableCompletely:!1},A=function(F){(0,y.default)(B,F);function B(U){var j;return(0,p.default)(this,B),j=(0,T.default)(this,(0,h.default)(B).call(this,U)),j.getDefaultChildren=j.getDefaultChildren.bind((0,d.default)(j)),j.getFullChildren=j.getFullChildren.bind((0,d.default)(j)),j}return(0,g.default)(B,[{key:"getDefaultChildren",value:function(){return[E.default.createElement(N.default,{key:"play-toggle",order:1}),E.default.createElement(o.default,{key:"volume-menu-button",order:4}),E.default.createElement(t.default,{key:"current-time-display",order:5.1}),E.default.createElement(r.default,{key:"time-divider",order:5.2}),E.default.createElement(l.default,{key:"duration-display",order:5.3}),E.default.createElement(M.default,{key:"progress-control",order:6}),E.default.createElement(P.default,{key:"fullscreen-toggle",order:8})]}},{key:"getFullChildren",value:function(){return[E.default.createElement(N.default,{key:"play-toggle",order:1}),E.default.createElement(b.default,{key:"replay-control",order:2}),E.default.createElement(S.default,{key:"forward-control",order:3}),E.default.createElement(o.default,{key:"volume-menu-button",order:4}),E.default.createElement(t.default,{key:"current-time-display",order:5}),E.default.createElement(r.default,{key:"time-divider",order:6}),E.default.createElement(l.default,{key:"duration-display",order:7}),E.default.createElement(M.default,{key:"progress-control",order:8}),E.default.createElement(i.default,{key:"remaining-time-display",order:9}),E.default.createElement(c.default,{rates:[1,1.25,1.5,2],key:"playback-rate",order:10}),E.default.createElement(P.default,{key:"fullscreen-toggle",order:11})]}},{key:"getChildren",value:function(){var j=E.default.Children.toArray(this.props.children),I=this.props.disableDefaultControls?[]:this.getDefaultChildren(),D=this.props,k=D.className,x=(0,u.default)(D,["className"]);return(0,C.mergeAndSortChildren)(I,j,x)}},{key:"render",value:function(){var j=this.props,I=j.autoHide,D=j.className,k=j.disableCompletely,x=this.getChildren();return k?null:E.default.createElement("div",{className:(0,m.default)("video-react-control-bar",{"video-react-control-bar-auto-hide":I},D)},x)}}]),B}(E.Component);n.default=A,A.propTypes=R,A.defaultProps=O,A.displayName="ControlBar"},50183:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(82073)),u=(0,a.default)("forward");u.displayName="ForwardControl";var p=u;n.default=p},82073:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E={actions:y.default.object,className:y.default.string,seconds:y.default.oneOf([5,10,30])},m={seconds:10},M=function(S){var b=function(P){(0,d.default)(i,P);function i(t,l){var r;return(0,u.default)(this,i),r=(0,g.default)(this,(0,T.default)(i).call(this,t,l)),r.handleClick=r.handleClick.bind((0,h.default)(r)),r}return(0,p.default)(i,[{key:"handleClick",value:function(){var l=this.props,r=l.actions,o=l.seconds;S==="forward"?r.forward(o):r.replay(o)}},{key:"render",value:function(){var l=this,r=this.props,o=r.seconds,c=r.className,C=["video-react-control","video-react-button","video-react-icon"];return C.push("video-react-icon-".concat(S,"-").concat(o),"video-react-".concat(S,"-control")),c&&C.push(c),f.default.createElement("button",{ref:function(O){l.button=O},className:C.join(" "),type:"button",onClick:this.handleClick},f.default.createElement("span",{className:"video-react-control-text"},"".concat(S," ").concat(o," seconds")))}}]),i}(f.Component);return b.propTypes=E,b.defaultProps=m,b};n.default=M},93282:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m={actions:y.default.object,player:y.default.object,className:y.default.string},M=function(N){(0,d.default)(S,N);function S(b,P){var i;return(0,u.default)(this,S),i=(0,g.default)(this,(0,T.default)(S).call(this,b,P)),i.handleClick=i.handleClick.bind((0,h.default)(i)),i}return(0,p.default)(S,[{key:"handleClick",value:function(){var P=this.props,i=P.player,t=P.actions;t.toggleFullscreen(i)}},{key:"render",value:function(){var P=this,i=this.props,t=i.player,l=i.className;return f.default.createElement("button",{className:(0,E.default)(l,{"video-react-icon-fullscreen-exit":t.isFullscreen,"video-react-icon-fullscreen":!t.isFullscreen},"video-react-fullscreen-control video-react-control video-react-button video-react-icon"),ref:function(o){P.button=o},type:"button",tabIndex:"0",onClick:this.handleClick},f.default.createElement("span",{className:"video-react-control-text"},"Non-Fullscreen"))}}]),S}(f.Component);n.default=M,M.propTypes=m,M.displayName="FullscreenToggle"},97828:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=T;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g={duration:a.default.number,buffered:a.default.object,className:a.default.string};function T(h){var d=h.buffered,y=h.duration,f=h.className;if(!d||!d.length)return null;var E=d.end(d.length-1),m={};E>y&&(E=y);function M(t,l){var r=t/l||0;return"".concat((r>=1?1:r)*100,"%")}m.width=M(E,y);for(var N=[],S=0;S<d.length;S++){var b=d.start(S),P=d.end(S),i=u.default.createElement("div",{style:{left:M(b,E),width:M(P-b,E)},key:"part-".concat(S)});N.push(i)}return N.length===0&&(N=null),u.default.createElement("div",{style:m,className:(0,p.default)("video-react-load-progress",f)},u.default.createElement("span",{className:"video-react-control-text"},"Loaded: 0%"),N)}T.propTypes=g,T.displayName="LoadProgressBar"},36082:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g=e(43453);function T(d){var y=d.duration,f=d.mouseTime,E=d.className,m=d.text;if(!f.time)return null;var M=m||(0,g.formatTime)(f.time,y);return u.default.createElement("div",{className:(0,p.default)("video-react-mouse-display",E),style:{left:"".concat(f.position,"px")},"data-current-time":M})}T.propTypes={duration:a.default.number,mouseTime:a.default.object,className:a.default.string},T.displayName="MouseTimeDisplay";var h=T;n.default=h},73258:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=h;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g=e(43453),T={currentTime:a.default.number,duration:a.default.number,percentage:a.default.string,className:a.default.string};function h(d){var y=d.currentTime,f=d.duration,E=d.percentage,m=d.className;return u.default.createElement("div",{"data-current-time":(0,g.formatTime)(y,f),className:(0,p.default)("video-react-play-progress video-react-slider-bar",m),style:{width:E}},u.default.createElement("span",{className:"video-react-control-text"},"Progress: ".concat(E)))}h.propTypes=T,h.displayName="PlayProgressBar"},17058:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m={actions:y.default.object,player:y.default.object,className:y.default.string},M=function(N){(0,d.default)(S,N);function S(b,P){var i;return(0,u.default)(this,S),i=(0,g.default)(this,(0,T.default)(S).call(this,b,P)),i.handleClick=i.handleClick.bind((0,h.default)(i)),i}return(0,p.default)(S,[{key:"handleClick",value:function(){var P=this.props,i=P.actions,t=P.player;t.paused?i.play():i.pause()}},{key:"render",value:function(){var P=this,i=this.props,t=i.player,l=i.className,r=t.paused?"Play":"Pause";return f.default.createElement("button",{ref:function(c){P.button=c},className:(0,E.default)(l,{"video-react-play-control":!0,"video-react-control":!0,"video-react-button":!0,"video-react-paused":t.paused,"video-react-playing":!t.paused}),type:"button",tabIndex:"0",onClick:this.handleClick},f.default.createElement("span",{className:"video-react-control-text"},r))}}]),S}(f.Component);n.default=M,M.propTypes=m,M.displayName="PlayToggle"},26203:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(50535)),d=s(e(67294)),y=a(e(44223)),f=e(43453),E=function(m){(0,h.default)(M,m);function M(N,S){var b;return(0,u.default)(this,M),b=(0,g.default)(this,(0,T.default)(M).call(this,N,S)),(0,f.deprecatedWarning)("PlaybackRate","PlaybackRateMenuButton"),b}return(0,p.default)(M,[{key:"render",value:function(){return d.default.createElement(y.default,this.props)}}]),M}(d.Component);n.default=E,E.displayName="PlaybackRate"},44223:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m=a(e(9077)),M={player:y.default.object,actions:y.default.object,rates:y.default.array,className:y.default.string},N={rates:[2,1.5,1.25,1,.5,.25]},S=function(P){(0,d.default)(i,P);function i(t,l){var r;return(0,u.default)(this,i),r=(0,g.default)(this,(0,T.default)(i).call(this,t,l)),r.handleSelectItem=r.handleSelectItem.bind((0,h.default)(r)),r}return(0,p.default)(i,[{key:"handleSelectItem",value:function(l){var r=this.props,o=r.rates,c=r.actions;l>=0&&l<o.length&&c.changeRate(o[l])}},{key:"render",value:function(){var l=this.props,r=l.rates,o=l.player,c=r.map(function(R){return{label:"".concat(R,"x"),value:R}}),C=r.indexOf(o.playbackRate)||0;return f.default.createElement(m.default,{className:(0,E.default)("video-react-playback-rate",this.props.className),onSelectItem:this.handleSelectItem,items:c,selectedIndex:C},f.default.createElement("span",{className:"video-react-control-text"},"Playback Rate"),f.default.createElement("div",{className:"video-react-playback-rate-value"},"".concat(o.playbackRate.toFixed(2),"x")))}}]),i}(f.Component);S.propTypes=M,S.defaultProps=N,S.displayName="PlaybackRateMenuButton";var b=S;n.default=b},54850:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(45740)),p=a(e(98785)),g=a(e(56385)),T=a(e(49781)),h=a(e(56156)),d=a(e(85049)),y=a(e(50535)),f=a(e(45697)),E=s(e(67294)),m=a(e(94184)),M=s(e(6021)),N=a(e(25775)),S={player:f.default.object,className:f.default.string},b=function(P){(0,y.default)(i,P);function i(t,l){var r;return(0,p.default)(this,i),r=(0,T.default)(this,(0,h.default)(i).call(this,t,l)),r.state={mouseTime:{time:null,position:0}},r.handleMouseMoveThrottle=r.handleMouseMove.bind((0,d.default)(r)),r}return(0,g.default)(i,[{key:"handleMouseMove",value:function(l){if(!!l.pageX){var r=this.props.player.duration,o=this.seekBar,c=M.getPointerPosition(o,l).x*r,C=l.pageX-M.findElPosition(o).left;this.setState({mouseTime:{time:c,position:C}})}}},{key:"render",value:function(){var l=this,r=this.props.className;return E.default.createElement("div",{onMouseMove:this.handleMouseMoveThrottle,className:(0,m.default)("video-react-progress-control video-react-control",r)},E.default.createElement(N.default,(0,u.default)({mouseTime:this.state.mouseTime,ref:function(c){l.seekBar=c}},this.props)))}}]),i}(E.Component);n.default=b,b.propTypes=S,b.displayName="ProgressControl"},9356:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(82073)),u=(0,a.default)("replay");u.displayName="ReplayControl";var p=u;n.default=p},25775:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m=a(e(21303)),M=a(e(73258)),N=a(e(97828)),S=a(e(36082)),b=e(43453),P={player:y.default.object,mouseTime:y.default.object,actions:y.default.object,className:y.default.string},i=function(t){(0,d.default)(l,t);function l(r,o){var c;return(0,u.default)(this,l),c=(0,g.default)(this,(0,T.default)(l).call(this,r,o)),c.getPercent=c.getPercent.bind((0,h.default)(c)),c.getNewTime=c.getNewTime.bind((0,h.default)(c)),c.stepForward=c.stepForward.bind((0,h.default)(c)),c.stepBack=c.stepBack.bind((0,h.default)(c)),c.handleMouseDown=c.handleMouseDown.bind((0,h.default)(c)),c.handleMouseMove=c.handleMouseMove.bind((0,h.default)(c)),c.handleMouseUp=c.handleMouseUp.bind((0,h.default)(c)),c}return(0,p.default)(l,[{key:"componentDidMount",value:function(){}},{key:"componentDidUpdate",value:function(){}},{key:"getPercent",value:function(){var o=this.props.player,c=o.currentTime,C=o.seekingTime,R=o.duration,O=C||c,A=O/R;return A>=1?1:A}},{key:"getNewTime",value:function(o){var c=this.props.player.duration,C=this.slider.calculateDistance(o),R=C*c;return R===c?R-.1:R}},{key:"handleMouseDown",value:function(){}},{key:"handleMouseUp",value:function(o){var c=this.props.actions,C=this.getNewTime(o);c.seek(C),c.handleEndSeeking(C)}},{key:"handleMouseMove",value:function(o){var c=this.props.actions,C=this.getNewTime(o);c.handleSeekingTime(C)}},{key:"stepForward",value:function(){var o=this.props.actions;o.forward(5)}},{key:"stepBack",value:function(){var o=this.props.actions;o.replay(5)}},{key:"render",value:function(){var o=this,c=this.props,C=c.player,R=C.currentTime,O=C.seekingTime,A=C.duration,F=C.buffered,B=c.mouseTime,U=O||R;return f.default.createElement(m.default,{ref:function(I){o.slider=I},label:"video progress bar",className:(0,E.default)("video-react-progress-holder",this.props.className),valuenow:(this.getPercent()*100).toFixed(2),valuetext:(0,b.formatTime)(U,A),onMouseDown:this.handleMouseDown,onMouseMove:this.handleMouseMove,onMouseUp:this.handleMouseUp,getPercent:this.getPercent,stepForward:this.stepForward,stepBack:this.stepBack},f.default.createElement(N.default,{buffered:F,currentTime:U,duration:A}),f.default.createElement(S.default,{duration:A,mouseTime:B}),f.default.createElement(M.default,{currentTime:U,duration:A}))}}]),l}(f.Component);n.default=i,i.propTypes=P,i.displayName="SeekBar"},90228:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(45740)),p=a(e(98785)),g=a(e(56385)),T=a(e(49781)),h=a(e(56156)),d=a(e(85049)),y=a(e(50535)),f=a(e(45697)),E=s(e(67294)),m=a(e(94184)),M=a(e(83842)),N=a(e(31251)),S={player:f.default.object,actions:f.default.object,vertical:f.default.bool,className:f.default.string,alwaysShowVolume:f.default.bool},b={vertical:!1},P=function(t){(0,y.default)(l,t);function l(r,o){var c;return(0,p.default)(this,l),c=(0,T.default)(this,(0,h.default)(l).call(this,r,o)),c.state={active:!1},c.handleClick=c.handleClick.bind((0,d.default)(c)),c.handleFocus=c.handleFocus.bind((0,d.default)(c)),c.handleBlur=c.handleBlur.bind((0,d.default)(c)),c}return(0,g.default)(l,[{key:"handleClick",value:function(){var o=this.props,c=o.player,C=o.actions;C.mute(!c.muted)}},{key:"handleFocus",value:function(){this.setState({active:!0})}},{key:"handleBlur",value:function(){this.setState({active:!1})}},{key:"render",value:function(){var o=this.props,c=o.vertical,C=o.player,R=o.className,O=!c,A=this.volumeLevel;return E.default.createElement(M.default,{className:(0,m.default)(R,{"video-react-volume-menu-button-vertical":c,"video-react-volume-menu-button-horizontal":!c,"video-react-vol-muted":C.muted,"video-react-vol-0":A===0&&!C.muted,"video-react-vol-1":A===1,"video-react-vol-2":A===2,"video-react-vol-3":A===3,"video-react-slider-active":this.props.alwaysShowVolume||this.state.active,"video-react-lock-showing":this.props.alwaysShowVolume||this.state.active},"video-react-volume-menu-button"),onClick:this.handleClick,inline:O},E.default.createElement(N.default,(0,u.default)({onFocus:this.handleFocus,onBlur:this.handleBlur},this.props)))}},{key:"volumeLevel",get:function(){var o=this.props.player,c=o.volume,C=o.muted,R=3;return c===0||C?R=0:c<.33?R=1:c<.67&&(R=2),R}}]),l}(E.Component);P.propTypes=S,P.defaultProps=b,P.displayName="VolumeMenuButton";var i=P;n.default=i},60239:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E={children:y.default.any},m=function(M){(0,d.default)(N,M);function N(S,b){var P;return(0,u.default)(this,N),P=(0,g.default)(this,(0,T.default)(N).call(this,S,b)),P.handleClick=P.handleClick.bind((0,h.default)(P)),P}return(0,p.default)(N,[{key:"handleClick",value:function(b){b.preventDefault()}},{key:"render",value:function(){return f.default.createElement("div",{className:"video-react-menu video-react-lock-showing",role:"presentation",onClick:this.handleClick},f.default.createElement("ul",{className:"video-react-menu-content"},this.props.children))}}]),N}(f.Component);n.default=m,m.propTypes=E,m.displayName="Menu"},9077:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m=a(e(60239)),M=a(e(79533)),N=a(e(17191)),S={inline:y.default.bool,items:y.default.array,className:y.default.string,onSelectItem:y.default.func,children:y.default.any,selectedIndex:y.default.number},b=function(P){(0,d.default)(i,P);function i(t,l){var r;return(0,u.default)(this,i),r=(0,g.default)(this,(0,T.default)(i).call(this,t,l)),r.state={active:!1,activateIndex:t.selectedIndex||0},r.commitSelection=r.commitSelection.bind((0,h.default)(r)),r.activateMenuItem=r.activateMenuItem.bind((0,h.default)(r)),r.handleClick=r.handleClick.bind((0,h.default)(r)),r.renderMenu=r.renderMenu.bind((0,h.default)(r)),r.handleFocus=r.handleFocus.bind((0,h.default)(r)),r.handleBlur=r.handleBlur.bind((0,h.default)(r)),r.handleUpArrow=r.handleUpArrow.bind((0,h.default)(r)),r.handleDownArrow=r.handleDownArrow.bind((0,h.default)(r)),r.handleEscape=r.handleEscape.bind((0,h.default)(r)),r.handleReturn=r.handleReturn.bind((0,h.default)(r)),r.handleTab=r.handleTab.bind((0,h.default)(r)),r.handleKeyPress=r.handleKeyPress.bind((0,h.default)(r)),r.handleSelectItem=r.handleSelectItem.bind((0,h.default)(r)),r.handleIndexChange=r.handleIndexChange.bind((0,h.default)(r)),r}return(0,p.default)(i,[{key:"componentDidUpdate",value:function(l){l.selectedIndex!==this.props.selectedIndex&&this.activateMenuItem(this.props.selectedIndex)}},{key:"commitSelection",value:function(l){this.setState({activateIndex:l}),this.handleIndexChange(l)}},{key:"activateMenuItem",value:function(l){this.setState({activateIndex:l}),this.handleIndexChange(l)}},{key:"handleIndexChange",value:function(l){var r=this.props.onSelectItem;r(l)}},{key:"handleClick",value:function(){this.setState(function(l){return{active:!l.active}})}},{key:"handleFocus",value:function(){document.addEventListener("keydown",this.handleKeyPress)}},{key:"handleBlur",value:function(){this.setState({active:!1}),document.removeEventListener("keydown",this.handleKeyPress)}},{key:"handleUpArrow",value:function(l){var r=this.props.items;if(this.state.active){l.preventDefault();var o=this.state.activateIndex-1;o<0&&(o=r.length?r.length-1:0),this.activateMenuItem(o)}}},{key:"handleDownArrow",value:function(l){var r=this.props.items;if(this.state.active){l.preventDefault();var o=this.state.activateIndex+1;o>=r.length&&(o=0),this.activateMenuItem(o)}}},{key:"handleTab",value:function(l){this.state.active&&(l.preventDefault(),this.commitSelection(this.state.activateIndex))}},{key:"handleReturn",value:function(l){l.preventDefault(),this.state.active?this.commitSelection(this.state.activateIndex):this.setState({active:!0})}},{key:"handleEscape",value:function(){this.setState({active:!1,activateIndex:0})}},{key:"handleKeyPress",value:function(l){l.which===27?this.handleEscape(l):l.which===9?this.handleTab(l):l.which===13?this.handleReturn(l):l.which===38?this.handleUpArrow(l):l.which===40&&this.handleDownArrow(l)}},{key:"handleSelectItem",value:function(l){this.commitSelection(l)}},{key:"renderMenu",value:function(){var l=this;if(!this.state.active)return null;var r=this.props.items;return f.default.createElement(m.default,null,r.map(function(o,c){return f.default.createElement(M.default,{item:o,index:c,onSelectItem:l.handleSelectItem,activateIndex:l.state.activateIndex,key:"item-".concat(c++)})}))}},{key:"render",value:function(){var l=this,r=this.props,o=r.inline,c=r.className;return f.default.createElement(N.default,{className:(0,E.default)(c,{"video-react-menu-button-inline":!!o,"video-react-menu-button-popup":!o,"video-react-menu-button-active":this.state.active},"video-react-control video-react-button video-react-menu-button"),role:"button",tabIndex:"0",ref:function(R){l.menuButton=R},onClick:this.handleClick,onFocus:this.handleFocus,onBlur:this.handleBlur},this.props.children,this.renderMenu())}}]),i}(f.Component);n.default=b,b.propTypes=S,b.displayName="MenuButton"},79533:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E=a(e(94184)),m={item:y.default.object,index:y.default.number,activateIndex:y.default.number,onSelectItem:y.default.func},M=function(N){(0,d.default)(S,N);function S(b,P){var i;return(0,u.default)(this,S),i=(0,g.default)(this,(0,T.default)(S).call(this,b,P)),i.handleClick=i.handleClick.bind((0,h.default)(i)),i}return(0,p.default)(S,[{key:"handleClick",value:function(){var P=this.props,i=P.index,t=P.onSelectItem;t(i)}},{key:"render",value:function(){var P=this.props,i=P.item,t=P.index,l=P.activateIndex;return f.default.createElement("li",{className:(0,E.default)({"video-react-menu-item":!0,"video-react-selected":t===l}),role:"menuitem",onClick:this.handleClick},i.label,f.default.createElement("span",{className:"video-react-control-text"}))}}]),S}(f.Component);n.default=M,M.propTypes=m,M.displayName="MenuItem"},62387:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(98785)),p=a(e(56385)),g=a(e(49781)),T=a(e(56156)),h=a(e(85049)),d=a(e(50535)),y=a(e(45697)),f=s(e(67294)),E={player:y.default.object,children:y.default.any},m=function(M){(0,d.default)(N,M);function N(S,b){var P;return(0,u.default)(this,N),P=(0,g.default)(this,(0,T.default)(N).call(this,S,b)),P.handleClick=P.handleClick.bind((0,h.default)(P)),P}return(0,p.default)(N,[{key:"handleClick",value:function(b){b.preventDefault()}},{key:"render",value:function(){var b=this.props.children;return f.default.createElement("div",{className:"video-react-menu",onClick:this.handleClick},f.default.createElement("div",{className:"video-react-menu-content"},b))}}]),N}(f.Component);n.default=m,m.propTypes=E,m.displayName="Popup"},83842:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=E;var a=s(e(45740)),u=s(e(8e3)),p=s(e(45697)),g=s(e(67294)),T=s(e(94184)),h=s(e(17191)),d=s(e(62387)),y={inline:p.default.bool,onClick:p.default.func.isRequired,onFocus:p.default.func,onBlur:p.default.func,className:p.default.string},f={inline:!0};function E(m){var M=m.inline,N=m.className,S=(0,u.default)({},m);return delete S.children,delete S.inline,delete S.className,g.default.createElement(h.default,(0,a.default)({className:(0,T.default)(N,{"video-react-menu-button-inline":!!M,"video-react-menu-button-popup":!M},"video-react-control video-react-button video-react-menu-button")},S),g.default.createElement(d.default,m))}E.propTypes=y,E.defaultProps=f,E.displayName="PopupButton"},66008:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g=e(43453),T={player:a.default.object,className:a.default.string};function h(y){var f=y.player,E=f.currentTime,m=f.duration,M=y.className,N=(0,g.formatTime)(E,m);return u.default.createElement("div",{className:(0,p.default)("video-react-current-time video-react-time-control video-react-control",M)},u.default.createElement("div",{className:"video-react-current-time-display","aria-live":"off"},u.default.createElement("span",{className:"video-react-control-text"},"Current Time "),N))}h.propTypes=T,h.displayName="CurrentTimeDisplay";var d=h;n.default=d},29999:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g=e(43453),T={player:a.default.object,className:a.default.string};function h(y){var f=y.player.duration,E=y.className,m=(0,g.formatTime)(f);return u.default.createElement("div",{className:(0,p.default)(E,"video-react-duration video-react-time-control video-react-control")},u.default.createElement("div",{className:"video-react-duration-display","aria-live":"off"},u.default.createElement("span",{className:"video-react-control-text"},"Duration Time "),m))}h.propTypes=T,h.displayName="DurationDisplay";var d=h;n.default=d},61866:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g=e(43453),T={player:a.default.object,className:a.default.string};function h(y){var f=y.player,E=f.currentTime,m=f.duration,M=y.className,N=m-E,S=(0,g.formatTime)(N);return u.default.createElement("div",{className:(0,p.default)("video-react-remaining-time video-react-time-control video-react-control",M)},u.default.createElement("div",{className:"video-react-remaining-time-display","aria-live":"off"},u.default.createElement("span",{className:"video-react-control-text"},"Remaining Time "),"-".concat(S)))}h.propTypes=T,h.displayName="RemainingTimeDisplay";var d=h;n.default=d},24609:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=T;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g={separator:a.default.string,className:a.default.string};function T(h){var d=h.separator,y=h.className,f=d||"/";return u.default.createElement("div",{className:(0,p.default)("video-react-time-control video-react-time-divider",y),dir:"ltr"},u.default.createElement("div",null,u.default.createElement("span",null,f)))}T.propTypes=g,T.displayName="TimeDivider"},31251:function(v,n,e){"use strict";var s=e(17185),a=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(e(45740)),p=a(e(98785)),g=a(e(56385)),T=a(e(49781)),h=a(e(56156)),d=a(e(85049)),y=a(e(50535)),f=a(e(45697)),E=s(e(67294)),m=a(e(94184)),M=a(e(21303)),N=a(e(30685)),S={actions:f.default.object,player:f.default.object,className:f.default.string,onFocus:f.default.func,onBlur:f.default.func},b=function(i){(0,y.default)(t,i);function t(l,r){var o;return(0,p.default)(this,t),o=(0,T.default)(this,(0,h.default)(t).call(this,l,r)),o.state={percentage:"0%"},o.handleMouseMove=o.handleMouseMove.bind((0,d.default)(o)),o.handlePercentageChange=o.handlePercentageChange.bind((0,d.default)(o)),o.checkMuted=o.checkMuted.bind((0,d.default)(o)),o.getPercent=o.getPercent.bind((0,d.default)(o)),o.stepForward=o.stepForward.bind((0,d.default)(o)),o.stepBack=o.stepBack.bind((0,d.default)(o)),o.handleFocus=o.handleFocus.bind((0,d.default)(o)),o.handleBlur=o.handleBlur.bind((0,d.default)(o)),o.handleClick=o.handleClick.bind((0,d.default)(o)),o}return(0,g.default)(t,[{key:"componentDidMount",value:function(){}},{key:"getPercent",value:function(){var r=this.props.player;return r.muted?0:r.volume}},{key:"checkMuted",value:function(){var r=this.props,o=r.player,c=r.actions;o.muted&&c.mute(!1)}},{key:"handleMouseMove",value:function(r){var o=this.props.actions;this.checkMuted();var c=this.slider.calculateDistance(r);o.changeVolume(c)}},{key:"stepForward",value:function(){var r=this.props,o=r.player,c=r.actions;this.checkMuted(),c.changeVolume(o.volume+.1)}},{key:"stepBack",value:function(){var r=this.props,o=r.player,c=r.actions;this.checkMuted(),c.changeVolume(o.volume-.1)}},{key:"handleFocus",value:function(r){this.props.onFocus&&this.props.onFocus(r)}},{key:"handleBlur",value:function(r){this.props.onBlur&&this.props.onBlur(r)}},{key:"handlePercentageChange",value:function(r){r!==this.state.percentage&&this.setState({percentage:r})}},{key:"handleClick",value:function(r){r.stopPropagation()}},{key:"render",value:function(){var r=this,o=this.props,c=o.player,C=o.className,R=(c.volume*100).toFixed(2);return E.default.createElement(M.default,(0,u.default)({ref:function(A){r.slider=A},label:"volume level",valuenow:R,valuetext:"".concat(R,"%"),onMouseMove:this.handleMouseMove,onFocus:this.handleFocus,onBlur:this.handleBlur,onClick:this.handleClick,sliderActive:this.handleFocus,sliderInactive:this.handleBlur,getPercent:this.getPercent,onPercentageChange:this.handlePercentageChange,stepForward:this.stepForward,stepBack:this.stepBack},this.props,{className:(0,m.default)(C,"video-react-volume-bar video-react-slider-bar")}),E.default.createElement(N.default,this.props))}}]),t}(E.Component);b.propTypes=S,b.displayName="VolumeBar";var P=b;n.default=P},30685:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(45697)),u=s(e(67294)),p=s(e(94184)),g={percentage:a.default.string,vertical:a.default.bool,className:a.default.string},T={percentage:"100%",vertical:!1};function h(y){var f=y.percentage,E=y.vertical,m=y.className,M={};return E?M.height=f:M.width=f,u.default.createElement("div",{className:(0,p.default)(m,"video-react-volume-level"),style:M},u.default.createElement("span",{className:"video-react-control-text"}))}h.propTypes=g,h.defaultProps=T,h.displayName="VolumeLevel";var d=h;n.default=d},88330:function(v,n,e){"use strict";var s,a=e(17185),u=e(16237);s={value:!0},Object.defineProperty(n,"J5",{enumerable:!0,get:function(){return p.default}}),s={enumerable:!0,get:function(){return g.default}},Object.defineProperty(n,"sT",{enumerable:!0,get:function(){return T.default}}),s={enumerable:!0,get:function(){return h.default}},s={enumerable:!0,get:function(){return d.default}},s={enumerable:!0,get:function(){return y.default}},s={enumerable:!0,get:function(){return f.default}},s={enumerable:!0,get:function(){return E.default}},Object.defineProperty(n,"VS",{enumerable:!0,get:function(){return m.default}}),Object.defineProperty(n,"_h",{enumerable:!0,get:function(){return M.default}}),s={enumerable:!0,get:function(){return N.default}},s={enumerable:!0,get:function(){return S.default}},s={enumerable:!0,get:function(){return b.default}},s={enumerable:!0,get:function(){return P.default}},s={enumerable:!0,get:function(){return i.default}},s={enumerable:!0,get:function(){return t.default}},s={enumerable:!0,get:function(){return l.default}},s={enumerable:!0,get:function(){return r.default}},Object.defineProperty(n,"S1",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(n,"yk",{enumerable:!0,get:function(){return c.default}}),s={enumerable:!0,get:function(){return C.default}},s={enumerable:!0,get:function(){return R.default}},s={enumerable:!0,get:function(){return O.default}},Object.defineProperty(n,"od",{enumerable:!0,get:function(){return A.default}}),s={enumerable:!0,get:function(){return F.default}},Object.defineProperty(n,"TS",{enumerable:!0,get:function(){return B.default}}),s={enumerable:!0,get:function(){return U.default}},s={enumerable:!0,get:function(){return D.playerReducer}},s={enumerable:!0,get:function(){return D.operationReducer}},s=s=void 0;var p=u(e(97617)),g=u(e(20122)),T=u(e(989)),h=u(e(6238)),d=u(e(78814)),y=u(e(21303)),f=u(e(77423)),E=u(e(19097)),m=u(e(7473)),M=u(e(17058)),N=u(e(50183)),S=u(e(9356)),b=u(e(93282)),P=u(e(54850)),i=u(e(25775)),t=u(e(73258)),l=u(e(97828)),r=u(e(36082)),o=u(e(90228)),c=u(e(44223)),C=u(e(26203)),R=u(e(77557)),O=u(e(61866)),A=u(e(66008)),F=u(e(29999)),B=u(e(24609)),U=u(e(9077)),j=a(e(52234));s=j;var I=a(e(38359));s=I;var D=e(99610)},99610:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=p,n.operationReducer=n.playerReducer=void 0;var a=s(e(39256)),u=s(e(60945));function p(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},d=arguments.length>1?arguments[1]:void 0;return{player:(0,a.default)(h.player,d),operation:(0,u.default)(h.operation,d)}}var g=a.default;n.playerReducer=g;var T=u.default;n.operationReducer=T},60945:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=g;var a=s(e(8e3)),u=e(52234),p={count:0,operation:{action:"",source:""}};function g(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p,h=arguments.length>1?arguments[1]:void 0;switch(h.type){case u.OPERATE:return(0,a.default)({},T,{count:T.count+1,operation:(0,a.default)({},T.operation,h.operation)});default:return T}}},39256:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=T;var a=s(e(8e3)),u=e(38359),p=e(52234),g={currentSrc:null,duration:0,currentTime:0,seekingTime:0,buffered:null,waiting:!1,seeking:!1,paused:!0,autoPaused:!1,ended:!1,playbackRate:1,muted:!1,volume:1,readyState:0,networkState:0,videoWidth:0,videoHeight:0,hasStarted:!1,userActivity:!0,isActive:!1,isFullscreen:!1,activeTextTrack:null};function T(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:g,d=arguments.length>1?arguments[1]:void 0;switch(d.type){case p.USER_ACTIVATE:return(0,a.default)({},h,{userActivity:d.activity});case p.PLAYER_ACTIVATE:return(0,a.default)({},h,{isActive:d.activity});case p.FULLSCREEN_CHANGE:return(0,a.default)({},h,{isFullscreen:!!d.isFullscreen});case u.SEEKING_TIME:return(0,a.default)({},h,{seekingTime:d.time});case u.END_SEEKING:return(0,a.default)({},h,{seekingTime:0});case u.LOAD_START:return(0,a.default)({},h,d.videoProps,{hasStarted:!1,ended:!1});case u.CAN_PLAY:return(0,a.default)({},h,d.videoProps,{waiting:!1});case u.WAITING:return(0,a.default)({},h,d.videoProps,{waiting:!0});case u.CAN_PLAY_THROUGH:case u.PLAYING:return(0,a.default)({},h,d.videoProps,{waiting:!1});case u.PLAY:return(0,a.default)({},h,d.videoProps,{ended:!1,paused:!1,autoPaused:!1,waiting:!1,hasStarted:!0});case u.PAUSE:return(0,a.default)({},h,d.videoProps,{paused:!0});case u.END:return(0,a.default)({},h,d.videoProps,{ended:!0});case u.SEEKING:return(0,a.default)({},h,d.videoProps,{seeking:!0});case u.SEEKED:return(0,a.default)({},h,d.videoProps,{seeking:!1});case u.ERROR:return(0,a.default)({},h,d.videoProps,{error:"UNKNOWN ERROR",ended:!0});case u.DURATION_CHANGE:case u.TIME_UPDATE:case u.VOLUME_CHANGE:case u.PROGRESS_CHANGE:case u.RATE_CHANGE:case u.SUSPEND:case u.ABORT:case u.EMPTIED:case u.STALLED:case u.LOADED_META_DATA:case u.LOADED_DATA:case u.RESIZE:return(0,a.default)({},h,d.videoProps);case u.ACTIVATE_TEXT_TRACK:return(0,a.default)({},h,{activeTextTrack:d.textTrack});default:return h}}},87152:function(v,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.IS_IOS=n.IS_IPOD=n.IS_IPHONE=n.IS_IPAD=void 0;var e=typeof window!="undefined"&&window.navigator?window.navigator.userAgent:"",s=/iPad/i.test(e);n.IS_IPAD=s;var a=/iPhone/i.test(e)&&!s;n.IS_IPHONE=a;var u=/iPod/i.test(e);n.IS_IPOD=u;var p=a||s||u;n.IS_IOS=p},6021:function(v,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.findElPosition=e,n.getPointerPosition=s,n.blurNode=a,n.focusNode=u,n.hasClass=p;function e(g){var T;if(g.getBoundingClientRect&&g.parentNode&&(T=g.getBoundingClientRect()),!T)return{left:0,top:0};var h=document,d=h.body,y=h.documentElement,f=y.clientLeft||d.clientLeft||0,E=window.pageXOffset||d.scrollLeft,m=T.left+E-f,M=y.clientTop||d.clientTop||0,N=window.pageYOffset||d.scrollTop,S=T.top+N-M;return{left:Math.round(m),top:Math.round(S)}}function s(g,T){var h={},d=e(g),y=g.offsetWidth,f=g.offsetHeight,E=d.top,m=d.left,M=T.pageY,N=T.pageX;return T.changedTouches&&(N=T.changedTouches[0].pageX,M=T.changedTouches[0].pageY),h.y=Math.max(0,Math.min(1,(E-M+f)/f)),h.x=Math.max(0,Math.min(1,(N-m)/y)),h}function a(g){g&&g.blur&&g.blur()}function u(g){g&&g.focus&&g.focus()}function p(g,T){for(var h=g.className.split(" "),d=0;d<h.length;d++)if(h[d].toLowerCase()===T.toLowerCase())return!0;return!1}},3387:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=s(e(98785)),u=s(e(56385)),p=function(){function T(){(0,a.default)(this,T)}return(0,u.default)(T,[{key:"request",value:function(d){d.requestFullscreen?d.requestFullscreen():d.webkitRequestFullscreen?d.webkitRequestFullscreen():d.mozRequestFullScreen?d.mozRequestFullScreen():d.msRequestFullscreen&&d.msRequestFullscreen()}},{key:"exit",value:function(){document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen()}},{key:"addEventListener",value:function(d){document.addEventListener("fullscreenchange",d),document.addEventListener("webkitfullscreenchange",d),document.addEventListener("mozfullscreenchange",d),document.addEventListener("MSFullscreenChange",d)}},{key:"removeEventListener",value:function(d){document.removeEventListener("fullscreenchange",d),document.removeEventListener("webkitfullscreenchange",d),document.removeEventListener("mozfullscreenchange",d),document.removeEventListener("MSFullscreenChange",d)}},{key:"isFullscreen",get:function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement}},{key:"enabled",get:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled||document.msFullscreenEnabled}}]),T}(),g=new p;n.default=g},43453:function(v,n,e){"use strict";var s=e(16237);Object.defineProperty(n,"__esModule",{value:!0}),n.formatTime=h,n.isVideoChild=d,n.mergeAndSortChildren=E,n.deprecatedWarning=m,n.throttle=M,n.mediaProperties=void 0;var a=s(e(76440)),u=s(e(8e3)),p=s(e(67701)),g=s(e(67294)),T=Number.isNaN||function(S){return S!==S};function h(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:S,P=Math.floor(S%60),i=Math.floor(S/60%60),t=Math.floor(S/3600),l=Math.floor(b/60%60),r=Math.floor(b/3600);return(T(S)||S===Infinity)&&(t="-",i="-",P="-"),t=t>0||r>0?"".concat(t,":"):"",i="".concat((t||l>=10)&&i<10?"0".concat(i):i,":"),P=P<10?"0".concat(P):P,t+i+P}function d(S){return S.props&&S.props.isVideoChild?!0:S.type==="source"||S.type==="track"}var y=function(b,P){return b.filter(P)[0]},f=function(b,P){var i=b.type,t=P.type;return typeof i=="string"||typeof t=="string"?i===t:typeof i=="function"&&typeof t=="function"?i.displayName===t.displayName:!1};function E(S,b,P){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1,t=g.default.Children.toArray(b),l=P.order,r=(0,p.default)(P,["order"]);return t.filter(function(o){return!o.props.disabled}).concat(S.filter(function(o){return!y(t,function(c){return f(c,o)})})).map(function(o){var c=y(S,function(A){return f(A,o)}),C=c?c.props:{},R=(0,u.default)({},r,C,o.props),O=g.default.cloneElement(o,R,o.props.children);return O}).sort(function(o,c){return(o.props.order||i)-(c.props.order||i)})}function m(S,b){console.warn("WARNING: ".concat(S," will be deprecated soon! Please use ").concat(b," instead."))}function M(S,b){var P=arguments,i=!1;return function(){i||(S.apply(void 0,(0,a.default)(P)),i=!0,setTimeout(function(){i=!1},b))}}var N=["error","src","srcObject","currentSrc","crossOrigin","networkState","preload","buffered","readyState","seeking","currentTime","duration","paused","defaultPlaybackRate","playbackRate","played","seekable","ended","autoplay","loop","mediaGroup","controller","controls","volume","muted","defaultMuted","audioTracks","videoTracks","textTracks","width","height","videoWidth","videoHeight","poster"];n.mediaProperties=N},58269:function(v){function n(e,s){(s==null||s>e.length)&&(s=e.length);for(var a=0,u=new Array(s);a<s;a++)u[a]=e[a];return u}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},41475:function(v,n,e){var s=e(58269);function a(u){if(Array.isArray(u))return s(u)}v.exports=a,v.exports.__esModule=!0,v.exports.default=v.exports},85049:function(v){function n(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},98785:function(v){function n(e,s){if(!(e instanceof s))throw new TypeError("Cannot call a class as a function")}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},56385:function(v){function n(s,a){for(var u=0;u<a.length;u++){var p=a[u];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(s,p.key,p)}}function e(s,a,u){return a&&n(s.prototype,a),u&&n(s,u),Object.defineProperty(s,"prototype",{writable:!1}),s}v.exports=e,v.exports.__esModule=!0,v.exports.default=v.exports},610:function(v){function n(e,s,a){return s in e?Object.defineProperty(e,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[s]=a,e}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},45740:function(v){function n(){return v.exports=n=Object.assign||function(e){for(var s=1;s<arguments.length;s++){var a=arguments[s];for(var u in a)Object.prototype.hasOwnProperty.call(a,u)&&(e[u]=a[u])}return e},v.exports.__esModule=!0,v.exports.default=v.exports,n.apply(this,arguments)}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},56156:function(v){function n(e){return v.exports=n=Object.setPrototypeOf?Object.getPrototypeOf:function(a){return a.__proto__||Object.getPrototypeOf(a)},v.exports.__esModule=!0,v.exports.default=v.exports,n(e)}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},50535:function(v,n,e){var s=e(61889);function a(u,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function");u.prototype=Object.create(p&&p.prototype,{constructor:{value:u,writable:!0,configurable:!0}}),Object.defineProperty(u,"prototype",{writable:!1}),p&&s(u,p)}v.exports=a,v.exports.__esModule=!0,v.exports.default=v.exports},16237:function(v){function n(e){return e&&e.__esModule?e:{default:e}}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},17185:function(v,n,e){var s=e(65222).default;function a(p){if(typeof WeakMap!="function")return null;var g=new WeakMap,T=new WeakMap;return(a=function(d){return d?T:g})(p)}function u(p,g){if(!g&&p&&p.__esModule)return p;if(p===null||s(p)!=="object"&&typeof p!="function")return{default:p};var T=a(g);if(T&&T.has(p))return T.get(p);var h={},d=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var y in p)if(y!=="default"&&Object.prototype.hasOwnProperty.call(p,y)){var f=d?Object.getOwnPropertyDescriptor(p,y):null;f&&(f.get||f.set)?Object.defineProperty(h,y,f):h[y]=p[y]}return h.default=p,T&&T.set(p,h),h}v.exports=u,v.exports.__esModule=!0,v.exports.default=v.exports},9769:function(v){function n(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},88517:function(v){function n(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},8e3:function(v,n,e){var s=e(610);function a(u){for(var p=1;p<arguments.length;p++){var g=arguments[p]!=null?Object(arguments[p]):{},T=Object.keys(g);typeof Object.getOwnPropertySymbols=="function"&&T.push.apply(T,Object.getOwnPropertySymbols(g).filter(function(h){return Object.getOwnPropertyDescriptor(g,h).enumerable})),T.forEach(function(h){s(u,h,g[h])})}return u}v.exports=a,v.exports.__esModule=!0,v.exports.default=v.exports},67701:function(v,n,e){var s=e(73831);function a(u,p){if(u==null)return{};var g=s(u,p),T,h;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(u);for(h=0;h<d.length;h++)T=d[h],!(p.indexOf(T)>=0)&&(!Object.prototype.propertyIsEnumerable.call(u,T)||(g[T]=u[T]))}return g}v.exports=a,v.exports.__esModule=!0,v.exports.default=v.exports},73831:function(v){function n(e,s){if(e==null)return{};var a={},u=Object.keys(e),p,g;for(g=0;g<u.length;g++)p=u[g],!(s.indexOf(p)>=0)&&(a[p]=e[p]);return a}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},49781:function(v,n,e){var s=e(65222).default,a=e(85049);function u(p,g){if(g&&(s(g)==="object"||typeof g=="function"))return g;if(g!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return a(p)}v.exports=u,v.exports.__esModule=!0,v.exports.default=v.exports},61889:function(v){function n(e,s){return v.exports=n=Object.setPrototypeOf||function(u,p){return u.__proto__=p,u},v.exports.__esModule=!0,v.exports.default=v.exports,n(e,s)}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},76440:function(v,n,e){var s=e(41475),a=e(9769),u=e(10838),p=e(88517);function g(T){return s(T)||a(T)||u(T)||p()}v.exports=g,v.exports.__esModule=!0,v.exports.default=v.exports},65222:function(v){function n(e){return v.exports=n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(s){return typeof s}:function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},v.exports.__esModule=!0,v.exports.default=v.exports,n(e)}v.exports=n,v.exports.__esModule=!0,v.exports.default=v.exports},10838:function(v,n,e){var s=e(58269);function a(u,p){if(!!u){if(typeof u=="string")return s(u,p);var g=Object.prototype.toString.call(u).slice(8,-1);if(g==="Object"&&u.constructor&&(g=u.constructor.name),g==="Map"||g==="Set")return Array.from(u);if(g==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(g))return s(u,p)}}v.exports=a,v.exports.__esModule=!0,v.exports.default=v.exports}}]);
