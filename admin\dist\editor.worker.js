(function(){var Pn={155:function(ne){var F=ne.exports={},B,Y;function _e(){throw new Error("setTimeout has not been defined")}function re(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?B=setTimeout:B=_e}catch(P){B=_e}try{typeof clearTimeout=="function"?Y=clearTimeout:Y=re}catch(P){Y=re}})();function Me(P){if(B===setTimeout)return setTimeout(P,0);if((B===_e||!B)&&setTimeout)return B=setTimeout,setTimeout(P,0);try{return B(P,0)}catch(U){try{return B.call(null,P,0)}catch(G){return B.call(this,P,0)}}}function l1(P){if(Y===clearTimeout)return clearTimeout(P);if((Y===re||!Y)&&clearTimeout)return Y=clearTimeout,clearTimeout(P);try{return Y(P)}catch(U){try{return Y.call(null,P)}catch(G){return Y.call(this,P)}}}var X=[],fe=!1,ie,xe=-1;function u1(){!fe||!ie||(fe=!1,ie.length?X=ie.concat(X):xe=-1,X.length&&ye())}function ye(){if(!fe){var P=Me(u1);fe=!0;for(var U=X.length;U;){for(ie=X,X=[];++xe<U;)ie&&ie[xe].run();xe=-1,U=X.length}ie=null,fe=!1,l1(P)}}F.nextTick=function(P){var U=new Array(arguments.length-1);if(arguments.length>1)for(var G=1;G<arguments.length;G++)U[G-1]=arguments[G];X.push(new $e(P,U)),X.length===1&&!fe&&Me(ye)};function $e(P,U){this.fun=P,this.array=U}$e.prototype.run=function(){this.fun.apply(null,this.array)},F.title="browser",F.browser=!0,F.env={},F.argv=[],F.version="",F.versions={};function T(){}F.on=T,F.addListener=T,F.once=T,F.off=T,F.removeListener=T,F.removeAllListeners=T,F.emit=T,F.prependListener=T,F.prependOnceListener=T,F.listeners=function(P){return[]},F.binding=function(P){throw new Error("process.binding is not supported")},F.cwd=function(){return"/"},F.chdir=function(P){throw new Error("process.chdir is not supported")},F.umask=function(){return 0}}},j1={};function we(ne){var F=j1[ne];if(F!==void 0)return F.exports;var B=j1[ne]={exports:{}};return Pn[ne](B,B.exports,we),B.exports}(function(){we.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch(ne){if(typeof window=="object")return window}}()})();var Ri={};(function(){"use strict";class ne{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(t){setTimeout(()=>{throw t.stack?new Error(t.message+`

`+t.stack):t},0)}}emit(t){this.listeners.forEach(n=>{n(t)})}onUnexpectedError(t){this.unexpectedErrorHandler(t),this.emit(t)}onUnexpectedExternalError(t){this.unexpectedErrorHandler(t)}}const F=new ne;function B(e){Me(e)||F.onUnexpectedError(e)}function Y(e){Me(e)||F.onUnexpectedExternalError(e)}function _e(e){if(e instanceof Error){let{name:t,message:n}=e;const i=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:i}}return e}const re="Canceled";function Me(e){return e instanceof l1?!0:e instanceof Error&&e.name===re&&e.message===re}class l1 extends Error{constructor(){super(re);this.name=this.message}}function X(){const e=new Error(re);return e.name=e.message,e}function fe(e){return e?new Error(`Illegal argument: ${e}`):new Error("Illegal argument")}function ie(e){return e?new Error(`Illegal state: ${e}`):new Error("Illegal state")}class xe extends Error{constructor(t){super("NotSupported");t&&(this.message=t)}}function u1(e){const t=this;let n=!1,i;return function(){return n||(n=!0,i=e.apply(t,arguments)),i}}var ye;(function(e){function t(w){return w&&typeof w=="object"&&typeof w[Symbol.iterator]=="function"}e.is=t;const n=Object.freeze([]);function i(){return n}e.empty=i;function*o(w){yield w}e.single=o;function s(w){return w||n}e.from=s;function a(w){return!w||w[Symbol.iterator]().next().done===!0}e.isEmpty=a;function l(w){return w[Symbol.iterator]().next().value}e.first=l;function c(w,_){for(const N of w)if(_(N))return!0;return!1}e.some=c;function u(w,_){for(const N of w)if(_(N))return N}e.find=u;function*d(w,_){for(const N of w)_(N)&&(yield N)}e.filter=d;function*h(w,_){let N=0;for(const b of w)yield _(b,N++)}e.map=h;function*f(...w){for(const _ of w)for(const N of _)yield N}e.concat=f;function*y(w){for(const _ of w)for(const N of _)yield N}e.concatNested=y;function v(w,_,N){let b=N;for(const g of w)b=_(b,g);return b}e.reduce=v;function*L(w,_,N=w.length){for(_<0&&(_+=w.length),N<0?N+=w.length:N>w.length&&(N=w.length);_<N;_++)yield w[_]}e.slice=L;function k(w,_=Number.POSITIVE_INFINITY){const N=[];if(_===0)return[N,w];const b=w[Symbol.iterator]();for(let g=0;g<_;g++){const p=b.next();if(p.done)return[N,e.empty()];N.push(p.value)}return[N,{[Symbol.iterator](){return b}}]}e.consume=k;function M(w,_,N=(b,g)=>b===g){const b=w[Symbol.iterator](),g=_[Symbol.iterator]();for(;;){const p=b.next(),C=g.next();if(p.done!==C.done)return!1;if(p.done)return!0;if(!N(p.value,C.value))return!1}}e.equals=M})(ye||(ye={}));const $e=!1;let T=null;function P(e){T=e}if($e){const e="__is_disposable_tracked__";P(new class{trackDisposable(t){const n=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(n)},3e3)}setParent(t,n){if(t&&t!==Fe.None)try{t[e]=!0}catch(i){}}markAsDisposed(t){if(t&&t!==Fe.None)try{t[e]=!0}catch(n){}}markAsSingleton(t){}})}function U(e){return T==null||T.trackDisposable(e),e}function G(e){T==null||T.markAsDisposed(e)}function je(e,t){T==null||T.setParent(e,t)}function In(e,t){if(T)for(const n of e)T.setParent(n,t)}function Oi(e){return T==null||T.markAsSingleton(e),e}class Tn extends Error{constructor(t){super(`Encountered errors while disposing of store. Errors: [${t.join(", ")}]`);this.errors=t}}function Di(e){return typeof e.dispose=="function"&&e.dispose.length===0}function G1(e){if(ye.is(e)){let t=[];for(const n of e)if(n)try{n.dispose()}catch(i){t.push(i)}if(t.length===1)throw t[0];if(t.length>1)throw new Tn(t);return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function Kn(...e){const t=Ge(()=>G1(e));return In(e,t),t}function Ge(e){const t=U({dispose:u1(()=>{G(t),e()})});return t}class ve{constructor(){this._toDispose=new Set,this._isDisposed=!1,U(this)}dispose(){this._isDisposed||(G(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){try{G1(this._toDispose.values())}finally{this._toDispose.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return je(t,this),this._isDisposed?ve.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(t),t}}ve.DISABLE_DISPOSED_WARNING=!1;class Fe{constructor(){this._store=new ve,U(this),je(this._store,this)}dispose(){G(this),this._store.dispose()}_register(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(t)}}Fe.None=Object.freeze({dispose(){}});class Pi{constructor(){this._isDisposed=!1,U(this)}get value(){return this._isDisposed?void 0:this._value}set value(t){var n;this._isDisposed||t===this._value||((n=this._value)===null||n===void 0||n.dispose(),t&&je(t,this),this._value=t)}clear(){this.value=void 0}dispose(){var t;this._isDisposed=!0,G(this),(t=this._value)===null||t===void 0||t.dispose(),this._value=void 0}clearAndLeak(){const t=this._value;return this._value=void 0,t&&je(t,null),t}}class Vn{constructor(){this.dispose=()=>{},this.unset=()=>{},this.isset=()=>!1,U(this)}set(t){let n=t;return this.unset=()=>n=void 0,this.isset=()=>n!==void 0,this.dispose=()=>{n&&(n(),n=void 0,G(this))},this}}class Ii{constructor(t){this.object=t}dispose(){}}class x{constructor(t){this.element=t,this.next=x.Undefined,this.prev=x.Undefined}}x.Undefined=new x(void 0);class Q1{constructor(){this._first=x.Undefined,this._last=x.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===x.Undefined}clear(){let t=this._first;for(;t!==x.Undefined;){const n=t.next;t.prev=x.Undefined,t.next=x.Undefined,t=n}this._first=x.Undefined,this._last=x.Undefined,this._size=0}unshift(t){return this._insert(t,!1)}push(t){return this._insert(t,!0)}_insert(t,n){const i=new x(t);if(this._first===x.Undefined)this._first=i,this._last=i;else if(n){const s=this._last;this._last=i,i.prev=s,s.next=i}else{const s=this._first;this._first=i,i.next=s,s.prev=i}this._size+=1;let o=!1;return()=>{o||(o=!0,this._remove(i))}}shift(){if(this._first!==x.Undefined){const t=this._first.element;return this._remove(this._first),t}}pop(){if(this._last!==x.Undefined){const t=this._last.element;return this._remove(this._last),t}}_remove(t){if(t.prev!==x.Undefined&&t.next!==x.Undefined){const n=t.prev;n.next=t.next,t.next.prev=n}else t.prev===x.Undefined&&t.next===x.Undefined?(this._first=x.Undefined,this._last=x.Undefined):t.next===x.Undefined?(this._last=this._last.prev,this._last.next=x.Undefined):t.prev===x.Undefined&&(this._first=this._first.next,this._first.prev=x.Undefined);this._size-=1}*[Symbol.iterator](){let t=this._first;for(;t!==x.Undefined;)yield t.element,t=t.next}}var Y1=we(155),c1;const h1="en";let Re=!1,Oe=!1,Qe=!1,Bn=!1,Un=!1,Z1=!1,qn=!1,X1=!1,Wn=!1,Ye,d1=null,Hn,oe;const V=typeof self=="object"?self:typeof we.g=="object"?we.g:{};let q;typeof V.vscode!="undefined"&&typeof V.vscode.process!="undefined"?q=V.vscode.process:typeof Y1!="undefined"&&(q=Y1);const J1=typeof((c1=q==null?void 0:q.versions)===null||c1===void 0?void 0:c1.electron)=="string",zn=J1&&(q==null?void 0:q.type)==="renderer";if(typeof navigator=="object"&&!zn)oe=navigator.userAgent,Re=oe.indexOf("Windows")>=0,Oe=oe.indexOf("Macintosh")>=0,X1=(oe.indexOf("Macintosh")>=0||oe.indexOf("iPad")>=0||oe.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,Qe=oe.indexOf("Linux")>=0,Z1=!0,Ye=navigator.language,d1=Ye;else if(typeof q=="object"){Re=q.platform==="win32",Oe=q.platform==="darwin",Qe=q.platform==="linux",Bn=Qe&&!!q.env.SNAP&&!!q.env.SNAP_REVISION,qn=J1,Wn=!!q.env.CI||!!q.env.BUILD_ARTIFACTSTAGINGDIRECTORY,Ye=h1,d1=h1;const e=q.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e),n=t.availableLanguages["*"];Ye=t.locale,d1=n||h1,Hn=t._translationsConfigFile}catch(t){}Un=!0}else console.error("Unable to resolve platform.");let f1=0;Oe?f1=1:Re?f1=3:Qe&&(f1=2);const De=Re,$n=Oe,Ti=null,Ki=null,Vi=null,Bi=Z1&&typeof V.importScripts=="function",Ui=null,J=oe,qi=null,Wi=(()=>{if(typeof V.postMessage=="function"&&!V.importScripts){let e=[];V.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let i=0,o=e.length;i<o;i++){const s=e[i];if(s.id===n.data.vscodeScheduleAsyncWork){e.splice(i,1),s.callback();return}}});let t=0;return n=>{const i=++t;e.push({id:i,callback:n}),V.postMessage({vscodeScheduleAsyncWork:i},"*")}}return e=>setTimeout(e)})(),Hi=Oe||X1?2:Re?1:3;let et=!0,tt=!1;function zi(){if(!tt){tt=!0;const e=new Uint8Array(2);e[0]=1,e[1]=2,et=new Uint16Array(e.buffer)[0]===(2<<8)+1}return et}const jn=!!(J&&J.indexOf("Chrome")>=0),$i=!!(J&&J.indexOf("Firefox")>=0),ji=!!(!jn&&J&&J.indexOf("Safari")>=0),Gi=!!(J&&J.indexOf("Edg/")>=0),Qi=!!(J&&J.indexOf("Android")>=0),Gn=V.performance&&typeof V.performance.now=="function";class Ze{constructor(t){this._highResolution=Gn&&t,this._startTime=this._now(),this._stopTime=-1}static create(t=!0){return new Ze(t)}stop(){this._stopTime=this._now()}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}_now(){return this._highResolution?V.performance.now():Date.now()}}let nt=!1,Qn=!1;var Xe;(function(e){e.None=()=>Fe.None;function t(b){if(Qn){const{onListenerDidAdd:g}=b,p=Pe.create();let C=0;b.onListenerDidAdd=()=>{++C==2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),p.print()),g==null||g()}}}function n(b){return(g,p=null,C)=>{let S=!1,E;return E=b(m=>{if(!S)return E?E.dispose():S=!0,g.call(p,m)},null,C),S&&E.dispose(),E}}e.once=n;function i(b,g,p){return u((C,S=null,E)=>b(m=>C.call(S,g(m)),null,E),p)}e.map=i;function o(b,g,p){return u((C,S=null,E)=>b(m=>{g(m),C.call(S,m)},null,E),p)}e.forEach=o;function s(b,g,p){return u((C,S=null,E)=>b(m=>g(m)&&C.call(S,m),null,E),p)}e.filter=s;function a(b){return b}e.signal=a;function l(...b){return(g,p=null,C)=>Kn(...b.map(S=>S(E=>g.call(p,E),null,C)))}e.any=l;function c(b,g,p,C){let S=p;return i(b,E=>(S=g(S,E),S),C)}e.reduce=c;function u(b,g){let p;const C={onFirstListenerAdd(){p=b(S.fire,S)},onLastListenerRemove(){p.dispose()}};g||t(C);const S=new ee(C);return g&&g.add(S),S.event}function d(b,g,p=100,C=!1,S,E){let m,O,Ce,$1=0;const Dn={leakWarningThreshold:S,onFirstListenerAdd(){m=b(xi=>{$1++,O=g(O,xi),C&&!Ce&&(a1.fire(O),O=void 0),clearTimeout(Ce),Ce=setTimeout(()=>{const Fi=O;O=void 0,Ce=void 0,(!C||$1>1)&&a1.fire(Fi),$1=0},p)})},onLastListenerRemove(){m.dispose()}};E||t(Dn);const a1=new ee(Dn);return E&&E.add(a1),a1.event}e.debounce=d;function h(b,g=(C,S)=>C===S,p){let C=!0,S;return s(b,E=>{const m=C||!g(E,S);return C=!1,S=E,m},p)}e.latch=h;function f(b,g,p){return[e.filter(b,g,p),e.filter(b,C=>!g(C),p)]}e.split=f;function y(b,g=!1,p=[]){let C=p.slice(),S=b(O=>{C?C.push(O):m.fire(O)});const E=()=>{C&&C.forEach(O=>m.fire(O)),C=null},m=new ee({onFirstListenerAdd(){S||(S=b(O=>m.fire(O)))},onFirstListenerDidAdd(){C&&(g?setTimeout(E):E())},onLastListenerRemove(){S&&S.dispose(),S=null}});return m.event}e.buffer=y;class v{constructor(g){this.event=g}map(g){return new v(i(this.event,g))}forEach(g){return new v(o(this.event,g))}filter(g){return new v(s(this.event,g))}reduce(g,p){return new v(c(this.event,g,p))}latch(){return new v(h(this.event))}debounce(g,p=100,C=!1,S){return new v(d(this.event,g,p,C,S))}on(g,p,C){return this.event(g,p,C)}once(g,p,C){return n(this.event)(g,p,C)}}function L(b){return new v(b)}e.chain=L;function k(b,g,p=C=>C){const C=(...O)=>m.fire(p(...O)),S=()=>b.on(g,C),E=()=>b.removeListener(g,C),m=new ee({onFirstListenerAdd:S,onLastListenerRemove:E});return m.event}e.fromNodeEventEmitter=k;function M(b,g,p=C=>C){const C=(...O)=>m.fire(p(...O)),S=()=>b.addEventListener(g,C),E=()=>b.removeEventListener(g,C),m=new ee({onFirstListenerAdd:S,onLastListenerRemove:E});return m.event}e.fromDOMEventEmitter=M;function w(b){return new Promise(g=>n(b)(g))}e.toPromise=w;function _(b,g){return g(void 0),b(p=>g(p))}e.runAndSubscribe=_;function N(b,g){let p=null;function C(E){p==null||p.dispose(),p=new ve,g(E,p)}C(void 0);const S=b(E=>C(E));return Ge(()=>{S.dispose(),p==null||p.dispose()})}e.runAndSubscribeWithStore=N})(Xe||(Xe={}));class Je{constructor(t){this._listenerCount=0,this._invocationCount=0,this._elapsedOverall=0,this._name=`${t}_${Je._idPool++}`}start(t){this._stopWatch=new Ze(!0),this._listenerCount=t}stop(){if(this._stopWatch){const t=this._stopWatch.elapsed();this._elapsedOverall+=t,this._invocationCount+=1,console.info(`did FIRE ${this._name}: elapsed_ms: ${t.toFixed(5)}, listener: ${this._listenerCount} (elapsed_overall: ${this._elapsedOverall.toFixed(2)}, invocations: ${this._invocationCount})`),this._stopWatch=void 0}}}Je._idPool=0;let rt=-1;class Yn{constructor(t,n=Math.random().toString(18).slice(2,5)){this.customThreshold=t,this.name=n,this._warnCountdown=0}dispose(){this._stacks&&this._stacks.clear()}check(t,n){let i=rt;if(typeof this.customThreshold=="number"&&(i=this.customThreshold),i<=0||n<i)return;this._stacks||(this._stacks=new Map);const o=this._stacks.get(t.value)||0;if(this._stacks.set(t.value,o+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=i*.5;let s,a=0;for(const[l,c]of this._stacks)(!s||a<c)&&(s=l,a=c);console.warn(`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${a}):`),console.warn(s)}return()=>{const s=this._stacks.get(t.value)||0;this._stacks.set(t.value,s-1)}}}class Pe{constructor(t){this.value=t}static create(){var t;return new Pe((t=new Error().stack)!==null&&t!==void 0?t:"")}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}}class Zn{constructor(t,n,i){this.callback=t,this.callbackThis=n,this.stack=i,this.subscription=new Vn}invoke(t){this.callback.call(this.callbackThis,t)}}class ee{constructor(t){var n;this._disposed=!1,this._options=t,this._leakageMon=rt>0?new Yn(this._options&&this._options.leakWarningThreshold):void 0,this._perfMon=((n=this._options)===null||n===void 0?void 0:n._profName)?new Je(this._options._profName):void 0}dispose(){var t,n,i,o;if(!this._disposed){if(this._disposed=!0,this._listeners){if(nt){const s=Array.from(this._listeners);queueMicrotask(()=>{var a;for(const l of s)l.subscription.isset()&&(l.subscription.unset(),(a=l.stack)===null||a===void 0||a.print())})}this._listeners.clear()}(t=this._deliveryQueue)===null||t===void 0||t.clear(),(i=(n=this._options)===null||n===void 0?void 0:n.onLastListenerRemove)===null||i===void 0||i.call(n),(o=this._leakageMon)===null||o===void 0||o.dispose()}}get event(){return this._event||(this._event=(t,n,i)=>{var o,s,a;this._listeners||(this._listeners=new Q1);const l=this._listeners.isEmpty();l&&((o=this._options)===null||o===void 0?void 0:o.onFirstListenerAdd)&&this._options.onFirstListenerAdd(this);let c,u;this._leakageMon&&this._listeners.size>=30&&(u=Pe.create(),c=this._leakageMon.check(u,this._listeners.size+1)),nt&&(u=u!=null?u:Pe.create());const d=new Zn(t,n,u),h=this._listeners.push(d);l&&((s=this._options)===null||s===void 0?void 0:s.onFirstListenerDidAdd)&&this._options.onFirstListenerDidAdd(this),((a=this._options)===null||a===void 0?void 0:a.onListenerDidAdd)&&this._options.onListenerDidAdd(this,t,n);const f=d.subscription.set(()=>{c&&c(),this._disposed||(h(),this._options&&this._options.onLastListenerRemove&&(this._listeners&&!this._listeners.isEmpty()||this._options.onLastListenerRemove(this)))});return i instanceof ve?i.add(f):Array.isArray(i)&&i.push(f),f}),this._event}fire(t){var n,i;if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new Q1);for(let o of this._listeners)this._deliveryQueue.push([o,t]);for((n=this._perfMon)===null||n===void 0||n.start(this._deliveryQueue.size);this._deliveryQueue.size>0;){const[o,s]=this._deliveryQueue.shift();try{o.invoke(s)}catch(a){B(a)}}(i=this._perfMon)===null||i===void 0||i.stop()}}}class Yi extends null{constructor(t){super(t);this._isPaused=0,this._eventQueue=new LinkedList,this._mergeFn=t==null?void 0:t.merge}pause(){this._isPaused++}resume(){if(this._isPaused!==0&&--this._isPaused==0)if(this._mergeFn){const t=Array.from(this._eventQueue);this._eventQueue.clear(),super.fire(this._mergeFn(t))}else for(;!this._isPaused&&this._eventQueue.size!==0;)super.fire(this._eventQueue.shift())}fire(t){this._listeners&&(this._isPaused!==0?this._eventQueue.push(t):super.fire(t))}}class Zi extends null{constructor(t){var n;super(t);this._delay=(n=t.delay)!==null&&n!==void 0?n:100}fire(t){this._handle||(this.pause(),this._handle=setTimeout(()=>{this._handle=void 0,this.resume()},this._delay)),super.fire(t)}}class Xi{constructor(){this.buffers=[]}wrapEvent(t){return(n,i,o)=>t(s=>{const a=this.buffers[this.buffers.length-1];a?a.push(()=>n.call(i,s)):n.call(i,s)},void 0,o)}bufferEvents(t){const n=[];this.buffers.push(n);const i=t();return this.buffers.pop(),n.forEach(o=>o()),i}}class Ji{constructor(){this.listening=!1,this.inputEvent=Xe.None,this.inputEventListener=Disposable.None,this.emitter=new ee({onFirstListenerDidAdd:()=>{this.listening=!0,this.inputEventListener=this.inputEvent(this.emitter.fire,this.emitter)},onLastListenerRemove:()=>{this.listening=!1,this.inputEventListener.dispose()}}),this.event=this.emitter.event}set input(t){this.inputEvent=t,this.listening&&(this.inputEventListener.dispose(),this.inputEventListener=t(this.emitter.fire,this.emitter))}dispose(){this.inputEventListener.dispose(),this.emitter.dispose()}}function eo(e){return Array.isArray(e)}function Xn(e){return typeof e=="string"}function to(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)&&!(e instanceof RegExp)&&!(e instanceof Date)}function no(e){return typeof e=="number"&&!isNaN(e)}function ro(e){return!!e&&typeof e[Symbol.iterator]=="function"}function io(e){return e===!0||e===!1}function Jn(e){return typeof e=="undefined"}function oo(e){return!m1(e)}function m1(e){return Jn(e)||e===null}function so(e,t){if(!e)throw new Error(t?`Unexpected type, expected '${t}'`:"Unexpected type")}function ao(e){if(m1(e))throw new Error("Assertion Failed: argument is undefined or null");return e}function er(e){return typeof e=="function"}function lo(e,t){const n=Math.min(e.length,t.length);for(let i=0;i<n;i++)tr(e[i],t[i])}function tr(e,t){if(Xn(t)){if(typeof e!==t)throw new Error(`argument does not match constraint: typeof ${t}`)}else if(er(t)){try{if(e instanceof t)return}catch(n){}if(!m1(e)&&e.constructor===t||t.length===1&&t.call(void 0,e)===!0)return;throw new Error("argument does not match one of these constraints: arg instanceof constraint, arg.constructor === constraint, nor constraint(arg) === true")}}function nr(e){let t=[],n=Object.getPrototypeOf(e);for(;Object.prototype!==n;)t=t.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return t}function g1(e){const t=[];for(const n of nr(e))typeof e[n]=="function"&&t.push(n);return t}function rr(e,t){const n=o=>function(){const s=Array.prototype.slice.call(arguments,0);return t(o,s)};let i={};for(const o of e)i[o]=n(o);return i}function uo(e){return e===null?void 0:e}function ir(e,t="Unreachable"){throw new Error(t)}class or{constructor(t){this.computeFn=t,this.lastCache=void 0,this.lastArgKey=void 0}get(t){const n=JSON.stringify(t);return this.lastArgKey!==n&&(this.lastArgKey=n,this.lastCache=this.computeFn(t)),this.lastCache}}class it{constructor(t){this.executor=t,this._didRun=!1}getValue(){if(!this._didRun)try{this._value=this.executor()}catch(t){this._error=t}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}var ot;function co(e){return!e||typeof e!="string"?!0:e.trim().length===0}const sr=/{(\d+)}/g;function ho(e,...t){return t.length===0?e:e.replace(sr,function(n,i){const o=parseInt(i,10);return isNaN(o)||o<0||o>=t.length?n:t[o]})}function fo(e){return e.replace(/[<>&]/g,function(t){switch(t){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return t}})}function st(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function mo(e,t=" "){const n=ar(e,t);return lr(n,t)}function ar(e,t){if(!e||!t)return e;const n=t.length;if(n===0||e.length===0)return e;let i=0;for(;e.indexOf(t,i)===i;)i=i+n;return e.substring(i)}function lr(e,t){if(!e||!t)return e;const n=t.length,i=e.length;if(n===0||i===0)return e;let o=i,s=-1;for(;s=e.lastIndexOf(t,o-1),!(s===-1||s+n!==o);){if(s===0)return"";o=s}return e.substring(0,o)}function go(e){return e.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")}function po(e){return e.replace(/\*/g,"")}function bo(e,t,n={}){if(!e)throw new Error("Cannot create regex from empty string");t||(e=st(e)),n.wholeWord&&(/\B/.test(e.charAt(0))||(e="\\b"+e),/\B/.test(e.charAt(e.length-1))||(e=e+"\\b"));let i="";return n.global&&(i+="g"),n.matchCase||(i+="i"),n.multiline&&(i+="m"),n.unicode&&(i+="u"),new RegExp(e,i)}function Co(e){return e.source==="^"||e.source==="^$"||e.source==="$"||e.source==="^\\s*$"?!1:!!(e.exec("")&&e.lastIndex===0)}function wo(e){return(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")}function ur(e){return e.split(/\r\n|\r|\n/)}function cr(e){for(let t=0,n=e.length;t<n;t++){const i=e.charCodeAt(t);if(i!==32&&i!==9)return t}return-1}function _o(e,t=0,n=e.length){for(let i=t;i<n;i++){const o=e.charCodeAt(i);if(o!==32&&o!==9)return e.substring(t,i)}return e.substring(t,n)}function hr(e,t=e.length-1){for(let n=t;n>=0;n--){const i=e.charCodeAt(n);if(i!==32&&i!==9)return n}return-1}function yo(e,t){return e<t?-1:e>t?1:0}function dr(e,t,n=0,i=e.length,o=0,s=t.length){for(;n<i&&o<s;n++,o++){let c=e.charCodeAt(n),u=t.charCodeAt(o);if(c<u)return-1;if(c>u)return 1}const a=i-n,l=s-o;return a<l?-1:a>l?1:0}function vo(e,t){return p1(e,t,0,e.length,0,t.length)}function p1(e,t,n=0,i=e.length,o=0,s=t.length){for(;n<i&&o<s;n++,o++){let c=e.charCodeAt(n),u=t.charCodeAt(o);if(c===u)continue;if(c>=128||u>=128)return dr(e.toLowerCase(),t.toLowerCase(),n,i,o,s);at(c)&&(c-=32),at(u)&&(u-=32);const d=c-u;if(d!==0)return d}const a=i-n,l=s-o;return a<l?-1:a>l?1:0}function at(e){return e>=97&&e<=122}function lt(e){return e>=65&&e<=90}function So(e,t){return e.length===t.length&&p1(e,t)===0}function Lo(e,t){const n=t.length;return t.length>e.length?!1:p1(e,t,0,n)===0}function No(e,t){let n,i=Math.min(e.length,t.length);for(n=0;n<i;n++)if(e.charCodeAt(n)!==t.charCodeAt(n))return n;return i}function Eo(e,t){let n,i=Math.min(e.length,t.length);const o=e.length-1,s=t.length-1;for(n=0;n<i;n++)if(e.charCodeAt(o-n)!==t.charCodeAt(s-n))return n;return i}function Ie(e){return 55296<=e&&e<=56319}function Te(e){return 56320<=e&&e<=57343}function b1(e,t){return(e-55296<<10)+(t-56320)+65536}function ut(e,t,n){const i=e.charCodeAt(n);if(Ie(i)&&n+1<t){const o=e.charCodeAt(n+1);if(Te(o))return b1(i,o)}return i}function fr(e,t){const n=e.charCodeAt(t-1);if(Te(n)&&t>1){const i=e.charCodeAt(t-2);if(Ie(i))return b1(i,n)}return n}class C1{constructor(t,n=0){this._str=t,this._len=t.length,this._offset=n}get offset(){return this._offset}setOffset(t){this._offset=t}prevCodePoint(){const t=fr(this._str,this._offset);return this._offset-=t>=65536?2:1,t}nextCodePoint(){const t=ut(this._str,this._len,this._offset);return this._offset+=t>=65536?2:1,t}eol(){return this._offset>=this._len}}class ct{constructor(t,n=0){this._iterator=new C1(t,n)}get offset(){return this._iterator.offset}nextGraphemeLength(){const t=se.getInstance(),n=this._iterator,i=n.offset;let o=t.getGraphemeBreakType(n.nextCodePoint());for(;!n.eol();){const s=n.offset,a=t.getGraphemeBreakType(n.nextCodePoint());if(ht(o,a)){n.setOffset(s);break}o=a}return n.offset-i}prevGraphemeLength(){const t=se.getInstance(),n=this._iterator,i=n.offset;let o=t.getGraphemeBreakType(n.prevCodePoint());for(;n.offset>0;){const s=n.offset,a=t.getGraphemeBreakType(n.prevCodePoint());if(ht(a,o)){n.setOffset(s);break}o=a}return i-n.offset}eol(){return this._iterator.eol()}}function mr(e,t){return new ct(e,t).nextGraphemeLength()}function gr(e,t){return new ct(e,t).prevGraphemeLength()}function Ao(e,t){t>0&&Te(e.charCodeAt(t))&&t--;const n=t+mr(e,t);return[n-gr(e,n),n]}const pr=/(?:[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u0710\u0712-\u072F\u074D-\u07A5\u07B1-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u0858\u085E-\u088E\u08A0-\u08C9\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFD3D\uFD50-\uFDC7\uFDF0-\uFDFC\uFE70-\uFEFC]|\uD802[\uDC00-\uDD1B\uDD20-\uDE00\uDE10-\uDE35\uDE40-\uDEE4\uDEEB-\uDF35\uDF40-\uDFFF]|\uD803[\uDC00-\uDD23\uDE80-\uDEA9\uDEAD-\uDF45\uDF51-\uDF81\uDF86-\uDFF6]|\uD83A[\uDC00-\uDCCF\uDD00-\uDD43\uDD4B-\uDFFF]|\uD83B[\uDC00-\uDEBB])/;function ko(e){return pr.test(e)}const br=/^[\t\n\r\x20-\x7E]*$/;function Cr(e){return br.test(e)}const wr=/[\u2028\u2029]/;function Mo(e){return wr.test(e)}function xo(e){return e>=11904&&e<=55215||e>=63744&&e<=64255||e>=65281&&e<=65374}function _r(e){return e>=127462&&e<=127487||e===8986||e===8987||e===9200||e===9203||e>=9728&&e<=10175||e===11088||e===11093||e>=127744&&e<=128591||e>=128640&&e<=128764||e>=128992&&e<=129008||e>=129280&&e<=129535||e>=129648&&e<=129782}const Fo=String.fromCharCode(65279);function Ro(e){return!!(e&&e.length>0&&e.charCodeAt(0)===65279)}function Oo(e,t=!1){return e?(t&&(e=e.replace(/\\./g,"")),e.toLowerCase()!==e):!1}function Do(e){const t=90-65+1;return e=e%(2*t),e<t?String.fromCharCode(97+e):String.fromCharCode(65+e-t)}function ht(e,t){return e===0?t!==5&&t!==7:e===2&&t===3?!1:e===4||e===2||e===3||t===4||t===2||t===3?!0:!(e===8&&(t===8||t===9||t===11||t===12)||(e===11||e===9)&&(t===9||t===10)||(e===12||e===10)&&t===10||t===5||t===13||t===7||e===1||e===13&&t===14||e===6&&t===6)}class se{constructor(){this._data=yr()}static getInstance(){return se._INSTANCE||(se._INSTANCE=new se),se._INSTANCE}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const n=this._data,i=n.length/3;let o=1;for(;o<=i;)if(t<n[3*o])o=2*o;else if(t>n[3*o+1])o=2*o+1;else return n[3*o+2];return 0}}se._INSTANCE=null;function yr(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}function Po(e,t){if(e===0)return 0;const n=vr(e,t);if(n!==void 0)return n;const i=new C1(t,e);return i.prevCodePoint(),i.offset}function vr(e,t){const n=new C1(t,e);let i=n.prevCodePoint();for(;Sr(i)||i===65039||i===8419;){if(n.offset===0)return;i=n.prevCodePoint()}if(!_r(i))return;let o=n.offset;return o>0&&n.prevCodePoint()===8205&&(o=n.offset),o}function Sr(e){return 127995<=e&&e<=127999}const Io="\xA0";class Q{constructor(t){this.confusableDictionary=t}static getInstance(t){return Q.cache.get(Array.from(t))}static getLocales(){return Q._locales.getValue()}isAmbiguous(t){return this.confusableDictionary.has(t)}getPrimaryConfusable(t){return this.confusableDictionary.get(t)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}ot=Q,Q.ambiguousCharacterData=new it(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}')),Q.cache=new or(e=>{function t(u){const d=new Map;for(let h=0;h<u.length;h+=2)d.set(u[h],u[h+1]);return d}function n(u,d){const h=new Map(u);for(const[f,y]of d)h.set(f,y);return h}function i(u,d){if(!u)return d;const h=new Map;for(const[f,y]of u)d.has(f)&&h.set(f,y);return h}const o=ot.ambiguousCharacterData.getValue();let s=e.filter(u=>!u.startsWith("_")&&u in o);s.length===0&&(s=["_default"]);let a;for(const u of s){const d=t(o[u]);a=i(a,d)}const l=t(o._common),c=n(l,a);return new Q(c)}),Q._locales=new it(()=>Object.keys(Q.ambiguousCharacterData.getValue()).filter(e=>!e.startsWith("_")));class ae{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(ae.getRawData())),this._data}static isInvisibleCharacter(t){return ae.getData().has(t)}static get codePoints(){return ae.getData()}}ae._data=void 0;const dt="$initialize";let ft=!1;function To(e){!isWeb||(ft||(ft=!0,console.warn("Could not create web worker(s). Falling back to loading web worker code in main thread, which might cause UI freezes. Please see https://github.com/microsoft/monaco-editor#faq")),console.warn(e.message))}class Lr{constructor(t,n,i,o){this.vsWorker=t,this.req=n,this.method=i,this.args=o,this.type=0}}class mt{constructor(t,n,i,o){this.vsWorker=t,this.seq=n,this.res=i,this.err=o,this.type=1}}class Nr{constructor(t,n,i,o){this.vsWorker=t,this.req=n,this.eventName=i,this.arg=o,this.type=2}}class Er{constructor(t,n,i){this.vsWorker=t,this.req=n,this.event=i,this.type=3}}class Ar{constructor(t,n){this.vsWorker=t,this.req=n,this.type=4}}class gt{constructor(t){this._workerId=-1,this._handler=t,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(t){this._workerId=t}sendMessage(t,n){const i=String(++this._lastSentReq);return new Promise((o,s)=>{this._pendingReplies[i]={resolve:o,reject:s},this._send(new Lr(this._workerId,i,t,n))})}listen(t,n){let i=null;const o=new ee({onFirstListenerAdd:()=>{i=String(++this._lastSentReq),this._pendingEmitters.set(i,o),this._send(new Nr(this._workerId,i,t,n))},onLastListenerRemove:()=>{this._pendingEmitters.delete(i),this._send(new Ar(this._workerId,i)),i=null}});return o.event}handleMessage(t){!t||!t.vsWorker||this._workerId!==-1&&t.vsWorker!==this._workerId||this._handleMessage(t)}_handleMessage(t){switch(t.type){case 1:return this._handleReplyMessage(t);case 0:return this._handleRequestMessage(t);case 2:return this._handleSubscribeEventMessage(t);case 3:return this._handleEventMessage(t);case 4:return this._handleUnsubscribeEventMessage(t)}}_handleReplyMessage(t){if(!this._pendingReplies[t.seq]){console.warn("Got reply to unknown seq");return}let n=this._pendingReplies[t.seq];if(delete this._pendingReplies[t.seq],t.err){let i=t.err;t.err.$isError&&(i=new Error,i.name=t.err.name,i.message=t.err.message,i.stack=t.err.stack),n.reject(i);return}n.resolve(t.res)}_handleRequestMessage(t){let n=t.req;this._handler.handleMessage(t.method,t.args).then(i=>{this._send(new mt(this._workerId,n,i,void 0))},i=>{i.detail instanceof Error&&(i.detail=_e(i.detail)),this._send(new mt(this._workerId,n,void 0,_e(i)))})}_handleSubscribeEventMessage(t){const n=t.req,i=this._handler.handleEvent(t.eventName,t.arg)(o=>{this._send(new Er(this._workerId,n,o))});this._pendingEvents.set(n,i)}_handleEventMessage(t){if(!this._pendingEmitters.has(t.req)){console.warn("Got event for unknown req");return}this._pendingEmitters.get(t.req).fire(t.event)}_handleUnsubscribeEventMessage(t){if(!this._pendingEvents.has(t.req)){console.warn("Got unsubscribe for unknown req");return}this._pendingEvents.get(t.req).dispose(),this._pendingEvents.delete(t.req)}_send(t){let n=[];if(t.type===0)for(let i=0;i<t.args.length;i++)t.args[i]instanceof ArrayBuffer&&n.push(t.args[i]);else t.type===1&&t.res instanceof ArrayBuffer&&n.push(t.res);this._handler.sendMessage(t,n)}}class Ko extends null{constructor(t,n,i){super();let o=null;this._worker=this._register(t.create("vs/base/common/worker/simpleWorker",u=>{this._protocol.handleMessage(u)},u=>{o&&o(u)})),this._protocol=new gt({sendMessage:(u,d)=>{this._worker.postMessage(u,d)},handleMessage:(u,d)=>{if(typeof i[u]!="function")return Promise.reject(new Error("Missing method "+u+" on main thread host."));try{return Promise.resolve(i[u].apply(i,d))}catch(h){return Promise.reject(h)}},handleEvent:(u,d)=>{if(_1(u)){const h=i[u].call(i,d);if(typeof h!="function")throw new Error(`Missing dynamic event ${u} on main thread host.`);return h}if(w1(u)){const h=i[u];if(typeof h!="function")throw new Error(`Missing event ${u} on main thread host.`);return h}throw new Error(`Malformed event name ${u}`)}}),this._protocol.setWorkerId(this._worker.getId());let s=null;typeof globals.require!="undefined"&&typeof globals.require.getConfig=="function"?s=globals.require.getConfig():typeof globals.requirejs!="undefined"&&(s=globals.requirejs.s.contexts._.config);const a=types.getAllMethodNames(i);this._onModuleLoaded=this._protocol.sendMessage(dt,[this._worker.getId(),JSON.parse(JSON.stringify(s)),n,a]);const l=(u,d)=>this._request(u,d),c=(u,d)=>this._protocol.listen(u,d);this._lazyProxy=new Promise((u,d)=>{o=d,this._onModuleLoaded.then(h=>{u(pt(h,l,c))},h=>{d(h),this._onError("Worker failed to load "+n,h)})})}getProxyObject(){return this._lazyProxy}_request(t,n){return new Promise((i,o)=>{this._onModuleLoaded.then(()=>{this._protocol.sendMessage(t,n).then(i,o)},o)})}_onError(t,n){console.error(t),console.info(n)}}function w1(e){return e[0]==="o"&&e[1]==="n"&&lt(e.charCodeAt(2))}function _1(e){return/^onDynamic/.test(e)&&lt(e.charCodeAt(9))}function pt(e,t,n){const i=a=>function(){const l=Array.prototype.slice.call(arguments,0);return t(a,l)},o=a=>function(l){return n(a,l)};let s={};for(const a of e){if(_1(a)){s[a]=o(a);continue}if(w1(a)){s[a]=n(a,void 0);continue}s[a]=i(a)}return s}class bt{constructor(t,n){this._requestHandlerFactory=n,this._requestHandler=null,this._protocol=new gt({sendMessage:(i,o)=>{t(i,o)},handleMessage:(i,o)=>this._handleMessage(i,o),handleEvent:(i,o)=>this._handleEvent(i,o)})}onmessage(t){this._protocol.handleMessage(t)}_handleMessage(t,n){if(t===dt)return this.initialize(n[0],n[1],n[2],n[3]);if(!this._requestHandler||typeof this._requestHandler[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._requestHandler[t].apply(this._requestHandler,n))}catch(i){return Promise.reject(i)}}_handleEvent(t,n){if(!this._requestHandler)throw new Error("Missing requestHandler");if(_1(t)){const i=this._requestHandler[t].call(this._requestHandler,n);if(typeof i!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return i}if(w1(t)){const i=this._requestHandler[t];if(typeof i!="function")throw new Error(`Missing event ${t} on request handler.`);return i}throw new Error(`Malformed event name ${t}`)}initialize(t,n,i,o){this._protocol.setWorkerId(t);const s=pt(o,(a,l)=>this._protocol.sendMessage(a,l),(a,l)=>this._protocol.listen(a,l));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(s),Promise.resolve(g1(this._requestHandler))):(n&&(typeof n.baseUrl!="undefined"&&delete n.baseUrl,typeof n.paths!="undefined"&&typeof n.paths.vs!="undefined"&&delete n.paths.vs,typeof n.trustedTypesPolicy!==void 0&&delete n.trustedTypesPolicy,n.catchError=!0,V.require.config(n)),new Promise((a,l)=>{V.require([i],c=>{if(this._requestHandler=c.create(s),!this._requestHandler){l(new Error("No RequestHandler!"));return}a(g1(this._requestHandler))},l)}))}}function Vo(e){return new bt(e,null)}class le{constructor(t,n,i,o){this.originalStart=t,this.originalLength=n,this.modifiedStart=i,this.modifiedLength=o}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function Bo(e){return y1(e,0)}function y1(e,t){switch(typeof e){case"object":return e===null?te(349,t):Array.isArray(e)?Mr(e,t):xr(e,t);case"string":return v1(e,t);case"boolean":return kr(e,t);case"number":return te(e,t);case"undefined":return te(937,t);default:return te(617,t)}}function te(e,t){return(t<<5)-t+e|0}function kr(e,t){return te(e?433:863,t)}function v1(e,t){t=te(149417,t);for(let n=0,i=e.length;n<i;n++)t=te(e.charCodeAt(n),t);return t}function Mr(e,t){return t=te(104579,t),e.reduce((n,i)=>y1(i,n),t)}function xr(e,t){return t=te(181387,t),Object.keys(e).sort().reduce((n,i)=>(n=v1(i,n),y1(e[i],n)),t)}function S1(e,t,n=32){const i=n-t,o=~((1<<i)-1);return(e<<t|(o&e)>>>i)>>>0}function Ct(e,t=0,n=e.byteLength,i=0){for(let o=0;o<n;o++)e[t+o]=i}function Fr(e,t,n="0"){for(;e.length<t;)e=n+e;return e}function Ke(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(n=>n.toString(16).padStart(2,"0")).join(""):Fr((e>>>0).toString(16),t/4)}class L1{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(64+3),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(t){const n=t.length;if(n===0)return;const i=this._buff;let o=this._buffLen,s=this._leftoverHighSurrogate,a,l;for(s!==0?(a=s,l=-1,s=0):(a=t.charCodeAt(0),l=0);;){let c=a;if(Ie(a))if(l+1<n){const u=t.charCodeAt(l+1);Te(u)?(l++,c=b1(a,u)):c=65533}else{s=a;break}else Te(a)&&(c=65533);if(o=this._push(i,o,c),l++,l<n)a=t.charCodeAt(l);else break}this._buffLen=o,this._leftoverHighSurrogate=s}_push(t,n,i){return i<128?t[n++]=i:i<2048?(t[n++]=192|(i&1984)>>>6,t[n++]=128|(i&63)>>>0):i<65536?(t[n++]=224|(i&61440)>>>12,t[n++]=128|(i&4032)>>>6,t[n++]=128|(i&63)>>>0):(t[n++]=240|(i&1835008)>>>18,t[n++]=128|(i&258048)>>>12,t[n++]=128|(i&4032)>>>6,t[n++]=128|(i&63)>>>0),n>=64&&(this._step(),n-=64,this._totalLen+=64,t[0]=t[64+0],t[1]=t[64+1],t[2]=t[64+2]),n}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),Ke(this._h0)+Ke(this._h1)+Ke(this._h2)+Ke(this._h3)+Ke(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,Ct(this._buff,this._buffLen),this._buffLen>56&&(this._step(),Ct(this._buff));const t=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(t/4294967296),!1),this._buffDV.setUint32(60,t%4294967296,!1),this._step()}_step(){const t=L1._bigBlock32,n=this._buffDV;for(let h=0;h<64;h+=4)t.setUint32(h,n.getUint32(h,!1),!1);for(let h=64;h<320;h+=4)t.setUint32(h,S1(t.getUint32(h-12,!1)^t.getUint32(h-32,!1)^t.getUint32(h-56,!1)^t.getUint32(h-64,!1),1),!1);let i=this._h0,o=this._h1,s=this._h2,a=this._h3,l=this._h4,c,u,d;for(let h=0;h<80;h++)h<20?(c=o&s|~o&a,u=1518500249):h<40?(c=o^s^a,u=1859775393):h<60?(c=o&s|o&a|s&a,u=2400959708):(c=o^s^a,u=3395469782),d=S1(i,5)+c+l+u+t.getUint32(h*4,!1)&4294967295,l=a,a=s,s=S1(o,30),o=i,i=d;this._h0=this._h0+i&4294967295,this._h1=this._h1+o&4294967295,this._h2=this._h2+s&4294967295,this._h3=this._h3+a&4294967295,this._h4=this._h4+l&4294967295}}L1._bigBlock32=new DataView(new ArrayBuffer(320));class wt{constructor(t){this.source=t}getElements(){const t=this.source,n=new Int32Array(t.length);for(let i=0,o=t.length;i<o;i++)n[i]=t.charCodeAt(i);return n}}function Rr(e,t,n){return new ue(new wt(e),new wt(t)).ComputeDiff(n).changes}class Se{static Assert(t,n){if(!t)throw new Error(n)}}class Le{static Copy(t,n,i,o,s){for(let a=0;a<s;a++)i[o+a]=t[n+a]}static Copy2(t,n,i,o,s){for(let a=0;a<s;a++)i[o+a]=t[n+a]}}class _t{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new le(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_originalCount++}AddModifiedElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class ue{constructor(t,n,i=null){this.ContinueProcessingPredicate=i,this._originalSequence=t,this._modifiedSequence=n;const[o,s,a]=ue._getElements(t),[l,c,u]=ue._getElements(n);this._hasStrings=a&&u,this._originalStringElements=o,this._originalElementsOrHash=s,this._modifiedStringElements=l,this._modifiedElementsOrHash=c,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(t){return t.length>0&&typeof t[0]=="string"}static _getElements(t){const n=t.getElements();if(ue._isStringArray(n)){const i=new Int32Array(n.length);for(let o=0,s=n.length;o<s;o++)i[o]=v1(n[o],0);return[n,i,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}ElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._modifiedStringElements[n]:!0}ElementsAreStrictEqual(t,n){if(!this.ElementsAreEqual(t,n))return!1;const i=ue._getStrictElement(this._originalSequence,t),o=ue._getStrictElement(this._modifiedSequence,n);return i===o}static _getStrictElement(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}OriginalElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._originalElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._originalStringElements[n]:!0}ModifiedElementsAreEqual(t,n){return this._modifiedElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._modifiedStringElements[t]===this._modifiedStringElements[n]:!0}ComputeDiff(t){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,t)}_ComputeDiff(t,n,i,o,s){const a=[!1];let l=this.ComputeDiffRecursive(t,n,i,o,a);return s&&(l=this.PrettifyChanges(l)),{quitEarly:a[0],changes:l}}ComputeDiffRecursive(t,n,i,o,s){for(s[0]=!1;t<=n&&i<=o&&this.ElementsAreEqual(t,i);)t++,i++;for(;n>=t&&o>=i&&this.ElementsAreEqual(n,o);)n--,o--;if(t>n||i>o){let h;return i<=o?(Se.Assert(t===n+1,"originalStart should only be one more than originalEnd"),h=[new le(t,0,i,o-i+1)]):t<=n?(Se.Assert(i===o+1,"modifiedStart should only be one more than modifiedEnd"),h=[new le(t,n-t+1,i,0)]):(Se.Assert(t===n+1,"originalStart should only be one more than originalEnd"),Se.Assert(i===o+1,"modifiedStart should only be one more than modifiedEnd"),h=[]),h}const a=[0],l=[0],c=this.ComputeRecursionPoint(t,n,i,o,a,l,s),u=a[0],d=l[0];if(c!==null)return c;if(!s[0]){const h=this.ComputeDiffRecursive(t,u,i,d,s);let f=[];return s[0]?f=[new le(u+1,n-(u+1)+1,d+1,o-(d+1)+1)]:f=this.ComputeDiffRecursive(u+1,n,d+1,o,s),this.ConcatenateChanges(h,f)}return[new le(t,n-t+1,i,o-i+1)]}WALKTRACE(t,n,i,o,s,a,l,c,u,d,h,f,y,v,L,k,M,w){let _=null,N=null,b=new _t,g=n,p=i,C=y[0]-k[0]-o,S=-1073741824,E=this.m_forwardHistory.length-1;do{const m=C+t;m===g||m<p&&u[m-1]<u[m+1]?(h=u[m+1],v=h-C-o,h<S&&b.MarkNextChange(),S=h,b.AddModifiedElement(h+1,v),C=m+1-t):(h=u[m-1]+1,v=h-C-o,h<S&&b.MarkNextChange(),S=h-1,b.AddOriginalElement(h,v+1),C=m-1-t),E>=0&&(u=this.m_forwardHistory[E],t=u[0],g=1,p=u.length-1)}while(--E>=-1);if(_=b.getReverseChanges(),w[0]){let m=y[0]+1,O=k[0]+1;if(_!==null&&_.length>0){const Ce=_[_.length-1];m=Math.max(m,Ce.getOriginalEnd()),O=Math.max(O,Ce.getModifiedEnd())}N=[new le(m,f-m+1,O,L-O+1)]}else{b=new _t,g=a,p=l,C=y[0]-k[0]-c,S=1073741824,E=M?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const m=C+s;m===g||m<p&&d[m-1]>=d[m+1]?(h=d[m+1]-1,v=h-C-c,h>S&&b.MarkNextChange(),S=h+1,b.AddOriginalElement(h+1,v+1),C=m+1-s):(h=d[m-1],v=h-C-c,h>S&&b.MarkNextChange(),S=h,b.AddModifiedElement(h+1,v+1),C=m-1-s),E>=0&&(d=this.m_reverseHistory[E],s=d[0],g=1,p=d.length-1)}while(--E>=-1);N=b.getChanges()}return this.ConcatenateChanges(_,N)}ComputeRecursionPoint(t,n,i,o,s,a,l){let c=0,u=0,d=0,h=0,f=0,y=0;t--,i--,s[0]=0,a[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const v=n-t+(o-i),L=v+1,k=new Int32Array(L),M=new Int32Array(L),w=o-i,_=n-t,N=t-i,b=n-o,g=(_-w)%2==0;k[w]=t,M[_]=n,l[0]=!1;for(let p=1;p<=v/2+1;p++){let C=0,S=0;d=this.ClipDiagonalBound(w-p,p,w,L),h=this.ClipDiagonalBound(w+p,p,w,L);for(let m=d;m<=h;m+=2){m===d||m<h&&k[m-1]<k[m+1]?c=k[m+1]:c=k[m-1]+1,u=c-(m-w)-N;const O=c;for(;c<n&&u<o&&this.ElementsAreEqual(c+1,u+1);)c++,u++;if(k[m]=c,c+u>C+S&&(C=c,S=u),!g&&Math.abs(m-_)<=p-1&&c>=M[m])return s[0]=c,a[0]=u,O<=M[m]&&1447>0&&p<=1447+1?this.WALKTRACE(w,d,h,N,_,f,y,b,k,M,c,n,s,u,o,a,g,l):null}const E=(C-t+(S-i)-p)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(C,E))return l[0]=!0,s[0]=C,a[0]=S,E>0&&1447>0&&p<=1447+1?this.WALKTRACE(w,d,h,N,_,f,y,b,k,M,c,n,s,u,o,a,g,l):(t++,i++,[new le(t,n-t+1,i,o-i+1)]);f=this.ClipDiagonalBound(_-p,p,_,L),y=this.ClipDiagonalBound(_+p,p,_,L);for(let m=f;m<=y;m+=2){m===f||m<y&&M[m-1]>=M[m+1]?c=M[m+1]-1:c=M[m-1],u=c-(m-_)-b;const O=c;for(;c>t&&u>i&&this.ElementsAreEqual(c,u);)c--,u--;if(M[m]=c,g&&Math.abs(m-w)<=p&&c<=k[m])return s[0]=c,a[0]=u,O>=k[m]&&1447>0&&p<=1447+1?this.WALKTRACE(w,d,h,N,_,f,y,b,k,M,c,n,s,u,o,a,g,l):null}if(p<=1447){let m=new Int32Array(h-d+2);m[0]=w-d+1,Le.Copy2(k,d,m,1,h-d+1),this.m_forwardHistory.push(m),m=new Int32Array(y-f+2),m[0]=_-f+1,Le.Copy2(M,f,m,1,y-f+1),this.m_reverseHistory.push(m)}}return this.WALKTRACE(w,d,h,N,_,f,y,b,k,M,c,n,s,u,o,a,g,l)}PrettifyChanges(t){for(let n=0;n<t.length;n++){const i=t[n],o=n<t.length-1?t[n+1].originalStart:this._originalElementsOrHash.length,s=n<t.length-1?t[n+1].modifiedStart:this._modifiedElementsOrHash.length,a=i.originalLength>0,l=i.modifiedLength>0;for(;i.originalStart+i.originalLength<o&&i.modifiedStart+i.modifiedLength<s&&(!a||this.OriginalElementsAreEqual(i.originalStart,i.originalStart+i.originalLength))&&(!l||this.ModifiedElementsAreEqual(i.modifiedStart,i.modifiedStart+i.modifiedLength));){const u=this.ElementsAreStrictEqual(i.originalStart,i.modifiedStart);if(this.ElementsAreStrictEqual(i.originalStart+i.originalLength,i.modifiedStart+i.modifiedLength)&&!u)break;i.originalStart++,i.modifiedStart++}let c=[null];if(n<t.length-1&&this.ChangesOverlap(t[n],t[n+1],c)){t[n]=c[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){const i=t[n];let o=0,s=0;if(n>0){const h=t[n-1];o=h.originalStart+h.originalLength,s=h.modifiedStart+h.modifiedLength}const a=i.originalLength>0,l=i.modifiedLength>0;let c=0,u=this._boundaryScore(i.originalStart,i.originalLength,i.modifiedStart,i.modifiedLength);for(let h=1;;h++){const f=i.originalStart-h,y=i.modifiedStart-h;if(f<o||y<s||a&&!this.OriginalElementsAreEqual(f,f+i.originalLength)||l&&!this.ModifiedElementsAreEqual(y,y+i.modifiedLength))break;const v=(f===o&&y===s?5:0)+this._boundaryScore(f,i.originalLength,y,i.modifiedLength);v>u&&(u=v,c=h)}i.originalStart-=c,i.modifiedStart-=c;const d=[null];if(n>0&&this.ChangesOverlap(t[n-1],t[n],d)){t[n-1]=d[0],t.splice(n,1),n++;continue}}if(this._hasStrings)for(let n=1,i=t.length;n<i;n++){const o=t[n-1],s=t[n],a=s.originalStart-o.originalStart-o.originalLength,l=o.originalStart,c=s.originalStart+s.originalLength,u=c-l,d=o.modifiedStart,h=s.modifiedStart+s.modifiedLength,f=h-d;if(a<5&&u<20&&f<20){const y=this._findBetterContiguousSequence(l,u,d,f,a);if(y){const[v,L]=y;(v!==o.originalStart+o.originalLength||L!==o.modifiedStart+o.modifiedLength)&&(o.originalLength=v-o.originalStart,o.modifiedLength=L-o.modifiedStart,s.originalStart=v+a,s.modifiedStart=L+a,s.originalLength=c-s.originalStart,s.modifiedLength=h-s.modifiedStart)}}}return t}_findBetterContiguousSequence(t,n,i,o,s){if(n<s||o<s)return null;const a=t+n-s+1,l=i+o-s+1;let c=0,u=0,d=0;for(let h=t;h<a;h++)for(let f=i;f<l;f++){const y=this._contiguousSequenceScore(h,f,s);y>0&&y>c&&(c=y,u=h,d=f)}return c>0?[u,d]:null}_contiguousSequenceScore(t,n,i){let o=0;for(let s=0;s<i;s++){if(!this.ElementsAreEqual(t+s,n+s))return 0;o+=this._originalStringElements[t+s].length}return o}_OriginalIsBoundary(t){return t<=0||t>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._originalStringElements[t])}_OriginalRegionIsBoundary(t,n){if(this._OriginalIsBoundary(t)||this._OriginalIsBoundary(t-1))return!0;if(n>0){const i=t+n;if(this._OriginalIsBoundary(i-1)||this._OriginalIsBoundary(i))return!0}return!1}_ModifiedIsBoundary(t){return t<=0||t>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[t])}_ModifiedRegionIsBoundary(t,n){if(this._ModifiedIsBoundary(t)||this._ModifiedIsBoundary(t-1))return!0;if(n>0){const i=t+n;if(this._ModifiedIsBoundary(i-1)||this._ModifiedIsBoundary(i))return!0}return!1}_boundaryScore(t,n,i,o){const s=this._OriginalRegionIsBoundary(t,n)?1:0,a=this._ModifiedRegionIsBoundary(i,o)?1:0;return s+a}ConcatenateChanges(t,n){let i=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.ChangesOverlap(t[t.length-1],n[0],i)){const o=new Array(t.length+n.length-1);return Le.Copy(t,0,o,0,t.length-1),o[t.length-1]=i[0],Le.Copy(n,1,o,t.length,n.length-1),o}else{const o=new Array(t.length+n.length);return Le.Copy(t,0,o,0,t.length),Le.Copy(n,0,o,t.length,n.length),o}}ChangesOverlap(t,n,i){if(Se.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),Se.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){const o=t.originalStart;let s=t.originalLength;const a=t.modifiedStart;let l=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(s=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(l=n.modifiedStart+n.modifiedLength-t.modifiedStart),i[0]=new le(o,s,a,l),!0}else return i[0]=null,!1}ClipDiagonalBound(t,n,i,o){if(t>=0&&t<o)return t;const s=i,a=o-i-1,l=n%2==0;if(t<0){const c=s%2==0;return l===c?0:1}else{const c=a%2==0;return l===c?o-1:o-2}}}var e1=we(155);let Ne;if(typeof V.vscode!="undefined"&&typeof V.vscode.process!="undefined"){const e=V.vscode.process;Ne={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof e1!="undefined"?Ne={get platform(){return e1.platform},get arch(){return e1.arch},get env(){return{NODE_ENV:"production"}},cwd(){return{NODE_ENV:"production"}.VSCODE_CWD||e1.cwd()}}:Ne={get platform(){return De?"win32":$n?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};const N1=Ne.cwd,Uo=Ne.env,me=Ne.platform,Or=65,Dr=97,Pr=90,Ir=122,ce=46,K=47,H=92,he=58,Tr=63;class yt extends Error{constructor(t,n,i){let o;typeof n=="string"&&n.indexOf("not ")===0?(o="must not be",n=n.replace(/^not /,"")):o="must be";const s=t.indexOf(".")!==-1?"property":"argument";let a=`The "${t}" ${s} ${o} of type ${n}`;a+=`. Received type ${typeof i}`,super(a),this.code="ERR_INVALID_ARG_TYPE"}}function I(e,t){if(typeof e!="string")throw new yt(t,"string",e)}function A(e){return e===K||e===H}function E1(e){return e===K}function de(e){return e>=Or&&e<=Pr||e>=Dr&&e<=Ir}function t1(e,t,n,i){let o="",s=0,a=-1,l=0,c=0;for(let u=0;u<=e.length;++u){if(u<e.length)c=e.charCodeAt(u);else{if(i(c))break;c=K}if(i(c)){if(!(a===u-1||l===1))if(l===2){if(o.length<2||s!==2||o.charCodeAt(o.length-1)!==ce||o.charCodeAt(o.length-2)!==ce){if(o.length>2){const d=o.lastIndexOf(n);d===-1?(o="",s=0):(o=o.slice(0,d),s=o.length-1-o.lastIndexOf(n)),a=u,l=0;continue}else if(o.length!==0){o="",s=0,a=u,l=0;continue}}t&&(o+=o.length>0?`${n}..`:"..",s=2)}else o.length>0?o+=`${n}${e.slice(a+1,u)}`:o=e.slice(a+1,u),s=u-a-1;a=u,l=0}else c===ce&&l!==-1?++l:l=-1}return o}function vt(e,t){if(t===null||typeof t!="object")throw new yt("pathObject","Object",t);const n=t.dir||t.root,i=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${i}`:`${n}${e}${i}`:i}const W={resolve(...e){let t="",n="",i=!1;for(let o=e.length-1;o>=-1;o--){let s;if(o>=0){if(s=e[o],I(s,"path"),s.length===0)continue}else t.length===0?s=N1():(s={NODE_ENV:"production"}[`=${t}`]||N1(),(s===void 0||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===H)&&(s=`${t}\\`));const a=s.length;let l=0,c="",u=!1;const d=s.charCodeAt(0);if(a===1)A(d)&&(l=1,u=!0);else if(A(d))if(u=!0,A(s.charCodeAt(1))){let h=2,f=h;for(;h<a&&!A(s.charCodeAt(h));)h++;if(h<a&&h!==f){const y=s.slice(f,h);for(f=h;h<a&&A(s.charCodeAt(h));)h++;if(h<a&&h!==f){for(f=h;h<a&&!A(s.charCodeAt(h));)h++;(h===a||h!==f)&&(c=`\\\\${y}\\${s.slice(f,h)}`,l=h)}}}else l=1;else de(d)&&s.charCodeAt(1)===he&&(c=s.slice(0,2),l=2,a>2&&A(s.charCodeAt(2))&&(u=!0,l=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(i){if(t.length>0)break}else if(n=`${s.slice(l)}\\${n}`,i=u,u&&t.length>0)break}return n=t1(n,!i,"\\",A),i?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){I(e,"path");const t=e.length;if(t===0)return".";let n=0,i,o=!1;const s=e.charCodeAt(0);if(t===1)return E1(s)?"\\":e;if(A(s))if(o=!0,A(e.charCodeAt(1))){let l=2,c=l;for(;l<t&&!A(e.charCodeAt(l));)l++;if(l<t&&l!==c){const u=e.slice(c,l);for(c=l;l<t&&A(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&!A(e.charCodeAt(l));)l++;if(l===t)return`\\\\${u}\\${e.slice(c)}\\`;l!==c&&(i=`\\\\${u}\\${e.slice(c,l)}`,n=l)}}}else n=1;else de(s)&&e.charCodeAt(1)===he&&(i=e.slice(0,2),n=2,t>2&&A(e.charCodeAt(2))&&(o=!0,n=3));let a=n<t?t1(e.slice(n),!o,"\\",A):"";return a.length===0&&!o&&(a="."),a.length>0&&A(e.charCodeAt(t-1))&&(a+="\\"),i===void 0?o?`\\${a}`:a:o?`${i}\\${a}`:`${i}${a}`},isAbsolute(e){I(e,"path");const t=e.length;if(t===0)return!1;const n=e.charCodeAt(0);return A(n)||t>2&&de(n)&&e.charCodeAt(1)===he&&A(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let s=0;s<e.length;++s){const a=e[s];I(a,"path"),a.length>0&&(t===void 0?t=n=a:t+=`\\${a}`)}if(t===void 0)return".";let i=!0,o=0;if(typeof n=="string"&&A(n.charCodeAt(0))){++o;const s=n.length;s>1&&A(n.charCodeAt(1))&&(++o,s>2&&(A(n.charCodeAt(2))?++o:i=!1))}if(i){for(;o<t.length&&A(t.charCodeAt(o));)o++;o>=2&&(t=`\\${t.slice(o)}`)}return W.normalize(t)},relative(e,t){if(I(e,"from"),I(t,"to"),e===t)return"";const n=W.resolve(e),i=W.resolve(t);if(n===i||(e=n.toLowerCase(),t=i.toLowerCase(),e===t))return"";let o=0;for(;o<e.length&&e.charCodeAt(o)===H;)o++;let s=e.length;for(;s-1>o&&e.charCodeAt(s-1)===H;)s--;const a=s-o;let l=0;for(;l<t.length&&t.charCodeAt(l)===H;)l++;let c=t.length;for(;c-1>l&&t.charCodeAt(c-1)===H;)c--;const u=c-l,d=a<u?a:u;let h=-1,f=0;for(;f<d;f++){const v=e.charCodeAt(o+f);if(v!==t.charCodeAt(l+f))break;v===H&&(h=f)}if(f!==d){if(h===-1)return i}else{if(u>d){if(t.charCodeAt(l+f)===H)return i.slice(l+f+1);if(f===2)return i.slice(l+f)}a>d&&(e.charCodeAt(o+f)===H?h=f:f===2&&(h=3)),h===-1&&(h=0)}let y="";for(f=o+h+1;f<=s;++f)(f===s||e.charCodeAt(f)===H)&&(y+=y.length===0?"..":"\\..");return l+=h,y.length>0?`${y}${i.slice(l,c)}`:(i.charCodeAt(l)===H&&++l,i.slice(l,c))},toNamespacedPath(e){if(typeof e!="string")return e;if(e.length===0)return"";const t=W.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===H){if(t.charCodeAt(1)===H){const n=t.charCodeAt(2);if(n!==Tr&&n!==ce)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(de(t.charCodeAt(0))&&t.charCodeAt(1)===he&&t.charCodeAt(2)===H)return`\\\\?\\${t}`;return e},dirname(e){I(e,"path");const t=e.length;if(t===0)return".";let n=-1,i=0;const o=e.charCodeAt(0);if(t===1)return A(o)?e:".";if(A(o)){if(n=i=1,A(e.charCodeAt(1))){let l=2,c=l;for(;l<t&&!A(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&A(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&!A(e.charCodeAt(l));)l++;if(l===t)return e;l!==c&&(n=i=l+1)}}}}else de(o)&&e.charCodeAt(1)===he&&(n=t>2&&A(e.charCodeAt(2))?3:2,i=n);let s=-1,a=!0;for(let l=t-1;l>=i;--l)if(A(e.charCodeAt(l))){if(!a){s=l;break}}else a=!1;if(s===-1){if(n===-1)return".";s=n}return e.slice(0,s)},basename(e,t){t!==void 0&&I(t,"ext"),I(e,"path");let n=0,i=-1,o=!0,s;if(e.length>=2&&de(e.charCodeAt(0))&&e.charCodeAt(1)===he&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,l=-1;for(s=e.length-1;s>=n;--s){const c=e.charCodeAt(s);if(A(c)){if(!o){n=s+1;break}}else l===-1&&(o=!1,l=s+1),a>=0&&(c===t.charCodeAt(a)?--a==-1&&(i=s):(a=-1,i=l))}return n===i?i=l:i===-1&&(i=e.length),e.slice(n,i)}for(s=e.length-1;s>=n;--s)if(A(e.charCodeAt(s))){if(!o){n=s+1;break}}else i===-1&&(o=!1,i=s+1);return i===-1?"":e.slice(n,i)},extname(e){I(e,"path");let t=0,n=-1,i=0,o=-1,s=!0,a=0;e.length>=2&&e.charCodeAt(1)===he&&de(e.charCodeAt(0))&&(t=i=2);for(let l=e.length-1;l>=t;--l){const c=e.charCodeAt(l);if(A(c)){if(!s){i=l+1;break}continue}o===-1&&(s=!1,o=l+1),c===ce?n===-1?n=l:a!==1&&(a=1):n!==-1&&(a=-1)}return n===-1||o===-1||a===0||a===1&&n===o-1&&n===i+1?"":e.slice(n,o)},format:vt.bind(null,"\\"),parse(e){I(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.length;let i=0,o=e.charCodeAt(0);if(n===1)return A(o)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(A(o)){if(i=1,A(e.charCodeAt(1))){let h=2,f=h;for(;h<n&&!A(e.charCodeAt(h));)h++;if(h<n&&h!==f){for(f=h;h<n&&A(e.charCodeAt(h));)h++;if(h<n&&h!==f){for(f=h;h<n&&!A(e.charCodeAt(h));)h++;h===n?i=h:h!==f&&(i=h+1)}}}}else if(de(o)&&e.charCodeAt(1)===he){if(n<=2)return t.root=t.dir=e,t;if(i=2,A(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;i=3}}i>0&&(t.root=e.slice(0,i));let s=-1,a=i,l=-1,c=!0,u=e.length-1,d=0;for(;u>=i;--u){if(o=e.charCodeAt(u),A(o)){if(!c){a=u+1;break}continue}l===-1&&(c=!1,l=u+1),o===ce?s===-1?s=u:d!==1&&(d=1):s!==-1&&(d=-1)}return l!==-1&&(s===-1||d===0||d===1&&s===l-1&&s===a+1?t.base=t.name=e.slice(a,l):(t.name=e.slice(a,s),t.base=e.slice(a,l),t.ext=e.slice(s,l))),a>0&&a!==i?t.dir=e.slice(0,a-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},z={resolve(...e){let t="",n=!1;for(let i=e.length-1;i>=-1&&!n;i--){const o=i>=0?e[i]:N1();I(o,"path"),o.length!==0&&(t=`${o}/${t}`,n=o.charCodeAt(0)===K)}return t=t1(t,!n,"/",E1),n?`/${t}`:t.length>0?t:"."},normalize(e){if(I(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===K,n=e.charCodeAt(e.length-1)===K;return e=t1(e,!t,"/",E1),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return I(e,"path"),e.length>0&&e.charCodeAt(0)===K},join(...e){if(e.length===0)return".";let t;for(let n=0;n<e.length;++n){const i=e[n];I(i,"path"),i.length>0&&(t===void 0?t=i:t+=`/${i}`)}return t===void 0?".":z.normalize(t)},relative(e,t){if(I(e,"from"),I(t,"to"),e===t||(e=z.resolve(e),t=z.resolve(t),e===t))return"";const n=1,i=e.length,o=i-n,s=1,a=t.length-s,l=o<a?o:a;let c=-1,u=0;for(;u<l;u++){const h=e.charCodeAt(n+u);if(h!==t.charCodeAt(s+u))break;h===K&&(c=u)}if(u===l)if(a>l){if(t.charCodeAt(s+u)===K)return t.slice(s+u+1);if(u===0)return t.slice(s+u)}else o>l&&(e.charCodeAt(n+u)===K?c=u:u===0&&(c=0));let d="";for(u=n+c+1;u<=i;++u)(u===i||e.charCodeAt(u)===K)&&(d+=d.length===0?"..":"/..");return`${d}${t.slice(s+c)}`},toNamespacedPath(e){return e},dirname(e){if(I(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===K;let n=-1,i=!0;for(let o=e.length-1;o>=1;--o)if(e.charCodeAt(o)===K){if(!i){n=o;break}}else i=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&I(t,"ext"),I(e,"path");let n=0,i=-1,o=!0,s;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,l=-1;for(s=e.length-1;s>=0;--s){const c=e.charCodeAt(s);if(c===K){if(!o){n=s+1;break}}else l===-1&&(o=!1,l=s+1),a>=0&&(c===t.charCodeAt(a)?--a==-1&&(i=s):(a=-1,i=l))}return n===i?i=l:i===-1&&(i=e.length),e.slice(n,i)}for(s=e.length-1;s>=0;--s)if(e.charCodeAt(s)===K){if(!o){n=s+1;break}}else i===-1&&(o=!1,i=s+1);return i===-1?"":e.slice(n,i)},extname(e){I(e,"path");let t=-1,n=0,i=-1,o=!0,s=0;for(let a=e.length-1;a>=0;--a){const l=e.charCodeAt(a);if(l===K){if(!o){n=a+1;break}continue}i===-1&&(o=!1,i=a+1),l===ce?t===-1?t=a:s!==1&&(s=1):t!==-1&&(s=-1)}return t===-1||i===-1||s===0||s===1&&t===i-1&&t===n+1?"":e.slice(t,i)},format:vt.bind(null,"/"),parse(e){I(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.charCodeAt(0)===K;let i;n?(t.root="/",i=1):i=0;let o=-1,s=0,a=-1,l=!0,c=e.length-1,u=0;for(;c>=i;--c){const d=e.charCodeAt(c);if(d===K){if(!l){s=c+1;break}continue}a===-1&&(l=!1,a=c+1),d===ce?o===-1?o=c:u!==1&&(u=1):o!==-1&&(u=-1)}if(a!==-1){const d=s===0&&n?1:s;o===-1||u===0||u===1&&o===a-1&&o===s+1?t.base=t.name=e.slice(d,a):(t.name=e.slice(d,o),t.base=e.slice(d,a),t.ext=e.slice(o,a))}return s>0?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};z.win32=W.win32=W,z.posix=W.posix=z;const qo=me==="win32"?W.normalize:z.normalize,Wo=me==="win32"?W.resolve:z.resolve,Ho=me==="win32"?W.relative:z.relative,zo=me==="win32"?W.dirname:z.dirname,$o=me==="win32"?W.basename:z.basename,jo=me==="win32"?W.extname:z.extname,Go=me==="win32"?W.sep:z.sep,Kr=/^\w[\w\d+.-]*$/,Vr=/^\//,Br=/^\/\//;function St(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!Kr.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!Vr.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Br.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function Ur(e,t){return!e&&!t?"file":e}function qr(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==Z&&(t=Z+t):t=Z;break}return t}const R="",Z="/",Wr=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class ge{constructor(t,n,i,o,s,a=!1){typeof t=="object"?(this.scheme=t.scheme||R,this.authority=t.authority||R,this.path=t.path||R,this.query=t.query||R,this.fragment=t.fragment||R):(this.scheme=Ur(t,a),this.authority=n||R,this.path=qr(this.scheme,i||R),this.query=o||R,this.fragment=s||R,St(this,a))}static isUri(t){return t instanceof ge?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}get fsPath(){return A1(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:i,path:o,query:s,fragment:a}=t;return n===void 0?n=this.scheme:n===null&&(n=R),i===void 0?i=this.authority:i===null&&(i=R),o===void 0?o=this.path:o===null&&(o=R),s===void 0?s=this.query:s===null&&(s=R),a===void 0?a=this.fragment:a===null&&(a=R),n===this.scheme&&i===this.authority&&o===this.path&&s===this.query&&a===this.fragment?this:new Ee(n,i,o,s,a)}static parse(t,n=!1){const i=Wr.exec(t);return i?new Ee(i[2]||R,n1(i[4]||R),n1(i[5]||R),n1(i[7]||R),n1(i[9]||R),n):new Ee(R,R,R,R,R)}static file(t){let n=R;if(De&&(t=t.replace(/\\/g,Z)),t[0]===Z&&t[1]===Z){const i=t.indexOf(Z,2);i===-1?(n=t.substring(2),t=Z):(n=t.substring(2,i),t=t.substring(i)||Z)}return new Ee("file",n,t,R,R)}static from(t){const n=new Ee(t.scheme,t.authority,t.path,t.query,t.fragment);return St(n,!0),n}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let i;return De&&t.scheme==="file"?i=ge.file(W.join(A1(t,!0),...n)).path:i=z.join(t.path,...n),t.with({path:i})}toString(t=!1){return k1(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof ge)return t;{const n=new Ee(t);return n._formatted=t.external,n._fsPath=t._sep===Lt?t.fsPath:null,n}}else return t}}const Lt=De?1:void 0;class Ee extends ge{constructor(){super(...arguments);this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=A1(this,!1)),this._fsPath}toString(t=!1){return t?k1(this,!0):(this._formatted||(this._formatted=k1(this,!1)),this._formatted)}toJSON(){const t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=Lt),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t}}const Nt={[58]:"%3A",[47]:"%2F",[63]:"%3F",[35]:"%23",[91]:"%5B",[93]:"%5D",[64]:"%40",[33]:"%21",[36]:"%24",[38]:"%26",[39]:"%27",[40]:"%28",[41]:"%29",[42]:"%2A",[43]:"%2B",[44]:"%2C",[59]:"%3B",[61]:"%3D",[32]:"%20"};function Et(e,t){let n,i=-1;for(let o=0;o<e.length;o++){const s=e.charCodeAt(o);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||s===45||s===46||s===95||s===126||t&&s===47)i!==-1&&(n+=encodeURIComponent(e.substring(i,o)),i=-1),n!==void 0&&(n+=e.charAt(o));else{n===void 0&&(n=e.substr(0,o));const a=Nt[s];a!==void 0?(i!==-1&&(n+=encodeURIComponent(e.substring(i,o)),i=-1),n+=a):i===-1&&(i=o)}}return i!==-1&&(n+=encodeURIComponent(e.substring(i))),n!==void 0?n:e}function Hr(e){let t;for(let n=0;n<e.length;n++){const i=e.charCodeAt(n);i===35||i===63?(t===void 0&&(t=e.substr(0,n)),t+=Nt[i]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function A1(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,De&&(n=n.replace(/\//g,"\\")),n}function k1(e,t){const n=t?Hr:Et;let i="",{scheme:o,authority:s,path:a,query:l,fragment:c}=e;if(o&&(i+=o,i+=":"),(s||o==="file")&&(i+=Z,i+=Z),s){let u=s.indexOf("@");if(u!==-1){const d=s.substr(0,u);s=s.substr(u+1),u=d.indexOf(":"),u===-1?i+=n(d,!1):(i+=n(d.substr(0,u),!1),i+=":",i+=n(d.substr(u+1),!1)),i+="@"}s=s.toLowerCase(),u=s.indexOf(":"),u===-1?i+=n(s,!1):(i+=n(s.substr(0,u),!1),i+=s.substr(u))}if(a){if(a.length>=3&&a.charCodeAt(0)===47&&a.charCodeAt(2)===58){const u=a.charCodeAt(1);u>=65&&u<=90&&(a=`/${String.fromCharCode(u+32)}:${a.substr(3)}`)}else if(a.length>=2&&a.charCodeAt(1)===58){const u=a.charCodeAt(0);u>=65&&u<=90&&(a=`${String.fromCharCode(u+32)}:${a.substr(2)}`)}i+=n(a,!0)}return l&&(i+="?",i+=n(l,!1)),c&&(i+="#",i+=t?c:Et(c,!1)),i}function At(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+At(e.substr(3)):e}}const kt=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function n1(e){return e.match(kt)?e.replace(kt,t=>At(t)):e}class ${constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new $(t,n)}delta(t=0,n=0){return this.with(this.lineNumber+t,this.column+n)}equals(t){return $.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return $.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return $.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){const i=t.lineNumber|0,o=n.lineNumber|0;if(i===o){const s=t.column|0,a=n.column|0;return s-a}return i-o}clone(){return new $(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new $(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}}class D{constructor(t,n,i,o){t>i||t===i&&n>o?(this.startLineNumber=i,this.startColumn=o,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=i,this.endColumn=o)}isEmpty(){return D.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return D.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return D.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return D.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return D.plusRange(this,t)}static plusRange(t,n){let i,o,s,a;return n.startLineNumber<t.startLineNumber?(i=n.startLineNumber,o=n.startColumn):n.startLineNumber===t.startLineNumber?(i=n.startLineNumber,o=Math.min(n.startColumn,t.startColumn)):(i=t.startLineNumber,o=t.startColumn),n.endLineNumber>t.endLineNumber?(s=n.endLineNumber,a=n.endColumn):n.endLineNumber===t.endLineNumber?(s=n.endLineNumber,a=Math.max(n.endColumn,t.endColumn)):(s=t.endLineNumber,a=t.endColumn),new D(i,o,s,a)}intersectRanges(t){return D.intersectRanges(this,t)}static intersectRanges(t,n){let i=t.startLineNumber,o=t.startColumn,s=t.endLineNumber,a=t.endColumn,l=n.startLineNumber,c=n.startColumn,u=n.endLineNumber,d=n.endColumn;return i<l?(i=l,o=c):i===l&&(o=Math.max(o,c)),s>u?(s=u,a=d):s===u&&(a=Math.min(a,d)),i>s||i===s&&o>a?null:new D(i,o,s,a)}equalsRange(t){return D.equalsRange(this,t)}static equalsRange(t,n){return!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return D.getEndPosition(this)}static getEndPosition(t){return new $(t.endLineNumber,t.endColumn)}getStartPosition(){return D.getStartPosition(this)}static getStartPosition(t){return new $(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new D(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new D(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return D.collapseToStart(this)}static collapseToStart(t){return new D(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}static fromPositions(t,n=t){return new D(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new D(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static compareRangesUsingStarts(t,n){if(t&&n){const i=t.startLineNumber|0,o=n.startLineNumber|0;if(i===o){const s=t.startColumn|0,a=n.startColumn|0;if(s===a){const l=t.endLineNumber|0,c=n.endLineNumber|0;if(l===c){const u=t.endColumn|0,d=n.endColumn|0;return u-d}return l-c}return s-a}return i-o}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}}const zr=3;function Mt(e,t,n,i){return new ue(e,t,n).ComputeDiff(i)}class xt{constructor(t){const n=[],i=[];for(let o=0,s=t.length;o<s;o++)n[o]=M1(t[o],1),i[o]=x1(t[o],1);this.lines=t,this._startColumns=n,this._endColumns=i}getElements(){const t=[];for(let n=0,i=this.lines.length;n<i;n++)t[n]=this.lines[n].substring(this._startColumns[n]-1,this._endColumns[n]-1);return t}getStrictElement(t){return this.lines[t]}getStartLineNumber(t){return t+1}getEndLineNumber(t){return t+1}createCharSequence(t,n,i){const o=[],s=[],a=[];let l=0;for(let c=n;c<=i;c++){const u=this.lines[c],d=t?this._startColumns[c]:1,h=t?this._endColumns[c]:u.length+1;for(let f=d;f<h;f++)o[l]=u.charCodeAt(f-1),s[l]=c+1,a[l]=f,l++}return new $r(o,s,a)}}class $r{constructor(t,n,i){this._charCodes=t,this._lineNumbers=n,this._columns=i}getElements(){return this._charCodes}getStartLineNumber(t){return this._lineNumbers[t]}getStartColumn(t){return this._columns[t]}getEndLineNumber(t){return this._lineNumbers[t]}getEndColumn(t){return this._columns[t]+1}}class Ve{constructor(t,n,i,o,s,a,l,c){this.originalStartLineNumber=t,this.originalStartColumn=n,this.originalEndLineNumber=i,this.originalEndColumn=o,this.modifiedStartLineNumber=s,this.modifiedStartColumn=a,this.modifiedEndLineNumber=l,this.modifiedEndColumn=c}static createFromDiffChange(t,n,i){let o,s,a,l,c,u,d,h;return t.originalLength===0?(o=0,s=0,a=0,l=0):(o=n.getStartLineNumber(t.originalStart),s=n.getStartColumn(t.originalStart),a=n.getEndLineNumber(t.originalStart+t.originalLength-1),l=n.getEndColumn(t.originalStart+t.originalLength-1)),t.modifiedLength===0?(c=0,u=0,d=0,h=0):(c=i.getStartLineNumber(t.modifiedStart),u=i.getStartColumn(t.modifiedStart),d=i.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),h=i.getEndColumn(t.modifiedStart+t.modifiedLength-1)),new Ve(o,s,a,l,c,u,d,h)}}function jr(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let i=1,o=e.length;i<o;i++){const s=e[i],a=s.originalStart-(n.originalStart+n.originalLength),l=s.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(a,l)<zr?(n.originalLength=s.originalStart+s.originalLength-n.originalStart,n.modifiedLength=s.modifiedStart+s.modifiedLength-n.modifiedStart):(t.push(s),n=s)}return t}class Be{constructor(t,n,i,o,s){this.originalStartLineNumber=t,this.originalEndLineNumber=n,this.modifiedStartLineNumber=i,this.modifiedEndLineNumber=o,this.charChanges=s}static createFromDiffResult(t,n,i,o,s,a,l){let c,u,d,h,f;if(n.originalLength===0?(c=i.getStartLineNumber(n.originalStart)-1,u=0):(c=i.getStartLineNumber(n.originalStart),u=i.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(d=o.getStartLineNumber(n.modifiedStart)-1,h=0):(d=o.getStartLineNumber(n.modifiedStart),h=o.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),a&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&s()){const y=i.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),v=o.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);let L=Mt(y,v,s,!0).changes;l&&(L=jr(L)),f=[];for(let k=0,M=L.length;k<M;k++)f.push(Ve.createFromDiffChange(L[k],y,v))}return new Be(c,u,d,h,f)}}class Gr{constructor(t,n,i){this.shouldComputeCharChanges=i.shouldComputeCharChanges,this.shouldPostProcessCharChanges=i.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=i.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=i.shouldMakePrettyDiff,this.originalLines=t,this.modifiedLines=n,this.original=new xt(t),this.modified=new xt(n),this.continueLineDiff=Ft(i.maxComputationTime),this.continueCharDiff=Ft(i.maxComputationTime===0?0:Math.min(i.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};const t=Mt(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),n=t.changes,i=t.quitEarly;if(this.shouldIgnoreTrimWhitespace){const l=[];for(let c=0,u=n.length;c<u;c++)l.push(Be.createFromDiffResult(this.shouldIgnoreTrimWhitespace,n[c],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:i,changes:l}}const o=[];let s=0,a=0;for(let l=-1,c=n.length;l<c;l++){const u=l+1<c?n[l+1]:null,d=u?u.originalStart:this.originalLines.length,h=u?u.modifiedStart:this.modifiedLines.length;for(;s<d&&a<h;){const f=this.originalLines[s],y=this.modifiedLines[a];if(f!==y){{let v=M1(f,1),L=M1(y,1);for(;v>1&&L>1;){const k=f.charCodeAt(v-2),M=y.charCodeAt(L-2);if(k!==M)break;v--,L--}(v>1||L>1)&&this._pushTrimWhitespaceCharChange(o,s+1,1,v,a+1,1,L)}{let v=x1(f,1),L=x1(y,1);const k=f.length+1,M=y.length+1;for(;v<k&&L<M;){const w=f.charCodeAt(v-1),_=f.charCodeAt(L-1);if(w!==_)break;v++,L++}(v<k||L<M)&&this._pushTrimWhitespaceCharChange(o,s+1,v,k,a+1,L,M)}}s++,a++}u&&(o.push(Be.createFromDiffResult(this.shouldIgnoreTrimWhitespace,u,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),s+=u.originalLength,a+=u.modifiedLength)}return{quitEarly:i,changes:o}}_pushTrimWhitespaceCharChange(t,n,i,o,s,a,l){if(this._mergeTrimWhitespaceCharChange(t,n,i,o,s,a,l))return;let c;this.shouldComputeCharChanges&&(c=[new Ve(n,i,n,o,s,a,s,l)]),t.push(new Be(n,n,s,s,c))}_mergeTrimWhitespaceCharChange(t,n,i,o,s,a,l){const c=t.length;if(c===0)return!1;const u=t[c-1];return u.originalEndLineNumber===0||u.modifiedEndLineNumber===0?!1:u.originalEndLineNumber+1===n&&u.modifiedEndLineNumber+1===s?(u.originalEndLineNumber=n,u.modifiedEndLineNumber=s,this.shouldComputeCharChanges&&u.charChanges&&u.charChanges.push(new Ve(n,i,n,o,s,a,s,l)),!0):!1}}function M1(e,t){const n=cr(e);return n===-1?t:n+1}function x1(e,t){const n=hr(e);return n===-1?t:n+2}function Ft(e){if(e===0)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}function Rt(e){return e<0?0:e>255?255:e|0}function Ae(e){return e<0?0:e>4294967295?4294967295:e|0}class Qr{constructor(t){this.values=t,this.prefixSum=new Uint32Array(t.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(t,n){t=Ae(t);const i=this.values,o=this.prefixSum,s=n.length;return s===0?!1:(this.values=new Uint32Array(i.length+s),this.values.set(i.subarray(0,t),0),this.values.set(i.subarray(t),t+s),this.values.set(n,t),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(o.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(t,n){return t=Ae(t),n=Ae(n),this.values[t]===n?!1:(this.values[t]=n,t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),!0)}removeValues(t,n){t=Ae(t),n=Ae(n);const i=this.values,o=this.prefixSum;if(t>=i.length)return!1;const s=i.length-t;return n>=s&&(n=s),n===0?!1:(this.values=new Uint32Array(i.length-n),this.values.set(i.subarray(0,t),0),this.values.set(i.subarray(t+n),t),this.prefixSum=new Uint32Array(this.values.length),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(o.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(t){return t<0?0:(t=Ae(t),this._getPrefixSum(t))}_getPrefixSum(t){if(t<=this.prefixSumValidIndex[0])return this.prefixSum[t];let n=this.prefixSumValidIndex[0]+1;n===0&&(this.prefixSum[0]=this.values[0],n++),t>=this.values.length&&(t=this.values.length-1);for(let i=n;i<=t;i++)this.prefixSum[i]=this.prefixSum[i-1]+this.values[i];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],t),this.prefixSum[t]}getIndexOf(t){t=Math.floor(t),this.getTotalSum();let n=0,i=this.values.length-1,o=0,s=0,a=0;for(;n<=i;)if(o=n+(i-n)/2|0,s=this.prefixSum[o],a=s-this.values[o],t<a)i=o-1;else if(t>=s)n=o+1;else break;return new Ot(o,t-a)}}class Qo{constructor(t){this._values=t,this._isValid=!1,this._validEndIndex=-1,this._prefixSum=[],this._indexBySum=[]}getTotalSum(){return this._ensureValid(),this._indexBySum.length}getPrefixSum(t){return this._ensureValid(),t===0?0:this._prefixSum[t-1]}getIndexOf(t){this._ensureValid();const n=this._indexBySum[t],i=n>0?this._prefixSum[n-1]:0;return new Ot(n,t-i)}removeValues(t,n){this._values.splice(t,n),this._invalidate(t)}insertValues(t,n){this._values=arrayInsert(this._values,t,n),this._invalidate(t)}_invalidate(t){this._isValid=!1,this._validEndIndex=Math.min(this._validEndIndex,t-1)}_ensureValid(){if(!this._isValid){for(let t=this._validEndIndex+1,n=this._values.length;t<n;t++){const i=this._values[t],o=t>0?this._prefixSum[t-1]:0;this._prefixSum[t]=o+i;for(let s=0;s<i;s++)this._indexBySum[o+s]=t}this._prefixSum.length=this._values.length,this._indexBySum.length=this._prefixSum[this._prefixSum.length-1],this._isValid=!0,this._validEndIndex=this._values.length-1}}setValue(t,n){this._values[t]!==n&&(this._values[t]=n,this._invalidate(t))}}class Ot{constructor(t,n){this.index=t,this.remainder=n,this._prefixSumIndexOfResultBrand=void 0,this.index=t,this.remainder=n}}class Yr{constructor(t,n,i,o){this._uri=t,this._lines=n,this._eol=i,this._versionId=o,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(t){t.eol&&t.eol!==this._eol&&(this._eol=t.eol,this._lineStarts=null);const n=t.changes;for(const i of n)this._acceptDeleteRange(i.range),this._acceptInsertText(new $(i.range.startLineNumber,i.range.startColumn),i.text);this._versionId=t.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const t=this._eol.length,n=this._lines.length,i=new Uint32Array(n);for(let o=0;o<n;o++)i[o]=this._lines[o].length+t;this._lineStarts=new Qr(i)}}_setLineText(t,n){this._lines[t]=n,this._lineStarts&&this._lineStarts.setValue(t,this._lines[t].length+this._eol.length)}_acceptDeleteRange(t){if(t.startLineNumber===t.endLineNumber){if(t.startColumn===t.endColumn)return;this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.startLineNumber-1].substring(t.endColumn-1));return}this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.endLineNumber-1].substring(t.endColumn-1)),this._lines.splice(t.startLineNumber,t.endLineNumber-t.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(t.startLineNumber,t.endLineNumber-t.startLineNumber)}_acceptInsertText(t,n){if(n.length===0)return;const i=ur(n);if(i.length===1){this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+i[0]+this._lines[t.lineNumber-1].substring(t.column-1));return}i[i.length-1]+=this._lines[t.lineNumber-1].substring(t.column-1),this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+i[0]);const o=new Uint32Array(i.length-1);for(let s=1;s<i.length;s++)this._lines.splice(t.lineNumber+s-1,0,i[s]),o[s-1]=i[s].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(t.lineNumber,o)}}const Zr="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function Xr(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of Zr)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}const Dt=Xr();function Jr(e){let t=Dt;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}const ei={maxLen:1e3,windowSize:15,timeBudget:150};function F1(e,t,n,i,o=ei){if(n.length>o.maxLen){let u=e-o.maxLen/2;return u<0?u=0:i+=u,n=n.substring(u,e+o.maxLen/2),F1(e,t,n,i,o)}const s=Date.now(),a=e-1-i;let l=-1,c=null;for(let u=1;!(Date.now()-s>=o.timeBudget);u++){const d=a-o.windowSize*u;t.lastIndex=Math.max(0,d);const h=ti(t,n,a,l);if(!h&&c||(c=h,d<=0))break;l=d}if(c){const u={word:c[0],startColumn:i+1+c.index,endColumn:i+1+c.index+c[0].length};return t.lastIndex=0,u}return null}function ti(e,t,n,i){let o;for(;o=e.exec(t);){const s=o.index||0;if(s<=n&&e.lastIndex>=n)return o;if(i>0&&s>i)return null}return null}class Ue{constructor(t){const n=Rt(t);this._defaultValue=n,this._asciiMap=Ue._createAsciiMap(n),this._map=new Map}static _createAsciiMap(t){const n=new Uint8Array(256);for(let i=0;i<256;i++)n[i]=t;return n}set(t,n){const i=Rt(n);t>=0&&t<256?this._asciiMap[t]=i:this._map.set(t,i)}get(t){return t>=0&&t<256?this._asciiMap[t]:this._map.get(t)||this._defaultValue}}class Yo{constructor(){this._actual=new Ue(0)}add(t){this._actual.set(t,1)}has(t){return this._actual.get(t)===1}}class ni{constructor(t,n,i){const o=new Uint8Array(t*n);for(let s=0,a=t*n;s<a;s++)o[s]=i;this._data=o,this.rows=t,this.cols=n}get(t,n){return this._data[t*this.cols+n]}set(t,n,i){this._data[t*this.cols+n]=i}}class ri{constructor(t){let n=0,i=0;for(let s=0,a=t.length;s<a;s++){const[l,c,u]=t[s];c>n&&(n=c),l>i&&(i=l),u>i&&(i=u)}n++,i++;const o=new ni(i,n,0);for(let s=0,a=t.length;s<a;s++){const[l,c,u]=t[s];o.set(l,c,u)}this._states=o,this._maxCharCode=n}nextState(t,n){return n<0||n>=this._maxCharCode?0:this._states.get(t,n)}}let R1=null;function ii(){return R1===null&&(R1=new ri([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),R1}let qe=null;function oi(){if(qe===null){qe=new Ue(0);const e=` 	<>'"\u3001\u3002\uFF61\uFF64\uFF0C\uFF0E\uFF1A\uFF1B\u2018\u3008\u300C\u300E\u3014\uFF08\uFF3B\uFF5B\uFF62\uFF63\uFF5D\uFF3D\uFF09\u3015\u300F\u300D\u3009\u2019\uFF40\uFF5E\u2026`;for(let n=0;n<e.length;n++)qe.set(e.charCodeAt(n),1);const t=".,;";for(let n=0;n<t.length;n++)qe.set(t.charCodeAt(n),2)}return qe}class r1{static _createLink(t,n,i,o,s){let a=s-1;do{const l=n.charCodeAt(a);if(t.get(l)!==2)break;a--}while(a>o);if(o>0){const l=n.charCodeAt(o-1),c=n.charCodeAt(a);(l===40&&c===41||l===91&&c===93||l===123&&c===125)&&a--}return{range:{startLineNumber:i,startColumn:o+1,endLineNumber:i,endColumn:a+2},url:n.substring(o,a+1)}}static computeLinks(t,n=ii()){const i=oi(),o=[];for(let s=1,a=t.getLineCount();s<=a;s++){const l=t.getLineContent(s),c=l.length;let u=0,d=0,h=0,f=1,y=!1,v=!1,L=!1,k=!1;for(;u<c;){let M=!1;const w=l.charCodeAt(u);if(f===13){let _;switch(w){case 40:y=!0,_=0;break;case 41:_=y?0:1;break;case 91:L=!0,v=!0,_=0;break;case 93:L=!1,_=v?0:1;break;case 123:k=!0,_=0;break;case 125:_=k?0:1;break;case 39:_=h===34||h===96?0:1;break;case 34:_=h===39||h===96?0:1;break;case 96:_=h===39||h===34?0:1;break;case 42:_=h===42?1:0;break;case 124:_=h===124?1:0;break;case 32:_=L?0:1;break;default:_=i.get(w)}_===1&&(o.push(r1._createLink(i,l,s,d,u)),M=!0)}else if(f===12){let _;w===91?(v=!0,_=0):_=i.get(w),_===1?M=!0:f=13}else f=n.nextState(f,w),f===0&&(M=!0);M&&(f=1,y=!1,v=!1,k=!1,d=u+1,h=w),u++}f===13&&o.push(r1._createLink(i,l,s,d,c))}return o}}function si(e){return!e||typeof e.getLineCount!="function"||typeof e.getLineContent!="function"?[]:r1.computeLinks(e)}class O1{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(t,n,i,o,s){if(t&&n){const a=this.doNavigateValueSet(n,s);if(a)return{range:t,value:a}}if(i&&o){const a=this.doNavigateValueSet(o,s);if(a)return{range:i,value:a}}return null}doNavigateValueSet(t,n){const i=this.numberReplace(t,n);return i!==null?i:this.textReplace(t,n)}numberReplace(t,n){const i=Math.pow(10,t.length-(t.lastIndexOf(".")+1));let o=Number(t),s=parseFloat(t);return!isNaN(o)&&!isNaN(s)&&o===s?o===0&&!n?null:(o=Math.floor(o*i),o+=n?i:-i,String(o/i)):null}textReplace(t,n){return this.valueSetsReplace(this._defaultValueSet,t,n)}valueSetsReplace(t,n,i){let o=null;for(let s=0,a=t.length;o===null&&s<a;s++)o=this.valueSetReplace(t[s],n,i);return o}valueSetReplace(t,n,i){let o=t.indexOf(n);return o>=0?(o+=i?1:-1,o<0?o=t.length-1:o%=t.length,t[o]):null}}O1.INSTANCE=new O1;const Pt=Object.freeze(function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}});var i1;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof o1?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Xe.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Pt})})(i1||(i1={}));class o1{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Pt:(this._emitter||(this._emitter=new ee),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class ai{constructor(t){this._token=void 0,this._parentListener=void 0,this._parentListener=t&&t.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new o1),this._token}cancel(){this._token?this._token instanceof o1&&this._token.cancel():this._token=i1.Cancelled}dispose(t=!1){t&&this.cancel(),this._parentListener&&this._parentListener.dispose(),this._token?this._token instanceof o1&&this._token.dispose():this._token=i1.None}}class D1{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(t,n){this._keyCodeToStr[t]=n,this._strToKeyCode[n.toLowerCase()]=t}keyCodeToStr(t){return this._keyCodeToStr[t]}strToKeyCode(t){return this._strToKeyCode[t.toLowerCase()]||0}}const s1=new D1,P1=new D1,I1=new D1,li=new Array(230),ui={},ci=[],hi=Object.create(null),di=Object.create(null),It=[],T1=[];for(let e=0;e<=193;e++)It[e]=-1;for(let e=0;e<=127;e++)T1[e]=-1;(function(){const e="",t=[[0,1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[0,1,1,"Hyper",0,e,0,e,e,e],[0,1,2,"Super",0,e,0,e,e,e],[0,1,3,"Fn",0,e,0,e,e,e],[0,1,4,"FnLock",0,e,0,e,e,e],[0,1,5,"Suspend",0,e,0,e,e,e],[0,1,6,"Resume",0,e,0,e,e,e],[0,1,7,"Turbo",0,e,0,e,e,e],[0,1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[0,1,9,"WakeUp",0,e,0,e,e,e],[31,0,10,"KeyA",31,"A",65,"VK_A",e,e],[32,0,11,"KeyB",32,"B",66,"VK_B",e,e],[33,0,12,"KeyC",33,"C",67,"VK_C",e,e],[34,0,13,"KeyD",34,"D",68,"VK_D",e,e],[35,0,14,"KeyE",35,"E",69,"VK_E",e,e],[36,0,15,"KeyF",36,"F",70,"VK_F",e,e],[37,0,16,"KeyG",37,"G",71,"VK_G",e,e],[38,0,17,"KeyH",38,"H",72,"VK_H",e,e],[39,0,18,"KeyI",39,"I",73,"VK_I",e,e],[40,0,19,"KeyJ",40,"J",74,"VK_J",e,e],[41,0,20,"KeyK",41,"K",75,"VK_K",e,e],[42,0,21,"KeyL",42,"L",76,"VK_L",e,e],[43,0,22,"KeyM",43,"M",77,"VK_M",e,e],[44,0,23,"KeyN",44,"N",78,"VK_N",e,e],[45,0,24,"KeyO",45,"O",79,"VK_O",e,e],[46,0,25,"KeyP",46,"P",80,"VK_P",e,e],[47,0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[48,0,27,"KeyR",48,"R",82,"VK_R",e,e],[49,0,28,"KeyS",49,"S",83,"VK_S",e,e],[50,0,29,"KeyT",50,"T",84,"VK_T",e,e],[51,0,30,"KeyU",51,"U",85,"VK_U",e,e],[52,0,31,"KeyV",52,"V",86,"VK_V",e,e],[53,0,32,"KeyW",53,"W",87,"VK_W",e,e],[54,0,33,"KeyX",54,"X",88,"VK_X",e,e],[55,0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[56,0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[22,0,36,"Digit1",22,"1",49,"VK_1",e,e],[23,0,37,"Digit2",23,"2",50,"VK_2",e,e],[24,0,38,"Digit3",24,"3",51,"VK_3",e,e],[25,0,39,"Digit4",25,"4",52,"VK_4",e,e],[26,0,40,"Digit5",26,"5",53,"VK_5",e,e],[27,0,41,"Digit6",27,"6",54,"VK_6",e,e],[28,0,42,"Digit7",28,"7",55,"VK_7",e,e],[29,0,43,"Digit8",29,"8",56,"VK_8",e,e],[30,0,44,"Digit9",30,"9",57,"VK_9",e,e],[21,0,45,"Digit0",21,"0",48,"VK_0",e,e],[3,1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[9,1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[2,1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[10,1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[83,0,51,"Minus",83,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[81,0,52,"Equal",81,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[87,0,53,"BracketLeft",87,"[",219,"VK_OEM_4","[","OEM_4"],[89,0,54,"BracketRight",89,"]",221,"VK_OEM_6","]","OEM_6"],[88,0,55,"Backslash",88,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,0,56,"IntlHash",0,e,0,e,e,e],[80,0,57,"Semicolon",80,";",186,"VK_OEM_1",";","OEM_1"],[90,0,58,"Quote",90,"'",222,"VK_OEM_7","'","OEM_7"],[86,0,59,"Backquote",86,"`",192,"VK_OEM_3","`","OEM_3"],[82,0,60,"Comma",82,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[84,0,61,"Period",84,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[85,0,62,"Slash",85,"/",191,"VK_OEM_2","/","OEM_2"],[8,1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[59,1,64,"F1",59,"F1",112,"VK_F1",e,e],[60,1,65,"F2",60,"F2",113,"VK_F2",e,e],[61,1,66,"F3",61,"F3",114,"VK_F3",e,e],[62,1,67,"F4",62,"F4",115,"VK_F4",e,e],[63,1,68,"F5",63,"F5",116,"VK_F5",e,e],[64,1,69,"F6",64,"F6",117,"VK_F6",e,e],[65,1,70,"F7",65,"F7",118,"VK_F7",e,e],[66,1,71,"F8",66,"F8",119,"VK_F8",e,e],[67,1,72,"F9",67,"F9",120,"VK_F9",e,e],[68,1,73,"F10",68,"F10",121,"VK_F10",e,e],[69,1,74,"F11",69,"F11",122,"VK_F11",e,e],[70,1,75,"F12",70,"F12",123,"VK_F12",e,e],[0,1,76,"PrintScreen",0,e,0,e,e,e],[79,1,77,"ScrollLock",79,"ScrollLock",145,"VK_SCROLL",e,e],[7,1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[19,1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[14,1,80,"Home",14,"Home",36,"VK_HOME",e,e],[11,1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[20,1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[13,1,83,"End",13,"End",35,"VK_END",e,e],[12,1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[17,1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[15,1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[18,1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[16,1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[78,1,89,"NumLock",78,"NumLock",144,"VK_NUMLOCK",e,e],[108,1,90,"NumpadDivide",108,"NumPad_Divide",111,"VK_DIVIDE",e,e],[103,1,91,"NumpadMultiply",103,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[106,1,92,"NumpadSubtract",106,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[104,1,93,"NumpadAdd",104,"NumPad_Add",107,"VK_ADD",e,e],[3,1,94,"NumpadEnter",3,e,0,e,e,e],[94,1,95,"Numpad1",94,"NumPad1",97,"VK_NUMPAD1",e,e],[95,1,96,"Numpad2",95,"NumPad2",98,"VK_NUMPAD2",e,e],[96,1,97,"Numpad3",96,"NumPad3",99,"VK_NUMPAD3",e,e],[97,1,98,"Numpad4",97,"NumPad4",100,"VK_NUMPAD4",e,e],[98,1,99,"Numpad5",98,"NumPad5",101,"VK_NUMPAD5",e,e],[99,1,100,"Numpad6",99,"NumPad6",102,"VK_NUMPAD6",e,e],[100,1,101,"Numpad7",100,"NumPad7",103,"VK_NUMPAD7",e,e],[101,1,102,"Numpad8",101,"NumPad8",104,"VK_NUMPAD8",e,e],[102,1,103,"Numpad9",102,"NumPad9",105,"VK_NUMPAD9",e,e],[93,1,104,"Numpad0",93,"NumPad0",96,"VK_NUMPAD0",e,e],[107,1,105,"NumpadDecimal",107,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[92,0,106,"IntlBackslash",92,"OEM_102",226,"VK_OEM_102",e,e],[58,1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[0,1,108,"Power",0,e,0,e,e,e],[0,1,109,"NumpadEqual",0,e,0,e,e,e],[71,1,110,"F13",71,"F13",124,"VK_F13",e,e],[72,1,111,"F14",72,"F14",125,"VK_F14",e,e],[73,1,112,"F15",73,"F15",126,"VK_F15",e,e],[74,1,113,"F16",74,"F16",127,"VK_F16",e,e],[75,1,114,"F17",75,"F17",128,"VK_F17",e,e],[76,1,115,"F18",76,"F18",129,"VK_F18",e,e],[77,1,116,"F19",77,"F19",130,"VK_F19",e,e],[0,1,117,"F20",0,e,0,"VK_F20",e,e],[0,1,118,"F21",0,e,0,"VK_F21",e,e],[0,1,119,"F22",0,e,0,"VK_F22",e,e],[0,1,120,"F23",0,e,0,"VK_F23",e,e],[0,1,121,"F24",0,e,0,"VK_F24",e,e],[0,1,122,"Open",0,e,0,e,e,e],[0,1,123,"Help",0,e,0,e,e,e],[0,1,124,"Select",0,e,0,e,e,e],[0,1,125,"Again",0,e,0,e,e,e],[0,1,126,"Undo",0,e,0,e,e,e],[0,1,127,"Cut",0,e,0,e,e,e],[0,1,128,"Copy",0,e,0,e,e,e],[0,1,129,"Paste",0,e,0,e,e,e],[0,1,130,"Find",0,e,0,e,e,e],[0,1,131,"AudioVolumeMute",112,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[0,1,132,"AudioVolumeUp",113,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[0,1,133,"AudioVolumeDown",114,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[105,1,134,"NumpadComma",105,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[110,0,135,"IntlRo",110,"ABNT_C1",193,"VK_ABNT_C1",e,e],[0,1,136,"KanaMode",0,e,0,e,e,e],[0,0,137,"IntlYen",0,e,0,e,e,e],[0,1,138,"Convert",0,e,0,e,e,e],[0,1,139,"NonConvert",0,e,0,e,e,e],[0,1,140,"Lang1",0,e,0,e,e,e],[0,1,141,"Lang2",0,e,0,e,e,e],[0,1,142,"Lang3",0,e,0,e,e,e],[0,1,143,"Lang4",0,e,0,e,e,e],[0,1,144,"Lang5",0,e,0,e,e,e],[0,1,145,"Abort",0,e,0,e,e,e],[0,1,146,"Props",0,e,0,e,e,e],[0,1,147,"NumpadParenLeft",0,e,0,e,e,e],[0,1,148,"NumpadParenRight",0,e,0,e,e,e],[0,1,149,"NumpadBackspace",0,e,0,e,e,e],[0,1,150,"NumpadMemoryStore",0,e,0,e,e,e],[0,1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[0,1,152,"NumpadMemoryClear",0,e,0,e,e,e],[0,1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[0,1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[0,1,155,"NumpadClear",126,"Clear",12,"VK_CLEAR",e,e],[0,1,156,"NumpadClearEntry",0,e,0,e,e,e],[5,1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[4,1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[6,1,0,e,6,"Alt",18,"VK_MENU",e,e],[57,1,0,e,57,"Meta",0,"VK_COMMAND",e,e],[5,1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[4,1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[6,1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[57,1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[5,1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[4,1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[6,1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[57,1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[0,1,165,"BrightnessUp",0,e,0,e,e,e],[0,1,166,"BrightnessDown",0,e,0,e,e,e],[0,1,167,"MediaPlay",0,e,0,e,e,e],[0,1,168,"MediaRecord",0,e,0,e,e,e],[0,1,169,"MediaFastForward",0,e,0,e,e,e],[0,1,170,"MediaRewind",0,e,0,e,e,e],[114,1,171,"MediaTrackNext",119,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[115,1,172,"MediaTrackPrevious",120,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[116,1,173,"MediaStop",121,"MediaStop",178,"VK_MEDIA_STOP",e,e],[0,1,174,"Eject",0,e,0,e,e,e],[117,1,175,"MediaPlayPause",122,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[0,1,176,"MediaSelect",123,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[0,1,177,"LaunchMail",124,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[0,1,178,"LaunchApp2",125,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[0,1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[0,1,180,"SelectTask",0,e,0,e,e,e],[0,1,181,"LaunchScreenSaver",0,e,0,e,e,e],[0,1,182,"BrowserSearch",115,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[0,1,183,"BrowserHome",116,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[112,1,184,"BrowserBack",117,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[113,1,185,"BrowserForward",118,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[0,1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[0,1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[0,1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[0,1,189,"ZoomToggle",0,e,0,e,e,e],[0,1,190,"MailReply",0,e,0,e,e,e],[0,1,191,"MailForward",0,e,0,e,e,e],[0,1,192,"MailSend",0,e,0,e,e,e],[109,1,0,e,109,"KeyInComposition",229,e,e,e],[111,1,0,e,111,"ABNT_C2",194,"VK_ABNT_C2",e,e],[91,1,0,e,91,"OEM_8",223,"VK_OEM_8",e,e],[0,1,0,e,0,e,0,"VK_KANA",e,e],[0,1,0,e,0,e,0,"VK_HANGUL",e,e],[0,1,0,e,0,e,0,"VK_JUNJA",e,e],[0,1,0,e,0,e,0,"VK_FINAL",e,e],[0,1,0,e,0,e,0,"VK_HANJA",e,e],[0,1,0,e,0,e,0,"VK_KANJI",e,e],[0,1,0,e,0,e,0,"VK_CONVERT",e,e],[0,1,0,e,0,e,0,"VK_NONCONVERT",e,e],[0,1,0,e,0,e,0,"VK_ACCEPT",e,e],[0,1,0,e,0,e,0,"VK_MODECHANGE",e,e],[0,1,0,e,0,e,0,"VK_SELECT",e,e],[0,1,0,e,0,e,0,"VK_PRINT",e,e],[0,1,0,e,0,e,0,"VK_EXECUTE",e,e],[0,1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[0,1,0,e,0,e,0,"VK_HELP",e,e],[0,1,0,e,0,e,0,"VK_APPS",e,e],[0,1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[0,1,0,e,0,e,0,"VK_PACKET",e,e],[0,1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[0,1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[0,1,0,e,0,e,0,"VK_ATTN",e,e],[0,1,0,e,0,e,0,"VK_CRSEL",e,e],[0,1,0,e,0,e,0,"VK_EXSEL",e,e],[0,1,0,e,0,e,0,"VK_EREOF",e,e],[0,1,0,e,0,e,0,"VK_PLAY",e,e],[0,1,0,e,0,e,0,"VK_ZOOM",e,e],[0,1,0,e,0,e,0,"VK_NONAME",e,e],[0,1,0,e,0,e,0,"VK_PA1",e,e],[0,1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]];let n=[],i=[];for(const o of t){const[s,a,l,c,u,d,h,f,y,v]=o;if(i[l]||(i[l]=!0,ci[l]=c,hi[c]=l,di[c.toLowerCase()]=l,a&&(It[l]=u,u!==0&&u!==3&&u!==5&&u!==4&&u!==6&&u!==57&&(T1[u]=l))),!n[u]){if(n[u]=!0,!d)throw new Error(`String representation missing for key code ${u} around scan code ${c}`);s1.define(u,d),P1.define(u,y||d),I1.define(u,v||y||d)}h&&(li[h]=u),f&&(ui[f]=u)}T1[3]=46})();var Tt;(function(e){function t(l){return s1.keyCodeToStr(l)}e.toString=t;function n(l){return s1.strToKeyCode(l)}e.fromString=n;function i(l){return P1.keyCodeToStr(l)}e.toUserSettingsUS=i;function o(l){return I1.keyCodeToStr(l)}e.toUserSettingsGeneral=o;function s(l){return P1.strToKeyCode(l)||I1.strToKeyCode(l)}e.fromUserSettings=s;function a(l){if(l>=93&&l<=108)return null;switch(l){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return s1.keyCodeToStr(l)}e.toElectronAccelerator=a})(Tt||(Tt={}));function fi(e,t){const n=(t&65535)<<16>>>0;return(e|n)>>>0}class j extends D{constructor(t,n,i,o){super(t,n,i,o);this.selectionStartLineNumber=t,this.selectionStartColumn=n,this.positionLineNumber=i,this.positionColumn=o}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(t){return j.selectionsEqual(this,t)}static selectionsEqual(t,n){return t.selectionStartLineNumber===n.selectionStartLineNumber&&t.selectionStartColumn===n.selectionStartColumn&&t.positionLineNumber===n.positionLineNumber&&t.positionColumn===n.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new j(this.startLineNumber,this.startColumn,t,n):new j(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new $(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new $(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new j(t,n,this.endLineNumber,this.endColumn):new j(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new j(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new j(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new j(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new j(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(t,n){if(t&&!n||!t&&n)return!1;if(!t&&!n)return!0;if(t.length!==n.length)return!1;for(let i=0,o=t.length;i<o;i++)if(!this.selectionsEqual(t[i],n[i]))return!1;return!0}static isISelection(t){return t&&typeof t.selectionStartLineNumber=="number"&&typeof t.selectionStartColumn=="number"&&typeof t.positionLineNumber=="number"&&typeof t.positionColumn=="number"}static createWithDirection(t,n,i,o,s){return s===0?new j(t,n,i,o):new j(i,o,t,n)}}var K1=function(e,t,n,i){function o(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function l(d){try{u(i.next(d))}catch(h){a(h)}}function c(d){try{u(i.throw(d))}catch(h){a(h)}}function u(d){d.done?s(d.value):o(d.value).then(l,c)}u((i=i.apply(e,t||[])).next())})};class mi{constructor(){this._map=new Map,this._factories=new Map,this._onDidChange=new ee,this.onDidChange=this._onDidChange.event,this._colorMap=null}fire(t){this._onDidChange.fire({changedLanguages:t,changedColorMap:!1})}register(t,n){return this._map.set(t,n),this.fire([t]),Ge(()=>{this._map.get(t)===n&&(this._map.delete(t),this.fire([t]))})}registerFactory(t,n){var i;(i=this._factories.get(t))===null||i===void 0||i.dispose();const o=new gi(this,t,n);return this._factories.set(t,o),Ge(()=>{const s=this._factories.get(t);!s||s!==o||(this._factories.delete(t),s.dispose())})}getOrCreate(t){return K1(this,void 0,void 0,function*(){const n=this.get(t);if(n)return n;const i=this._factories.get(t);return!i||i.isResolved?null:(yield i.resolve(),this.get(t))})}get(t){return this._map.get(t)||null}isResolved(t){if(this.get(t))return!0;const n=this._factories.get(t);return!!(!n||n.isResolved)}setColorMap(t){this._colorMap=t,this._onDidChange.fire({changedLanguages:Array.from(this._map.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}}class gi extends Fe{constructor(t,n,i){super();this._registry=t,this._languageId=n,this._factory=i,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}get isResolved(){return this._isResolved}dispose(){this._isDisposed=!0,super.dispose()}resolve(){return K1(this,void 0,void 0,function*(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise})}_create(){return K1(this,void 0,void 0,function*(){const t=yield Promise.resolve(this._factory.createTokenizationSupport());this._isResolved=!0,t&&!this._isDisposed&&this._register(this._registry.register(this._languageId,t))})}}function Zo(e){return e?e.replace(/\$\((.*?)\)/g,(t,n)=>` ${n} `).trim():""}class r{constructor(t,n,i){this.id=t,this.definition=n,this.description=i,r._allCodicons.push(this)}get classNames(){return"codicon codicon-"+this.id}get classNamesArray(){return["codicon","codicon-"+this.id]}get cssSelector(){return".codicon.codicon-"+this.id}static getAll(){return r._allCodicons}}r._allCodicons=[],r.add=new r("add",{fontCharacter:"\\ea60"}),r.plus=new r("plus",r.add.definition),r.gistNew=new r("gist-new",r.add.definition),r.repoCreate=new r("repo-create",r.add.definition),r.lightbulb=new r("lightbulb",{fontCharacter:"\\ea61"}),r.lightBulb=new r("light-bulb",{fontCharacter:"\\ea61"}),r.repo=new r("repo",{fontCharacter:"\\ea62"}),r.repoDelete=new r("repo-delete",{fontCharacter:"\\ea62"}),r.gistFork=new r("gist-fork",{fontCharacter:"\\ea63"}),r.repoForked=new r("repo-forked",{fontCharacter:"\\ea63"}),r.gitPullRequest=new r("git-pull-request",{fontCharacter:"\\ea64"}),r.gitPullRequestAbandoned=new r("git-pull-request-abandoned",{fontCharacter:"\\ea64"}),r.recordKeys=new r("record-keys",{fontCharacter:"\\ea65"}),r.keyboard=new r("keyboard",{fontCharacter:"\\ea65"}),r.tag=new r("tag",{fontCharacter:"\\ea66"}),r.tagAdd=new r("tag-add",{fontCharacter:"\\ea66"}),r.tagRemove=new r("tag-remove",{fontCharacter:"\\ea66"}),r.person=new r("person",{fontCharacter:"\\ea67"}),r.personFollow=new r("person-follow",{fontCharacter:"\\ea67"}),r.personOutline=new r("person-outline",{fontCharacter:"\\ea67"}),r.personFilled=new r("person-filled",{fontCharacter:"\\ea67"}),r.gitBranch=new r("git-branch",{fontCharacter:"\\ea68"}),r.gitBranchCreate=new r("git-branch-create",{fontCharacter:"\\ea68"}),r.gitBranchDelete=new r("git-branch-delete",{fontCharacter:"\\ea68"}),r.sourceControl=new r("source-control",{fontCharacter:"\\ea68"}),r.mirror=new r("mirror",{fontCharacter:"\\ea69"}),r.mirrorPublic=new r("mirror-public",{fontCharacter:"\\ea69"}),r.star=new r("star",{fontCharacter:"\\ea6a"}),r.starAdd=new r("star-add",{fontCharacter:"\\ea6a"}),r.starDelete=new r("star-delete",{fontCharacter:"\\ea6a"}),r.starEmpty=new r("star-empty",{fontCharacter:"\\ea6a"}),r.comment=new r("comment",{fontCharacter:"\\ea6b"}),r.commentAdd=new r("comment-add",{fontCharacter:"\\ea6b"}),r.alert=new r("alert",{fontCharacter:"\\ea6c"}),r.warning=new r("warning",{fontCharacter:"\\ea6c"}),r.search=new r("search",{fontCharacter:"\\ea6d"}),r.searchSave=new r("search-save",{fontCharacter:"\\ea6d"}),r.logOut=new r("log-out",{fontCharacter:"\\ea6e"}),r.signOut=new r("sign-out",{fontCharacter:"\\ea6e"}),r.logIn=new r("log-in",{fontCharacter:"\\ea6f"}),r.signIn=new r("sign-in",{fontCharacter:"\\ea6f"}),r.eye=new r("eye",{fontCharacter:"\\ea70"}),r.eyeUnwatch=new r("eye-unwatch",{fontCharacter:"\\ea70"}),r.eyeWatch=new r("eye-watch",{fontCharacter:"\\ea70"}),r.circleFilled=new r("circle-filled",{fontCharacter:"\\ea71"}),r.primitiveDot=new r("primitive-dot",{fontCharacter:"\\ea71"}),r.closeDirty=new r("close-dirty",{fontCharacter:"\\ea71"}),r.debugBreakpoint=new r("debug-breakpoint",{fontCharacter:"\\ea71"}),r.debugBreakpointDisabled=new r("debug-breakpoint-disabled",{fontCharacter:"\\ea71"}),r.debugHint=new r("debug-hint",{fontCharacter:"\\ea71"}),r.primitiveSquare=new r("primitive-square",{fontCharacter:"\\ea72"}),r.edit=new r("edit",{fontCharacter:"\\ea73"}),r.pencil=new r("pencil",{fontCharacter:"\\ea73"}),r.info=new r("info",{fontCharacter:"\\ea74"}),r.issueOpened=new r("issue-opened",{fontCharacter:"\\ea74"}),r.gistPrivate=new r("gist-private",{fontCharacter:"\\ea75"}),r.gitForkPrivate=new r("git-fork-private",{fontCharacter:"\\ea75"}),r.lock=new r("lock",{fontCharacter:"\\ea75"}),r.mirrorPrivate=new r("mirror-private",{fontCharacter:"\\ea75"}),r.close=new r("close",{fontCharacter:"\\ea76"}),r.removeClose=new r("remove-close",{fontCharacter:"\\ea76"}),r.x=new r("x",{fontCharacter:"\\ea76"}),r.repoSync=new r("repo-sync",{fontCharacter:"\\ea77"}),r.sync=new r("sync",{fontCharacter:"\\ea77"}),r.clone=new r("clone",{fontCharacter:"\\ea78"}),r.desktopDownload=new r("desktop-download",{fontCharacter:"\\ea78"}),r.beaker=new r("beaker",{fontCharacter:"\\ea79"}),r.microscope=new r("microscope",{fontCharacter:"\\ea79"}),r.vm=new r("vm",{fontCharacter:"\\ea7a"}),r.deviceDesktop=new r("device-desktop",{fontCharacter:"\\ea7a"}),r.file=new r("file",{fontCharacter:"\\ea7b"}),r.fileText=new r("file-text",{fontCharacter:"\\ea7b"}),r.more=new r("more",{fontCharacter:"\\ea7c"}),r.ellipsis=new r("ellipsis",{fontCharacter:"\\ea7c"}),r.kebabHorizontal=new r("kebab-horizontal",{fontCharacter:"\\ea7c"}),r.mailReply=new r("mail-reply",{fontCharacter:"\\ea7d"}),r.reply=new r("reply",{fontCharacter:"\\ea7d"}),r.organization=new r("organization",{fontCharacter:"\\ea7e"}),r.organizationFilled=new r("organization-filled",{fontCharacter:"\\ea7e"}),r.organizationOutline=new r("organization-outline",{fontCharacter:"\\ea7e"}),r.newFile=new r("new-file",{fontCharacter:"\\ea7f"}),r.fileAdd=new r("file-add",{fontCharacter:"\\ea7f"}),r.newFolder=new r("new-folder",{fontCharacter:"\\ea80"}),r.fileDirectoryCreate=new r("file-directory-create",{fontCharacter:"\\ea80"}),r.trash=new r("trash",{fontCharacter:"\\ea81"}),r.trashcan=new r("trashcan",{fontCharacter:"\\ea81"}),r.history=new r("history",{fontCharacter:"\\ea82"}),r.clock=new r("clock",{fontCharacter:"\\ea82"}),r.folder=new r("folder",{fontCharacter:"\\ea83"}),r.fileDirectory=new r("file-directory",{fontCharacter:"\\ea83"}),r.symbolFolder=new r("symbol-folder",{fontCharacter:"\\ea83"}),r.logoGithub=new r("logo-github",{fontCharacter:"\\ea84"}),r.markGithub=new r("mark-github",{fontCharacter:"\\ea84"}),r.github=new r("github",{fontCharacter:"\\ea84"}),r.terminal=new r("terminal",{fontCharacter:"\\ea85"}),r.console=new r("console",{fontCharacter:"\\ea85"}),r.repl=new r("repl",{fontCharacter:"\\ea85"}),r.zap=new r("zap",{fontCharacter:"\\ea86"}),r.symbolEvent=new r("symbol-event",{fontCharacter:"\\ea86"}),r.error=new r("error",{fontCharacter:"\\ea87"}),r.stop=new r("stop",{fontCharacter:"\\ea87"}),r.variable=new r("variable",{fontCharacter:"\\ea88"}),r.symbolVariable=new r("symbol-variable",{fontCharacter:"\\ea88"}),r.array=new r("array",{fontCharacter:"\\ea8a"}),r.symbolArray=new r("symbol-array",{fontCharacter:"\\ea8a"}),r.symbolModule=new r("symbol-module",{fontCharacter:"\\ea8b"}),r.symbolPackage=new r("symbol-package",{fontCharacter:"\\ea8b"}),r.symbolNamespace=new r("symbol-namespace",{fontCharacter:"\\ea8b"}),r.symbolObject=new r("symbol-object",{fontCharacter:"\\ea8b"}),r.symbolMethod=new r("symbol-method",{fontCharacter:"\\ea8c"}),r.symbolFunction=new r("symbol-function",{fontCharacter:"\\ea8c"}),r.symbolConstructor=new r("symbol-constructor",{fontCharacter:"\\ea8c"}),r.symbolBoolean=new r("symbol-boolean",{fontCharacter:"\\ea8f"}),r.symbolNull=new r("symbol-null",{fontCharacter:"\\ea8f"}),r.symbolNumeric=new r("symbol-numeric",{fontCharacter:"\\ea90"}),r.symbolNumber=new r("symbol-number",{fontCharacter:"\\ea90"}),r.symbolStructure=new r("symbol-structure",{fontCharacter:"\\ea91"}),r.symbolStruct=new r("symbol-struct",{fontCharacter:"\\ea91"}),r.symbolParameter=new r("symbol-parameter",{fontCharacter:"\\ea92"}),r.symbolTypeParameter=new r("symbol-type-parameter",{fontCharacter:"\\ea92"}),r.symbolKey=new r("symbol-key",{fontCharacter:"\\ea93"}),r.symbolText=new r("symbol-text",{fontCharacter:"\\ea93"}),r.symbolReference=new r("symbol-reference",{fontCharacter:"\\ea94"}),r.goToFile=new r("go-to-file",{fontCharacter:"\\ea94"}),r.symbolEnum=new r("symbol-enum",{fontCharacter:"\\ea95"}),r.symbolValue=new r("symbol-value",{fontCharacter:"\\ea95"}),r.symbolRuler=new r("symbol-ruler",{fontCharacter:"\\ea96"}),r.symbolUnit=new r("symbol-unit",{fontCharacter:"\\ea96"}),r.activateBreakpoints=new r("activate-breakpoints",{fontCharacter:"\\ea97"}),r.archive=new r("archive",{fontCharacter:"\\ea98"}),r.arrowBoth=new r("arrow-both",{fontCharacter:"\\ea99"}),r.arrowDown=new r("arrow-down",{fontCharacter:"\\ea9a"}),r.arrowLeft=new r("arrow-left",{fontCharacter:"\\ea9b"}),r.arrowRight=new r("arrow-right",{fontCharacter:"\\ea9c"}),r.arrowSmallDown=new r("arrow-small-down",{fontCharacter:"\\ea9d"}),r.arrowSmallLeft=new r("arrow-small-left",{fontCharacter:"\\ea9e"}),r.arrowSmallRight=new r("arrow-small-right",{fontCharacter:"\\ea9f"}),r.arrowSmallUp=new r("arrow-small-up",{fontCharacter:"\\eaa0"}),r.arrowUp=new r("arrow-up",{fontCharacter:"\\eaa1"}),r.bell=new r("bell",{fontCharacter:"\\eaa2"}),r.bold=new r("bold",{fontCharacter:"\\eaa3"}),r.book=new r("book",{fontCharacter:"\\eaa4"}),r.bookmark=new r("bookmark",{fontCharacter:"\\eaa5"}),r.debugBreakpointConditionalUnverified=new r("debug-breakpoint-conditional-unverified",{fontCharacter:"\\eaa6"}),r.debugBreakpointConditional=new r("debug-breakpoint-conditional",{fontCharacter:"\\eaa7"}),r.debugBreakpointConditionalDisabled=new r("debug-breakpoint-conditional-disabled",{fontCharacter:"\\eaa7"}),r.debugBreakpointDataUnverified=new r("debug-breakpoint-data-unverified",{fontCharacter:"\\eaa8"}),r.debugBreakpointData=new r("debug-breakpoint-data",{fontCharacter:"\\eaa9"}),r.debugBreakpointDataDisabled=new r("debug-breakpoint-data-disabled",{fontCharacter:"\\eaa9"}),r.debugBreakpointLogUnverified=new r("debug-breakpoint-log-unverified",{fontCharacter:"\\eaaa"}),r.debugBreakpointLog=new r("debug-breakpoint-log",{fontCharacter:"\\eaab"}),r.debugBreakpointLogDisabled=new r("debug-breakpoint-log-disabled",{fontCharacter:"\\eaab"}),r.briefcase=new r("briefcase",{fontCharacter:"\\eaac"}),r.broadcast=new r("broadcast",{fontCharacter:"\\eaad"}),r.browser=new r("browser",{fontCharacter:"\\eaae"}),r.bug=new r("bug",{fontCharacter:"\\eaaf"}),r.calendar=new r("calendar",{fontCharacter:"\\eab0"}),r.caseSensitive=new r("case-sensitive",{fontCharacter:"\\eab1"}),r.check=new r("check",{fontCharacter:"\\eab2"}),r.checklist=new r("checklist",{fontCharacter:"\\eab3"}),r.chevronDown=new r("chevron-down",{fontCharacter:"\\eab4"}),r.dropDownButton=new r("drop-down-button",r.chevronDown.definition),r.chevronLeft=new r("chevron-left",{fontCharacter:"\\eab5"}),r.chevronRight=new r("chevron-right",{fontCharacter:"\\eab6"}),r.chevronUp=new r("chevron-up",{fontCharacter:"\\eab7"}),r.chromeClose=new r("chrome-close",{fontCharacter:"\\eab8"}),r.chromeMaximize=new r("chrome-maximize",{fontCharacter:"\\eab9"}),r.chromeMinimize=new r("chrome-minimize",{fontCharacter:"\\eaba"}),r.chromeRestore=new r("chrome-restore",{fontCharacter:"\\eabb"}),r.circleOutline=new r("circle-outline",{fontCharacter:"\\eabc"}),r.debugBreakpointUnverified=new r("debug-breakpoint-unverified",{fontCharacter:"\\eabc"}),r.circleSlash=new r("circle-slash",{fontCharacter:"\\eabd"}),r.circuitBoard=new r("circuit-board",{fontCharacter:"\\eabe"}),r.clearAll=new r("clear-all",{fontCharacter:"\\eabf"}),r.clippy=new r("clippy",{fontCharacter:"\\eac0"}),r.closeAll=new r("close-all",{fontCharacter:"\\eac1"}),r.cloudDownload=new r("cloud-download",{fontCharacter:"\\eac2"}),r.cloudUpload=new r("cloud-upload",{fontCharacter:"\\eac3"}),r.code=new r("code",{fontCharacter:"\\eac4"}),r.collapseAll=new r("collapse-all",{fontCharacter:"\\eac5"}),r.colorMode=new r("color-mode",{fontCharacter:"\\eac6"}),r.commentDiscussion=new r("comment-discussion",{fontCharacter:"\\eac7"}),r.compareChanges=new r("compare-changes",{fontCharacter:"\\eafd"}),r.creditCard=new r("credit-card",{fontCharacter:"\\eac9"}),r.dash=new r("dash",{fontCharacter:"\\eacc"}),r.dashboard=new r("dashboard",{fontCharacter:"\\eacd"}),r.database=new r("database",{fontCharacter:"\\eace"}),r.debugContinue=new r("debug-continue",{fontCharacter:"\\eacf"}),r.debugDisconnect=new r("debug-disconnect",{fontCharacter:"\\ead0"}),r.debugPause=new r("debug-pause",{fontCharacter:"\\ead1"}),r.debugRestart=new r("debug-restart",{fontCharacter:"\\ead2"}),r.debugStart=new r("debug-start",{fontCharacter:"\\ead3"}),r.debugStepInto=new r("debug-step-into",{fontCharacter:"\\ead4"}),r.debugStepOut=new r("debug-step-out",{fontCharacter:"\\ead5"}),r.debugStepOver=new r("debug-step-over",{fontCharacter:"\\ead6"}),r.debugStop=new r("debug-stop",{fontCharacter:"\\ead7"}),r.debug=new r("debug",{fontCharacter:"\\ead8"}),r.deviceCameraVideo=new r("device-camera-video",{fontCharacter:"\\ead9"}),r.deviceCamera=new r("device-camera",{fontCharacter:"\\eada"}),r.deviceMobile=new r("device-mobile",{fontCharacter:"\\eadb"}),r.diffAdded=new r("diff-added",{fontCharacter:"\\eadc"}),r.diffIgnored=new r("diff-ignored",{fontCharacter:"\\eadd"}),r.diffModified=new r("diff-modified",{fontCharacter:"\\eade"}),r.diffRemoved=new r("diff-removed",{fontCharacter:"\\eadf"}),r.diffRenamed=new r("diff-renamed",{fontCharacter:"\\eae0"}),r.diff=new r("diff",{fontCharacter:"\\eae1"}),r.discard=new r("discard",{fontCharacter:"\\eae2"}),r.editorLayout=new r("editor-layout",{fontCharacter:"\\eae3"}),r.emptyWindow=new r("empty-window",{fontCharacter:"\\eae4"}),r.exclude=new r("exclude",{fontCharacter:"\\eae5"}),r.extensions=new r("extensions",{fontCharacter:"\\eae6"}),r.eyeClosed=new r("eye-closed",{fontCharacter:"\\eae7"}),r.fileBinary=new r("file-binary",{fontCharacter:"\\eae8"}),r.fileCode=new r("file-code",{fontCharacter:"\\eae9"}),r.fileMedia=new r("file-media",{fontCharacter:"\\eaea"}),r.filePdf=new r("file-pdf",{fontCharacter:"\\eaeb"}),r.fileSubmodule=new r("file-submodule",{fontCharacter:"\\eaec"}),r.fileSymlinkDirectory=new r("file-symlink-directory",{fontCharacter:"\\eaed"}),r.fileSymlinkFile=new r("file-symlink-file",{fontCharacter:"\\eaee"}),r.fileZip=new r("file-zip",{fontCharacter:"\\eaef"}),r.files=new r("files",{fontCharacter:"\\eaf0"}),r.filter=new r("filter",{fontCharacter:"\\eaf1"}),r.flame=new r("flame",{fontCharacter:"\\eaf2"}),r.foldDown=new r("fold-down",{fontCharacter:"\\eaf3"}),r.foldUp=new r("fold-up",{fontCharacter:"\\eaf4"}),r.fold=new r("fold",{fontCharacter:"\\eaf5"}),r.folderActive=new r("folder-active",{fontCharacter:"\\eaf6"}),r.folderOpened=new r("folder-opened",{fontCharacter:"\\eaf7"}),r.gear=new r("gear",{fontCharacter:"\\eaf8"}),r.gift=new r("gift",{fontCharacter:"\\eaf9"}),r.gistSecret=new r("gist-secret",{fontCharacter:"\\eafa"}),r.gist=new r("gist",{fontCharacter:"\\eafb"}),r.gitCommit=new r("git-commit",{fontCharacter:"\\eafc"}),r.gitCompare=new r("git-compare",{fontCharacter:"\\eafd"}),r.gitMerge=new r("git-merge",{fontCharacter:"\\eafe"}),r.githubAction=new r("github-action",{fontCharacter:"\\eaff"}),r.githubAlt=new r("github-alt",{fontCharacter:"\\eb00"}),r.globe=new r("globe",{fontCharacter:"\\eb01"}),r.grabber=new r("grabber",{fontCharacter:"\\eb02"}),r.graph=new r("graph",{fontCharacter:"\\eb03"}),r.gripper=new r("gripper",{fontCharacter:"\\eb04"}),r.heart=new r("heart",{fontCharacter:"\\eb05"}),r.home=new r("home",{fontCharacter:"\\eb06"}),r.horizontalRule=new r("horizontal-rule",{fontCharacter:"\\eb07"}),r.hubot=new r("hubot",{fontCharacter:"\\eb08"}),r.inbox=new r("inbox",{fontCharacter:"\\eb09"}),r.issueClosed=new r("issue-closed",{fontCharacter:"\\eba4"}),r.issueReopened=new r("issue-reopened",{fontCharacter:"\\eb0b"}),r.issues=new r("issues",{fontCharacter:"\\eb0c"}),r.italic=new r("italic",{fontCharacter:"\\eb0d"}),r.jersey=new r("jersey",{fontCharacter:"\\eb0e"}),r.json=new r("json",{fontCharacter:"\\eb0f"}),r.kebabVertical=new r("kebab-vertical",{fontCharacter:"\\eb10"}),r.key=new r("key",{fontCharacter:"\\eb11"}),r.law=new r("law",{fontCharacter:"\\eb12"}),r.lightbulbAutofix=new r("lightbulb-autofix",{fontCharacter:"\\eb13"}),r.linkExternal=new r("link-external",{fontCharacter:"\\eb14"}),r.link=new r("link",{fontCharacter:"\\eb15"}),r.listOrdered=new r("list-ordered",{fontCharacter:"\\eb16"}),r.listUnordered=new r("list-unordered",{fontCharacter:"\\eb17"}),r.liveShare=new r("live-share",{fontCharacter:"\\eb18"}),r.loading=new r("loading",{fontCharacter:"\\eb19"}),r.location=new r("location",{fontCharacter:"\\eb1a"}),r.mailRead=new r("mail-read",{fontCharacter:"\\eb1b"}),r.mail=new r("mail",{fontCharacter:"\\eb1c"}),r.markdown=new r("markdown",{fontCharacter:"\\eb1d"}),r.megaphone=new r("megaphone",{fontCharacter:"\\eb1e"}),r.mention=new r("mention",{fontCharacter:"\\eb1f"}),r.milestone=new r("milestone",{fontCharacter:"\\eb20"}),r.mortarBoard=new r("mortar-board",{fontCharacter:"\\eb21"}),r.move=new r("move",{fontCharacter:"\\eb22"}),r.multipleWindows=new r("multiple-windows",{fontCharacter:"\\eb23"}),r.mute=new r("mute",{fontCharacter:"\\eb24"}),r.noNewline=new r("no-newline",{fontCharacter:"\\eb25"}),r.note=new r("note",{fontCharacter:"\\eb26"}),r.octoface=new r("octoface",{fontCharacter:"\\eb27"}),r.openPreview=new r("open-preview",{fontCharacter:"\\eb28"}),r.package_=new r("package",{fontCharacter:"\\eb29"}),r.paintcan=new r("paintcan",{fontCharacter:"\\eb2a"}),r.pin=new r("pin",{fontCharacter:"\\eb2b"}),r.play=new r("play",{fontCharacter:"\\eb2c"}),r.run=new r("run",{fontCharacter:"\\eb2c"}),r.plug=new r("plug",{fontCharacter:"\\eb2d"}),r.preserveCase=new r("preserve-case",{fontCharacter:"\\eb2e"}),r.preview=new r("preview",{fontCharacter:"\\eb2f"}),r.project=new r("project",{fontCharacter:"\\eb30"}),r.pulse=new r("pulse",{fontCharacter:"\\eb31"}),r.question=new r("question",{fontCharacter:"\\eb32"}),r.quote=new r("quote",{fontCharacter:"\\eb33"}),r.radioTower=new r("radio-tower",{fontCharacter:"\\eb34"}),r.reactions=new r("reactions",{fontCharacter:"\\eb35"}),r.references=new r("references",{fontCharacter:"\\eb36"}),r.refresh=new r("refresh",{fontCharacter:"\\eb37"}),r.regex=new r("regex",{fontCharacter:"\\eb38"}),r.remoteExplorer=new r("remote-explorer",{fontCharacter:"\\eb39"}),r.remote=new r("remote",{fontCharacter:"\\eb3a"}),r.remove=new r("remove",{fontCharacter:"\\eb3b"}),r.replaceAll=new r("replace-all",{fontCharacter:"\\eb3c"}),r.replace=new r("replace",{fontCharacter:"\\eb3d"}),r.repoClone=new r("repo-clone",{fontCharacter:"\\eb3e"}),r.repoForcePush=new r("repo-force-push",{fontCharacter:"\\eb3f"}),r.repoPull=new r("repo-pull",{fontCharacter:"\\eb40"}),r.repoPush=new r("repo-push",{fontCharacter:"\\eb41"}),r.report=new r("report",{fontCharacter:"\\eb42"}),r.requestChanges=new r("request-changes",{fontCharacter:"\\eb43"}),r.rocket=new r("rocket",{fontCharacter:"\\eb44"}),r.rootFolderOpened=new r("root-folder-opened",{fontCharacter:"\\eb45"}),r.rootFolder=new r("root-folder",{fontCharacter:"\\eb46"}),r.rss=new r("rss",{fontCharacter:"\\eb47"}),r.ruby=new r("ruby",{fontCharacter:"\\eb48"}),r.saveAll=new r("save-all",{fontCharacter:"\\eb49"}),r.saveAs=new r("save-as",{fontCharacter:"\\eb4a"}),r.save=new r("save",{fontCharacter:"\\eb4b"}),r.screenFull=new r("screen-full",{fontCharacter:"\\eb4c"}),r.screenNormal=new r("screen-normal",{fontCharacter:"\\eb4d"}),r.searchStop=new r("search-stop",{fontCharacter:"\\eb4e"}),r.server=new r("server",{fontCharacter:"\\eb50"}),r.settingsGear=new r("settings-gear",{fontCharacter:"\\eb51"}),r.settings=new r("settings",{fontCharacter:"\\eb52"}),r.shield=new r("shield",{fontCharacter:"\\eb53"}),r.smiley=new r("smiley",{fontCharacter:"\\eb54"}),r.sortPrecedence=new r("sort-precedence",{fontCharacter:"\\eb55"}),r.splitHorizontal=new r("split-horizontal",{fontCharacter:"\\eb56"}),r.splitVertical=new r("split-vertical",{fontCharacter:"\\eb57"}),r.squirrel=new r("squirrel",{fontCharacter:"\\eb58"}),r.starFull=new r("star-full",{fontCharacter:"\\eb59"}),r.starHalf=new r("star-half",{fontCharacter:"\\eb5a"}),r.symbolClass=new r("symbol-class",{fontCharacter:"\\eb5b"}),r.symbolColor=new r("symbol-color",{fontCharacter:"\\eb5c"}),r.symbolCustomColor=new r("symbol-customcolor",{fontCharacter:"\\eb5c"}),r.symbolConstant=new r("symbol-constant",{fontCharacter:"\\eb5d"}),r.symbolEnumMember=new r("symbol-enum-member",{fontCharacter:"\\eb5e"}),r.symbolField=new r("symbol-field",{fontCharacter:"\\eb5f"}),r.symbolFile=new r("symbol-file",{fontCharacter:"\\eb60"}),r.symbolInterface=new r("symbol-interface",{fontCharacter:"\\eb61"}),r.symbolKeyword=new r("symbol-keyword",{fontCharacter:"\\eb62"}),r.symbolMisc=new r("symbol-misc",{fontCharacter:"\\eb63"}),r.symbolOperator=new r("symbol-operator",{fontCharacter:"\\eb64"}),r.symbolProperty=new r("symbol-property",{fontCharacter:"\\eb65"}),r.wrench=new r("wrench",{fontCharacter:"\\eb65"}),r.wrenchSubaction=new r("wrench-subaction",{fontCharacter:"\\eb65"}),r.symbolSnippet=new r("symbol-snippet",{fontCharacter:"\\eb66"}),r.tasklist=new r("tasklist",{fontCharacter:"\\eb67"}),r.telescope=new r("telescope",{fontCharacter:"\\eb68"}),r.textSize=new r("text-size",{fontCharacter:"\\eb69"}),r.threeBars=new r("three-bars",{fontCharacter:"\\eb6a"}),r.thumbsdown=new r("thumbsdown",{fontCharacter:"\\eb6b"}),r.thumbsup=new r("thumbsup",{fontCharacter:"\\eb6c"}),r.tools=new r("tools",{fontCharacter:"\\eb6d"}),r.triangleDown=new r("triangle-down",{fontCharacter:"\\eb6e"}),r.triangleLeft=new r("triangle-left",{fontCharacter:"\\eb6f"}),r.triangleRight=new r("triangle-right",{fontCharacter:"\\eb70"}),r.triangleUp=new r("triangle-up",{fontCharacter:"\\eb71"}),r.twitter=new r("twitter",{fontCharacter:"\\eb72"}),r.unfold=new r("unfold",{fontCharacter:"\\eb73"}),r.unlock=new r("unlock",{fontCharacter:"\\eb74"}),r.unmute=new r("unmute",{fontCharacter:"\\eb75"}),r.unverified=new r("unverified",{fontCharacter:"\\eb76"}),r.verified=new r("verified",{fontCharacter:"\\eb77"}),r.versions=new r("versions",{fontCharacter:"\\eb78"}),r.vmActive=new r("vm-active",{fontCharacter:"\\eb79"}),r.vmOutline=new r("vm-outline",{fontCharacter:"\\eb7a"}),r.vmRunning=new r("vm-running",{fontCharacter:"\\eb7b"}),r.watch=new r("watch",{fontCharacter:"\\eb7c"}),r.whitespace=new r("whitespace",{fontCharacter:"\\eb7d"}),r.wholeWord=new r("whole-word",{fontCharacter:"\\eb7e"}),r.window=new r("window",{fontCharacter:"\\eb7f"}),r.wordWrap=new r("word-wrap",{fontCharacter:"\\eb80"}),r.zoomIn=new r("zoom-in",{fontCharacter:"\\eb81"}),r.zoomOut=new r("zoom-out",{fontCharacter:"\\eb82"}),r.listFilter=new r("list-filter",{fontCharacter:"\\eb83"}),r.listFlat=new r("list-flat",{fontCharacter:"\\eb84"}),r.listSelection=new r("list-selection",{fontCharacter:"\\eb85"}),r.selection=new r("selection",{fontCharacter:"\\eb85"}),r.listTree=new r("list-tree",{fontCharacter:"\\eb86"}),r.debugBreakpointFunctionUnverified=new r("debug-breakpoint-function-unverified",{fontCharacter:"\\eb87"}),r.debugBreakpointFunction=new r("debug-breakpoint-function",{fontCharacter:"\\eb88"}),r.debugBreakpointFunctionDisabled=new r("debug-breakpoint-function-disabled",{fontCharacter:"\\eb88"}),r.debugStackframeActive=new r("debug-stackframe-active",{fontCharacter:"\\eb89"}),r.debugStackframeDot=new r("debug-stackframe-dot",{fontCharacter:"\\eb8a"}),r.debugStackframe=new r("debug-stackframe",{fontCharacter:"\\eb8b"}),r.debugStackframeFocused=new r("debug-stackframe-focused",{fontCharacter:"\\eb8b"}),r.debugBreakpointUnsupported=new r("debug-breakpoint-unsupported",{fontCharacter:"\\eb8c"}),r.symbolString=new r("symbol-string",{fontCharacter:"\\eb8d"}),r.debugReverseContinue=new r("debug-reverse-continue",{fontCharacter:"\\eb8e"}),r.debugStepBack=new r("debug-step-back",{fontCharacter:"\\eb8f"}),r.debugRestartFrame=new r("debug-restart-frame",{fontCharacter:"\\eb90"}),r.callIncoming=new r("call-incoming",{fontCharacter:"\\eb92"}),r.callOutgoing=new r("call-outgoing",{fontCharacter:"\\eb93"}),r.menu=new r("menu",{fontCharacter:"\\eb94"}),r.expandAll=new r("expand-all",{fontCharacter:"\\eb95"}),r.feedback=new r("feedback",{fontCharacter:"\\eb96"}),r.groupByRefType=new r("group-by-ref-type",{fontCharacter:"\\eb97"}),r.ungroupByRefType=new r("ungroup-by-ref-type",{fontCharacter:"\\eb98"}),r.account=new r("account",{fontCharacter:"\\eb99"}),r.bellDot=new r("bell-dot",{fontCharacter:"\\eb9a"}),r.debugConsole=new r("debug-console",{fontCharacter:"\\eb9b"}),r.library=new r("library",{fontCharacter:"\\eb9c"}),r.output=new r("output",{fontCharacter:"\\eb9d"}),r.runAll=new r("run-all",{fontCharacter:"\\eb9e"}),r.syncIgnored=new r("sync-ignored",{fontCharacter:"\\eb9f"}),r.pinned=new r("pinned",{fontCharacter:"\\eba0"}),r.githubInverted=new r("github-inverted",{fontCharacter:"\\eba1"}),r.debugAlt=new r("debug-alt",{fontCharacter:"\\eb91"}),r.serverProcess=new r("server-process",{fontCharacter:"\\eba2"}),r.serverEnvironment=new r("server-environment",{fontCharacter:"\\eba3"}),r.pass=new r("pass",{fontCharacter:"\\eba4"}),r.stopCircle=new r("stop-circle",{fontCharacter:"\\eba5"}),r.playCircle=new r("play-circle",{fontCharacter:"\\eba6"}),r.record=new r("record",{fontCharacter:"\\eba7"}),r.debugAltSmall=new r("debug-alt-small",{fontCharacter:"\\eba8"}),r.vmConnect=new r("vm-connect",{fontCharacter:"\\eba9"}),r.cloud=new r("cloud",{fontCharacter:"\\ebaa"}),r.merge=new r("merge",{fontCharacter:"\\ebab"}),r.exportIcon=new r("export",{fontCharacter:"\\ebac"}),r.graphLeft=new r("graph-left",{fontCharacter:"\\ebad"}),r.magnet=new r("magnet",{fontCharacter:"\\ebae"}),r.notebook=new r("notebook",{fontCharacter:"\\ebaf"}),r.redo=new r("redo",{fontCharacter:"\\ebb0"}),r.checkAll=new r("check-all",{fontCharacter:"\\ebb1"}),r.pinnedDirty=new r("pinned-dirty",{fontCharacter:"\\ebb2"}),r.passFilled=new r("pass-filled",{fontCharacter:"\\ebb3"}),r.circleLargeFilled=new r("circle-large-filled",{fontCharacter:"\\ebb4"}),r.circleLargeOutline=new r("circle-large-outline",{fontCharacter:"\\ebb5"}),r.combine=new r("combine",{fontCharacter:"\\ebb6"}),r.gather=new r("gather",{fontCharacter:"\\ebb6"}),r.table=new r("table",{fontCharacter:"\\ebb7"}),r.variableGroup=new r("variable-group",{fontCharacter:"\\ebb8"}),r.typeHierarchy=new r("type-hierarchy",{fontCharacter:"\\ebb9"}),r.typeHierarchySub=new r("type-hierarchy-sub",{fontCharacter:"\\ebba"}),r.typeHierarchySuper=new r("type-hierarchy-super",{fontCharacter:"\\ebbb"}),r.gitPullRequestCreate=new r("git-pull-request-create",{fontCharacter:"\\ebbc"}),r.runAbove=new r("run-above",{fontCharacter:"\\ebbd"}),r.runBelow=new r("run-below",{fontCharacter:"\\ebbe"}),r.notebookTemplate=new r("notebook-template",{fontCharacter:"\\ebbf"}),r.debugRerun=new r("debug-rerun",{fontCharacter:"\\ebc0"}),r.workspaceTrusted=new r("workspace-trusted",{fontCharacter:"\\ebc1"}),r.workspaceUntrusted=new r("workspace-untrusted",{fontCharacter:"\\ebc2"}),r.workspaceUnspecified=new r("workspace-unspecified",{fontCharacter:"\\ebc3"}),r.terminalCmd=new r("terminal-cmd",{fontCharacter:"\\ebc4"}),r.terminalDebian=new r("terminal-debian",{fontCharacter:"\\ebc5"}),r.terminalLinux=new r("terminal-linux",{fontCharacter:"\\ebc6"}),r.terminalPowershell=new r("terminal-powershell",{fontCharacter:"\\ebc7"}),r.terminalTmux=new r("terminal-tmux",{fontCharacter:"\\ebc8"}),r.terminalUbuntu=new r("terminal-ubuntu",{fontCharacter:"\\ebc9"}),r.terminalBash=new r("terminal-bash",{fontCharacter:"\\ebca"}),r.arrowSwap=new r("arrow-swap",{fontCharacter:"\\ebcb"}),r.copy=new r("copy",{fontCharacter:"\\ebcc"}),r.personAdd=new r("person-add",{fontCharacter:"\\ebcd"}),r.filterFilled=new r("filter-filled",{fontCharacter:"\\ebce"}),r.wand=new r("wand",{fontCharacter:"\\ebcf"}),r.debugLineByLine=new r("debug-line-by-line",{fontCharacter:"\\ebd0"}),r.inspect=new r("inspect",{fontCharacter:"\\ebd1"}),r.layers=new r("layers",{fontCharacter:"\\ebd2"}),r.layersDot=new r("layers-dot",{fontCharacter:"\\ebd3"}),r.layersActive=new r("layers-active",{fontCharacter:"\\ebd4"}),r.compass=new r("compass",{fontCharacter:"\\ebd5"}),r.compassDot=new r("compass-dot",{fontCharacter:"\\ebd6"}),r.compassActive=new r("compass-active",{fontCharacter:"\\ebd7"}),r.azure=new r("azure",{fontCharacter:"\\ebd8"}),r.issueDraft=new r("issue-draft",{fontCharacter:"\\ebd9"}),r.gitPullRequestClosed=new r("git-pull-request-closed",{fontCharacter:"\\ebda"}),r.gitPullRequestDraft=new r("git-pull-request-draft",{fontCharacter:"\\ebdb"}),r.debugAll=new r("debug-all",{fontCharacter:"\\ebdc"}),r.debugCoverage=new r("debug-coverage",{fontCharacter:"\\ebdd"}),r.runErrors=new r("run-errors",{fontCharacter:"\\ebde"}),r.folderLibrary=new r("folder-library",{fontCharacter:"\\ebdf"}),r.debugContinueSmall=new r("debug-continue-small",{fontCharacter:"\\ebe0"}),r.beakerStop=new r("beaker-stop",{fontCharacter:"\\ebe1"}),r.graphLine=new r("graph-line",{fontCharacter:"\\ebe2"}),r.graphScatter=new r("graph-scatter",{fontCharacter:"\\ebe3"}),r.pieChart=new r("pie-chart",{fontCharacter:"\\ebe4"}),r.bracket=new r("bracket",r.json.definition),r.bracketDot=new r("bracket-dot",{fontCharacter:"\\ebe5"}),r.bracketError=new r("bracket-error",{fontCharacter:"\\ebe6"}),r.lockSmall=new r("lock-small",{fontCharacter:"\\ebe7"}),r.azureDevops=new r("azure-devops",{fontCharacter:"\\ebe8"}),r.verifiedFilled=new r("verified-filled",{fontCharacter:"\\ebe9"}),r.newLine=new r("newline",{fontCharacter:"\\ebea"}),r.layout=new r("layout",{fontCharacter:"\\ebeb"}),r.layoutActivitybarLeft=new r("layout-activitybar-left",{fontCharacter:"\\ebec"}),r.layoutActivitybarRight=new r("layout-activitybar-right",{fontCharacter:"\\ebed"}),r.layoutPanelLeft=new r("layout-panel-left",{fontCharacter:"\\ebee"}),r.layoutPanelCenter=new r("layout-panel-center",{fontCharacter:"\\ebef"}),r.layoutPanelJustify=new r("layout-panel-justify",{fontCharacter:"\\ebf0"}),r.layoutPanelRight=new r("layout-panel-right",{fontCharacter:"\\ebf1"}),r.layoutPanel=new r("layout-panel",{fontCharacter:"\\ebf2"}),r.layoutSidebarLeft=new r("layout-sidebar-left",{fontCharacter:"\\ebf3"}),r.layoutSidebarRight=new r("layout-sidebar-right",{fontCharacter:"\\ebf4"}),r.layoutStatusbar=new r("layout-statusbar",{fontCharacter:"\\ebf5"}),r.layoutMenubar=new r("layout-menubar",{fontCharacter:"\\ebf6"}),r.layoutCentered=new r("layout-centered",{fontCharacter:"\\ebf7"}),r.target=new r("target",{fontCharacter:"\\ebf8"}),r.indent=new r("indent",{fontCharacter:"\\ebf9"}),r.recordSmall=new r("record-small",{fontCharacter:"\\ebfa"}),r.errorSmall=new r("error-small",{fontCharacter:"\\ebfb"}),r.arrowCircleDown=new r("arrow-circle-down",{fontCharacter:"\\ebfc"}),r.arrowCircleLeft=new r("arrow-circle-left",{fontCharacter:"\\ebfd"}),r.arrowCircleRight=new r("arrow-circle-right",{fontCharacter:"\\ebfe"}),r.arrowCircleUp=new r("arrow-circle-up",{fontCharacter:"\\ebff"}),r.dialogError=new r("dialog-error",r.error.definition),r.dialogWarning=new r("dialog-warning",r.warning.definition),r.dialogInfo=new r("dialog-info",r.info.definition),r.dialogClose=new r("dialog-close",r.close.definition),r.treeItemExpanded=new r("tree-item-expanded",r.chevronDown.definition),r.treeFilterOnTypeOn=new r("tree-filter-on-type-on",r.listFilter.definition),r.treeFilterOnTypeOff=new r("tree-filter-on-type-off",r.listSelection.definition),r.treeFilterClear=new r("tree-filter-clear",r.close.definition),r.treeItemLoading=new r("tree-item-loading",r.loading.definition),r.menuSelection=new r("menu-selection",r.check.definition),r.menuSubmenu=new r("menu-submenu",r.chevronRight.definition),r.menuBarMore=new r("menubar-more",r.more.definition),r.scrollbarButtonLeft=new r("scrollbar-button-left",r.triangleLeft.definition),r.scrollbarButtonRight=new r("scrollbar-button-right",r.triangleRight.definition),r.scrollbarButtonUp=new r("scrollbar-button-up",r.triangleUp.definition),r.scrollbarButtonDown=new r("scrollbar-button-down",r.triangleDown.definition),r.toolBarMore=new r("toolbar-more",r.more.definition),r.quickInputBack=new r("quick-input-back",r.arrowLeft.definition);var Kt;(function(e){e.iconNameSegment="[A-Za-z0-9]+",e.iconNameExpression="[A-Za-z0-9-]+",e.iconModifierExpression="~[A-Za-z]+",e.iconNameCharacter="[A-Za-z0-9~-]";const t=new RegExp(`^(${e.iconNameExpression})(${e.iconModifierExpression})?$`);function n(s){if(s instanceof r)return["codicon","codicon-"+s.id];const a=t.exec(s.id);if(!a)return n(r.error);let[,l,c]=a;const u=["codicon","codicon-"+l];return c&&u.push("codicon-modifier-"+c.substr(1)),u}e.asClassNameArray=n;function i(s){return n(s).join(" ")}e.asClassName=i;function o(s){return"."+n(s).join(".")}e.asCSSSelector=o})(Kt||(Kt={}));class Xo{static getLanguageId(t){return(t&255)>>>0}static getTokenType(t){return(t&768)>>>8}static getFontStyle(t){return(t&15360)>>>10}static getForeground(t){return(t&8372224)>>>14}static getBackground(t){return(t&4286578688)>>>23}static getClassNameFromMetadata(t){const n=this.getForeground(t);let i="mtk"+n;const o=this.getFontStyle(t);return o&1&&(i+=" mtki"),o&2&&(i+=" mtkb"),o&4&&(i+=" mtku"),o&8&&(i+=" mtks"),i}static getInlineStyleFromMetadata(t,n){const i=this.getForeground(t),o=this.getFontStyle(t);let s=`color: ${n[i]};`;o&1&&(s+="font-style: italic;"),o&2&&(s+="font-weight: bold;");let a="";return o&4&&(a+=" underline"),o&8&&(a+=" line-through"),a&&(s+=`text-decoration:${a};`),s}static getPresentationFromMetadata(t){const n=this.getForeground(t),i=this.getFontStyle(t);return{foreground:n,italic:Boolean(i&1),bold:Boolean(i&2),underline:Boolean(i&4),strikethrough:Boolean(i&8)}}}class pi{constructor(t,n,i){this._tokenBrand=void 0,this.offset=t,this.type=n,this.language=i}toString(){return"("+this.offset+", "+this.type+")"}}class Jo{constructor(t,n){this._tokenizationResultBrand=void 0,this.tokens=t,this.endState=n}}class es{constructor(t,n){this._encodedTokenizationResultBrand=void 0,this.tokens=t,this.endState=n}}var Vt;(function(e){const t=new Map;t.set(0,r.symbolMethod),t.set(1,r.symbolFunction),t.set(2,r.symbolConstructor),t.set(3,r.symbolField),t.set(4,r.symbolVariable),t.set(5,r.symbolClass),t.set(6,r.symbolStruct),t.set(7,r.symbolInterface),t.set(8,r.symbolModule),t.set(9,r.symbolProperty),t.set(10,r.symbolEvent),t.set(11,r.symbolOperator),t.set(12,r.symbolUnit),t.set(13,r.symbolValue),t.set(15,r.symbolEnum),t.set(14,r.symbolConstant),t.set(15,r.symbolEnum),t.set(16,r.symbolEnumMember),t.set(17,r.symbolKeyword),t.set(27,r.symbolSnippet),t.set(18,r.symbolText),t.set(19,r.symbolColor),t.set(20,r.symbolFile),t.set(21,r.symbolReference),t.set(22,r.symbolCustomColor),t.set(23,r.symbolFolder),t.set(24,r.symbolTypeParameter),t.set(25,r.account),t.set(26,r.issues);function n(s){let a=t.get(s);return a||(console.info("No codicon found for CompletionItemKind "+s),a=r.symbolProperty),a}e.toIcon=n;const i=new Map;i.set("method",0),i.set("function",1),i.set("constructor",2),i.set("field",3),i.set("variable",4),i.set("class",5),i.set("struct",6),i.set("interface",7),i.set("module",8),i.set("property",9),i.set("event",10),i.set("operator",11),i.set("unit",12),i.set("value",13),i.set("constant",14),i.set("enum",15),i.set("enum-member",16),i.set("enumMember",16),i.set("keyword",17),i.set("snippet",27),i.set("text",18),i.set("color",19),i.set("file",20),i.set("reference",21),i.set("customcolor",22),i.set("folder",23),i.set("type-parameter",24),i.set("typeParameter",24),i.set("account",25),i.set("issue",26);function o(s,a){let l=i.get(s);return typeof l=="undefined"&&!a&&(l=9),l}e.fromString=o})(Vt||(Vt={}));var Bt;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(Bt||(Bt={}));var Ut;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(Ut||(Ut={}));var qt;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(qt||(qt={}));function ts(e){return e&&URI.isUri(e.uri)&&Range.isIRange(e.range)&&(Range.isIRange(e.originSelectionRange)||Range.isIRange(e.targetSelectionRange))}var Wt;(function(e){const t=new Map;t.set(0,r.symbolFile),t.set(1,r.symbolModule),t.set(2,r.symbolNamespace),t.set(3,r.symbolPackage),t.set(4,r.symbolClass),t.set(5,r.symbolMethod),t.set(6,r.symbolProperty),t.set(7,r.symbolField),t.set(8,r.symbolConstructor),t.set(9,r.symbolEnum),t.set(10,r.symbolInterface),t.set(11,r.symbolFunction),t.set(12,r.symbolVariable),t.set(13,r.symbolConstant),t.set(14,r.symbolString),t.set(15,r.symbolNumber),t.set(16,r.symbolBoolean),t.set(17,r.symbolArray),t.set(18,r.symbolObject),t.set(19,r.symbolKey),t.set(20,r.symbolNull),t.set(21,r.symbolEnumMember),t.set(22,r.symbolStruct),t.set(23,r.symbolEvent),t.set(24,r.symbolOperator),t.set(25,r.symbolTypeParameter);function n(i){let o=t.get(i);return o||(console.info("No codicon found for SymbolKind "+i),o=r.symbolProperty),o}e.toIcon=n})(Wt||(Wt={}));class ke{constructor(t){this.value=t}}ke.Comment=new ke("comment"),ke.Imports=new ke("imports"),ke.Region=new ke("region");var Ht;(function(e){function t(n){return!n||typeof n!="object"?!1:typeof n.id=="string"&&typeof n.title=="string"}e.is=t})(Ht||(Ht={}));var zt;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(zt||(zt={}));const ns=new mi;var $t;(function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"})($t||($t={}));var jt;(function(e){e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(jt||(jt={}));var Gt;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"})(Gt||(Gt={}));var Qt;(function(e){e[e.Deprecated=1]="Deprecated"})(Qt||(Qt={}));var Yt;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(Yt||(Yt={}));var Zt;(function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"})(Zt||(Zt={}));var Xt;(function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"})(Xt||(Xt={}));var Jt;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Jt||(Jt={}));var en;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(en||(en={}));var tn;(function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"})(tn||(tn={}));var nn;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.autoClosingBrackets=5]="autoClosingBrackets",e[e.autoClosingDelete=6]="autoClosingDelete",e[e.autoClosingOvertype=7]="autoClosingOvertype",e[e.autoClosingQuotes=8]="autoClosingQuotes",e[e.autoIndent=9]="autoIndent",e[e.automaticLayout=10]="automaticLayout",e[e.autoSurround=11]="autoSurround",e[e.bracketPairColorization=12]="bracketPairColorization",e[e.guides=13]="guides",e[e.codeLens=14]="codeLens",e[e.codeLensFontFamily=15]="codeLensFontFamily",e[e.codeLensFontSize=16]="codeLensFontSize",e[e.colorDecorators=17]="colorDecorators",e[e.columnSelection=18]="columnSelection",e[e.comments=19]="comments",e[e.contextmenu=20]="contextmenu",e[e.copyWithSyntaxHighlighting=21]="copyWithSyntaxHighlighting",e[e.cursorBlinking=22]="cursorBlinking",e[e.cursorSmoothCaretAnimation=23]="cursorSmoothCaretAnimation",e[e.cursorStyle=24]="cursorStyle",e[e.cursorSurroundingLines=25]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=26]="cursorSurroundingLinesStyle",e[e.cursorWidth=27]="cursorWidth",e[e.disableLayerHinting=28]="disableLayerHinting",e[e.disableMonospaceOptimizations=29]="disableMonospaceOptimizations",e[e.domReadOnly=30]="domReadOnly",e[e.dragAndDrop=31]="dragAndDrop",e[e.emptySelectionClipboard=32]="emptySelectionClipboard",e[e.extraEditorClassName=33]="extraEditorClassName",e[e.fastScrollSensitivity=34]="fastScrollSensitivity",e[e.find=35]="find",e[e.fixedOverflowWidgets=36]="fixedOverflowWidgets",e[e.folding=37]="folding",e[e.foldingStrategy=38]="foldingStrategy",e[e.foldingHighlight=39]="foldingHighlight",e[e.foldingImportsByDefault=40]="foldingImportsByDefault",e[e.foldingMaximumRegions=41]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=42]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=43]="fontFamily",e[e.fontInfo=44]="fontInfo",e[e.fontLigatures=45]="fontLigatures",e[e.fontSize=46]="fontSize",e[e.fontWeight=47]="fontWeight",e[e.formatOnPaste=48]="formatOnPaste",e[e.formatOnType=49]="formatOnType",e[e.glyphMargin=50]="glyphMargin",e[e.gotoLocation=51]="gotoLocation",e[e.hideCursorInOverviewRuler=52]="hideCursorInOverviewRuler",e[e.hover=53]="hover",e[e.inDiffEditor=54]="inDiffEditor",e[e.inlineSuggest=55]="inlineSuggest",e[e.letterSpacing=56]="letterSpacing",e[e.lightbulb=57]="lightbulb",e[e.lineDecorationsWidth=58]="lineDecorationsWidth",e[e.lineHeight=59]="lineHeight",e[e.lineNumbers=60]="lineNumbers",e[e.lineNumbersMinChars=61]="lineNumbersMinChars",e[e.linkedEditing=62]="linkedEditing",e[e.links=63]="links",e[e.matchBrackets=64]="matchBrackets",e[e.minimap=65]="minimap",e[e.mouseStyle=66]="mouseStyle",e[e.mouseWheelScrollSensitivity=67]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=68]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=69]="multiCursorMergeOverlapping",e[e.multiCursorModifier=70]="multiCursorModifier",e[e.multiCursorPaste=71]="multiCursorPaste",e[e.occurrencesHighlight=72]="occurrencesHighlight",e[e.overviewRulerBorder=73]="overviewRulerBorder",e[e.overviewRulerLanes=74]="overviewRulerLanes",e[e.padding=75]="padding",e[e.parameterHints=76]="parameterHints",e[e.peekWidgetDefaultFocus=77]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=78]="definitionLinkOpensInPeek",e[e.quickSuggestions=79]="quickSuggestions",e[e.quickSuggestionsDelay=80]="quickSuggestionsDelay",e[e.readOnly=81]="readOnly",e[e.renameOnType=82]="renameOnType",e[e.renderControlCharacters=83]="renderControlCharacters",e[e.renderFinalNewline=84]="renderFinalNewline",e[e.renderLineHighlight=85]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=86]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=87]="renderValidationDecorations",e[e.renderWhitespace=88]="renderWhitespace",e[e.revealHorizontalRightPadding=89]="revealHorizontalRightPadding",e[e.roundedSelection=90]="roundedSelection",e[e.rulers=91]="rulers",e[e.scrollbar=92]="scrollbar",e[e.scrollBeyondLastColumn=93]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=94]="scrollBeyondLastLine",e[e.scrollPredominantAxis=95]="scrollPredominantAxis",e[e.selectionClipboard=96]="selectionClipboard",e[e.selectionHighlight=97]="selectionHighlight",e[e.selectOnLineNumbers=98]="selectOnLineNumbers",e[e.showFoldingControls=99]="showFoldingControls",e[e.showUnused=100]="showUnused",e[e.snippetSuggestions=101]="snippetSuggestions",e[e.smartSelect=102]="smartSelect",e[e.smoothScrolling=103]="smoothScrolling",e[e.stickyTabStops=104]="stickyTabStops",e[e.stopRenderingLineAfter=105]="stopRenderingLineAfter",e[e.suggest=106]="suggest",e[e.suggestFontSize=107]="suggestFontSize",e[e.suggestLineHeight=108]="suggestLineHeight",e[e.suggestOnTriggerCharacters=109]="suggestOnTriggerCharacters",e[e.suggestSelection=110]="suggestSelection",e[e.tabCompletion=111]="tabCompletion",e[e.tabIndex=112]="tabIndex",e[e.unicodeHighlighting=113]="unicodeHighlighting",e[e.unusualLineTerminators=114]="unusualLineTerminators",e[e.useShadowDOM=115]="useShadowDOM",e[e.useTabStops=116]="useTabStops",e[e.wordSeparators=117]="wordSeparators",e[e.wordWrap=118]="wordWrap",e[e.wordWrapBreakAfterCharacters=119]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=120]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=121]="wordWrapColumn",e[e.wordWrapOverride1=122]="wordWrapOverride1",e[e.wordWrapOverride2=123]="wordWrapOverride2",e[e.wrappingIndent=124]="wrappingIndent",e[e.wrappingStrategy=125]="wrappingStrategy",e[e.showDeprecated=126]="showDeprecated",e[e.inlayHints=127]="inlayHints",e[e.editorClassName=128]="editorClassName",e[e.pixelRatio=129]="pixelRatio",e[e.tabFocusMode=130]="tabFocusMode",e[e.layoutInfo=131]="layoutInfo",e[e.wrappingInfo=132]="wrappingInfo"})(nn||(nn={}));var rn;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(rn||(rn={}));var on;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(on||(on={}));var sn;(function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"})(sn||(sn={}));var an;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(an||(an={}));var ln;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(ln||(ln={}));var un;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(un||(un={}));var V1;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.NumLock=78]="NumLock",e[e.ScrollLock=79]="ScrollLock",e[e.Semicolon=80]="Semicolon",e[e.Equal=81]="Equal",e[e.Comma=82]="Comma",e[e.Minus=83]="Minus",e[e.Period=84]="Period",e[e.Slash=85]="Slash",e[e.Backquote=86]="Backquote",e[e.BracketLeft=87]="BracketLeft",e[e.Backslash=88]="Backslash",e[e.BracketRight=89]="BracketRight",e[e.Quote=90]="Quote",e[e.OEM_8=91]="OEM_8",e[e.IntlBackslash=92]="IntlBackslash",e[e.Numpad0=93]="Numpad0",e[e.Numpad1=94]="Numpad1",e[e.Numpad2=95]="Numpad2",e[e.Numpad3=96]="Numpad3",e[e.Numpad4=97]="Numpad4",e[e.Numpad5=98]="Numpad5",e[e.Numpad6=99]="Numpad6",e[e.Numpad7=100]="Numpad7",e[e.Numpad8=101]="Numpad8",e[e.Numpad9=102]="Numpad9",e[e.NumpadMultiply=103]="NumpadMultiply",e[e.NumpadAdd=104]="NumpadAdd",e[e.NUMPAD_SEPARATOR=105]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=106]="NumpadSubtract",e[e.NumpadDecimal=107]="NumpadDecimal",e[e.NumpadDivide=108]="NumpadDivide",e[e.KEY_IN_COMPOSITION=109]="KEY_IN_COMPOSITION",e[e.ABNT_C1=110]="ABNT_C1",e[e.ABNT_C2=111]="ABNT_C2",e[e.AudioVolumeMute=112]="AudioVolumeMute",e[e.AudioVolumeUp=113]="AudioVolumeUp",e[e.AudioVolumeDown=114]="AudioVolumeDown",e[e.BrowserSearch=115]="BrowserSearch",e[e.BrowserHome=116]="BrowserHome",e[e.BrowserBack=117]="BrowserBack",e[e.BrowserForward=118]="BrowserForward",e[e.MediaTrackNext=119]="MediaTrackNext",e[e.MediaTrackPrevious=120]="MediaTrackPrevious",e[e.MediaStop=121]="MediaStop",e[e.MediaPlayPause=122]="MediaPlayPause",e[e.LaunchMediaPlayer=123]="LaunchMediaPlayer",e[e.LaunchMail=124]="LaunchMail",e[e.LaunchApp2=125]="LaunchApp2",e[e.Clear=126]="Clear",e[e.MAX_VALUE=127]="MAX_VALUE"})(V1||(V1={}));var B1;(function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"})(B1||(B1={}));var U1;(function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"})(U1||(U1={}));var cn;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(cn||(cn={}));var hn;(function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(hn||(hn={}));var dn;(function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"})(dn||(dn={}));var fn;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(fn||(fn={}));var mn;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None"})(mn||(mn={}));var gn;(function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"})(gn||(gn={}));var pn;(function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"})(pn||(pn={}));var bn;(function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"})(bn||(bn={}));var Cn;(function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"})(Cn||(Cn={}));var q1;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(q1||(q1={}));var wn;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(wn||(wn={}));var _n;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(_n||(_n={}));var yn;(function(e){e[e.Deprecated=1]="Deprecated"})(yn||(yn={}));var vn;(function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"})(vn||(vn={}));var Sn;(function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"})(Sn||(Sn={}));var Ln;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(Ln||(Ln={}));var Nn;(function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"})(Nn||(Nn={}));class We{static chord(t,n){return fi(t,n)}}We.CtrlCmd=2048,We.Shift=1024,We.Alt=512,We.WinCtrl=256;function bi(){return{editor:void 0,languages:void 0,CancellationTokenSource:ai,Emitter:ee,KeyCode:V1,KeyMod:We,Position:$,Range:D,Selection:j,SelectionDirection:q1,MarkerSeverity:B1,MarkerTag:U1,Uri:ge,Token:pi}}class Ci extends Ue{constructor(t){super(0);for(let n=0,i=t.length;n<i;n++)this.set(t.charCodeAt(n),2);this.set(32,1),this.set(9,1)}}function wi(e){const t={};return n=>(t.hasOwnProperty(n)||(t[n]=e(n)),t[n])}const rs=wi(e=>new Ci(e));function _i(e){if(!e||typeof e!="object"||e instanceof RegExp)return e;const t=Array.isArray(e)?[]:{};return Object.keys(e).forEach(n=>{e[n]&&typeof e[n]=="object"?t[n]=_i(e[n]):t[n]=e[n]}),t}function is(e){if(!e||typeof e!="object")return e;const t=[e];for(;t.length>0;){const n=t.shift();Object.freeze(n);for(const i in n)if(En.call(n,i)){const o=n[i];typeof o=="object"&&!Object.isFrozen(o)&&t.push(o)}}return e}const En=Object.prototype.hasOwnProperty;function os(e,t){return W1(e,t,new Set)}function W1(e,t,n){if(isUndefinedOrNull(e))return e;const i=t(e);if(typeof i!="undefined")return i;if(isArray(e)){const o=[];for(const s of e)o.push(W1(s,t,n));return o}if(isObject(e)){if(n.has(e))throw new Error("Cannot clone recursive data-structure");n.add(e);const o={};for(let s in e)En.call(e,s)&&(o[s]=W1(e[s],t,n));return n.delete(e),o}return e}function yi(e,t,n=!0){return isObject(e)?(isObject(t)&&Object.keys(t).forEach(i=>{i in e?n&&(isObject(e[i])&&isObject(t[i])?yi(e[i],t[i],n):e[i]=t[i]):e[i]=t[i]}),e):t}function H1(e,t){if(e===t)return!0;if(e==null||t===null||t===void 0||typeof e!=typeof t||typeof e!="object"||Array.isArray(e)!==Array.isArray(t))return!1;let n,i;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!H1(e[n],t[n]))return!1}else{const o=[];for(i in e)o.push(i);o.sort();const s=[];for(i in t)s.push(i);if(s.sort(),!H1(o,s))return!1;for(n=0;n<o.length;n++)if(!H1(e[o[n]],t[o[n]]))return!1}return!0}function ss(e,t,n){const i=t(e);return typeof i=="undefined"?n:i}var An;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(An||(An={}));var kn;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(kn||(kn={}));var Mn;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(Mn||(Mn={}));class as{constructor(t){this._textModelResolvedOptionsBrand=void 0,this.tabSize=Math.max(1,t.tabSize|0),this.indentSize=t.tabSize|0,this.insertSpaces=Boolean(t.insertSpaces),this.defaultEOL=t.defaultEOL|0,this.trimAutoWhitespace=Boolean(t.trimAutoWhitespace),this.bracketPairColorizationOptions=t.bracketPairColorizationOptions}equals(t){return this.tabSize===t.tabSize&&this.indentSize===t.indentSize&&this.insertSpaces===t.insertSpaces&&this.defaultEOL===t.defaultEOL&&this.trimAutoWhitespace===t.trimAutoWhitespace&&equals(this.bracketPairColorizationOptions,t.bracketPairColorizationOptions)}createChangeEvent(t){return{tabSize:this.tabSize!==t.tabSize,indentSize:this.indentSize!==t.indentSize,insertSpaces:this.insertSpaces!==t.insertSpaces,trimAutoWhitespace:this.trimAutoWhitespace!==t.trimAutoWhitespace}}}class ls{constructor(t,n){this._findMatchBrand=void 0,this.range=t,this.matches=n}}class us{constructor(t,n,i,o,s,a){this.identifier=t,this.range=n,this.text=i,this.forceMoveMarkers=o,this.isAutoWhitespaceEdit=s,this._isTracked=a}}class cs{constructor(t,n,i){this.regex=t,this.wordSeparators=n,this.simpleSearch=i}}class hs{constructor(t,n,i){this.reverseEdits=t,this.changes=n,this.trimAutoWhitespaceLineNumbers=i}}function ds(e){return!e.isTooLargeForSyncing()&&!e.isForSimpleWidget}const vi=999;class fs{constructor(t,n,i,o){this.searchString=t,this.isRegex=n,this.matchCase=i,this.wordSeparators=o}parseSearchRequest(){if(this.searchString==="")return null;let t;this.isRegex?t=Si(this.searchString):t=this.searchString.indexOf(`
`)>=0;let n=null;try{n=strings.createRegExp(this.searchString,this.isRegex,{matchCase:this.matchCase,wholeWord:!1,multiline:t,global:!0,unicode:!0})}catch(o){return null}if(!n)return null;let i=!this.isRegex&&!t;return i&&this.searchString.toLowerCase()!==this.searchString.toUpperCase()&&(i=this.matchCase),new SearchData(n,this.wordSeparators?getMapForWordSeparators(this.wordSeparators):null,i?this.searchString:null)}}function Si(e){if(!e||e.length===0)return!1;for(let t=0,n=e.length;t<n;t++)if(e.charCodeAt(t)===92){if(t++,t>=n)break;const i=e.charCodeAt(t);if(i===110||i===114||i===87)return!0}return!1}function He(e,t,n){if(!n)return new FindMatch(e,null);const i=[];for(let o=0,s=t.length;o<s;o++)i[o]=t[o];return new FindMatch(e,i)}class xn{constructor(t){const n=[];let i=0;for(let o=0,s=t.length;o<s;o++)t.charCodeAt(o)===10&&(n[i++]=o);this._lineFeedsOffsets=n}findLineFeedCountBeforeOffset(t){const n=this._lineFeedsOffsets;let i=0,o=n.length-1;if(o===-1||t<=n[0])return 0;for(;i<o;){const s=i+((o-i)/2>>0);n[s]>=t?o=s-1:n[s+1]>=t?(i=s,o=s):i=s+1}return i+1}}class ms{static findMatches(t,n,i,o,s){const a=n.parseSearchRequest();return a?a.regex.multiline?this._doFindMatchesMultiline(t,i,new ze(a.wordSeparators,a.regex),o,s):this._doFindMatchesLineByLine(t,i,a,o,s):[]}static _getMultilineMatchRange(t,n,i,o,s,a){let l,c=0;o?(c=o.findLineFeedCountBeforeOffset(s),l=n+s+c):l=n+s;let u;if(o){const f=o.findLineFeedCountBeforeOffset(s+a.length)-c;u=l+a.length+f}else u=l+a.length;const d=t.getPositionAt(l),h=t.getPositionAt(u);return new Range(d.lineNumber,d.column,h.lineNumber,h.column)}static _doFindMatchesMultiline(t,n,i,o,s){const a=t.getOffsetAt(n.getStartPosition()),l=t.getValueInRange(n,1),c=t.getEOL()===`\r
`?new xn(l):null,u=[];let d=0,h;for(i.reset(0);h=i.next(l);)if(u[d++]=He(this._getMultilineMatchRange(t,a,l,c,h.index,h[0]),h,o),d>=s)return u;return u}static _doFindMatchesLineByLine(t,n,i,o,s){const a=[];let l=0;if(n.startLineNumber===n.endLineNumber){const u=t.getLineContent(n.startLineNumber).substring(n.startColumn-1,n.endColumn-1);return l=this._findMatchesInLine(i,u,n.startLineNumber,n.startColumn-1,l,a,o,s),a}const c=t.getLineContent(n.startLineNumber).substring(n.startColumn-1);l=this._findMatchesInLine(i,c,n.startLineNumber,n.startColumn-1,l,a,o,s);for(let u=n.startLineNumber+1;u<n.endLineNumber&&l<s;u++)l=this._findMatchesInLine(i,t.getLineContent(u),u,0,l,a,o,s);if(l<s){const u=t.getLineContent(n.endLineNumber).substring(0,n.endColumn-1);l=this._findMatchesInLine(i,u,n.endLineNumber,0,l,a,o,s)}return a}static _findMatchesInLine(t,n,i,o,s,a,l,c){const u=t.wordSeparators;if(!l&&t.simpleSearch){const f=t.simpleSearch,y=f.length,v=n.length;let L=-y;for(;(L=n.indexOf(f,L+y))!==-1;)if((!u||Fn(u,n,v,L,y))&&(a[s++]=new FindMatch(new Range(i,L+1+o,i,L+1+y+o),null),s>=c))return s;return s}const d=new ze(t.wordSeparators,t.regex);let h;d.reset(0);do if(h=d.next(n),h&&(a[s++]=He(new Range(i,h.index+1+o,i,h.index+1+h[0].length+o),h,l),s>=c))return s;while(h);return s}static findNextMatch(t,n,i,o){const s=n.parseSearchRequest();if(!s)return null;const a=new ze(s.wordSeparators,s.regex);return s.regex.multiline?this._doFindNextMatchMultiline(t,i,a,o):this._doFindNextMatchLineByLine(t,i,a,o)}static _doFindNextMatchMultiline(t,n,i,o){const s=new Position(n.lineNumber,1),a=t.getOffsetAt(s),l=t.getLineCount(),c=t.getValueInRange(new Range(s.lineNumber,s.column,l,t.getLineMaxColumn(l)),1),u=t.getEOL()===`\r
`?new xn(c):null;i.reset(n.column-1);let d=i.next(c);return d?He(this._getMultilineMatchRange(t,a,c,u,d.index,d[0]),d,o):n.lineNumber!==1||n.column!==1?this._doFindNextMatchMultiline(t,new Position(1,1),i,o):null}static _doFindNextMatchLineByLine(t,n,i,o){const s=t.getLineCount(),a=n.lineNumber,l=t.getLineContent(a),c=this._findFirstMatchInLine(i,l,a,n.column,o);if(c)return c;for(let u=1;u<=s;u++){const d=(a+u-1)%s,h=t.getLineContent(d+1),f=this._findFirstMatchInLine(i,h,d+1,1,o);if(f)return f}return null}static _findFirstMatchInLine(t,n,i,o,s){t.reset(o-1);const a=t.next(n);return a?He(new Range(i,a.index+1,i,a.index+1+a[0].length),a,s):null}static findPreviousMatch(t,n,i,o){const s=n.parseSearchRequest();if(!s)return null;const a=new ze(s.wordSeparators,s.regex);return s.regex.multiline?this._doFindPreviousMatchMultiline(t,i,a,o):this._doFindPreviousMatchLineByLine(t,i,a,o)}static _doFindPreviousMatchMultiline(t,n,i,o){const s=this._doFindMatchesMultiline(t,new Range(1,1,n.lineNumber,n.column),i,o,10*vi);if(s.length>0)return s[s.length-1];const a=t.getLineCount();return n.lineNumber!==a||n.column!==t.getLineMaxColumn(a)?this._doFindPreviousMatchMultiline(t,new Position(a,t.getLineMaxColumn(a)),i,o):null}static _doFindPreviousMatchLineByLine(t,n,i,o){const s=t.getLineCount(),a=n.lineNumber,l=t.getLineContent(a).substring(0,n.column-1),c=this._findLastMatchInLine(i,l,a,o);if(c)return c;for(let u=1;u<=s;u++){const d=(s+a-u-1)%s,h=t.getLineContent(d+1),f=this._findLastMatchInLine(i,h,d+1,o);if(f)return f}return null}static _findLastMatchInLine(t,n,i,o){let s=null,a;for(t.reset(0);a=t.next(n);)s=He(new Range(i,a.index+1,i,a.index+1+a[0].length),a,o);return s}}function Li(e,t,n,i,o){if(i===0)return!0;const s=t.charCodeAt(i-1);if(e.get(s)!==0||s===13||s===10)return!0;if(o>0){const a=t.charCodeAt(i);if(e.get(a)!==0)return!0}return!1}function Ni(e,t,n,i,o){if(i+o===n)return!0;const s=t.charCodeAt(i+o);if(e.get(s)!==0||s===13||s===10)return!0;if(o>0){const a=t.charCodeAt(i+o-1);if(e.get(a)!==0)return!0}return!1}function Fn(e,t,n,i,o){return Li(e,t,n,i,o)&&Ni(e,t,n,i,o)}class ze{constructor(t,n){this._wordSeparators=t,this._searchRegex=n,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(t){this._searchRegex.lastIndex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(t){const n=t.length;let i;do{if(this._prevMatchStartIndex+this._prevMatchLength===n||(i=this._searchRegex.exec(t),!i))return null;const o=i.index,s=i[0].length;if(o===this._prevMatchStartIndex&&s===this._prevMatchLength){if(s===0){ut(t,n,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=o,this._prevMatchLength=s,!this._wordSeparators||Fn(this._wordSeparators,t,n,o,s))return i}while(i);return null}}class Ei{static computeUnicodeHighlights(t,n,i){const o=i?i.startLineNumber:1,s=i?i.endLineNumber:t.getLineCount(),a=new Rn(n),l=a.getCandidateCodePoints();let c;l==="allNonBasicAscii"?c=new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):c=new RegExp(`${Ai(Array.from(l))}`,"g");const u=new ze(null,c),d=[];let h=!1,f,y=0,v=0,L=0;e:for(let k=o,M=s;k<=M;k++){const w=t.getLineContent(k),_=w.length;u.reset(0);do if(f=u.next(w),f){let N=f.index,b=f.index+f[0].length;if(N>0){const S=w.charCodeAt(N-1);Ie(S)&&N--}if(b+1<_){const S=w.charCodeAt(b-1);Ie(S)&&b++}const g=w.substring(N,b),p=F1(N+1,Dt,w,0),C=a.shouldHighlightNonBasicASCII(g,p?p.word:null);if(C!==0){C===3?y++:C===2?v++:C===1?L++:ir(C);const S=1e3;if(d.length>=S){h=!0;break e}d.push(new D(k,N+1,k,b+1))}}while(f)}return{ranges:d,hasMore:h,ambiguousCharacterCount:y,invisibleCharacterCount:v,nonBasicAsciiCharacterCount:L}}static computeUnicodeHighlightReason(t,n){const i=new Rn(n);switch(i.shouldHighlightNonBasicASCII(t,null)){case 0:return null;case 2:return{kind:1};case 3:{const o=t.codePointAt(0),s=i.ambiguousCharacters.getPrimaryConfusable(o),a=Q.getLocales().filter(l=>!Q.getInstance(new Set([...n.allowedLocales,l])).isAmbiguous(o));return{kind:0,confusableWith:String.fromCodePoint(s),notAmbiguousInLocales:a}}case 1:return{kind:2}}}}function Ai(e,t){return`[${st(e.map(n=>String.fromCodePoint(n)).join(""))}]`}class Rn{constructor(t){this.options=t,this.allowedCodePoints=new Set(t.allowedCodePoints),this.ambiguousCharacters=Q.getInstance(new Set(t.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const t=new Set;if(this.options.invisibleCharacters)for(const n of ae.codePoints)On(String.fromCodePoint(n))||t.add(n);if(this.options.ambiguousCharacters)for(const n of this.ambiguousCharacters.getConfusableCodePoints())t.add(n);for(const n of this.allowedCodePoints)t.delete(n);return t}shouldHighlightNonBasicASCII(t,n){const i=t.codePointAt(0);if(this.allowedCodePoints.has(i))return 0;if(this.options.nonBasicASCII)return 1;let o=!1,s=!1;if(n)for(let a of n){const l=a.codePointAt(0),c=Cr(a);o=o||c,!c&&!this.ambiguousCharacters.isAmbiguous(l)&&!ae.isInvisibleCharacter(l)&&(s=!0)}return!o&&s?0:this.options.invisibleCharacters&&!On(t)&&ae.isInvisibleCharacter(i)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(i)?3:0}}function On(e){return e===" "||e===`
`||e==="	"}var pe=function(e,t,n,i){function o(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function l(d){try{u(i.next(d))}catch(h){a(h)}}function c(d){try{u(i.throw(d))}catch(h){a(h)}}function u(d){d.done?s(d.value):o(d.value).then(l,c)}u((i=i.apply(e,t||[])).next())})};class ki extends Yr{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(t){return this._lines[t-1]}getWordAtPosition(t,n){const i=F1(t.column,Jr(n),this._lines[t.lineNumber-1],0);return i?new D(t.lineNumber,i.startColumn,t.lineNumber,i.endColumn):null}words(t){const n=this._lines,i=this._wordenize.bind(this);let o=0,s="",a=0,l=[];return{*[Symbol.iterator](){for(;;)if(a<l.length){const c=s.substring(l[a].start,l[a].end);a+=1,yield c}else if(o<n.length)s=n[o],l=i(s,t),a=0,o+=1;else break}}}getLineWords(t,n){const i=this._lines[t-1],o=this._wordenize(i,n),s=[];for(const a of o)s.push({word:i.substring(a.start,a.end),startColumn:a.start+1,endColumn:a.end+1});return s}_wordenize(t,n){const i=[];let o;for(n.lastIndex=0;(o=n.exec(t))&&o[0].length!==0;)i.push({start:o.index,end:o.index+o[0].length});return i}getValueInRange(t){if(t=this._validateRange(t),t.startLineNumber===t.endLineNumber)return this._lines[t.startLineNumber-1].substring(t.startColumn-1,t.endColumn-1);const n=this._eol,i=t.startLineNumber-1,o=t.endLineNumber-1,s=[];s.push(this._lines[i].substring(t.startColumn-1));for(let a=i+1;a<o;a++)s.push(this._lines[a]);return s.push(this._lines[o].substring(0,t.endColumn-1)),s.join(n)}offsetAt(t){return t=this._validatePosition(t),this._ensureLineStarts(),this._lineStarts.getPrefixSum(t.lineNumber-2)+(t.column-1)}positionAt(t){t=Math.floor(t),t=Math.max(0,t),this._ensureLineStarts();const n=this._lineStarts.getIndexOf(t),i=this._lines[n.index].length;return{lineNumber:1+n.index,column:1+Math.min(n.remainder,i)}}_validateRange(t){const n=this._validatePosition({lineNumber:t.startLineNumber,column:t.startColumn}),i=this._validatePosition({lineNumber:t.endLineNumber,column:t.endColumn});return n.lineNumber!==t.startLineNumber||n.column!==t.startColumn||i.lineNumber!==t.endLineNumber||i.column!==t.endColumn?{startLineNumber:n.lineNumber,startColumn:n.column,endLineNumber:i.lineNumber,endColumn:i.column}:t}_validatePosition(t){if(!$.isIPosition(t))throw new Error("bad position");let{lineNumber:n,column:i}=t,o=!1;if(n<1)n=1,i=1,o=!0;else if(n>this._lines.length)n=this._lines.length,i=this._lines[n-1].length+1,o=!0;else{const s=this._lines[n-1].length+1;i<1?(i=1,o=!0):i>s&&(i=s,o=!0)}return o?{lineNumber:n,column:i}:t}}class be{constructor(t,n){this._host=t,this._models=Object.create(null),this._foreignModuleFactory=n,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(t){return this._models[t]}_getModels(){const t=[];return Object.keys(this._models).forEach(n=>t.push(this._models[n])),t}acceptNewModel(t){this._models[t.url]=new ki(ge.parse(t.url),t.lines,t.EOL,t.versionId)}acceptModelChanged(t,n){!this._models[t]||this._models[t].onEvents(n)}acceptRemovedModel(t){!this._models[t]||delete this._models[t]}computeUnicodeHighlights(t,n,i){return pe(this,void 0,void 0,function*(){const o=this._getModel(t);return o?Ei.computeUnicodeHighlights(o,n,i):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}})}computeDiff(t,n,i,o){return pe(this,void 0,void 0,function*(){const s=this._getModel(t),a=this._getModel(n);if(!s||!a)return null;const l=s.getLinesContent(),c=a.getLinesContent(),u=new Gr(l,c,{shouldComputeCharChanges:!0,shouldPostProcessCharChanges:!0,shouldIgnoreTrimWhitespace:i,shouldMakePrettyDiff:!0,maxComputationTime:o}).computeDiff(),d=u.changes.length>0?!1:this._modelsAreIdentical(s,a);return{quitEarly:u.quitEarly,identical:d,changes:u.changes}})}_modelsAreIdentical(t,n){const i=t.getLineCount(),o=n.getLineCount();if(i!==o)return!1;for(let s=1;s<=i;s++){const a=t.getLineContent(s),l=n.getLineContent(s);if(a!==l)return!1}return!0}computeMoreMinimalEdits(t,n){return pe(this,void 0,void 0,function*(){const i=this._getModel(t);if(!i)return n;const o=[];let s;n=n.slice(0).sort((a,l)=>{if(a.range&&l.range)return D.compareRangesUsingStarts(a.range,l.range);const c=a.range?0:1,u=l.range?0:1;return c-u});for(let{range:a,text:l,eol:c}of n){if(typeof c=="number"&&(s=c),D.isEmpty(a)&&!l)continue;const u=i.getValueInRange(a);if(l=l.replace(/\r\n|\n|\r/g,i.eol),u===l)continue;if(Math.max(l.length,u.length)>be._diffLimit){o.push({range:a,text:l});continue}const d=Rr(u,l,!1),h=i.offsetAt(D.lift(a).getStartPosition());for(const f of d){const y=i.positionAt(h+f.originalStart),v=i.positionAt(h+f.originalStart+f.originalLength),L={text:l.substr(f.modifiedStart,f.modifiedLength),range:{startLineNumber:y.lineNumber,startColumn:y.column,endLineNumber:v.lineNumber,endColumn:v.column}};i.getValueInRange(L.range)!==L.text&&o.push(L)}}return typeof s=="number"&&o.push({eol:s,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),o})}computeLinks(t){return pe(this,void 0,void 0,function*(){const n=this._getModel(t);return n?si(n):null})}textualSuggest(t,n,i,o){return pe(this,void 0,void 0,function*(){const s=new Ze(!0),a=new RegExp(i,o),l=new Set;e:for(let c of t){const u=this._getModel(c);if(u){for(let d of u.words(a))if(!(d===n||!isNaN(Number(d)))&&(l.add(d),l.size>be._suggestionsLimit))break e}}return{words:Array.from(l),duration:s.elapsed()}})}computeWordRanges(t,n,i,o){return pe(this,void 0,void 0,function*(){const s=this._getModel(t);if(!s)return Object.create(null);const a=new RegExp(i,o),l=Object.create(null);for(let c=n.startLineNumber;c<n.endLineNumber;c++){const u=s.getLineWords(c,a);for(const d of u){if(!isNaN(Number(d.word)))continue;let h=l[d.word];h||(h=[],l[d.word]=h),h.push({startLineNumber:c,startColumn:d.startColumn,endLineNumber:c,endColumn:d.endColumn})}}return l})}navigateValueSet(t,n,i,o,s){return pe(this,void 0,void 0,function*(){const a=this._getModel(t);if(!a)return null;const l=new RegExp(o,s);n.startColumn===n.endColumn&&(n={startLineNumber:n.startLineNumber,startColumn:n.startColumn,endLineNumber:n.endLineNumber,endColumn:n.endColumn+1});const c=a.getValueInRange(n),u=a.getWordAtPosition({lineNumber:n.startLineNumber,column:n.startColumn},l);if(!u)return null;const d=a.getValueInRange(u);return O1.INSTANCE.navigateValueSet(n,c,u,d,i)})}loadForeignModule(t,n,i){const o={host:rr(i,(s,a)=>this._host.fhr(s,a)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(o,n),Promise.resolve(g1(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(t,n){if(!this._foreignModule||typeof this._foreignModule[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._foreignModule[t].apply(this._foreignModule,n))}catch(i){return Promise.reject(i)}}}be._diffLimit=1e5,be._suggestionsLimit=1e4;function gs(e){return new be(e,null)}typeof importScripts=="function"&&(V.monaco=bi());let z1=!1;function Mi(e){if(z1)return;z1=!0;const t=new bt(n=>{self.postMessage(n)},n=>new be(n,e));self.onmessage=n=>{t.onmessage(n.data)}}self.onmessage=e=>{z1||Mi(null)}})()})();
