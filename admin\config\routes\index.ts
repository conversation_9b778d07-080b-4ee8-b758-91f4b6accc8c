﻿import Admin from './admin';
import BlogAdmin from './blogAdmin';
import DataAdmin from './dataAdmin';
import OtherAdmin from './otherAdmin';
import QuickAdmin from './quickAdmin';
import DistributionAdmin from './distributionAdmin';
import NovelAdmin from './novelAdmin';
import WebsiteAdmin from './websiteAdmin';
import ReadAdmin from './readAdmin';
import WritingAdmin from './writingAdmin';
import BusinessAdmin from './businessAdmin';
import codeMessageAdmin from './codeMessageAdmin';
export default [
  {
    path: '/login',
    layout: false,
    component: './admin/Login',
  },
  ...Admin,
  ...BlogAdmin,
  ...DataAdmin,
  ...OtherAdmin,
  ...QuickAdmin,
  ...DistributionAdmin,
  ...NovelAdmin,
  ...WebsiteAdmin,
  ...ReadAdmin,
  ...WritingAdmin,
  ...BusinessAdmin,
  ...codeMessageAdmin,
  {
    path: '/403',
    component: './error/403',
  },
  {
    component: './error/404',
  },
];
