(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_antd_es_date-picker_index_js"],{

/***/ "./node_modules/@ant-design/icons-svg/es/asn/CalendarOutlined.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/CalendarOutlined.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
// This icon file is generated automatically.
var CalendarOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z" } }] }, "name": "calendar", "theme": "outlined" };
/* harmony default export */ __webpack_exports__["default"] = (CalendarOutlined);


/***/ }),

/***/ "./node_modules/@ant-design/icons-svg/es/asn/ClockCircleOutlined.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/ClockCircleOutlined.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
// This icon file is generated automatically.
var ClockCircleOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" } }, { "tag": "path", "attrs": { "d": "M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z" } }] }, "name": "clock-circle", "theme": "outlined" };
/* harmony default export */ __webpack_exports__["default"] = (ClockCircleOutlined);


/***/ }),

/***/ "./node_modules/@ant-design/icons-svg/es/asn/SwapRightOutlined.js":
/*!************************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/SwapRightOutlined.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
// This icon file is generated automatically.
var SwapRightOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "0 0 1024 1024", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z" } }] }, "name": "swap-right", "theme": "outlined" };
/* harmony default export */ __webpack_exports__["default"] = (SwapRightOutlined);


/***/ }),

/***/ "./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _ant_design_icons_svg_es_asn_CalendarOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/CalendarOutlined */ "./node_modules/@ant-design/icons-svg/es/asn/CalendarOutlined.js");
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AntdIcon */ "./node_modules/@ant-design/icons/es/components/AntdIcon.js");

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var CalendarOutlined = function CalendarOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_CalendarOutlined__WEBPACK_IMPORTED_MODULE_3__.default
  }));
};

CalendarOutlined.displayName = 'CalendarOutlined';
/* harmony default export */ __webpack_exports__["default"] = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(CalendarOutlined));

/***/ }),

/***/ "./node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js":
/*!************************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _ant_design_icons_svg_es_asn_ClockCircleOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/ClockCircleOutlined */ "./node_modules/@ant-design/icons-svg/es/asn/ClockCircleOutlined.js");
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AntdIcon */ "./node_modules/@ant-design/icons/es/components/AntdIcon.js");

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var ClockCircleOutlined = function ClockCircleOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_ClockCircleOutlined__WEBPACK_IMPORTED_MODULE_3__.default
  }));
};

ClockCircleOutlined.displayName = 'ClockCircleOutlined';
/* harmony default export */ __webpack_exports__["default"] = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(ClockCircleOutlined));

/***/ }),

/***/ "./node_modules/@ant-design/icons/es/icons/SwapRightOutlined.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/SwapRightOutlined.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _ant_design_icons_svg_es_asn_SwapRightOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/SwapRightOutlined */ "./node_modules/@ant-design/icons-svg/es/asn/SwapRightOutlined.js");
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AntdIcon */ "./node_modules/@ant-design/icons/es/components/AntdIcon.js");

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var SwapRightOutlined = function SwapRightOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_SwapRightOutlined__WEBPACK_IMPORTED_MODULE_3__.default
  }));
};

SwapRightOutlined.displayName = 'SwapRightOutlined';
/* harmony default export */ __webpack_exports__["default"] = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(SwapRightOutlined));

/***/ }),

/***/ "./node_modules/antd/es/date-picker/PickerButton.js":
/*!**********************************************************!*\
  !*** ./node_modules/antd/es/date-picker/PickerButton.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ PickerButton; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../button */ "./node_modules/antd/es/button/index.js");



function PickerButton(props) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_button__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    size: "small",
    type: "primary"
  }, props));
}

/***/ }),

/***/ "./node_modules/antd/es/date-picker/PickerTag.js":
/*!*******************************************************!*\
  !*** ./node_modules/antd/es/date-picker/PickerTag.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ PickerTag; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _tag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../tag */ "./node_modules/antd/es/tag/index.js");



function PickerTag(props) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_tag__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    color: "blue"
  }, props));
}

/***/ }),

/***/ "./node_modules/antd/es/date-picker/generatePicker/generateRangePicker.js":
/*!********************************************************************************!*\
  !*** ./node_modules/antd/es/date-picker/generatePicker/generateRangePicker.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ generateRangePicker; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ "./node_modules/@babel/runtime/helpers/esm/classCallCheck.js");
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ "./node_modules/@babel/runtime/helpers/esm/createClass.js");
/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ "./node_modules/@babel/runtime/helpers/esm/inherits.js");
/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ "./node_modules/@babel/runtime/helpers/esm/createSuper.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _ant_design_icons_es_icons_CalendarOutlined__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ant-design/icons/es/icons/CalendarOutlined */ "./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js");
/* harmony import */ var _ant_design_icons_es_icons_ClockCircleOutlined__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @ant-design/icons/es/icons/ClockCircleOutlined */ "./node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js");
/* harmony import */ var _ant_design_icons_es_icons_CloseCircleFilled__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @ant-design/icons/es/icons/CloseCircleFilled */ "./node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js");
/* harmony import */ var _ant_design_icons_es_icons_SwapRightOutlined__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ant-design/icons/es/icons/SwapRightOutlined */ "./node_modules/@ant-design/icons/es/icons/SwapRightOutlined.js");
/* harmony import */ var rc_picker__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-picker */ "./node_modules/rc-picker/es/index.js");
/* harmony import */ var _locale_en_US__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../locale/en_US */ "./node_modules/antd/es/date-picker/locale/en_US.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../config-provider/SizeContext */ "./node_modules/antd/es/config-provider/SizeContext.js");
/* harmony import */ var _locale_provider_LocaleReceiver__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../locale-provider/LocaleReceiver */ "./node_modules/antd/es/locale-provider/LocaleReceiver.js");
/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../util */ "./node_modules/antd/es/date-picker/util.js");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! . */ "./node_modules/antd/es/date-picker/generatePicker/index.js");







var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};














function generateRangePicker(generateConfig) {
  var RangePicker = /*#__PURE__*/function (_React$Component) {
    (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_4__.default)(RangePicker, _React$Component);

    var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_5__.default)(RangePicker);

    function RangePicker() {
      var _this;

      (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_2__.default)(this, RangePicker);

      _this = _super.apply(this, arguments);
      _this.pickerRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createRef();

      _this.focus = function () {
        if (_this.pickerRef.current) {
          _this.pickerRef.current.focus();
        }
      };

      _this.blur = function () {
        if (_this.pickerRef.current) {
          _this.pickerRef.current.blur();
        }
      };

      _this.renderPicker = function (contextLocale) {
        var locale = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, contextLocale), _this.props.locale);

        var _this$context = _this.context,
            getPrefixCls = _this$context.getPrefixCls,
            direction = _this$context.direction,
            getPopupContainer = _this$context.getPopupContainer;

        var _a = _this.props,
            customizePrefixCls = _a.prefixCls,
            customGetPopupContainer = _a.getPopupContainer,
            className = _a.className,
            customizeSize = _a.size,
            _a$bordered = _a.bordered,
            bordered = _a$bordered === void 0 ? true : _a$bordered,
            placeholder = _a.placeholder,
            restProps = __rest(_a, ["prefixCls", "getPopupContainer", "className", "size", "bordered", "placeholder"]);

        var _this$props = _this.props,
            format = _this$props.format,
            showTime = _this$props.showTime,
            picker = _this$props.picker;
        var prefixCls = getPrefixCls('picker', customizePrefixCls);
        var additionalOverrideProps = {};
        additionalOverrideProps = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, additionalOverrideProps), showTime ? (0,___WEBPACK_IMPORTED_MODULE_9__.getTimeProps)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({
          format: format,
          picker: picker
        }, showTime)) : {}), picker === 'time' ? (0,___WEBPACK_IMPORTED_MODULE_9__.getTimeProps)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({
          format: format
        }, _this.props), {
          picker: picker
        })) : {});
        var rootPrefixCls = getPrefixCls();
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_10__.default.Consumer, null, function (size) {
          var _classNames;

          var mergedSize = customizeSize || size;
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(rc_picker__WEBPACK_IMPORTED_MODULE_8__.RangePicker, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({
            separator: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
              "aria-label": "to",
              className: "".concat(prefixCls, "-separator")
            }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons_es_icons_SwapRightOutlined__WEBPACK_IMPORTED_MODULE_11__.default, null)),
            ref: _this.pickerRef,
            placeholder: (0,_util__WEBPACK_IMPORTED_MODULE_12__.getRangePlaceholder)(picker, locale, placeholder),
            suffixIcon: picker === 'time' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons_es_icons_ClockCircleOutlined__WEBPACK_IMPORTED_MODULE_13__.default, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons_es_icons_CalendarOutlined__WEBPACK_IMPORTED_MODULE_14__.default, null),
            clearIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons_es_icons_CloseCircleFilled__WEBPACK_IMPORTED_MODULE_15__.default, null),
            allowClear: true,
            transitionName: "".concat(rootPrefixCls, "-slide-up")
          }, restProps, additionalOverrideProps, {
            className: classnames__WEBPACK_IMPORTED_MODULE_7___default()((_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-").concat(mergedSize), mergedSize), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-borderless"), !bordered), _classNames), className),
            locale: locale.lang,
            prefixCls: prefixCls,
            getPopupContainer: customGetPopupContainer || getPopupContainer,
            generateConfig: generateConfig,
            prevIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
              className: "".concat(prefixCls, "-prev-icon")
            }),
            nextIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
              className: "".concat(prefixCls, "-next-icon")
            }),
            superPrevIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
              className: "".concat(prefixCls, "-super-prev-icon")
            }),
            superNextIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
              className: "".concat(prefixCls, "-super-next-icon")
            }),
            components: ___WEBPACK_IMPORTED_MODULE_9__.Components,
            direction: direction
          }));
        });
      };

      return _this;
    }

    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_3__.default)(RangePicker, [{
      key: "render",
      value: function render() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_locale_provider_LocaleReceiver__WEBPACK_IMPORTED_MODULE_16__.default, {
          componentName: "DatePicker",
          defaultLocale: _locale_en_US__WEBPACK_IMPORTED_MODULE_17__.default
        }, this.renderPicker);
      }
    }]);

    return RangePicker;
  }(react__WEBPACK_IMPORTED_MODULE_6__.Component);

  RangePicker.contextType = _config_provider__WEBPACK_IMPORTED_MODULE_18__.ConfigContext;
  return RangePicker;
}

/***/ }),

/***/ "./node_modules/antd/es/date-picker/generatePicker/generateSinglePicker.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/antd/es/date-picker/generatePicker/generateSinglePicker.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ generatePicker; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ "./node_modules/@babel/runtime/helpers/esm/classCallCheck.js");
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ "./node_modules/@babel/runtime/helpers/esm/createClass.js");
/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ "./node_modules/@babel/runtime/helpers/esm/inherits.js");
/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ "./node_modules/@babel/runtime/helpers/esm/createSuper.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _ant_design_icons_es_icons_CalendarOutlined__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @ant-design/icons/es/icons/CalendarOutlined */ "./node_modules/@ant-design/icons/es/icons/CalendarOutlined.js");
/* harmony import */ var _ant_design_icons_es_icons_ClockCircleOutlined__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ant-design/icons/es/icons/ClockCircleOutlined */ "./node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js");
/* harmony import */ var _ant_design_icons_es_icons_CloseCircleFilled__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ant-design/icons/es/icons/CloseCircleFilled */ "./node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js");
/* harmony import */ var rc_picker__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-picker */ "./node_modules/rc-picker/es/index.js");
/* harmony import */ var _locale_en_US__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../locale/en_US */ "./node_modules/antd/es/date-picker/locale/en_US.js");
/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../util */ "./node_modules/antd/es/date-picker/util.js");
/* harmony import */ var _util_devWarning__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../_util/devWarning */ "./node_modules/antd/es/_util/devWarning.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _locale_provider_LocaleReceiver__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../locale-provider/LocaleReceiver */ "./node_modules/antd/es/locale-provider/LocaleReceiver.js");
/* harmony import */ var _config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../config-provider/SizeContext */ "./node_modules/antd/es/config-provider/SizeContext.js");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! . */ "./node_modules/antd/es/date-picker/generatePicker/index.js");







var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};














function generatePicker(generateConfig) {
  function getPicker(picker, displayName) {
    var Picker = /*#__PURE__*/function (_React$Component) {
      (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_4__.default)(Picker, _React$Component);

      var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_5__.default)(Picker);

      function Picker(props) {
        var _this;

        (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_2__.default)(this, Picker);

        _this = _super.call(this, props);
        _this.pickerRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createRef();

        _this.focus = function () {
          if (_this.pickerRef.current) {
            _this.pickerRef.current.focus();
          }
        };

        _this.blur = function () {
          if (_this.pickerRef.current) {
            _this.pickerRef.current.blur();
          }
        };

        _this.renderPicker = function (contextLocale) {
          var locale = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, contextLocale), _this.props.locale);

          var _this$context = _this.context,
              getPrefixCls = _this$context.getPrefixCls,
              direction = _this$context.direction,
              getPopupContainer = _this$context.getPopupContainer;

          var _a = _this.props,
              customizePrefixCls = _a.prefixCls,
              customizeGetPopupContainer = _a.getPopupContainer,
              className = _a.className,
              customizeSize = _a.size,
              _a$bordered = _a.bordered,
              bordered = _a$bordered === void 0 ? true : _a$bordered,
              placeholder = _a.placeholder,
              restProps = __rest(_a, ["prefixCls", "getPopupContainer", "className", "size", "bordered", "placeholder"]);

          var _this$props = _this.props,
              format = _this$props.format,
              showTime = _this$props.showTime;
          var prefixCls = getPrefixCls('picker', customizePrefixCls);
          var additionalProps = {
            showToday: true
          };
          var additionalOverrideProps = {};

          if (picker) {
            additionalOverrideProps.picker = picker;
          }

          var mergedPicker = picker || _this.props.picker;
          additionalOverrideProps = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, additionalOverrideProps), showTime ? (0,___WEBPACK_IMPORTED_MODULE_9__.getTimeProps)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({
            format: format,
            picker: mergedPicker
          }, showTime)) : {}), mergedPicker === 'time' ? (0,___WEBPACK_IMPORTED_MODULE_9__.getTimeProps)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({
            format: format
          }, _this.props), {
            picker: mergedPicker
          })) : {});
          var rootPrefixCls = getPrefixCls();
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_10__.default.Consumer, null, function (size) {
            var _classNames;

            var mergedSize = customizeSize || size;
            return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(rc_picker__WEBPACK_IMPORTED_MODULE_8__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({
              ref: _this.pickerRef,
              placeholder: (0,_util__WEBPACK_IMPORTED_MODULE_11__.getPlaceholder)(mergedPicker, locale, placeholder),
              suffixIcon: mergedPicker === 'time' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons_es_icons_ClockCircleOutlined__WEBPACK_IMPORTED_MODULE_12__.default, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons_es_icons_CalendarOutlined__WEBPACK_IMPORTED_MODULE_13__.default, null),
              clearIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons_es_icons_CloseCircleFilled__WEBPACK_IMPORTED_MODULE_14__.default, null),
              allowClear: true,
              transitionName: "".concat(rootPrefixCls, "-slide-up")
            }, additionalProps, restProps, additionalOverrideProps, {
              locale: locale.lang,
              className: classnames__WEBPACK_IMPORTED_MODULE_7___default()((_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-").concat(mergedSize), mergedSize), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-borderless"), !bordered), _classNames), className),
              prefixCls: prefixCls,
              getPopupContainer: customizeGetPopupContainer || getPopupContainer,
              generateConfig: generateConfig,
              prevIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
                className: "".concat(prefixCls, "-prev-icon")
              }),
              nextIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
                className: "".concat(prefixCls, "-next-icon")
              }),
              superPrevIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
                className: "".concat(prefixCls, "-super-prev-icon")
              }),
              superNextIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", {
                className: "".concat(prefixCls, "-super-next-icon")
              }),
              components: ___WEBPACK_IMPORTED_MODULE_9__.Components,
              direction: direction
            }));
          });
        };

        (0,_util_devWarning__WEBPACK_IMPORTED_MODULE_15__.default)(picker !== 'quarter', displayName, "DatePicker.".concat(displayName, " is legacy usage. Please use DatePicker[picker='").concat(picker, "'] directly."));
        return _this;
      }

      (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_3__.default)(Picker, [{
        key: "render",
        value: function render() {
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_locale_provider_LocaleReceiver__WEBPACK_IMPORTED_MODULE_16__.default, {
            componentName: "DatePicker",
            defaultLocale: _locale_en_US__WEBPACK_IMPORTED_MODULE_17__.default
          }, this.renderPicker);
        }
      }]);

      return Picker;
    }(react__WEBPACK_IMPORTED_MODULE_6__.Component);

    Picker.contextType = _config_provider__WEBPACK_IMPORTED_MODULE_18__.ConfigContext;

    if (displayName) {
      Picker.displayName = displayName;
    }

    return Picker;
  }

  var DatePicker = getPicker();
  var WeekPicker = getPicker('week', 'WeekPicker');
  var MonthPicker = getPicker('month', 'MonthPicker');
  var YearPicker = getPicker('year', 'YearPicker');
  var TimePicker = getPicker('time', 'TimePicker');
  var QuarterPicker = getPicker('quarter', 'QuarterPicker');
  return {
    DatePicker: DatePicker,
    WeekPicker: WeekPicker,
    MonthPicker: MonthPicker,
    YearPicker: YearPicker,
    TimePicker: TimePicker,
    QuarterPicker: QuarterPicker
  };
}

/***/ }),

/***/ "./node_modules/antd/es/date-picker/generatePicker/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/antd/es/date-picker/generatePicker/index.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Components": function() { return /* binding */ Components; },
/* harmony export */   "getTimeProps": function() { return /* binding */ getTimeProps; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _PickerButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../PickerButton */ "./node_modules/antd/es/date-picker/PickerButton.js");
/* harmony import */ var _PickerTag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../PickerTag */ "./node_modules/antd/es/date-picker/PickerTag.js");
/* harmony import */ var _generateSinglePicker__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./generateSinglePicker */ "./node_modules/antd/es/date-picker/generatePicker/generateSinglePicker.js");
/* harmony import */ var _generateRangePicker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./generateRangePicker */ "./node_modules/antd/es/date-picker/generatePicker/generateRangePicker.js");





var Components = {
  button: _PickerButton__WEBPACK_IMPORTED_MODULE_1__.default,
  rangeItem: _PickerTag__WEBPACK_IMPORTED_MODULE_2__.default
};

function toArray(list) {
  if (!list) {
    return [];
  }

  return Array.isArray(list) ? list : [list];
}

function getTimeProps(props) {
  var format = props.format,
      picker = props.picker,
      showHour = props.showHour,
      showMinute = props.showMinute,
      showSecond = props.showSecond,
      use12Hours = props.use12Hours;
  var firstFormat = toArray(format)[0];

  var showTimeObj = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props);

  if (firstFormat && typeof firstFormat === 'string') {
    if (!firstFormat.includes('s') && showSecond === undefined) {
      showTimeObj.showSecond = false;
    }

    if (!firstFormat.includes('m') && showMinute === undefined) {
      showTimeObj.showMinute = false;
    }

    if (!firstFormat.includes('H') && !firstFormat.includes('h') && showHour === undefined) {
      showTimeObj.showHour = false;
    }

    if ((firstFormat.includes('a') || firstFormat.includes('A')) && use12Hours === undefined) {
      showTimeObj.use12Hours = true;
    }
  }

  if (picker === 'time') {
    return showTimeObj;
  }

  if (typeof firstFormat === 'function') {
    // format of showTime should use default when format is custom format function
    delete showTimeObj.format;
  }

  return {
    showTime: showTimeObj
  };
}

function generatePicker(generateConfig) {
  // =========================== Picker ===========================
  var _generateSinglePicker = (0,_generateSinglePicker__WEBPACK_IMPORTED_MODULE_3__.default)(generateConfig),
      DatePicker = _generateSinglePicker.DatePicker,
      WeekPicker = _generateSinglePicker.WeekPicker,
      MonthPicker = _generateSinglePicker.MonthPicker,
      YearPicker = _generateSinglePicker.YearPicker,
      TimePicker = _generateSinglePicker.TimePicker,
      QuarterPicker = _generateSinglePicker.QuarterPicker; // ======================== Range Picker ========================


  var RangePicker = (0,_generateRangePicker__WEBPACK_IMPORTED_MODULE_4__.default)(generateConfig);
  var MergedDatePicker = DatePicker;
  MergedDatePicker.WeekPicker = WeekPicker;
  MergedDatePicker.MonthPicker = MonthPicker;
  MergedDatePicker.YearPicker = YearPicker;
  MergedDatePicker.RangePicker = RangePicker;
  MergedDatePicker.TimePicker = TimePicker;
  MergedDatePicker.QuarterPicker = QuarterPicker;
  return MergedDatePicker;
}

/* harmony default export */ __webpack_exports__["default"] = (generatePicker);

/***/ }),

/***/ "./node_modules/antd/es/date-picker/index.js":
/*!***************************************************!*\
  !*** ./node_modules/antd/es/date-picker/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var rc_picker_es_generate_moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-picker/es/generate/moment */ "./node_modules/rc-picker/es/generate/moment.js");
/* harmony import */ var _generatePicker__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./generatePicker */ "./node_modules/antd/es/date-picker/generatePicker/index.js");


var DatePicker = (0,_generatePicker__WEBPACK_IMPORTED_MODULE_1__.default)(rc_picker_es_generate_moment__WEBPACK_IMPORTED_MODULE_0__.default);
/* harmony default export */ __webpack_exports__["default"] = (DatePicker);

/***/ }),

/***/ "./node_modules/antd/es/date-picker/util.js":
/*!**************************************************!*\
  !*** ./node_modules/antd/es/date-picker/util.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "getPlaceholder": function() { return /* binding */ getPlaceholder; },
/* harmony export */   "getRangePlaceholder": function() { return /* binding */ getRangePlaceholder; }
/* harmony export */ });
function getPlaceholder(picker, locale, customizePlaceholder) {
  if (customizePlaceholder !== undefined) {
    return customizePlaceholder;
  }

  if (picker === 'year' && locale.lang.yearPlaceholder) {
    return locale.lang.yearPlaceholder;
  }

  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {
    return locale.lang.quarterPlaceholder;
  }

  if (picker === 'month' && locale.lang.monthPlaceholder) {
    return locale.lang.monthPlaceholder;
  }

  if (picker === 'week' && locale.lang.weekPlaceholder) {
    return locale.lang.weekPlaceholder;
  }

  if (picker === 'time' && locale.timePickerLocale.placeholder) {
    return locale.timePickerLocale.placeholder;
  }

  return locale.lang.placeholder;
}
function getRangePlaceholder(picker, locale, customizePlaceholder) {
  if (customizePlaceholder !== undefined) {
    return customizePlaceholder;
  }

  if (picker === 'year' && locale.lang.yearPlaceholder) {
    return locale.lang.rangeYearPlaceholder;
  }

  if (picker === 'month' && locale.lang.monthPlaceholder) {
    return locale.lang.rangeMonthPlaceholder;
  }

  if (picker === 'week' && locale.lang.weekPlaceholder) {
    return locale.lang.rangeWeekPlaceholder;
  }

  if (picker === 'time' && locale.timePickerLocale.placeholder) {
    return locale.timePickerLocale.rangePlaceholder;
  }

  return locale.lang.rangePlaceholder;
}

/***/ }),

/***/ "./node_modules/antd/es/tag/CheckableTag.js":
/*!**************************************************!*\
  !*** ./node_modules/antd/es/tag/CheckableTag.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");



var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};





var CheckableTag = function CheckableTag(_a) {
  var _classNames;

  var customizePrefixCls = _a.prefixCls,
      className = _a.className,
      checked = _a.checked,
      onChange = _a.onChange,
      onClick = _a.onClick,
      restProps = __rest(_a, ["prefixCls", "className", "checked", "onChange", "onClick"]);

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_4__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls;

  var handleClick = function handleClick(e) {
    onChange === null || onChange === void 0 ? void 0 : onChange(!checked);
    onClick === null || onClick === void 0 ? void 0 : onClick(e);
  };

  var prefixCls = getPrefixCls('tag', customizePrefixCls);
  var cls = classnames__WEBPACK_IMPORTED_MODULE_3___default()(prefixCls, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-checkable"), true), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-checkable-checked"), checked), _classNames), className);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, restProps, {
    className: cls,
    onClick: handleClick
  }));
};

/* harmony default export */ __webpack_exports__["default"] = (CheckableTag);

/***/ }),

/***/ "./node_modules/antd/es/tag/index.js":
/*!*******************************************!*\
  !*** ./node_modules/antd/es/tag/index.js ***!
  \*******************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var rc_util_es_omit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/omit */ "./node_modules/antd/node_modules/rc-util/es/omit.js");
/* harmony import */ var _ant_design_icons_es_icons_CloseOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ant-design/icons/es/icons/CloseOutlined */ "./node_modules/@ant-design/icons/es/icons/CloseOutlined.js");
/* harmony import */ var _CheckableTag__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CheckableTag */ "./node_modules/antd/es/tag/CheckableTag.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _util_colors__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../_util/colors */ "./node_modules/antd/es/_util/colors.js");
/* harmony import */ var _util_wave__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../_util/wave */ "./node_modules/antd/es/_util/wave.js");




var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};









var PresetColorRegex = new RegExp("^(".concat(_util_colors__WEBPACK_IMPORTED_MODULE_6__.PresetColorTypes.join('|'), ")(-inverse)?$"));
var PresetStatusColorRegex = new RegExp("^(".concat(_util_colors__WEBPACK_IMPORTED_MODULE_6__.PresetStatusColorTypes.join('|'), ")$"));

var InternalTag = function InternalTag(_a, ref) {
  var _classNames;

  var customizePrefixCls = _a.prefixCls,
      className = _a.className,
      style = _a.style,
      children = _a.children,
      icon = _a.icon,
      color = _a.color,
      onClose = _a.onClose,
      closeIcon = _a.closeIcon,
      _a$closable = _a.closable,
      closable = _a$closable === void 0 ? false : _a$closable,
      props = __rest(_a, ["prefixCls", "className", "style", "children", "icon", "color", "onClose", "closeIcon", "closable"]);

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_3__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_7__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState(true),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__.default)(_React$useState, 2),
      visible = _React$useState2[0],
      setVisible = _React$useState2[1];

  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {
    if ('visible' in props) {
      setVisible(props.visible);
    }
  }, [props.visible]);

  var isPresetColor = function isPresetColor() {
    if (!color) {
      return false;
    }

    return PresetColorRegex.test(color) || PresetStatusColorRegex.test(color);
  };

  var tagStyle = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({
    backgroundColor: color && !isPresetColor() ? color : undefined
  }, style);

  var presetColor = isPresetColor();
  var prefixCls = getPrefixCls('tag', customizePrefixCls);
  var tagClassName = classnames__WEBPACK_IMPORTED_MODULE_4___default()(prefixCls, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-").concat(color), presetColor), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-has-color"), color && !presetColor), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-hidden"), !visible), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-rtl"), direction === 'rtl'), _classNames), className);

  var handleCloseClick = function handleCloseClick(e) {
    e.stopPropagation();
    onClose === null || onClose === void 0 ? void 0 : onClose(e);

    if (e.defaultPrevented) {
      return;
    }

    if (!('visible' in props)) {
      setVisible(false);
    }
  };

  var renderCloseIcon = function renderCloseIcon() {
    if (closable) {
      return closeIcon ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
        className: "".concat(prefixCls, "-close-icon"),
        onClick: handleCloseClick
      }, closeIcon) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons_es_icons_CloseOutlined__WEBPACK_IMPORTED_MODULE_8__.default, {
        className: "".concat(prefixCls, "-close-icon"),
        onClick: handleCloseClick
      });
    }

    return null;
  };

  var isNeedWave = 'onClick' in props || children && children.type === 'a';
  var tagProps = (0,rc_util_es_omit__WEBPACK_IMPORTED_MODULE_5__.default)(props, ['visible']);
  var iconNode = icon || null;
  var kids = iconNode ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, iconNode, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, children)) : children;
  var tagNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, tagProps, {
    ref: ref,
    className: tagClassName,
    style: tagStyle
  }), kids, renderCloseIcon());
  return isNeedWave ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_util_wave__WEBPACK_IMPORTED_MODULE_9__.default, null, tagNode) : tagNode;
};

var Tag = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(InternalTag);
Tag.displayName = 'Tag';
Tag.CheckableTag = _CheckableTag__WEBPACK_IMPORTED_MODULE_10__.default;
/* harmony default export */ __webpack_exports__["default"] = (Tag);

/***/ }),

/***/ "./node_modules/rc-picker/es/PanelContext.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-picker/es/PanelContext.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

var PanelContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext({});
/* harmony default export */ __webpack_exports__["default"] = (PanelContext);

/***/ }),

/***/ "./node_modules/rc-picker/es/Picker.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-picker/es/Picker.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ "./node_modules/@babel/runtime/helpers/esm/classCallCheck.js");
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ "./node_modules/@babel/runtime/helpers/esm/createClass.js");
/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ "./node_modules/@babel/runtime/helpers/esm/inherits.js");
/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ "./node_modules/@babel/runtime/helpers/esm/createSuper.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/rc-util/es/warning.js");
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ "./node_modules/rc-util/es/hooks/useMergedState.js");
/* harmony import */ var _PickerPanel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./PickerPanel */ "./node_modules/rc-picker/es/PickerPanel.js");
/* harmony import */ var _PickerTrigger__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PickerTrigger */ "./node_modules/rc-picker/es/PickerTrigger.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _utils_miscUtil__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./utils/miscUtil */ "./node_modules/rc-picker/es/utils/miscUtil.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");
/* harmony import */ var _hooks_usePickerInput__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./hooks/usePickerInput */ "./node_modules/rc-picker/es/hooks/usePickerInput.js");
/* harmony import */ var _hooks_useTextValueMapping__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./hooks/useTextValueMapping */ "./node_modules/rc-picker/es/hooks/useTextValueMapping.js");
/* harmony import */ var _hooks_useValueTexts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./hooks/useValueTexts */ "./node_modules/rc-picker/es/hooks/useValueTexts.js");
/* harmony import */ var _hooks_useHoverValue__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./hooks/useHoverValue */ "./node_modules/rc-picker/es/hooks/useHoverValue.js");









/**
 * Removed:
 *  - getCalendarContainer: use `getPopupContainer` instead
 *  - onOk
 *
 * New Feature:
 *  - picker
 *  - allowEmpty
 *  - selectable
 *
 * Tips: Should add faq about `datetime` mode with `defaultValue`
 */















function InnerPicker(props) {
  var _classNames2;

  var _props$prefixCls = props.prefixCls,
      prefixCls = _props$prefixCls === void 0 ? 'rc-picker' : _props$prefixCls,
      id = props.id,
      tabIndex = props.tabIndex,
      style = props.style,
      className = props.className,
      dropdownClassName = props.dropdownClassName,
      dropdownAlign = props.dropdownAlign,
      popupStyle = props.popupStyle,
      transitionName = props.transitionName,
      generateConfig = props.generateConfig,
      locale = props.locale,
      inputReadOnly = props.inputReadOnly,
      allowClear = props.allowClear,
      autoFocus = props.autoFocus,
      showTime = props.showTime,
      _props$picker = props.picker,
      picker = _props$picker === void 0 ? 'date' : _props$picker,
      format = props.format,
      use12Hours = props.use12Hours,
      value = props.value,
      defaultValue = props.defaultValue,
      open = props.open,
      defaultOpen = props.defaultOpen,
      defaultOpenValue = props.defaultOpenValue,
      suffixIcon = props.suffixIcon,
      clearIcon = props.clearIcon,
      disabled = props.disabled,
      disabledDate = props.disabledDate,
      placeholder = props.placeholder,
      getPopupContainer = props.getPopupContainer,
      pickerRef = props.pickerRef,
      panelRender = props.panelRender,
      onChange = props.onChange,
      onOpenChange = props.onOpenChange,
      onFocus = props.onFocus,
      onBlur = props.onBlur,
      onMouseDown = props.onMouseDown,
      onMouseUp = props.onMouseUp,
      onMouseEnter = props.onMouseEnter,
      onMouseLeave = props.onMouseLeave,
      onContextMenu = props.onContextMenu,
      onClick = props.onClick,
      _onKeyDown = props.onKeyDown,
      _onSelect = props.onSelect,
      direction = props.direction,
      _props$autoComplete = props.autoComplete,
      autoComplete = _props$autoComplete === void 0 ? 'off' : _props$autoComplete;
  var inputRef = react__WEBPACK_IMPORTED_MODULE_8__.useRef(null);
  var needConfirmButton = picker === 'date' && !!showTime || picker === 'time'; // ============================= State =============================

  var formatList = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_15__.toArray)((0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_17__.getDefaultFormat)(format, picker, showTime, use12Hours)); // Panel ref

  var panelDivRef = react__WEBPACK_IMPORTED_MODULE_8__.useRef(null);
  var inputDivRef = react__WEBPACK_IMPORTED_MODULE_8__.useRef(null);
  var containerRef = react__WEBPACK_IMPORTED_MODULE_8__.useRef(null); // Real value

  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_11__.default)(null, {
    value: value,
    defaultValue: defaultValue
  }),
      _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useMergedState, 2),
      mergedValue = _useMergedState2[0],
      setInnerValue = _useMergedState2[1]; // Selected value


  var _React$useState = react__WEBPACK_IMPORTED_MODULE_8__.useState(mergedValue),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_React$useState, 2),
      selectedValue = _React$useState2[0],
      setSelectedValue = _React$useState2[1]; // Operation ref


  var operationRef = react__WEBPACK_IMPORTED_MODULE_8__.useRef(null); // Open

  var _useMergedState3 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_11__.default)(false, {
    value: open,
    defaultValue: defaultOpen,
    postState: function postState(postOpen) {
      return disabled ? false : postOpen;
    },
    onChange: function onChange(newOpen) {
      if (onOpenChange) {
        onOpenChange(newOpen);
      }

      if (!newOpen && operationRef.current && operationRef.current.onClose) {
        operationRef.current.onClose();
      }
    }
  }),
      _useMergedState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useMergedState3, 2),
      mergedOpen = _useMergedState4[0],
      triggerInnerOpen = _useMergedState4[1]; // ============================= Text ==============================


  var _useValueTexts = (0,_hooks_useValueTexts__WEBPACK_IMPORTED_MODULE_20__.default)(selectedValue, {
    formatList: formatList,
    generateConfig: generateConfig,
    locale: locale
  }),
      _useValueTexts2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useValueTexts, 2),
      valueTexts = _useValueTexts2[0],
      firstValueText = _useValueTexts2[1];

  var _useTextValueMapping = (0,_hooks_useTextValueMapping__WEBPACK_IMPORTED_MODULE_19__.default)({
    valueTexts: valueTexts,
    onTextChange: function onTextChange(newText) {
      var inputDate = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_14__.parseValue)(newText, {
        locale: locale,
        formatList: formatList,
        generateConfig: generateConfig
      });

      if (inputDate && (!disabledDate || !disabledDate(inputDate))) {
        setSelectedValue(inputDate);
      }
    }
  }),
      _useTextValueMapping2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useTextValueMapping, 3),
      text = _useTextValueMapping2[0],
      triggerTextChange = _useTextValueMapping2[1],
      resetText = _useTextValueMapping2[2]; // ============================ Trigger ============================


  var triggerChange = function triggerChange(newValue) {
    setSelectedValue(newValue);
    setInnerValue(newValue);

    if (onChange && !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_14__.isEqual)(generateConfig, mergedValue, newValue)) {
      onChange(newValue, newValue ? (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_14__.formatValue)(newValue, {
        generateConfig: generateConfig,
        locale: locale,
        format: formatList[0]
      }) : '');
    }
  };

  var triggerOpen = function triggerOpen(newOpen) {
    if (disabled && newOpen) {
      return;
    }

    triggerInnerOpen(newOpen);
  };

  var forwardKeyDown = function forwardKeyDown(e) {
    if (mergedOpen && operationRef.current && operationRef.current.onKeyDown) {
      // Let popup panel handle keyboard
      return operationRef.current.onKeyDown(e);
    }
    /* istanbul ignore next */

    /* eslint-disable no-lone-blocks */


    {
      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_10__.default)(false, 'Picker not correct forward KeyDown operation. Please help to fire issue about this.');
      return false;
    }
  };

  var onInternalMouseUp = function onInternalMouseUp() {
    if (onMouseUp) {
      onMouseUp.apply(void 0, arguments);
    }

    if (inputRef.current) {
      inputRef.current.focus();
      triggerOpen(true);
    }
  }; // ============================= Input =============================


  var _usePickerInput = (0,_hooks_usePickerInput__WEBPACK_IMPORTED_MODULE_18__.default)({
    blurToCancel: needConfirmButton,
    open: mergedOpen,
    value: text,
    triggerOpen: triggerOpen,
    forwardKeyDown: forwardKeyDown,
    isClickOutside: function isClickOutside(target) {
      return !(0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_17__.elementsContains)([panelDivRef.current, inputDivRef.current, containerRef.current], target);
    },
    onSubmit: function onSubmit() {
      if ( // When user typing disabledDate with keyboard and enter, this value will be empty
      !selectedValue || // Normal disabled check
      disabledDate && disabledDate(selectedValue)) {
        return false;
      }

      triggerChange(selectedValue);
      triggerOpen(false);
      resetText();
      return true;
    },
    onCancel: function onCancel() {
      triggerOpen(false);
      setSelectedValue(mergedValue);
      resetText();
    },
    onKeyDown: function onKeyDown(e, preventDefault) {
      _onKeyDown === null || _onKeyDown === void 0 ? void 0 : _onKeyDown(e, preventDefault);
    },
    onFocus: onFocus,
    onBlur: onBlur
  }),
      _usePickerInput2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_usePickerInput, 2),
      inputProps = _usePickerInput2[0],
      _usePickerInput2$ = _usePickerInput2[1],
      focused = _usePickerInput2$.focused,
      typing = _usePickerInput2$.typing; // ============================= Sync ==============================
  // Close should sync back with text value


  react__WEBPACK_IMPORTED_MODULE_8__.useEffect(function () {
    if (!mergedOpen) {
      setSelectedValue(mergedValue);

      if (!valueTexts.length || valueTexts[0] === '') {
        triggerTextChange('');
      } else if (firstValueText !== text) {
        resetText();
      }
    }
  }, [mergedOpen, valueTexts]); // Change picker should sync back with text value

  react__WEBPACK_IMPORTED_MODULE_8__.useEffect(function () {
    if (!mergedOpen) {
      resetText();
    }
  }, [picker]); // Sync innerValue with control mode

  react__WEBPACK_IMPORTED_MODULE_8__.useEffect(function () {
    // Sync select value
    setSelectedValue(mergedValue);
  }, [mergedValue]); // ============================ Private ============================

  if (pickerRef) {
    pickerRef.current = {
      focus: function focus() {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      },
      blur: function blur() {
        if (inputRef.current) {
          inputRef.current.blur();
        }
      }
    };
  }

  var _useHoverValue = (0,_hooks_useHoverValue__WEBPACK_IMPORTED_MODULE_21__.default)(text, {
    formatList: formatList,
    generateConfig: generateConfig,
    locale: locale
  }),
      _useHoverValue2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_7__.default)(_useHoverValue, 3),
      hoverValue = _useHoverValue2[0],
      onEnter = _useHoverValue2[1],
      onLeave = _useHoverValue2[2]; // ============================= Panel =============================


  var panelProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)({}, props), {}, {
    className: undefined,
    style: undefined,
    pickerValue: undefined,
    onPickerValueChange: undefined,
    onChange: null
  });

  var panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement(_PickerPanel__WEBPACK_IMPORTED_MODULE_12__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({}, panelProps, {
    generateConfig: generateConfig,
    className: classnames__WEBPACK_IMPORTED_MODULE_9___default()((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_5__.default)({}, "".concat(prefixCls, "-panel-focused"), !typing)),
    value: selectedValue,
    locale: locale,
    tabIndex: -1,
    onSelect: function onSelect(date) {
      _onSelect === null || _onSelect === void 0 ? void 0 : _onSelect(date);
      setSelectedValue(date);
    },
    direction: direction,
    onPanelChange: function onPanelChange(viewDate, mode) {
      var onPanelChange = props.onPanelChange;
      onLeave(true);
      onPanelChange === null || onPanelChange === void 0 ? void 0 : onPanelChange(viewDate, mode);
    }
  }));

  if (panelRender) {
    panelNode = panelRender(panelNode);
  }

  var panel = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement("div", {
    className: "".concat(prefixCls, "-panel-container"),
    onMouseDown: function onMouseDown(e) {
      e.preventDefault();
    }
  }, panelNode);
  var suffixNode;

  if (suffixIcon) {
    suffixNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement("span", {
      className: "".concat(prefixCls, "-suffix")
    }, suffixIcon);
  }

  var clearNode;

  if (allowClear && mergedValue && !disabled) {
    clearNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement("span", {
      onMouseDown: function onMouseDown(e) {
        e.preventDefault();
        e.stopPropagation();
      },
      onMouseUp: function onMouseUp(e) {
        e.preventDefault();
        e.stopPropagation();
        triggerChange(null);
        triggerOpen(false);
      },
      className: "".concat(prefixCls, "-clear"),
      role: "button"
    }, clearIcon || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement("span", {
      className: "".concat(prefixCls, "-clear-btn")
    }));
  } // ============================ Warning ============================


  if (true) {
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_10__.default)(!defaultOpenValue, '`defaultOpenValue` may confuse user for the current value status. Please use `defaultValue` instead.');
  } // ============================ Return =============================


  var onContextSelect = function onContextSelect(date, type) {
    if (type === 'submit' || type !== 'key' && !needConfirmButton) {
      // triggerChange will also update selected values
      triggerChange(date);
      triggerOpen(false);
    }
  };

  var popupPlacement = direction === 'rtl' ? 'bottomRight' : 'bottomLeft';
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement(_PanelContext__WEBPACK_IMPORTED_MODULE_16__.default.Provider, {
    value: {
      operationRef: operationRef,
      hideHeader: picker === 'time',
      panelRef: panelDivRef,
      onSelect: onContextSelect,
      open: mergedOpen,
      defaultOpenValue: defaultOpenValue,
      onDateMouseEnter: onEnter,
      onDateMouseLeave: onLeave
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement(_PickerTrigger__WEBPACK_IMPORTED_MODULE_13__.default, {
    visible: mergedOpen,
    popupElement: panel,
    popupStyle: popupStyle,
    prefixCls: prefixCls,
    dropdownClassName: dropdownClassName,
    dropdownAlign: dropdownAlign,
    getPopupContainer: getPopupContainer,
    transitionName: transitionName,
    popupPlacement: popupPlacement,
    direction: direction
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement("div", {
    ref: containerRef,
    className: classnames__WEBPACK_IMPORTED_MODULE_9___default()(prefixCls, className, (_classNames2 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_5__.default)(_classNames2, "".concat(prefixCls, "-disabled"), disabled), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_5__.default)(_classNames2, "".concat(prefixCls, "-focused"), focused), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_5__.default)(_classNames2, "".concat(prefixCls, "-rtl"), direction === 'rtl'), _classNames2)),
    style: style,
    onMouseDown: onMouseDown,
    onMouseUp: onInternalMouseUp,
    onMouseEnter: onMouseEnter,
    onMouseLeave: onMouseLeave,
    onContextMenu: onContextMenu,
    onClick: onClick
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_9___default()("".concat(prefixCls, "-input"), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_5__.default)({}, "".concat(prefixCls, "-input-placeholder"), !!hoverValue)),
    ref: inputDivRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement("input", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({
    id: id,
    tabIndex: tabIndex,
    disabled: disabled,
    readOnly: inputReadOnly || typeof formatList[0] === 'function' || !typing,
    value: hoverValue || text,
    onChange: function onChange(e) {
      triggerTextChange(e.target.value);
    },
    autoFocus: autoFocus,
    placeholder: placeholder,
    ref: inputRef,
    title: text
  }, inputProps, {
    size: (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_17__.getInputSize)(picker, formatList[0], generateConfig)
  }, (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_15__.default)(props), {
    autoComplete: autoComplete
  })), suffixNode, clearNode))));
} // Wrap with class component to enable pass generic with instance method


var Picker = /*#__PURE__*/function (_React$Component) {
  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__.default)(Picker, _React$Component);

  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__.default)(Picker);

  function Picker() {
    var _this;

    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__.default)(this, Picker);

    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }

    _this = _super.call.apply(_super, [this].concat(args));
    _this.pickerRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createRef();

    _this.focus = function () {
      if (_this.pickerRef.current) {
        _this.pickerRef.current.focus();
      }
    };

    _this.blur = function () {
      if (_this.pickerRef.current) {
        _this.pickerRef.current.blur();
      }
    };

    return _this;
  }

  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__.default)(Picker, [{
    key: "render",
    value: function render() {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_8__.createElement(InnerPicker, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({}, this.props, {
        pickerRef: this.pickerRef
      }));
    }
  }]);

  return Picker;
}(react__WEBPACK_IMPORTED_MODULE_8__.Component);

/* harmony default export */ __webpack_exports__["default"] = (Picker);

/***/ }),

/***/ "./node_modules/rc-picker/es/PickerPanel.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-picker/es/PickerPanel.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/KeyCode */ "./node_modules/rc-util/es/KeyCode.js");
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/rc-util/es/warning.js");
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ "./node_modules/rc-util/es/hooks/useMergedState.js");
/* harmony import */ var _panels_TimePanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./panels/TimePanel */ "./node_modules/rc-picker/es/panels/TimePanel/index.js");
/* harmony import */ var _panels_DatetimePanel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./panels/DatetimePanel */ "./node_modules/rc-picker/es/panels/DatetimePanel/index.js");
/* harmony import */ var _panels_DatePanel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./panels/DatePanel */ "./node_modules/rc-picker/es/panels/DatePanel/index.js");
/* harmony import */ var _panels_WeekPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./panels/WeekPanel */ "./node_modules/rc-picker/es/panels/WeekPanel/index.js");
/* harmony import */ var _panels_MonthPanel__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./panels/MonthPanel */ "./node_modules/rc-picker/es/panels/MonthPanel/index.js");
/* harmony import */ var _panels_QuarterPanel__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./panels/QuarterPanel */ "./node_modules/rc-picker/es/panels/QuarterPanel/index.js");
/* harmony import */ var _panels_YearPanel__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./panels/YearPanel */ "./node_modules/rc-picker/es/panels/YearPanel/index.js");
/* harmony import */ var _panels_DecadePanel__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./panels/DecadePanel */ "./node_modules/rc-picker/es/panels/DecadePanel/index.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");
/* harmony import */ var _RangeContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./RangeContext */ "./node_modules/rc-picker/es/RangeContext.js");
/* harmony import */ var _utils_getExtraFooter__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./utils/getExtraFooter */ "./node_modules/rc-picker/es/utils/getExtraFooter.js");
/* harmony import */ var _utils_getRanges__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./utils/getRanges */ "./node_modules/rc-picker/es/utils/getRanges.js");
/* harmony import */ var _utils_timeUtil__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./utils/timeUtil */ "./node_modules/rc-picker/es/utils/timeUtil.js");






/**
 * Logic:
 *  When `mode` === `picker`,
 *  click will trigger `onSelect` (if value changed trigger `onChange` also).
 *  Panel change will not trigger `onSelect` but trigger `onPanelChange`
 */





















function PickerPanel(props) {
  var _classNames;

  var _props$prefixCls = props.prefixCls,
      prefixCls = _props$prefixCls === void 0 ? 'rc-picker' : _props$prefixCls,
      className = props.className,
      style = props.style,
      locale = props.locale,
      generateConfig = props.generateConfig,
      value = props.value,
      defaultValue = props.defaultValue,
      pickerValue = props.pickerValue,
      defaultPickerValue = props.defaultPickerValue,
      disabledDate = props.disabledDate,
      mode = props.mode,
      _props$picker = props.picker,
      picker = _props$picker === void 0 ? 'date' : _props$picker,
      _props$tabIndex = props.tabIndex,
      tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,
      showNow = props.showNow,
      showTime = props.showTime,
      showToday = props.showToday,
      renderExtraFooter = props.renderExtraFooter,
      hideHeader = props.hideHeader,
      onSelect = props.onSelect,
      onChange = props.onChange,
      onPanelChange = props.onPanelChange,
      onMouseDown = props.onMouseDown,
      onPickerValueChange = props.onPickerValueChange,
      _onOk = props.onOk,
      components = props.components,
      direction = props.direction,
      _props$hourStep = props.hourStep,
      hourStep = _props$hourStep === void 0 ? 1 : _props$hourStep,
      _props$minuteStep = props.minuteStep,
      minuteStep = _props$minuteStep === void 0 ? 1 : _props$minuteStep,
      _props$secondStep = props.secondStep,
      secondStep = _props$secondStep === void 0 ? 1 : _props$secondStep;
  var needConfirmButton = picker === 'date' && !!showTime || picker === 'time';
  var isHourStepValid = 24 % hourStep === 0;
  var isMinuteStepValid = 60 % minuteStep === 0;
  var isSecondStepValid = 60 % secondStep === 0;

  if (true) {
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__.default)(!value || generateConfig.isValidate(value), 'Invalidate date pass to `value`.');
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__.default)(!value || generateConfig.isValidate(value), 'Invalidate date pass to `defaultValue`.');
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__.default)(isHourStepValid, "`hourStep` ".concat(hourStep, " is invalid. It should be a factor of 24."));
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__.default)(isMinuteStepValid, "`minuteStep` ".concat(minuteStep, " is invalid. It should be a factor of 60."));
    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__.default)(isSecondStepValid, "`secondStep` ".concat(secondStep, " is invalid. It should be a factor of 60."));
  } // ============================ State =============================


  var panelContext = react__WEBPACK_IMPORTED_MODULE_5__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_19__.default);
  var operationRef = panelContext.operationRef,
      panelDivRef = panelContext.panelRef,
      onContextSelect = panelContext.onSelect,
      hideRanges = panelContext.hideRanges,
      defaultOpenValue = panelContext.defaultOpenValue;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_5__.useContext(_RangeContext__WEBPACK_IMPORTED_MODULE_21__.default),
      inRange = _React$useContext.inRange,
      panelPosition = _React$useContext.panelPosition,
      rangedValue = _React$useContext.rangedValue,
      hoverRangedValue = _React$useContext.hoverRangedValue;

  var panelRef = react__WEBPACK_IMPORTED_MODULE_5__.useRef({}); // Handle init logic

  var initRef = react__WEBPACK_IMPORTED_MODULE_5__.useRef(true); // Value

  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__.default)(null, {
    value: value,
    defaultValue: defaultValue,
    postState: function postState(val) {
      if (!val && defaultOpenValue && picker === 'time') {
        return defaultOpenValue;
      }

      return val;
    }
  }),
      _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__.default)(_useMergedState, 2),
      mergedValue = _useMergedState2[0],
      setInnerValue = _useMergedState2[1]; // View date control


  var _useMergedState3 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__.default)(null, {
    value: pickerValue,
    defaultValue: defaultPickerValue || mergedValue,
    postState: function postState(date) {
      var now = generateConfig.getNow();
      if (!date) return now; // When value is null and set showTime

      // When value is null and set showTime
      if (!mergedValue && showTime) {
        if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__.default)(showTime) === 'object') {
          return (0,_utils_timeUtil__WEBPACK_IMPORTED_MODULE_24__.setDateTime)(generateConfig, Array.isArray(date) ? date[0] : date, showTime.defaultValue || now);
        }

        if (defaultValue) {
          return (0,_utils_timeUtil__WEBPACK_IMPORTED_MODULE_24__.setDateTime)(generateConfig, Array.isArray(date) ? date[0] : date, defaultValue);
        }

        return (0,_utils_timeUtil__WEBPACK_IMPORTED_MODULE_24__.setDateTime)(generateConfig, Array.isArray(date) ? date[0] : date, now);
      }

      return date;
    }
  }),
      _useMergedState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__.default)(_useMergedState3, 2),
      viewDate = _useMergedState4[0],
      setInnerViewDate = _useMergedState4[1];

  var setViewDate = function setViewDate(date) {
    setInnerViewDate(date);

    if (onPickerValueChange) {
      onPickerValueChange(date);
    }
  }; // Panel control


  var getInternalNextMode = function getInternalNextMode(nextMode) {
    var getNextMode = _utils_uiUtil__WEBPACK_IMPORTED_MODULE_20__.PickerModeMap[picker];

    if (getNextMode) {
      return getNextMode(nextMode);
    }

    return nextMode;
  }; // Save panel is changed from which panel


  var _useMergedState5 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__.default)(function () {
    if (picker === 'time') {
      return 'time';
    }

    return getInternalNextMode('date');
  }, {
    value: mode
  }),
      _useMergedState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__.default)(_useMergedState5, 2),
      mergedMode = _useMergedState6[0],
      setInnerMode = _useMergedState6[1];

  react__WEBPACK_IMPORTED_MODULE_5__.useEffect(function () {
    setInnerMode(picker);
  }, [picker]);

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_5__.useState(function () {
    return mergedMode;
  }),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__.default)(_React$useState, 2),
      sourceMode = _React$useState2[0],
      setSourceMode = _React$useState2[1];

  var onInternalPanelChange = function onInternalPanelChange(newMode, viewValue) {
    var nextMode = getInternalNextMode(newMode || mergedMode);
    setSourceMode(mergedMode);
    setInnerMode(nextMode);

    if (onPanelChange && (mergedMode !== nextMode || (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_18__.isEqual)(generateConfig, viewDate, viewDate))) {
      onPanelChange(viewValue, nextMode);
    }
  };

  var triggerSelect = function triggerSelect(date, type) {
    var forceTriggerSelect = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;

    if (mergedMode === picker || forceTriggerSelect) {
      setInnerValue(date);

      if (onSelect) {
        onSelect(date);
      }

      if (onContextSelect) {
        onContextSelect(date, type);
      }

      if (onChange && !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_18__.isEqual)(generateConfig, date, mergedValue) && !(disabledDate === null || disabledDate === void 0 ? void 0 : disabledDate(date))) {
        onChange(date);
      }
    }
  }; // ========================= Interactive ==========================


  var onInternalKeyDown = function onInternalKeyDown(e) {
    if (panelRef.current && panelRef.current.onKeyDown) {
      if ([rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.LEFT, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.RIGHT, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.UP, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.DOWN, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.PAGE_UP, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.PAGE_DOWN, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.ENTER].includes(e.which)) {
        e.preventDefault();
      }

      return panelRef.current.onKeyDown(e);
    }
    /* istanbul ignore next */

    /* eslint-disable no-lone-blocks */


    {
      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_8__.default)(false, 'Panel not correct handle keyDown event. Please help to fire issue about this.');
      return false;
    }
    /* eslint-enable no-lone-blocks */
  };

  var onInternalBlur = function onInternalBlur(e) {
    if (panelRef.current && panelRef.current.onBlur) {
      panelRef.current.onBlur(e);
    }
  };

  if (operationRef && panelPosition !== 'right') {
    operationRef.current = {
      onKeyDown: onInternalKeyDown,
      onClose: function onClose() {
        if (panelRef.current && panelRef.current.onClose) {
          panelRef.current.onClose();
        }
      }
    };
  } // ============================ Effect ============================


  react__WEBPACK_IMPORTED_MODULE_5__.useEffect(function () {
    if (value && !initRef.current) {
      setInnerViewDate(value);
    }
  }, [value]);
  react__WEBPACK_IMPORTED_MODULE_5__.useEffect(function () {
    initRef.current = false;
  }, []); // ============================ Panels ============================

  var panelNode;

  var pickerProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__.default)({}, props), {}, {
    operationRef: panelRef,
    prefixCls: prefixCls,
    viewDate: viewDate,
    value: mergedValue,
    onViewDateChange: setViewDate,
    sourceMode: sourceMode,
    onPanelChange: onInternalPanelChange,
    disabledDate: disabledDate
  });

  delete pickerProps.onChange;
  delete pickerProps.onSelect;

  switch (mergedMode) {
    case 'decade':
      panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_panels_DecadePanel__WEBPACK_IMPORTED_MODULE_17__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, pickerProps, {
        onSelect: function onSelect(date, type) {
          setViewDate(date);
          triggerSelect(date, type);
        }
      }));
      break;

    case 'year':
      panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_panels_YearPanel__WEBPACK_IMPORTED_MODULE_16__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, pickerProps, {
        onSelect: function onSelect(date, type) {
          setViewDate(date);
          triggerSelect(date, type);
        }
      }));
      break;

    case 'month':
      panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_panels_MonthPanel__WEBPACK_IMPORTED_MODULE_14__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, pickerProps, {
        onSelect: function onSelect(date, type) {
          setViewDate(date);
          triggerSelect(date, type);
        }
      }));
      break;

    case 'quarter':
      panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_panels_QuarterPanel__WEBPACK_IMPORTED_MODULE_15__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, pickerProps, {
        onSelect: function onSelect(date, type) {
          setViewDate(date);
          triggerSelect(date, type);
        }
      }));
      break;

    case 'week':
      panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_panels_WeekPanel__WEBPACK_IMPORTED_MODULE_13__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, pickerProps, {
        onSelect: function onSelect(date, type) {
          setViewDate(date);
          triggerSelect(date, type);
        }
      }));
      break;

    case 'time':
      delete pickerProps.showTime;
      panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_panels_TimePanel__WEBPACK_IMPORTED_MODULE_10__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, pickerProps, (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__.default)(showTime) === 'object' ? showTime : null, {
        onSelect: function onSelect(date, type) {
          setViewDate(date);
          triggerSelect(date, type);
        }
      }));
      break;

    default:
      if (showTime) {
        panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_panels_DatetimePanel__WEBPACK_IMPORTED_MODULE_11__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, pickerProps, {
          onSelect: function onSelect(date, type) {
            setViewDate(date);
            triggerSelect(date, type);
          }
        }));
      } else {
        panelNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_panels_DatePanel__WEBPACK_IMPORTED_MODULE_12__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__.default)({}, pickerProps, {
          onSelect: function onSelect(date, type) {
            setViewDate(date);
            triggerSelect(date, type);
          }
        }));
      }

  } // ============================ Footer ============================


  var extraFooter;
  var rangesNode;

  var onNow = function onNow() {
    var now = generateConfig.getNow();
    var lowerBoundTime = (0,_utils_timeUtil__WEBPACK_IMPORTED_MODULE_24__.getLowerBoundTime)(generateConfig.getHour(now), generateConfig.getMinute(now), generateConfig.getSecond(now), isHourStepValid ? hourStep : 1, isMinuteStepValid ? minuteStep : 1, isSecondStepValid ? secondStep : 1);
    var adjustedNow = (0,_utils_timeUtil__WEBPACK_IMPORTED_MODULE_24__.setTime)(generateConfig, now, lowerBoundTime[0], // hour
    lowerBoundTime[1], // minute
    lowerBoundTime[2]);
    triggerSelect(adjustedNow, 'submit');
  };

  if (!hideRanges) {
    extraFooter = (0,_utils_getExtraFooter__WEBPACK_IMPORTED_MODULE_22__.default)(prefixCls, mergedMode, renderExtraFooter);
    rangesNode = (0,_utils_getRanges__WEBPACK_IMPORTED_MODULE_23__.default)({
      prefixCls: prefixCls,
      components: components,
      needConfirmButton: needConfirmButton,
      okDisabled: !mergedValue || disabledDate && disabledDate(mergedValue),
      locale: locale,
      showNow: showNow,
      onNow: needConfirmButton && onNow,
      onOk: function onOk() {
        if (mergedValue) {
          triggerSelect(mergedValue, 'submit', true);

          if (_onOk) {
            _onOk(mergedValue);
          }
        }
      }
    });
  }

  var todayNode;

  if (showToday && mergedMode === 'date' && picker === 'date' && !showTime) {
    var now = generateConfig.getNow();
    var todayCls = "".concat(prefixCls, "-today-btn");
    var disabled = disabledDate && disabledDate(now);
    todayNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("a", {
      className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(todayCls, disabled && "".concat(todayCls, "-disabled")),
      "aria-disabled": disabled,
      onClick: function onClick() {
        if (!disabled) {
          triggerSelect(now, 'mouse', true);
        }
      }
    }, locale.today);
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_PanelContext__WEBPACK_IMPORTED_MODULE_19__.default.Provider, {
    value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__.default)({}, panelContext), {}, {
      mode: mergedMode,
      hideHeader: 'hideHeader' in props ? hideHeader : panelContext.hideHeader,
      hidePrevBtn: inRange && panelPosition === 'right',
      hideNextBtn: inRange && panelPosition === 'left'
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    tabIndex: tabIndex,
    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()("".concat(prefixCls, "-panel"), className, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-panel-has-range"), rangedValue && rangedValue[0] && rangedValue[1]), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-panel-has-range-hover"), hoverRangedValue && hoverRangedValue[0] && hoverRangedValue[1]), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(prefixCls, "-panel-rtl"), direction === 'rtl'), _classNames)),
    style: style,
    onKeyDown: onInternalKeyDown,
    onBlur: onInternalBlur,
    onMouseDown: onMouseDown,
    ref: panelDivRef
  }, panelNode, extraFooter || rangesNode || todayNode ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "".concat(prefixCls, "-footer")
  }, extraFooter, rangesNode, todayNode) : null));
}

/* harmony default export */ __webpack_exports__["default"] = (PickerPanel);
/* eslint-enable */

/***/ }),

/***/ "./node_modules/rc-picker/es/PickerTrigger.js":
/*!****************************************************!*\
  !*** ./node_modules/rc-picker/es/PickerTrigger.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var rc_trigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-trigger */ "./node_modules/rc-trigger/es/index.js");




var BUILT_IN_PLACEMENTS = {
  bottomLeft: {
    points: ['tl', 'bl'],
    offset: [0, 4],
    overflow: {
      adjustX: 1,
      adjustY: 1
    }
  },
  bottomRight: {
    points: ['tr', 'br'],
    offset: [0, 4],
    overflow: {
      adjustX: 1,
      adjustY: 1
    }
  },
  topLeft: {
    points: ['bl', 'tl'],
    offset: [0, -4],
    overflow: {
      adjustX: 0,
      adjustY: 1
    }
  },
  topRight: {
    points: ['br', 'tr'],
    offset: [0, -4],
    overflow: {
      adjustX: 0,
      adjustY: 1
    }
  }
};

function PickerTrigger(_ref) {
  var _classNames;

  var prefixCls = _ref.prefixCls,
      popupElement = _ref.popupElement,
      popupStyle = _ref.popupStyle,
      visible = _ref.visible,
      dropdownClassName = _ref.dropdownClassName,
      dropdownAlign = _ref.dropdownAlign,
      transitionName = _ref.transitionName,
      getPopupContainer = _ref.getPopupContainer,
      children = _ref.children,
      range = _ref.range,
      popupPlacement = _ref.popupPlacement,
      direction = _ref.direction;
  var dropdownPrefixCls = "".concat(prefixCls, "-dropdown");

  var getPopupPlacement = function getPopupPlacement() {
    if (popupPlacement !== undefined) {
      return popupPlacement;
    }

    return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(rc_trigger__WEBPACK_IMPORTED_MODULE_3__.default, {
    showAction: [],
    hideAction: [],
    popupPlacement: getPopupPlacement(),
    builtinPlacements: BUILT_IN_PLACEMENTS,
    prefixCls: dropdownPrefixCls,
    popupTransitionName: transitionName,
    popup: popupElement,
    popupAlign: dropdownAlign,
    popupVisible: visible,
    popupClassName: classnames__WEBPACK_IMPORTED_MODULE_2___default()(dropdownClassName, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(dropdownPrefixCls, "-range"), range), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames, "".concat(dropdownPrefixCls, "-rtl"), direction === 'rtl'), _classNames)),
    popupStyle: popupStyle,
    getPopupContainer: getPopupContainer
  }, children);
}

/* harmony default export */ __webpack_exports__["default"] = (PickerTrigger);

/***/ }),

/***/ "./node_modules/rc-picker/es/RangeContext.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-picker/es/RangeContext.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

var RangeContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext({});
/* harmony default export */ __webpack_exports__["default"] = (RangeContext);

/***/ }),

/***/ "./node_modules/rc-picker/es/RangePicker.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-picker/es/RangePicker.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ "./node_modules/@babel/runtime/helpers/esm/classCallCheck.js");
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ "./node_modules/@babel/runtime/helpers/esm/createClass.js");
/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ "./node_modules/@babel/runtime/helpers/esm/inherits.js");
/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ "./node_modules/@babel/runtime/helpers/esm/createSuper.js");
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/rc-util/es/warning.js");
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ "./node_modules/rc-util/es/hooks/useMergedState.js");
/* harmony import */ var _PickerTrigger__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PickerTrigger */ "./node_modules/rc-picker/es/PickerTrigger.js");
/* harmony import */ var _PickerPanel__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./PickerPanel */ "./node_modules/rc-picker/es/PickerPanel.js");
/* harmony import */ var _hooks_usePickerInput__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./hooks/usePickerInput */ "./node_modules/rc-picker/es/hooks/usePickerInput.js");
/* harmony import */ var _utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./utils/miscUtil */ "./node_modules/rc-picker/es/utils/miscUtil.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _hooks_useValueTexts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./hooks/useValueTexts */ "./node_modules/rc-picker/es/hooks/useValueTexts.js");
/* harmony import */ var _hooks_useTextValueMapping__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./hooks/useTextValueMapping */ "./node_modules/rc-picker/es/hooks/useTextValueMapping.js");
/* harmony import */ var _RangeContext__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./RangeContext */ "./node_modules/rc-picker/es/RangeContext.js");
/* harmony import */ var _hooks_useRangeDisabled__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./hooks/useRangeDisabled */ "./node_modules/rc-picker/es/hooks/useRangeDisabled.js");
/* harmony import */ var _utils_getExtraFooter__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./utils/getExtraFooter */ "./node_modules/rc-picker/es/utils/getExtraFooter.js");
/* harmony import */ var _utils_getRanges__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./utils/getRanges */ "./node_modules/rc-picker/es/utils/getRanges.js");
/* harmony import */ var _hooks_useRangeViewDates__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./hooks/useRangeViewDates */ "./node_modules/rc-picker/es/hooks/useRangeViewDates.js");
/* harmony import */ var _hooks_useHoverValue__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./hooks/useHoverValue */ "./node_modules/rc-picker/es/hooks/useHoverValue.js");






























function reorderValues(values, generateConfig) {
  if (values && values[0] && values[1] && generateConfig.isAfter(values[0], values[1])) {
    return [values[1], values[0]];
  }

  return values;
}

function canValueTrigger(value, index, disabled, allowEmpty) {
  if (value) {
    return true;
  }

  if (allowEmpty && allowEmpty[index]) {
    return true;
  }

  if (disabled[(index + 1) % 2]) {
    return true;
  }

  return false;
}

function InnerRangePicker(props) {
  var _classNames2, _classNames3, _classNames4;

  var _props$prefixCls = props.prefixCls,
      prefixCls = _props$prefixCls === void 0 ? 'rc-picker' : _props$prefixCls,
      id = props.id,
      style = props.style,
      className = props.className,
      popupStyle = props.popupStyle,
      dropdownClassName = props.dropdownClassName,
      transitionName = props.transitionName,
      dropdownAlign = props.dropdownAlign,
      getPopupContainer = props.getPopupContainer,
      generateConfig = props.generateConfig,
      locale = props.locale,
      placeholder = props.placeholder,
      autoFocus = props.autoFocus,
      disabled = props.disabled,
      format = props.format,
      _props$picker = props.picker,
      picker = _props$picker === void 0 ? 'date' : _props$picker,
      showTime = props.showTime,
      use12Hours = props.use12Hours,
      _props$separator = props.separator,
      separator = _props$separator === void 0 ? '~' : _props$separator,
      value = props.value,
      defaultValue = props.defaultValue,
      defaultPickerValue = props.defaultPickerValue,
      open = props.open,
      defaultOpen = props.defaultOpen,
      disabledDate = props.disabledDate,
      _disabledTime = props.disabledTime,
      dateRender = props.dateRender,
      panelRender = props.panelRender,
      ranges = props.ranges,
      allowEmpty = props.allowEmpty,
      allowClear = props.allowClear,
      suffixIcon = props.suffixIcon,
      clearIcon = props.clearIcon,
      pickerRef = props.pickerRef,
      inputReadOnly = props.inputReadOnly,
      mode = props.mode,
      renderExtraFooter = props.renderExtraFooter,
      onChange = props.onChange,
      onOpenChange = props.onOpenChange,
      onPanelChange = props.onPanelChange,
      onCalendarChange = props.onCalendarChange,
      _onFocus = props.onFocus,
      onBlur = props.onBlur,
      onMouseEnter = props.onMouseEnter,
      onMouseLeave = props.onMouseLeave,
      _onOk = props.onOk,
      _onKeyDown = props.onKeyDown,
      components = props.components,
      order = props.order,
      direction = props.direction,
      activePickerIndex = props.activePickerIndex,
      _props$autoComplete = props.autoComplete,
      autoComplete = _props$autoComplete === void 0 ? 'off' : _props$autoComplete;
  var needConfirmButton = picker === 'date' && !!showTime || picker === 'time'; // We record opened status here in case repeat open with picker

  var openRecordsRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)({});
  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);
  var panelDivRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);
  var startInputDivRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);
  var endInputDivRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);
  var separatorRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);
  var startInputRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);
  var endInputRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null); // ============================= Misc ==============================

  var formatList = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.toArray)((0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_17__.getDefaultFormat)(format, picker, showTime, use12Hours)); // Active picker

  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_12__.default)(0, {
    value: activePickerIndex
  }),
      _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useMergedState, 2),
      mergedActivePickerIndex = _useMergedState2[0],
      setMergedActivePickerIndex = _useMergedState2[1]; // Operation ref


  var operationRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);
  var mergedDisabled = react__WEBPACK_IMPORTED_MODULE_9__.useMemo(function () {
    if (Array.isArray(disabled)) {
      return disabled;
    }

    return [disabled || false, disabled || false];
  }, [disabled]); // ============================= Value =============================

  var _useMergedState3 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_12__.default)(null, {
    value: value,
    defaultValue: defaultValue,
    postState: function postState(values) {
      return picker === 'time' && !order ? values : reorderValues(values, generateConfig);
    }
  }),
      _useMergedState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useMergedState3, 2),
      mergedValue = _useMergedState4[0],
      setInnerValue = _useMergedState4[1]; // =========================== View Date ===========================
  // Config view panel


  var _useRangeViewDates = (0,_hooks_useRangeViewDates__WEBPACK_IMPORTED_MODULE_26__.default)({
    values: mergedValue,
    picker: picker,
    defaultDates: defaultPickerValue,
    generateConfig: generateConfig
  }),
      _useRangeViewDates2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useRangeViewDates, 2),
      getViewDate = _useRangeViewDates2[0],
      setViewDate = _useRangeViewDates2[1]; // ========================= Select Values =========================


  var _useMergedState5 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_12__.default)(mergedValue, {
    postState: function postState(values) {
      var postValues = values;

      if (mergedDisabled[0] && mergedDisabled[1]) {
        return postValues;
      } // Fill disabled unit


      // Fill disabled unit
      for (var i = 0; i < 2; i += 1) {
        if (mergedDisabled[i] && !(0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(postValues, i) && !(0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(allowEmpty, i)) {
          postValues = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(postValues, generateConfig.getNow(), i);
        }
      }

      return postValues;
    }
  }),
      _useMergedState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useMergedState5, 2),
      selectedValue = _useMergedState6[0],
      setSelectedValue = _useMergedState6[1]; // ============================= Modes =============================


  var _useMergedState7 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_12__.default)([picker, picker], {
    value: mode
  }),
      _useMergedState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useMergedState7, 2),
      mergedModes = _useMergedState8[0],
      setInnerModes = _useMergedState8[1];

  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    setInnerModes([picker, picker]);
  }, [picker]);

  var triggerModesChange = function triggerModesChange(modes, values) {
    setInnerModes(modes);

    if (onPanelChange) {
      onPanelChange(values, modes);
    }
  }; // ========================= Disable Date ==========================


  var _useRangeDisabled = (0,_hooks_useRangeDisabled__WEBPACK_IMPORTED_MODULE_23__.default)({
    picker: picker,
    selectedValue: selectedValue,
    locale: locale,
    disabled: mergedDisabled,
    disabledDate: disabledDate,
    generateConfig: generateConfig
  }, openRecordsRef.current[1], openRecordsRef.current[0]),
      _useRangeDisabled2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useRangeDisabled, 2),
      disabledStartDate = _useRangeDisabled2[0],
      disabledEndDate = _useRangeDisabled2[1]; // ============================= Open ==============================


  var _useMergedState9 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_12__.default)(false, {
    value: open,
    defaultValue: defaultOpen,
    postState: function postState(postOpen) {
      return mergedDisabled[mergedActivePickerIndex] ? false : postOpen;
    },
    onChange: function onChange(newOpen) {
      if (onOpenChange) {
        onOpenChange(newOpen);
      }

      if (!newOpen && operationRef.current && operationRef.current.onClose) {
        operationRef.current.onClose();
      }
    }
  }),
      _useMergedState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useMergedState9, 2),
      mergedOpen = _useMergedState10[0],
      triggerInnerOpen = _useMergedState10[1];

  var startOpen = mergedOpen && mergedActivePickerIndex === 0;
  var endOpen = mergedOpen && mergedActivePickerIndex === 1; // ============================= Popup =============================
  // Popup min width

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0),
      _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useState, 2),
      popupMinWidth = _useState2[0],
      setPopupMinWidth = _useState2[1];

  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    if (!mergedOpen && containerRef.current) {
      setPopupMinWidth(containerRef.current.offsetWidth);
    }
  }, [mergedOpen]); // ============================ Trigger ============================

  var triggerRef = react__WEBPACK_IMPORTED_MODULE_9__.useRef();

  function _triggerOpen(newOpen, index) {
    if (newOpen) {
      clearTimeout(triggerRef.current);
      openRecordsRef.current[index] = true;
      setMergedActivePickerIndex(index);
      triggerInnerOpen(newOpen); // Open to reset view date

      if (!mergedOpen) {
        setViewDate(null, index);
      }
    } else if (mergedActivePickerIndex === index) {
      triggerInnerOpen(newOpen); // Clean up async
      // This makes ref not quick refresh in case user open another input with blur trigger

      var openRecords = openRecordsRef.current;
      triggerRef.current = setTimeout(function () {
        if (openRecords === openRecordsRef.current) {
          openRecordsRef.current = {};
        }
      });
    }
  }

  function triggerOpenAndFocus(index) {
    _triggerOpen(true, index); // Use setTimeout to make sure panel DOM exists


    setTimeout(function () {
      var inputRef = [startInputRef, endInputRef][index];

      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 0);
  }

  function triggerChange(newValue, sourceIndex) {
    var values = newValue;
    var startValue = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(values, 0);
    var endValue = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(values, 1); // >>>>> Format start & end values

    if (startValue && endValue && generateConfig.isAfter(startValue, endValue)) {
      if ( // WeekPicker only compare week
      picker === 'week' && !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.isSameWeek)(generateConfig, locale.locale, startValue, endValue) || // QuotaPicker only compare week
      picker === 'quarter' && !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.isSameQuarter)(generateConfig, startValue, endValue) || // Other non-TimePicker compare date
      picker !== 'week' && picker !== 'quarter' && picker !== 'time' && !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.isSameDate)(generateConfig, startValue, endValue)) {
        // Clean up end date when start date is after end date
        if (sourceIndex === 0) {
          values = [startValue, null];
          endValue = null;
        } else {
          startValue = null;
          values = [null, endValue];
        } // Clean up cache since invalidate


        openRecordsRef.current = (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)({}, sourceIndex, true);
      } else if (picker !== 'time' || order !== false) {
        // Reorder when in same date
        values = reorderValues(values, generateConfig);
      }
    }

    setSelectedValue(values);
    var startStr = values && values[0] ? (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.formatValue)(values[0], {
      generateConfig: generateConfig,
      locale: locale,
      format: formatList[0]
    }) : '';
    var endStr = values && values[1] ? (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.formatValue)(values[1], {
      generateConfig: generateConfig,
      locale: locale,
      format: formatList[0]
    }) : '';

    if (onCalendarChange) {
      var info = {
        range: sourceIndex === 0 ? 'start' : 'end'
      };
      onCalendarChange(values, [startStr, endStr], info);
    } // >>>>> Trigger `onChange` event


    var canStartValueTrigger = canValueTrigger(startValue, 0, mergedDisabled, allowEmpty);
    var canEndValueTrigger = canValueTrigger(endValue, 1, mergedDisabled, allowEmpty);
    var canTrigger = values === null || canStartValueTrigger && canEndValueTrigger;

    if (canTrigger) {
      // Trigger onChange only when value is validate
      setInnerValue(values);

      if (onChange && (!(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.isEqual)(generateConfig, (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(mergedValue, 0), startValue) || !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.isEqual)(generateConfig, (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(mergedValue, 1), endValue))) {
        onChange(values, [startStr, endStr]);
      }
    } // >>>>> Open picker when
    // Always open another picker if possible


    var nextOpenIndex = null;

    if (sourceIndex === 0 && !mergedDisabled[1]) {
      nextOpenIndex = 1;
    } else if (sourceIndex === 1 && !mergedDisabled[0]) {
      nextOpenIndex = 0;
    }

    if (nextOpenIndex !== null && nextOpenIndex !== mergedActivePickerIndex && (!openRecordsRef.current[nextOpenIndex] || !(0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(values, nextOpenIndex)) && (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(values, sourceIndex)) {
      // Delay to focus to avoid input blur trigger expired selectedValues
      triggerOpenAndFocus(nextOpenIndex);
    } else {
      _triggerOpen(false, sourceIndex);
    }
  }

  var forwardKeyDown = function forwardKeyDown(e) {
    if (mergedOpen && operationRef.current && operationRef.current.onKeyDown) {
      // Let popup panel handle keyboard
      return operationRef.current.onKeyDown(e);
    }
    /* istanbul ignore next */

    /* eslint-disable no-lone-blocks */


    {
      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_11__.default)(false, 'Picker not correct forward KeyDown operation. Please help to fire issue about this.');
      return false;
    }
  }; // ============================= Text ==============================


  var sharedTextHooksProps = {
    formatList: formatList,
    generateConfig: generateConfig,
    locale: locale
  };

  var _useValueTexts = (0,_hooks_useValueTexts__WEBPACK_IMPORTED_MODULE_20__.default)((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(selectedValue, 0), sharedTextHooksProps),
      _useValueTexts2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useValueTexts, 2),
      startValueTexts = _useValueTexts2[0],
      firstStartValueText = _useValueTexts2[1];

  var _useValueTexts3 = (0,_hooks_useValueTexts__WEBPACK_IMPORTED_MODULE_20__.default)((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(selectedValue, 1), sharedTextHooksProps),
      _useValueTexts4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useValueTexts3, 2),
      endValueTexts = _useValueTexts4[0],
      firstEndValueText = _useValueTexts4[1];

  var _onTextChange = function onTextChange(newText, index) {
    var inputDate = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.parseValue)(newText, {
      locale: locale,
      formatList: formatList,
      generateConfig: generateConfig
    });
    var disabledFunc = index === 0 ? disabledStartDate : disabledEndDate;

    if (inputDate && !disabledFunc(inputDate)) {
      setSelectedValue((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(selectedValue, inputDate, index));
      setViewDate(inputDate, index);
    }
  };

  var _useTextValueMapping = (0,_hooks_useTextValueMapping__WEBPACK_IMPORTED_MODULE_21__.default)({
    valueTexts: startValueTexts,
    onTextChange: function onTextChange(newText) {
      return _onTextChange(newText, 0);
    }
  }),
      _useTextValueMapping2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useTextValueMapping, 3),
      startText = _useTextValueMapping2[0],
      triggerStartTextChange = _useTextValueMapping2[1],
      resetStartText = _useTextValueMapping2[2];

  var _useTextValueMapping3 = (0,_hooks_useTextValueMapping__WEBPACK_IMPORTED_MODULE_21__.default)({
    valueTexts: endValueTexts,
    onTextChange: function onTextChange(newText) {
      return _onTextChange(newText, 1);
    }
  }),
      _useTextValueMapping4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useTextValueMapping3, 3),
      endText = _useTextValueMapping4[0],
      triggerEndTextChange = _useTextValueMapping4[1],
      resetEndText = _useTextValueMapping4[2];

  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null),
      _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useState3, 2),
      rangeHoverValue = _useState4[0],
      setRangeHoverValue = _useState4[1]; // ========================== Hover Range ==========================


  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null),
      _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useState5, 2),
      hoverRangedValue = _useState6[0],
      setHoverRangedValue = _useState6[1];

  var _useHoverValue = (0,_hooks_useHoverValue__WEBPACK_IMPORTED_MODULE_27__.default)(startText, {
    formatList: formatList,
    generateConfig: generateConfig,
    locale: locale
  }),
      _useHoverValue2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useHoverValue, 3),
      startHoverValue = _useHoverValue2[0],
      onStartEnter = _useHoverValue2[1],
      onStartLeave = _useHoverValue2[2];

  var _useHoverValue3 = (0,_hooks_useHoverValue__WEBPACK_IMPORTED_MODULE_27__.default)(endText, {
    formatList: formatList,
    generateConfig: generateConfig,
    locale: locale
  }),
      _useHoverValue4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_useHoverValue3, 3),
      endHoverValue = _useHoverValue4[0],
      onEndEnter = _useHoverValue4[1],
      onEndLeave = _useHoverValue4[2];

  var onDateMouseEnter = function onDateMouseEnter(date) {
    setHoverRangedValue((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(selectedValue, date, mergedActivePickerIndex));

    if (mergedActivePickerIndex === 0) {
      onStartEnter(date);
    } else {
      onEndEnter(date);
    }
  };

  var onDateMouseLeave = function onDateMouseLeave() {
    setHoverRangedValue((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(selectedValue, null, mergedActivePickerIndex));

    if (mergedActivePickerIndex === 0) {
      onStartLeave();
    } else {
      onEndLeave();
    }
  }; // ============================= Input =============================


  var getSharedInputHookProps = function getSharedInputHookProps(index, resetText) {
    return {
      blurToCancel: needConfirmButton,
      forwardKeyDown: forwardKeyDown,
      onBlur: onBlur,
      isClickOutside: function isClickOutside(target) {
        return !(0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_17__.elementsContains)([panelDivRef.current, startInputDivRef.current, endInputDivRef.current, containerRef.current], target);
      },
      onFocus: function onFocus(e) {
        setMergedActivePickerIndex(index);

        if (_onFocus) {
          _onFocus(e);
        }
      },
      triggerOpen: function triggerOpen(newOpen) {
        _triggerOpen(newOpen, index);
      },
      onSubmit: function onSubmit() {
        triggerChange(selectedValue, index);
        resetText();
      },
      onCancel: function onCancel() {
        _triggerOpen(false, index);

        setSelectedValue(mergedValue);
        resetText();
      }
    };
  };

  var _usePickerInput = (0,_hooks_usePickerInput__WEBPACK_IMPORTED_MODULE_15__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)({}, getSharedInputHookProps(0, resetStartText)), {}, {
    open: startOpen,
    value: startText,
    onKeyDown: function onKeyDown(e, preventDefault) {
      _onKeyDown === null || _onKeyDown === void 0 ? void 0 : _onKeyDown(e, preventDefault);
    }
  })),
      _usePickerInput2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_usePickerInput, 2),
      startInputProps = _usePickerInput2[0],
      _usePickerInput2$ = _usePickerInput2[1],
      startFocused = _usePickerInput2$.focused,
      startTyping = _usePickerInput2$.typing;

  var _usePickerInput3 = (0,_hooks_usePickerInput__WEBPACK_IMPORTED_MODULE_15__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)({}, getSharedInputHookProps(1, resetEndText)), {}, {
    open: endOpen,
    value: endText,
    onKeyDown: function onKeyDown(e, preventDefault) {
      _onKeyDown === null || _onKeyDown === void 0 ? void 0 : _onKeyDown(e, preventDefault);
    }
  })),
      _usePickerInput4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_8__.default)(_usePickerInput3, 2),
      endInputProps = _usePickerInput4[0],
      _usePickerInput4$ = _usePickerInput4[1],
      endFocused = _usePickerInput4$.focused,
      endTyping = _usePickerInput4$.typing; // ========================== Click Picker ==========================


  var onPickerClick = function onPickerClick(e) {
    // When click inside the picker & outside the picker's input elements
    // the panel should still be opened
    if (!mergedOpen && !startInputRef.current.contains(e.target) && !endInputRef.current.contains(e.target)) {
      if (!mergedDisabled[0]) {
        triggerOpenAndFocus(0);
      } else if (!mergedDisabled[1]) {
        triggerOpenAndFocus(1);
      }
    }
  };

  var onPickerMouseDown = function onPickerMouseDown(e) {
    // shouldn't affect input elements if picker is active
    if (mergedOpen && (startFocused || endFocused) && !startInputRef.current.contains(e.target) && !endInputRef.current.contains(e.target)) {
      e.preventDefault();
    }
  }; // ============================= Sync ==============================
  // Close should sync back with text value


  var startStr = mergedValue && mergedValue[0] ? (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.formatValue)(mergedValue[0], {
    locale: locale,
    format: 'YYYYMMDDHHmmss',
    generateConfig: generateConfig
  }) : '';
  var endStr = mergedValue && mergedValue[1] ? (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.formatValue)(mergedValue[1], {
    locale: locale,
    format: 'YYYYMMDDHHmmss',
    generateConfig: generateConfig
  }) : '';
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    if (!mergedOpen) {
      setSelectedValue(mergedValue);

      if (!startValueTexts.length || startValueTexts[0] === '') {
        triggerStartTextChange('');
      } else if (firstStartValueText !== startText) {
        resetStartText();
      }

      if (!endValueTexts.length || endValueTexts[0] === '') {
        triggerEndTextChange('');
      } else if (firstEndValueText !== endText) {
        resetEndText();
      }
    }
  }, [mergedOpen, startValueTexts, endValueTexts]); // Sync innerValue with control mode

  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    setSelectedValue(mergedValue);
  }, [startStr, endStr]); // ============================ Warning ============================

  if (true) {
    if (value && Array.isArray(disabled) && ((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(disabled, 0) && !(0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(value, 0) || (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(disabled, 1) && !(0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(value, 1))) {
      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_11__.default)(false, '`disabled` should not set with empty `value`. You should set `allowEmpty` or `value` instead.');
    }
  } // ============================ Private ============================


  if (pickerRef) {
    pickerRef.current = {
      focus: function focus() {
        if (startInputRef.current) {
          startInputRef.current.focus();
        }
      },
      blur: function blur() {
        if (startInputRef.current) {
          startInputRef.current.blur();
        }

        if (endInputRef.current) {
          endInputRef.current.blur();
        }
      }
    };
  } // ============================ Ranges =============================


  var rangeLabels = Object.keys(ranges || {});
  var rangeList = rangeLabels.map(function (label) {
    var range = ranges[label];
    var newValues = typeof range === 'function' ? range() : range;
    return {
      label: label,
      onClick: function onClick() {
        triggerChange(newValues, null);

        _triggerOpen(false, mergedActivePickerIndex);
      },
      onMouseEnter: function onMouseEnter() {
        setRangeHoverValue(newValues);
      },
      onMouseLeave: function onMouseLeave() {
        setRangeHoverValue(null);
      }
    };
  }); // ============================= Panel =============================

  function renderPanel() {
    var panelPosition = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
    var panelProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var panelHoverRangedValue = null;

    if (mergedOpen && hoverRangedValue && hoverRangedValue[0] && hoverRangedValue[1] && generateConfig.isAfter(hoverRangedValue[1], hoverRangedValue[0])) {
      panelHoverRangedValue = hoverRangedValue;
    }

    var panelShowTime = showTime;

    if (showTime && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_5__.default)(showTime) === 'object' && showTime.defaultValue) {
      var timeDefaultValues = showTime.defaultValue;
      panelShowTime = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)({}, showTime), {}, {
        defaultValue: (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(timeDefaultValues, mergedActivePickerIndex) || undefined
      });
    }

    var panelDateRender = null;

    if (dateRender) {
      panelDateRender = function panelDateRender(date, today) {
        return dateRender(date, today, {
          range: mergedActivePickerIndex ? 'end' : 'start'
        });
      };
    }

    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_RangeContext__WEBPACK_IMPORTED_MODULE_22__.default.Provider, {
      value: {
        inRange: true,
        panelPosition: panelPosition,
        rangedValue: rangeHoverValue || selectedValue,
        hoverRangedValue: panelHoverRangedValue
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_PickerPanel__WEBPACK_IMPORTED_MODULE_14__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({}, props, panelProps, {
      dateRender: panelDateRender,
      showTime: panelShowTime,
      mode: mergedModes[mergedActivePickerIndex],
      generateConfig: generateConfig,
      style: undefined,
      direction: direction,
      disabledDate: mergedActivePickerIndex === 0 ? disabledStartDate : disabledEndDate,
      disabledTime: function disabledTime(date) {
        if (_disabledTime) {
          return _disabledTime(date, mergedActivePickerIndex === 0 ? 'start' : 'end');
        }

        return false;
      },
      className: classnames__WEBPACK_IMPORTED_MODULE_10___default()((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)({}, "".concat(prefixCls, "-panel-focused"), mergedActivePickerIndex === 0 ? !startTyping : !endTyping)),
      value: (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(selectedValue, mergedActivePickerIndex),
      locale: locale,
      tabIndex: -1,
      onPanelChange: function onPanelChange(date, newMode) {
        // clear hover value when panel change
        if (mergedActivePickerIndex === 0) {
          onStartLeave(true);
        }

        if (mergedActivePickerIndex === 1) {
          onEndLeave(true);
        }

        triggerModesChange((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(mergedModes, newMode, mergedActivePickerIndex), (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(selectedValue, date, mergedActivePickerIndex));
        var viewDate = date;

        if (panelPosition === 'right' && mergedModes[mergedActivePickerIndex] === newMode) {
          viewDate = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.getClosingViewDate)(viewDate, newMode, generateConfig, -1);
        }

        setViewDate(viewDate, mergedActivePickerIndex);
      },
      onOk: null,
      onSelect: undefined,
      onChange: undefined,
      defaultValue: mergedActivePickerIndex === 0 ? (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(selectedValue, 1) : (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(selectedValue, 0)
    })));
  }

  var arrowLeft = 0;
  var panelLeft = 0;

  if (mergedActivePickerIndex && startInputDivRef.current && separatorRef.current && panelDivRef.current) {
    // Arrow offset
    arrowLeft = startInputDivRef.current.offsetWidth + separatorRef.current.offsetWidth;

    if (panelDivRef.current.offsetWidth && arrowLeft > panelDivRef.current.offsetWidth) {
      panelLeft = arrowLeft;
    }
  }

  var arrowPositionStyle = direction === 'rtl' ? {
    right: arrowLeft
  } : {
    left: arrowLeft
  };

  function renderPanels() {
    var panels;
    var extraNode = (0,_utils_getExtraFooter__WEBPACK_IMPORTED_MODULE_24__.default)(prefixCls, mergedModes[mergedActivePickerIndex], renderExtraFooter);
    var rangesNode = (0,_utils_getRanges__WEBPACK_IMPORTED_MODULE_25__.default)({
      prefixCls: prefixCls,
      components: components,
      needConfirmButton: needConfirmButton,
      okDisabled: !(0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(selectedValue, mergedActivePickerIndex) || disabledDate && disabledDate(selectedValue[mergedActivePickerIndex]),
      locale: locale,
      rangeList: rangeList,
      onOk: function onOk() {
        if ((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(selectedValue, mergedActivePickerIndex)) {
          // triggerChangeOld(selectedValue);
          triggerChange(selectedValue, mergedActivePickerIndex);

          if (_onOk) {
            _onOk(selectedValue);
          }
        }
      }
    });

    if (picker !== 'time' && !showTime) {
      var viewDate = getViewDate(mergedActivePickerIndex);
      var nextViewDate = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.getClosingViewDate)(viewDate, picker, generateConfig);
      var currentMode = mergedModes[mergedActivePickerIndex];
      var showDoublePanel = currentMode === picker;
      var leftPanel = renderPanel(showDoublePanel ? 'left' : false, {
        pickerValue: viewDate,
        onPickerValueChange: function onPickerValueChange(newViewDate) {
          setViewDate(newViewDate, mergedActivePickerIndex);
        }
      });
      var rightPanel = renderPanel('right', {
        pickerValue: nextViewDate,
        onPickerValueChange: function onPickerValueChange(newViewDate) {
          setViewDate((0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_19__.getClosingViewDate)(newViewDate, picker, generateConfig, -1), mergedActivePickerIndex);
        }
      });

      if (direction === 'rtl') {
        panels = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(react__WEBPACK_IMPORTED_MODULE_9__.Fragment, null, rightPanel, showDoublePanel && leftPanel);
      } else {
        panels = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(react__WEBPACK_IMPORTED_MODULE_9__.Fragment, null, leftPanel, showDoublePanel && rightPanel);
      }
    } else {
      panels = renderPanel();
    }

    var mergedNodes = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(react__WEBPACK_IMPORTED_MODULE_9__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
      className: "".concat(prefixCls, "-panels")
    }, panels), (extraNode || rangesNode) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
      className: "".concat(prefixCls, "-footer")
    }, extraNode, rangesNode));

    if (panelRender) {
      mergedNodes = panelRender(mergedNodes);
    }

    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
      className: "".concat(prefixCls, "-panel-container"),
      style: {
        marginLeft: panelLeft
      },
      ref: panelDivRef,
      onMouseDown: function onMouseDown(e) {
        e.preventDefault();
      }
    }, mergedNodes);
  }

  var rangePanel = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_10___default()("".concat(prefixCls, "-range-wrapper"), "".concat(prefixCls, "-").concat(picker, "-range-wrapper")),
    style: {
      minWidth: popupMinWidth
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
    className: "".concat(prefixCls, "-range-arrow"),
    style: arrowPositionStyle
  }), renderPanels()); // ============================= Icons =============================

  var suffixNode;

  if (suffixIcon) {
    suffixNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("span", {
      className: "".concat(prefixCls, "-suffix")
    }, suffixIcon);
  }

  var clearNode;

  if (allowClear && ((0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(mergedValue, 0) && !mergedDisabled[0] || (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(mergedValue, 1) && !mergedDisabled[1])) {
    clearNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("span", {
      onMouseDown: function onMouseDown(e) {
        e.preventDefault();
        e.stopPropagation();
      },
      onMouseUp: function onMouseUp(e) {
        e.preventDefault();
        e.stopPropagation();
        var values = mergedValue;

        if (!mergedDisabled[0]) {
          values = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(values, null, 0);
        }

        if (!mergedDisabled[1]) {
          values = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(values, null, 1);
        }

        triggerChange(values, null);

        _triggerOpen(false, mergedActivePickerIndex);
      },
      className: "".concat(prefixCls, "-clear")
    }, clearIcon || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("span", {
      className: "".concat(prefixCls, "-clear-btn")
    }));
  }

  var inputSharedProps = {
    size: (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_17__.getInputSize)(picker, formatList[0], generateConfig)
  };
  var activeBarLeft = 0;
  var activeBarWidth = 0;

  if (startInputDivRef.current && endInputDivRef.current && separatorRef.current) {
    if (mergedActivePickerIndex === 0) {
      activeBarWidth = startInputDivRef.current.offsetWidth;
    } else {
      activeBarLeft = arrowLeft;
      activeBarWidth = endInputDivRef.current.offsetWidth;
    }
  }

  var activeBarPositionStyle = direction === 'rtl' ? {
    right: activeBarLeft
  } : {
    left: activeBarLeft
  }; // ============================ Return =============================

  var onContextSelect = function onContextSelect(date, type) {
    var values = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.updateValues)(selectedValue, date, mergedActivePickerIndex);

    if (type === 'submit' || type !== 'key' && !needConfirmButton) {
      // triggerChange will also update selected values
      triggerChange(values, mergedActivePickerIndex); // clear hover value style

      if (mergedActivePickerIndex === 0) {
        onStartLeave();
      } else {
        onEndLeave();
      }
    } else {
      setSelectedValue(values);
    }
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_PanelContext__WEBPACK_IMPORTED_MODULE_18__.default.Provider, {
    value: {
      operationRef: operationRef,
      hideHeader: picker === 'time',
      onDateMouseEnter: onDateMouseEnter,
      onDateMouseLeave: onDateMouseLeave,
      hideRanges: true,
      onSelect: onContextSelect,
      open: mergedOpen
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_PickerTrigger__WEBPACK_IMPORTED_MODULE_13__.default, {
    visible: mergedOpen,
    popupElement: rangePanel,
    popupStyle: popupStyle,
    prefixCls: prefixCls,
    dropdownClassName: dropdownClassName,
    dropdownAlign: dropdownAlign,
    getPopupContainer: getPopupContainer,
    transitionName: transitionName,
    range: true,
    direction: direction
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({
    ref: containerRef,
    className: classnames__WEBPACK_IMPORTED_MODULE_10___default()(prefixCls, "".concat(prefixCls, "-range"), className, (_classNames2 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)(_classNames2, "".concat(prefixCls, "-disabled"), mergedDisabled[0] && mergedDisabled[1]), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)(_classNames2, "".concat(prefixCls, "-focused"), mergedActivePickerIndex === 0 ? startFocused : endFocused), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)(_classNames2, "".concat(prefixCls, "-rtl"), direction === 'rtl'), _classNames2)),
    style: style,
    onClick: onPickerClick,
    onMouseEnter: onMouseEnter,
    onMouseLeave: onMouseLeave,
    onMouseDown: onPickerMouseDown
  }, (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.default)(props)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_10___default()("".concat(prefixCls, "-input"), (_classNames3 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)(_classNames3, "".concat(prefixCls, "-input-active"), mergedActivePickerIndex === 0), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)(_classNames3, "".concat(prefixCls, "-input-placeholder"), !!startHoverValue), _classNames3)),
    ref: startInputDivRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("input", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({
    id: id,
    disabled: mergedDisabled[0],
    readOnly: inputReadOnly || typeof formatList[0] === 'function' || !startTyping,
    value: startHoverValue || startText,
    onChange: function onChange(e) {
      triggerStartTextChange(e.target.value);
    },
    autoFocus: autoFocus,
    placeholder: (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(placeholder, 0) || '',
    ref: startInputRef
  }, startInputProps, inputSharedProps, {
    autoComplete: autoComplete
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
    className: "".concat(prefixCls, "-range-separator"),
    ref: separatorRef
  }, separator), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_10___default()("".concat(prefixCls, "-input"), (_classNames4 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)(_classNames4, "".concat(prefixCls, "-input-active"), mergedActivePickerIndex === 1), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__.default)(_classNames4, "".concat(prefixCls, "-input-placeholder"), !!endHoverValue), _classNames4)),
    ref: endInputDivRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("input", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({
    disabled: mergedDisabled[1],
    readOnly: inputReadOnly || typeof formatList[0] === 'function' || !endTyping,
    value: endHoverValue || endText,
    onChange: function onChange(e) {
      triggerEndTextChange(e.target.value);
    },
    placeholder: (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_16__.getValue)(placeholder, 1) || '',
    ref: endInputRef
  }, endInputProps, inputSharedProps, {
    autoComplete: autoComplete
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement("div", {
    className: "".concat(prefixCls, "-active-bar"),
    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_6__.default)({}, activeBarPositionStyle), {}, {
      width: activeBarWidth,
      position: 'absolute'
    })
  }), suffixNode, clearNode)));
} // Wrap with class component to enable pass generic with instance method


var RangePicker = /*#__PURE__*/function (_React$Component) {
  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__.default)(RangePicker, _React$Component);

  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__.default)(RangePicker);

  function RangePicker() {
    var _this;

    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__.default)(this, RangePicker);

    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }

    _this = _super.call.apply(_super, [this].concat(args));
    _this.pickerRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createRef();

    _this.focus = function () {
      if (_this.pickerRef.current) {
        _this.pickerRef.current.focus();
      }
    };

    _this.blur = function () {
      if (_this.pickerRef.current) {
        _this.pickerRef.current.blur();
      }
    };

    return _this;
  }

  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__.default)(RangePicker, [{
    key: "render",
    value: function render() {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(InnerRangePicker, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_4__.default)({}, this.props, {
        pickerRef: this.pickerRef
      }));
    }
  }]);

  return RangePicker;
}(react__WEBPACK_IMPORTED_MODULE_9__.Component);

/* harmony default export */ __webpack_exports__["default"] = (RangePicker);

/***/ }),

/***/ "./node_modules/rc-picker/es/generate/moment.js":
/*!******************************************************!*\
  !*** ./node_modules/rc-picker/es/generate/moment.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ "./node_modules/moment/moment.js");
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/rc-util/es/warning.js");


var generateConfig = {
  // get
  getNow: function getNow() {
    return moment__WEBPACK_IMPORTED_MODULE_0___default()();
  },
  getFixedDate: function getFixedDate(string) {
    return moment__WEBPACK_IMPORTED_MODULE_0___default()(string, 'YYYY-MM-DD');
  },
  getEndDate: function getEndDate(date) {
    var clone = date.clone();
    return clone.endOf('month');
  },
  getWeekDay: function getWeekDay(date) {
    var clone = date.clone().locale('en_US');
    return clone.weekday() + clone.localeData().firstDayOfWeek();
  },
  getYear: function getYear(date) {
    return date.year();
  },
  getMonth: function getMonth(date) {
    return date.month();
  },
  getDate: function getDate(date) {
    return date.date();
  },
  getHour: function getHour(date) {
    return date.hour();
  },
  getMinute: function getMinute(date) {
    return date.minute();
  },
  getSecond: function getSecond(date) {
    return date.second();
  },
  // set
  addYear: function addYear(date, diff) {
    var clone = date.clone();
    return clone.add(diff, 'year');
  },
  addMonth: function addMonth(date, diff) {
    var clone = date.clone();
    return clone.add(diff, 'month');
  },
  addDate: function addDate(date, diff) {
    var clone = date.clone();
    return clone.add(diff, 'day');
  },
  setYear: function setYear(date, year) {
    var clone = date.clone();
    return clone.year(year);
  },
  setMonth: function setMonth(date, month) {
    var clone = date.clone();
    return clone.month(month);
  },
  setDate: function setDate(date, num) {
    var clone = date.clone();
    return clone.date(num);
  },
  setHour: function setHour(date, hour) {
    var clone = date.clone();
    return clone.hour(hour);
  },
  setMinute: function setMinute(date, minute) {
    var clone = date.clone();
    return clone.minute(minute);
  },
  setSecond: function setSecond(date, second) {
    var clone = date.clone();
    return clone.second(second);
  },
  // Compare
  isAfter: function isAfter(date1, date2) {
    return date1.isAfter(date2);
  },
  isValidate: function isValidate(date) {
    return date.isValid();
  },
  locale: {
    getWeekFirstDay: function getWeekFirstDay(locale) {
      var date = moment__WEBPACK_IMPORTED_MODULE_0___default()().locale(locale);
      return date.localeData().firstDayOfWeek();
    },
    getWeekFirstDate: function getWeekFirstDate(locale, date) {
      var clone = date.clone();
      var result = clone.locale(locale);
      return result.weekday(0);
    },
    getWeek: function getWeek(locale, date) {
      var clone = date.clone();
      var result = clone.locale(locale);
      return result.week();
    },
    getShortWeekDays: function getShortWeekDays(locale) {
      var date = moment__WEBPACK_IMPORTED_MODULE_0___default()().locale(locale);
      return date.localeData().weekdaysMin();
    },
    getShortMonths: function getShortMonths(locale) {
      var date = moment__WEBPACK_IMPORTED_MODULE_0___default()().locale(locale);
      return date.localeData().monthsShort();
    },
    format: function format(locale, date, _format) {
      var clone = date.clone();
      var result = clone.locale(locale);
      return result.format(_format);
    },
    parse: function parse(locale, text, formats) {
      var fallbackFormatList = [];

      for (var i = 0; i < formats.length; i += 1) {
        var format = formats[i];
        var formatText = text;

        if (format.includes('wo') || format.includes('Wo')) {
          format = format.replace(/wo/g, 'w').replace(/Wo/g, 'W');
          var matchFormat = format.match(/[-YyMmDdHhSsWwGg]+/g);
          var matchText = formatText.match(/[-\d]+/g);

          if (matchFormat && matchText) {
            format = matchFormat.join('');
            formatText = matchText.join('');
          } else {
            fallbackFormatList.push(format.replace(/o/g, ''));
          }
        }

        var date = moment__WEBPACK_IMPORTED_MODULE_0___default()(formatText, format, locale, true);

        if (date.isValid()) {
          return date;
        }
      } // Fallback to fuzzy matching, this should always not reach match or need fire a issue


      for (var _i = 0; _i < fallbackFormatList.length; _i += 1) {
        var _date = moment__WEBPACK_IMPORTED_MODULE_0___default()(text, fallbackFormatList[_i], locale, false);
        /* istanbul ignore next */


        if (_date.isValid()) {
          (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__.noteOnce)(false, 'Not match any format strictly and fallback to fuzzy match. Please help to fire a issue about this.');
          return _date;
        }
      }

      return null;
    }
  }
};
/* harmony default export */ __webpack_exports__["default"] = (generateConfig);

/***/ }),

/***/ "./node_modules/rc-picker/es/hooks/useCellClassName.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-picker/es/hooks/useCellClassName.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useCellClassName; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/miscUtil */ "./node_modules/rc-picker/es/utils/miscUtil.js");



function useCellClassName(_ref) {
  var cellPrefixCls = _ref.cellPrefixCls,
      generateConfig = _ref.generateConfig,
      rangedValue = _ref.rangedValue,
      hoverRangedValue = _ref.hoverRangedValue,
      isInView = _ref.isInView,
      isSameCell = _ref.isSameCell,
      offsetCell = _ref.offsetCell,
      today = _ref.today,
      value = _ref.value;

  function getClassName(currentDate) {
    var _ref2;

    var prevDate = offsetCell(currentDate, -1);
    var nextDate = offsetCell(currentDate, 1);
    var rangeStart = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(rangedValue, 0);
    var rangeEnd = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(rangedValue, 1);
    var hoverStart = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(hoverRangedValue, 0);
    var hoverEnd = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(hoverRangedValue, 1);
    var isRangeHovered = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_1__.isInRange)(generateConfig, hoverStart, hoverEnd, currentDate);

    function isRangeStart(date) {
      return isSameCell(rangeStart, date);
    }

    function isRangeEnd(date) {
      return isSameCell(rangeEnd, date);
    }

    var isHoverStart = isSameCell(hoverStart, currentDate);
    var isHoverEnd = isSameCell(hoverEnd, currentDate);
    var isHoverEdgeStart = (isRangeHovered || isHoverEnd) && (!isInView(prevDate) || isRangeEnd(prevDate));
    var isHoverEdgeEnd = (isRangeHovered || isHoverStart) && (!isInView(nextDate) || isRangeStart(nextDate));
    return _ref2 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-in-view"), isInView(currentDate)), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-in-range"), (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_1__.isInRange)(generateConfig, rangeStart, rangeEnd, currentDate)), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-start"), isRangeStart(currentDate)), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-end"), isRangeEnd(currentDate)), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-start-single"), isRangeStart(currentDate) && !rangeEnd), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-end-single"), isRangeEnd(currentDate) && !rangeStart), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-start-near-hover"), isRangeStart(currentDate) && (isSameCell(prevDate, hoverStart) || (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_1__.isInRange)(generateConfig, hoverStart, hoverEnd, prevDate))), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-end-near-hover"), isRangeEnd(currentDate) && (isSameCell(nextDate, hoverEnd) || (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_1__.isInRange)(generateConfig, hoverStart, hoverEnd, nextDate))), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-hover"), isRangeHovered), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-hover-start"), isHoverStart), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-hover-end"), isHoverEnd), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-hover-edge-start"), isHoverEdgeStart), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-hover-edge-end"), isHoverEdgeEnd), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-hover-edge-start-near-range"), isHoverEdgeStart && isSameCell(prevDate, rangeEnd)), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-range-hover-edge-end-near-range"), isHoverEdgeEnd && isSameCell(nextDate, rangeStart)), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-today"), isSameCell(today, currentDate)), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_ref2, "".concat(cellPrefixCls, "-selected"), isSameCell(value, currentDate)), _ref2;
  }

  return getClassName;
}

/***/ }),

/***/ "./node_modules/rc-picker/es/hooks/useHoverValue.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-picker/es/hooks/useHoverValue.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useHoverValue; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useValueTexts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useValueTexts */ "./node_modules/rc-picker/es/hooks/useValueTexts.js");



function useHoverValue(valueText, _ref) {
  var formatList = _ref.formatList,
      generateConfig = _ref.generateConfig,
      locale = _ref.locale;

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),
      _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_useState, 2),
      value = _useState2[0],
      internalSetValue = _useState2[1];

  var raf = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);

  function setValue(val) {
    var immediately = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    cancelAnimationFrame(raf.current);

    if (immediately) {
      internalSetValue(val);
      return;
    }

    raf.current = requestAnimationFrame(function () {
      internalSetValue(val);
    });
  }

  var _useValueTexts = (0,_useValueTexts__WEBPACK_IMPORTED_MODULE_2__.default)(value, {
    formatList: formatList,
    generateConfig: generateConfig,
    locale: locale
  }),
      _useValueTexts2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_useValueTexts, 2),
      firstText = _useValueTexts2[1];

  function onEnter(date) {
    setValue(date);
  }

  function onLeave() {
    var immediately = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
    setValue(null, immediately);
  }

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    onLeave(true);
  }, [valueText]);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    return function () {
      return cancelAnimationFrame(raf.current);
    };
  }, []);
  return [firstText, onEnter, onLeave];
}

/***/ }),

/***/ "./node_modules/rc-picker/es/hooks/usePickerInput.js":
/*!***********************************************************!*\
  !*** ./node_modules/rc-picker/es/hooks/usePickerInput.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ usePickerInput; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/KeyCode */ "./node_modules/rc-util/es/KeyCode.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");




function usePickerInput(_ref) {
  var open = _ref.open,
      value = _ref.value,
      isClickOutside = _ref.isClickOutside,
      triggerOpen = _ref.triggerOpen,
      forwardKeyDown = _ref.forwardKeyDown,
      _onKeyDown = _ref.onKeyDown,
      blurToCancel = _ref.blurToCancel,
      onSubmit = _ref.onSubmit,
      onCancel = _ref.onCancel,
      _onFocus = _ref.onFocus,
      _onBlur = _ref.onBlur;

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),
      _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_useState, 2),
      typing = _useState2[0],
      setTyping = _useState2[1];

  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),
      _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_useState3, 2),
      focused = _useState4[0],
      setFocused = _useState4[1];
  /**
   * We will prevent blur to handle open event when user click outside,
   * since this will repeat trigger `onOpenChange` event.
   */


  var preventBlurRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);
  var valueChangedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);
  var preventDefaultRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);
  var inputProps = {
    onMouseDown: function onMouseDown() {
      setTyping(true);
      triggerOpen(true);
    },
    onKeyDown: function onKeyDown(e) {
      var preventDefault = function preventDefault() {
        preventDefaultRef.current = true;
      };

      _onKeyDown(e, preventDefault);

      if (preventDefaultRef.current) return;

      switch (e.which) {
        case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__.default.ENTER:
          {
            if (!open) {
              triggerOpen(true);
            } else if (onSubmit() !== false) {
              setTyping(true);
            }

            e.preventDefault();
            return;
          }

        case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__.default.TAB:
          {
            if (typing && open && !e.shiftKey) {
              setTyping(false);
              e.preventDefault();
            } else if (!typing && open) {
              if (!forwardKeyDown(e) && e.shiftKey) {
                setTyping(true);
                e.preventDefault();
              }
            }

            return;
          }

        case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__.default.ESC:
          {
            setTyping(true);
            onCancel();
            return;
          }
      }

      if (!open && ![rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__.default.SHIFT].includes(e.which)) {
        triggerOpen(true);
      } else if (!typing) {
        // Let popup panel handle keyboard
        forwardKeyDown(e);
      }
    },
    onFocus: function onFocus(e) {
      setTyping(true);
      setFocused(true);

      if (_onFocus) {
        _onFocus(e);
      }
    },
    onBlur: function onBlur(e) {
      if (preventBlurRef.current || !isClickOutside(document.activeElement)) {
        preventBlurRef.current = false;
        return;
      }

      if (blurToCancel) {
        setTimeout(function () {
          var _document = document,
              activeElement = _document.activeElement;

          while (activeElement && activeElement.shadowRoot) {
            activeElement = activeElement.shadowRoot.activeElement;
          }

          if (isClickOutside(activeElement)) {
            onCancel();
          }
        }, 0);
      } else if (open) {
        triggerOpen(false);

        if (valueChangedRef.current) {
          onSubmit();
        }
      }

      setFocused(false);

      if (_onBlur) {
        _onBlur(e);
      }
    }
  }; // check if value changed

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    valueChangedRef.current = false;
  }, [open]);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    valueChangedRef.current = true;
  }, [value]); // Global click handler

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    return (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_3__.addGlobalMouseDownEvent)(function (e) {
      var target = (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_3__.getTargetFromEvent)(e);

      if (open) {
        var clickedOutside = isClickOutside(target);

        if (!clickedOutside) {
          preventBlurRef.current = true; // Always set back in case `onBlur` prevented by user

          requestAnimationFrame(function () {
            preventBlurRef.current = false;
          });
        } else if (!focused || clickedOutside) {
          triggerOpen(false);
        }
      }
    });
  });
  return [inputProps, {
    focused: focused,
    typing: typing
  }];
}

/***/ }),

/***/ "./node_modules/rc-picker/es/hooks/useRangeDisabled.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-picker/es/hooks/useRangeDisabled.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useRangeDisabled; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_miscUtil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/miscUtil */ "./node_modules/rc-picker/es/utils/miscUtil.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");



function useRangeDisabled(_ref, disabledStart, disabledEnd) {
  var picker = _ref.picker,
      locale = _ref.locale,
      selectedValue = _ref.selectedValue,
      disabledDate = _ref.disabledDate,
      disabled = _ref.disabled,
      generateConfig = _ref.generateConfig;
  var startDate = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_1__.getValue)(selectedValue, 0);
  var endDate = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_1__.getValue)(selectedValue, 1);

  function weekFirstDate(date) {
    return generateConfig.locale.getWeekFirstDate(locale.locale, date);
  }

  function monthNumber(date) {
    var year = generateConfig.getYear(date);
    var month = generateConfig.getMonth(date);
    return year * 100 + month;
  }

  function quarterNumber(date) {
    var year = generateConfig.getYear(date);
    var quarter = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.getQuarter)(generateConfig, date);
    return year * 10 + quarter;
  }

  var disabledStartDate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (date) {
    if (disabledDate && disabledDate(date)) {
      return true;
    } // Disabled range


    if (disabled[1] && endDate) {
      return !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.isSameDate)(generateConfig, date, endDate) && generateConfig.isAfter(date, endDate);
    } // Disabled part


    if (disabledStart && endDate) {
      switch (picker) {
        case 'quarter':
          return quarterNumber(date) > quarterNumber(endDate);

        case 'month':
          return monthNumber(date) > monthNumber(endDate);

        case 'week':
          return weekFirstDate(date) > weekFirstDate(endDate);

        default:
          return !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.isSameDate)(generateConfig, date, endDate) && generateConfig.isAfter(date, endDate);
      }
    }

    return false;
  }, [disabledDate, disabled[1], endDate, disabledStart]);
  var disabledEndDate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (date) {
    if (disabledDate && disabledDate(date)) {
      return true;
    } // Disabled range


    if (disabled[0] && startDate) {
      return !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.isSameDate)(generateConfig, date, endDate) && generateConfig.isAfter(startDate, date);
    } // Disabled part


    if (disabledEnd && startDate) {
      switch (picker) {
        case 'quarter':
          return quarterNumber(date) < quarterNumber(startDate);

        case 'month':
          return monthNumber(date) < monthNumber(startDate);

        case 'week':
          return weekFirstDate(date) < weekFirstDate(startDate);

        default:
          return !(0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.isSameDate)(generateConfig, date, startDate) && generateConfig.isAfter(startDate, date);
      }
    }

    return false;
  }, [disabledDate, disabled[0], startDate, disabledEnd]);
  return [disabledStartDate, disabledEndDate];
}

/***/ }),

/***/ "./node_modules/rc-picker/es/hooks/useRangeViewDates.js":
/*!**************************************************************!*\
  !*** ./node_modules/rc-picker/es/hooks/useRangeViewDates.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useRangeViewDates; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/miscUtil */ "./node_modules/rc-picker/es/utils/miscUtil.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");





function getStartEndDistance(startDate, endDate, picker, generateConfig) {
  var startNext = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_3__.getClosingViewDate)(startDate, picker, generateConfig, 1);

  function getDistance(compareFunc) {
    if (compareFunc(startDate, endDate)) {
      return 'same';
    }

    if (compareFunc(startNext, endDate)) {
      return 'closing';
    }

    return 'far';
  }

  switch (picker) {
    case 'year':
      return getDistance(function (start, end) {
        return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_3__.isSameDecade)(generateConfig, start, end);
      });

    case 'quarter':
    case 'month':
      return getDistance(function (start, end) {
        return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_3__.isSameYear)(generateConfig, start, end);
      });

    default:
      return getDistance(function (start, end) {
        return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_3__.isSameMonth)(generateConfig, start, end);
      });
  }
}

function getRangeViewDate(values, index, picker, generateConfig) {
  var startDate = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(values, 0);
  var endDate = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(values, 1);

  if (index === 0) {
    return startDate;
  }

  if (startDate && endDate) {
    var distance = getStartEndDistance(startDate, endDate, picker, generateConfig);

    switch (distance) {
      case 'same':
        return startDate;

      case 'closing':
        return startDate;

      default:
        return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_3__.getClosingViewDate)(endDate, picker, generateConfig, -1);
    }
  }

  return startDate;
}

function useRangeViewDates(_ref) {
  var values = _ref.values,
      picker = _ref.picker,
      defaultDates = _ref.defaultDates,
      generateConfig = _ref.generateConfig;

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1__.useState(function () {
    return [(0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(defaultDates, 0), (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(defaultDates, 1)];
  }),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_React$useState, 2),
      defaultViewDates = _React$useState2[0],
      setDefaultViewDates = _React$useState2[1];

  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_1__.useState(null),
      _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_React$useState3, 2),
      viewDates = _React$useState4[0],
      setInternalViewDates = _React$useState4[1];

  var startDate = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(values, 0);
  var endDate = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(values, 1);

  function getViewDate(index) {
    // If set default view date, use it
    if (defaultViewDates[index]) {
      return defaultViewDates[index];
    }

    return (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(viewDates, index) || getRangeViewDate(values, index, picker, generateConfig) || startDate || endDate || generateConfig.getNow();
  }

  function setViewDate(viewDate, index) {
    if (viewDate) {
      var newViewDates = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.updateValues)(viewDates, viewDate, index); // Set view date will clean up default one

      setDefaultViewDates( // Should always be an array
      (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.updateValues)(defaultViewDates, null, index) || [null, null]); // Reset another one when not have value

      var anotherIndex = (index + 1) % 2;

      if (!(0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.getValue)(values, anotherIndex)) {
        newViewDates = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_2__.updateValues)(newViewDates, viewDate, anotherIndex);
      }

      setInternalViewDates(newViewDates);
    } else if (startDate || endDate) {
      // Reset all when has values when `viewDate` is `null` which means from open trigger
      setInternalViewDates(null);
    }
  }

  return [getViewDate, setViewDate];
}

/***/ }),

/***/ "./node_modules/rc-picker/es/hooks/useTextValueMapping.js":
/*!****************************************************************!*\
  !*** ./node_modules/rc-picker/es/hooks/useTextValueMapping.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useTextValueMapping; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");


function useTextValueMapping(_ref) {
  var valueTexts = _ref.valueTexts,
      onTextChange = _ref.onTextChange;

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1__.useState(''),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__.default)(_React$useState, 2),
      text = _React$useState2[0],
      setInnerText = _React$useState2[1];

  var valueTextsRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef([]);
  valueTextsRef.current = valueTexts;

  function triggerTextChange(value) {
    setInnerText(value);
    onTextChange(value);
  }

  function resetText() {
    setInnerText(valueTextsRef.current[0]);
  }

  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {
    if (valueTexts.every(function (valText) {
      return valText !== text;
    })) {
      resetText();
    }
  }, [valueTexts.join('||')]);
  return [text, triggerTextChange, resetText];
}

/***/ }),

/***/ "./node_modules/rc-picker/es/hooks/useValueTexts.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-picker/es/hooks/useValueTexts.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useValueTexts; }
/* harmony export */ });
/* harmony import */ var shallowequal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! shallowequal */ "./node_modules/shallowequal/index.js");
/* harmony import */ var shallowequal__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(shallowequal__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var rc_util_es_hooks_useMemo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useMemo */ "./node_modules/rc-util/es/hooks/useMemo.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");



function useValueTexts(value, _ref) {
  var formatList = _ref.formatList,
      generateConfig = _ref.generateConfig,
      locale = _ref.locale;
  return (0,rc_util_es_hooks_useMemo__WEBPACK_IMPORTED_MODULE_1__.default)(function () {
    if (!value) {
      return [[''], ''];
    } // We will convert data format back to first format


    var firstValueText = '';
    var fullValueTexts = [];

    for (var i = 0; i < formatList.length; i += 1) {
      var format = formatList[i];
      var formatStr = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.formatValue)(value, {
        generateConfig: generateConfig,
        locale: locale,
        format: format
      });
      fullValueTexts.push(formatStr);

      if (i === 0) {
        firstValueText = formatStr;
      }
    }

    return [fullValueTexts, firstValueText];
  }, [value, formatList], function (prev, next) {
    return prev[0] !== next[0] || !shallowequal__WEBPACK_IMPORTED_MODULE_0___default()(prev[1], next[1]);
  });
}

/***/ }),

/***/ "./node_modules/rc-picker/es/index.js":
/*!********************************************!*\
  !*** ./node_modules/rc-picker/es/index.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "PickerPanel": function() { return /* reexport safe */ _PickerPanel__WEBPACK_IMPORTED_MODULE_1__.default; },
/* harmony export */   "RangePicker": function() { return /* reexport safe */ _RangePicker__WEBPACK_IMPORTED_MODULE_2__.default; }
/* harmony export */ });
/* harmony import */ var _Picker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Picker */ "./node_modules/rc-picker/es/Picker.js");
/* harmony import */ var _PickerPanel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PickerPanel */ "./node_modules/rc-picker/es/PickerPanel.js");
/* harmony import */ var _RangePicker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RangePicker */ "./node_modules/rc-picker/es/RangePicker.js");




/* harmony default export */ __webpack_exports__["default"] = (_Picker__WEBPACK_IMPORTED_MODULE_0__.default);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/DatePanel/DateBody.js":
/*!****************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/DatePanel/DateBody.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _RangeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../RangeContext */ "./node_modules/rc-picker/es/RangeContext.js");
/* harmony import */ var _hooks_useCellClassName__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useCellClassName */ "./node_modules/rc-picker/es/hooks/useCellClassName.js");
/* harmony import */ var _PanelBody__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PanelBody */ "./node_modules/rc-picker/es/panels/PanelBody.js");







function DateBody(props) {
  var prefixCls = props.prefixCls,
      generateConfig = props.generateConfig,
      prefixColumn = props.prefixColumn,
      locale = props.locale,
      rowCount = props.rowCount,
      viewDate = props.viewDate,
      value = props.value,
      dateRender = props.dateRender;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_RangeContext__WEBPACK_IMPORTED_MODULE_3__.default),
      rangedValue = _React$useContext.rangedValue,
      hoverRangedValue = _React$useContext.hoverRangedValue;

  var baseDate = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.getWeekStartDate)(locale.locale, generateConfig, viewDate);
  var cellPrefixCls = "".concat(prefixCls, "-cell");
  var weekFirstDay = generateConfig.locale.getWeekFirstDay(locale.locale);
  var today = generateConfig.getNow(); // ============================== Header ==============================

  var headerCells = [];
  var weekDaysLocale = locale.shortWeekDays || (generateConfig.locale.getShortWeekDays ? generateConfig.locale.getShortWeekDays(locale.locale) : []);

  if (prefixColumn) {
    headerCells.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("th", {
      key: "empty",
      "aria-label": "empty cell"
    }));
  }

  for (var i = 0; i < _utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.WEEK_DAY_COUNT; i += 1) {
    headerCells.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("th", {
      key: i
    }, weekDaysLocale[(i + weekFirstDay) % _utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.WEEK_DAY_COUNT]));
  } // =============================== Body ===============================


  var getCellClassName = (0,_hooks_useCellClassName__WEBPACK_IMPORTED_MODULE_4__.default)({
    cellPrefixCls: cellPrefixCls,
    today: today,
    value: value,
    generateConfig: generateConfig,
    rangedValue: prefixColumn ? null : rangedValue,
    hoverRangedValue: prefixColumn ? null : hoverRangedValue,
    isSameCell: function isSameCell(current, target) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.isSameDate)(generateConfig, current, target);
    },
    isInView: function isInView(date) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.isSameMonth)(generateConfig, date, viewDate);
    },
    offsetCell: function offsetCell(date, offset) {
      return generateConfig.addDate(date, offset);
    }
  });
  var getCellNode = dateRender ? function (date) {
    return dateRender(date, today);
  } : undefined;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_PanelBody__WEBPACK_IMPORTED_MODULE_5__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    rowNum: rowCount,
    colNum: _utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.WEEK_DAY_COUNT,
    baseDate: baseDate,
    getCellNode: getCellNode,
    getCellText: generateConfig.getDate,
    getCellClassName: getCellClassName,
    getCellDate: generateConfig.addDate,
    titleCell: function titleCell(date) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.formatValue)(date, {
        locale: locale,
        format: 'YYYY-MM-DD',
        generateConfig: generateConfig
      });
    },
    headerCells: headerCells
  }));
}

/* harmony default export */ __webpack_exports__["default"] = (DateBody);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/DatePanel/DateHeader.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/DatePanel/DateHeader.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Header */ "./node_modules/rc-picker/es/panels/Header.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");






function DateHeader(props) {
  var prefixCls = props.prefixCls,
      generateConfig = props.generateConfig,
      locale = props.locale,
      viewDate = props.viewDate,
      onNextMonth = props.onNextMonth,
      onPrevMonth = props.onPrevMonth,
      onNextYear = props.onNextYear,
      onPrevYear = props.onPrevYear,
      onYearClick = props.onYearClick,
      onMonthClick = props.onMonthClick;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_3__.default),
      hideHeader = _React$useContext.hideHeader;

  if (hideHeader) {
    return null;
  }

  var headerPrefixCls = "".concat(prefixCls, "-header");
  var monthsLocale = locale.shortMonths || (generateConfig.locale.getShortMonths ? generateConfig.locale.getShortMonths(locale.locale) : []);
  var month = generateConfig.getMonth(viewDate); // =================== Month & Year ===================

  var yearNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("button", {
    type: "button",
    key: "year",
    onClick: onYearClick,
    tabIndex: -1,
    className: "".concat(prefixCls, "-year-btn")
  }, (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__.formatValue)(viewDate, {
    locale: locale,
    format: locale.yearFormat,
    generateConfig: generateConfig
  }));
  var monthNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("button", {
    type: "button",
    key: "month",
    onClick: onMonthClick,
    tabIndex: -1,
    className: "".concat(prefixCls, "-month-btn")
  }, locale.monthFormat ? (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__.formatValue)(viewDate, {
    locale: locale,
    format: locale.monthFormat,
    generateConfig: generateConfig
  }) : monthsLocale[month]);
  var monthYearNodes = locale.monthBeforeYear ? [monthNode, yearNode] : [yearNode, monthNode];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Header__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: headerPrefixCls,
    onSuperPrev: onPrevYear,
    onPrev: onPrevMonth,
    onNext: onNextMonth,
    onSuperNext: onNextYear
  }), monthYearNodes);
}

/* harmony default export */ __webpack_exports__["default"] = (DateHeader);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/DatePanel/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/DatePanel/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _DateBody__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./DateBody */ "./node_modules/rc-picker/es/panels/DatePanel/DateBody.js");
/* harmony import */ var _DateHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DateHeader */ "./node_modules/rc-picker/es/panels/DatePanel/DateHeader.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");









var DATE_ROW_COUNT = 6;

function DatePanel(props) {
  var prefixCls = props.prefixCls,
      _props$panelName = props.panelName,
      panelName = _props$panelName === void 0 ? 'date' : _props$panelName,
      keyboardConfig = props.keyboardConfig,
      active = props.active,
      operationRef = props.operationRef,
      generateConfig = props.generateConfig,
      value = props.value,
      viewDate = props.viewDate,
      onViewDateChange = props.onViewDateChange,
      onPanelChange = props.onPanelChange,
      _onSelect = props.onSelect;
  var panelPrefixCls = "".concat(prefixCls, "-").concat(panelName, "-panel"); // ======================= Keyboard =======================

  operationRef.current = {
    onKeyDown: function onKeyDown(event) {
      return (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_8__.createKeyDownHandler)(event, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__.default)({
        onLeftRight: function onLeftRight(diff) {
          _onSelect(generateConfig.addDate(value || viewDate, diff), 'key');
        },
        onCtrlLeftRight: function onCtrlLeftRight(diff) {
          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');
        },
        onUpDown: function onUpDown(diff) {
          _onSelect(generateConfig.addDate(value || viewDate, diff * _utils_dateUtil__WEBPACK_IMPORTED_MODULE_7__.WEEK_DAY_COUNT), 'key');
        },
        onPageUpDown: function onPageUpDown(diff) {
          _onSelect(generateConfig.addMonth(value || viewDate, diff), 'key');
        }
      }, keyboardConfig));
    }
  }; // ==================== View Operation ====================

  var onYearChange = function onYearChange(diff) {
    var newDate = generateConfig.addYear(viewDate, diff);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };

  var onMonthChange = function onMonthChange(diff) {
    var newDate = generateConfig.addMonth(viewDate, diff);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(panelPrefixCls, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(panelPrefixCls, "-active"), active))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_DateHeader__WEBPACK_IMPORTED_MODULE_6__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    value: value,
    viewDate: viewDate // View Operation
    ,
    onPrevYear: function onPrevYear() {
      onYearChange(-1);
    },
    onNextYear: function onNextYear() {
      onYearChange(1);
    },
    onPrevMonth: function onPrevMonth() {
      onMonthChange(-1);
    },
    onNextMonth: function onNextMonth() {
      onMonthChange(1);
    },
    onMonthClick: function onMonthClick() {
      onPanelChange('month', viewDate);
    },
    onYearClick: function onYearClick() {
      onPanelChange('year', viewDate);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_DateBody__WEBPACK_IMPORTED_MODULE_5__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    onSelect: function onSelect(date) {
      return _onSelect(date, 'mouse');
    },
    prefixCls: prefixCls,
    value: value,
    viewDate: viewDate,
    rowCount: DATE_ROW_COUNT
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (DatePanel);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/DatetimePanel/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/DatetimePanel/index.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/KeyCode */ "./node_modules/rc-util/es/KeyCode.js");
/* harmony import */ var _DatePanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../DatePanel */ "./node_modules/rc-picker/es/panels/DatePanel/index.js");
/* harmony import */ var _TimePanel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../TimePanel */ "./node_modules/rc-picker/es/panels/TimePanel/index.js");
/* harmony import */ var _utils_miscUtil__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/miscUtil */ "./node_modules/rc-picker/es/utils/miscUtil.js");
/* harmony import */ var _utils_timeUtil__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/timeUtil */ "./node_modules/rc-picker/es/utils/timeUtil.js");












var ACTIVE_PANEL = (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_10__.tuple)('date', 'time');

function DatetimePanel(props) {
  var prefixCls = props.prefixCls,
      operationRef = props.operationRef,
      generateConfig = props.generateConfig,
      value = props.value,
      defaultValue = props.defaultValue,
      disabledTime = props.disabledTime,
      showTime = props.showTime,
      onSelect = props.onSelect;
  var panelPrefixCls = "".concat(prefixCls, "-datetime-panel");

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_5__.useState(null),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__.default)(_React$useState, 2),
      activePanel = _React$useState2[0],
      setActivePanel = _React$useState2[1];

  var dateOperationRef = react__WEBPACK_IMPORTED_MODULE_5__.useRef({});
  var timeOperationRef = react__WEBPACK_IMPORTED_MODULE_5__.useRef({});
  var timeProps = (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__.default)(showTime) === 'object' ? (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__.default)({}, showTime) : {}; // ======================= Keyboard =======================

  function getNextActive(offset) {
    var activeIndex = ACTIVE_PANEL.indexOf(activePanel) + offset;
    var nextActivePanel = ACTIVE_PANEL[activeIndex] || null;
    return nextActivePanel;
  }

  var onBlur = function onBlur(e) {
    if (timeOperationRef.current.onBlur) {
      timeOperationRef.current.onBlur(e);
    }

    setActivePanel(null);
  };

  operationRef.current = {
    onKeyDown: function onKeyDown(event) {
      // Switch active panel
      if (event.which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.TAB) {
        var nextActivePanel = getNextActive(event.shiftKey ? -1 : 1);
        setActivePanel(nextActivePanel);

        if (nextActivePanel) {
          event.preventDefault();
        }

        return true;
      } // Operate on current active panel


      if (activePanel) {
        var ref = activePanel === 'date' ? dateOperationRef : timeOperationRef;

        if (ref.current && ref.current.onKeyDown) {
          ref.current.onKeyDown(event);
        }

        return true;
      } // Switch first active panel if operate without panel


      if ([rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.LEFT, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.RIGHT, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.UP, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.DOWN].includes(event.which)) {
        setActivePanel('date');
        return true;
      }

      return false;
    },
    onBlur: onBlur,
    onClose: onBlur
  }; // ======================== Events ========================

  var onInternalSelect = function onInternalSelect(date, source) {
    var selectedDate = date;

    if (source === 'date' && !value && timeProps.defaultValue) {
      // Date with time defaultValue
      selectedDate = generateConfig.setHour(selectedDate, generateConfig.getHour(timeProps.defaultValue));
      selectedDate = generateConfig.setMinute(selectedDate, generateConfig.getMinute(timeProps.defaultValue));
      selectedDate = generateConfig.setSecond(selectedDate, generateConfig.getSecond(timeProps.defaultValue));
    } else if (source === 'time' && !value && defaultValue) {
      selectedDate = generateConfig.setYear(selectedDate, generateConfig.getYear(defaultValue));
      selectedDate = generateConfig.setMonth(selectedDate, generateConfig.getMonth(defaultValue));
      selectedDate = generateConfig.setDate(selectedDate, generateConfig.getDate(defaultValue));
    }

    if (onSelect) {
      onSelect(selectedDate, 'mouse');
    }
  }; // ======================== Render ========================


  var disabledTimes = disabledTime ? disabledTime(value || null) : {};
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(panelPrefixCls, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(panelPrefixCls, "-active"), activePanel))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_DatePanel__WEBPACK_IMPORTED_MODULE_8__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    operationRef: dateOperationRef,
    active: activePanel === 'date',
    onSelect: function onSelect(date) {
      onInternalSelect((0,_utils_timeUtil__WEBPACK_IMPORTED_MODULE_11__.setDateTime)(generateConfig, date, showTime && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__.default)(showTime) === 'object' ? showTime.defaultValue : null), 'date');
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_TimePanel__WEBPACK_IMPORTED_MODULE_9__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    format: undefined
  }, timeProps, disabledTimes, {
    defaultValue: undefined,
    operationRef: timeOperationRef,
    active: activePanel === 'time',
    onSelect: function onSelect(date) {
      onInternalSelect(date, 'time');
    }
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (DatetimePanel);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/DecadePanel/DecadeBody.js":
/*!********************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/DecadePanel/DecadeBody.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "DECADE_COL_COUNT": function() { return /* binding */ DECADE_COL_COUNT; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! . */ "./node_modules/rc-picker/es/panels/DecadePanel/index.js");
/* harmony import */ var _PanelBody__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../PanelBody */ "./node_modules/rc-picker/es/panels/PanelBody.js");





var DECADE_COL_COUNT = 3;
var DECADE_ROW_COUNT = 4;

function DecadeBody(props) {
  var DECADE_UNIT_DIFF_DES = ___WEBPACK_IMPORTED_MODULE_3__.DECADE_UNIT_DIFF - 1;
  var prefixCls = props.prefixCls,
      viewDate = props.viewDate,
      generateConfig = props.generateConfig;
  var cellPrefixCls = "".concat(prefixCls, "-cell");
  var yearNumber = generateConfig.getYear(viewDate);
  var decadeYearNumber = Math.floor(yearNumber / ___WEBPACK_IMPORTED_MODULE_3__.DECADE_UNIT_DIFF) * ___WEBPACK_IMPORTED_MODULE_3__.DECADE_UNIT_DIFF;
  var startDecadeYear = Math.floor(yearNumber / ___WEBPACK_IMPORTED_MODULE_3__.DECADE_DISTANCE_COUNT) * ___WEBPACK_IMPORTED_MODULE_3__.DECADE_DISTANCE_COUNT;
  var endDecadeYear = startDecadeYear + ___WEBPACK_IMPORTED_MODULE_3__.DECADE_DISTANCE_COUNT - 1;
  var baseDecadeYear = generateConfig.setYear(viewDate, startDecadeYear - Math.ceil((DECADE_COL_COUNT * DECADE_ROW_COUNT * ___WEBPACK_IMPORTED_MODULE_3__.DECADE_UNIT_DIFF - ___WEBPACK_IMPORTED_MODULE_3__.DECADE_DISTANCE_COUNT) / 2));

  var getCellClassName = function getCellClassName(date) {
    var _ref;

    var startDecadeNumber = generateConfig.getYear(date);
    var endDecadeNumber = startDecadeNumber + DECADE_UNIT_DIFF_DES;
    return _ref = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_ref, "".concat(cellPrefixCls, "-in-view"), startDecadeYear <= startDecadeNumber && endDecadeNumber <= endDecadeYear), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_ref, "".concat(cellPrefixCls, "-selected"), startDecadeNumber === decadeYearNumber), _ref;
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_PanelBody__WEBPACK_IMPORTED_MODULE_4__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    rowNum: DECADE_ROW_COUNT,
    colNum: DECADE_COL_COUNT,
    baseDate: baseDecadeYear,
    getCellText: function getCellText(date) {
      var startDecadeNumber = generateConfig.getYear(date);
      return "".concat(startDecadeNumber, "-").concat(startDecadeNumber + DECADE_UNIT_DIFF_DES);
    },
    getCellClassName: getCellClassName,
    getCellDate: function getCellDate(date, offset) {
      return generateConfig.addYear(date, offset * ___WEBPACK_IMPORTED_MODULE_3__.DECADE_UNIT_DIFF);
    }
  }));
}

/* harmony default export */ __webpack_exports__["default"] = (DecadeBody);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/DecadePanel/DecadeHeader.js":
/*!**********************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/DecadePanel/DecadeHeader.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Header */ "./node_modules/rc-picker/es/panels/Header.js");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! . */ "./node_modules/rc-picker/es/panels/DecadePanel/index.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");






function DecadeHeader(props) {
  var prefixCls = props.prefixCls,
      generateConfig = props.generateConfig,
      viewDate = props.viewDate,
      onPrevDecades = props.onPrevDecades,
      onNextDecades = props.onNextDecades;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_4__.default),
      hideHeader = _React$useContext.hideHeader;

  if (hideHeader) {
    return null;
  }

  var headerPrefixCls = "".concat(prefixCls, "-header");
  var yearNumber = generateConfig.getYear(viewDate);
  var startYear = Math.floor(yearNumber / ___WEBPACK_IMPORTED_MODULE_3__.DECADE_DISTANCE_COUNT) * ___WEBPACK_IMPORTED_MODULE_3__.DECADE_DISTANCE_COUNT;
  var endYear = startYear + ___WEBPACK_IMPORTED_MODULE_3__.DECADE_DISTANCE_COUNT - 1;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Header__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: headerPrefixCls,
    onSuperPrev: onPrevDecades,
    onSuperNext: onNextDecades
  }), startYear, "-", endYear);
}

/* harmony default export */ __webpack_exports__["default"] = (DecadeHeader);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/DecadePanel/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/DecadePanel/index.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "DECADE_UNIT_DIFF": function() { return /* binding */ DECADE_UNIT_DIFF; },
/* harmony export */   "DECADE_DISTANCE_COUNT": function() { return /* binding */ DECADE_DISTANCE_COUNT; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _DecadeHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DecadeHeader */ "./node_modules/rc-picker/es/panels/DecadePanel/DecadeHeader.js");
/* harmony import */ var _DecadeBody__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DecadeBody */ "./node_modules/rc-picker/es/panels/DecadePanel/DecadeBody.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");





var DECADE_UNIT_DIFF = 10;
var DECADE_DISTANCE_COUNT = DECADE_UNIT_DIFF * 10;

function DecadePanel(props) {
  var prefixCls = props.prefixCls,
      onViewDateChange = props.onViewDateChange,
      generateConfig = props.generateConfig,
      viewDate = props.viewDate,
      operationRef = props.operationRef,
      onSelect = props.onSelect,
      onPanelChange = props.onPanelChange;
  var panelPrefixCls = "".concat(prefixCls, "-decade-panel"); // ======================= Keyboard =======================

  operationRef.current = {
    onKeyDown: function onKeyDown(event) {
      return (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_4__.createKeyDownHandler)(event, {
        onLeftRight: function onLeftRight(diff) {
          onSelect(generateConfig.addYear(viewDate, diff * DECADE_UNIT_DIFF), 'key');
        },
        onCtrlLeftRight: function onCtrlLeftRight(diff) {
          onSelect(generateConfig.addYear(viewDate, diff * DECADE_DISTANCE_COUNT), 'key');
        },
        onUpDown: function onUpDown(diff) {
          onSelect(generateConfig.addYear(viewDate, diff * DECADE_UNIT_DIFF * _DecadeBody__WEBPACK_IMPORTED_MODULE_3__.DECADE_COL_COUNT), 'key');
        },
        onEnter: function onEnter() {
          onPanelChange('year', viewDate);
        }
      });
    }
  }; // ==================== View Operation ====================

  var onDecadesChange = function onDecadesChange(diff) {
    var newDate = generateConfig.addYear(viewDate, diff * DECADE_DISTANCE_COUNT);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };

  var onInternalSelect = function onInternalSelect(date) {
    onSelect(date, 'mouse');
    onPanelChange('year', date);
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: panelPrefixCls
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_DecadeHeader__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    onPrevDecades: function onPrevDecades() {
      onDecadesChange(-1);
    },
    onNextDecades: function onNextDecades() {
      onDecadesChange(1);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_DecadeBody__WEBPACK_IMPORTED_MODULE_3__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    onSelect: onInternalSelect
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (DecadePanel);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/Header.js":
/*!****************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/Header.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");


var HIDDEN_STYLE = {
  visibility: 'hidden'
};

function Header(_ref) {
  var prefixCls = _ref.prefixCls,
      _ref$prevIcon = _ref.prevIcon,
      prevIcon = _ref$prevIcon === void 0 ? "\u2039" : _ref$prevIcon,
      _ref$nextIcon = _ref.nextIcon,
      nextIcon = _ref$nextIcon === void 0 ? "\u203A" : _ref$nextIcon,
      _ref$superPrevIcon = _ref.superPrevIcon,
      superPrevIcon = _ref$superPrevIcon === void 0 ? "\xAB" : _ref$superPrevIcon,
      _ref$superNextIcon = _ref.superNextIcon,
      superNextIcon = _ref$superNextIcon === void 0 ? "\xBB" : _ref$superNextIcon,
      onSuperPrev = _ref.onSuperPrev,
      onSuperNext = _ref.onSuperNext,
      onPrev = _ref.onPrev,
      onNext = _ref.onNext,
      children = _ref.children;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_1__.default),
      hideNextBtn = _React$useContext.hideNextBtn,
      hidePrevBtn = _React$useContext.hidePrevBtn;

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    className: prefixCls
  }, onSuperPrev && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("button", {
    type: "button",
    onClick: onSuperPrev,
    tabIndex: -1,
    className: "".concat(prefixCls, "-super-prev-btn"),
    style: hidePrevBtn ? HIDDEN_STYLE : {}
  }, superPrevIcon), onPrev && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("button", {
    type: "button",
    onClick: onPrev,
    tabIndex: -1,
    className: "".concat(prefixCls, "-prev-btn"),
    style: hidePrevBtn ? HIDDEN_STYLE : {}
  }, prevIcon), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    className: "".concat(prefixCls, "-view")
  }, children), onNext && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("button", {
    type: "button",
    onClick: onNext,
    tabIndex: -1,
    className: "".concat(prefixCls, "-next-btn"),
    style: hideNextBtn ? HIDDEN_STYLE : {}
  }, nextIcon), onSuperNext && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("button", {
    type: "button",
    onClick: onSuperNext,
    tabIndex: -1,
    className: "".concat(prefixCls, "-super-next-btn"),
    style: hideNextBtn ? HIDDEN_STYLE : {}
  }, superNextIcon));
}

/* harmony default export */ __webpack_exports__["default"] = (Header);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/MonthPanel/MonthBody.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/MonthPanel/MonthBody.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "MONTH_COL_COUNT": function() { return /* binding */ MONTH_COL_COUNT; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _RangeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../RangeContext */ "./node_modules/rc-picker/es/RangeContext.js");
/* harmony import */ var _hooks_useCellClassName__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useCellClassName */ "./node_modules/rc-picker/es/hooks/useCellClassName.js");
/* harmony import */ var _PanelBody__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PanelBody */ "./node_modules/rc-picker/es/panels/PanelBody.js");






var MONTH_COL_COUNT = 3;
var MONTH_ROW_COUNT = 4;

function MonthBody(props) {
  var prefixCls = props.prefixCls,
      locale = props.locale,
      value = props.value,
      viewDate = props.viewDate,
      generateConfig = props.generateConfig,
      monthCellRender = props.monthCellRender;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_RangeContext__WEBPACK_IMPORTED_MODULE_3__.default),
      rangedValue = _React$useContext.rangedValue,
      hoverRangedValue = _React$useContext.hoverRangedValue;

  var cellPrefixCls = "".concat(prefixCls, "-cell");
  var getCellClassName = (0,_hooks_useCellClassName__WEBPACK_IMPORTED_MODULE_4__.default)({
    cellPrefixCls: cellPrefixCls,
    value: value,
    generateConfig: generateConfig,
    rangedValue: rangedValue,
    hoverRangedValue: hoverRangedValue,
    isSameCell: function isSameCell(current, target) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.isSameMonth)(generateConfig, current, target);
    },
    isInView: function isInView() {
      return true;
    },
    offsetCell: function offsetCell(date, offset) {
      return generateConfig.addMonth(date, offset);
    }
  });
  var monthsLocale = locale.shortMonths || (generateConfig.locale.getShortMonths ? generateConfig.locale.getShortMonths(locale.locale) : []);
  var baseMonth = generateConfig.setMonth(viewDate, 0);
  var getCellNode = monthCellRender ? function (date) {
    return monthCellRender(date, locale);
  } : undefined;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_PanelBody__WEBPACK_IMPORTED_MODULE_5__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    rowNum: MONTH_ROW_COUNT,
    colNum: MONTH_COL_COUNT,
    baseDate: baseMonth,
    getCellNode: getCellNode,
    getCellText: function getCellText(date) {
      return locale.monthFormat ? (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.formatValue)(date, {
        locale: locale,
        format: locale.monthFormat,
        generateConfig: generateConfig
      }) : monthsLocale[generateConfig.getMonth(date)];
    },
    getCellClassName: getCellClassName,
    getCellDate: generateConfig.addMonth,
    titleCell: function titleCell(date) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.formatValue)(date, {
        locale: locale,
        format: 'YYYY-MM',
        generateConfig: generateConfig
      });
    }
  }));
}

/* harmony default export */ __webpack_exports__["default"] = (MonthBody);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/MonthPanel/MonthHeader.js":
/*!********************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/MonthPanel/MonthHeader.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Header */ "./node_modules/rc-picker/es/panels/Header.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");






function MonthHeader(props) {
  var prefixCls = props.prefixCls,
      generateConfig = props.generateConfig,
      locale = props.locale,
      viewDate = props.viewDate,
      onNextYear = props.onNextYear,
      onPrevYear = props.onPrevYear,
      onYearClick = props.onYearClick;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_3__.default),
      hideHeader = _React$useContext.hideHeader;

  if (hideHeader) {
    return null;
  }

  var headerPrefixCls = "".concat(prefixCls, "-header");
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Header__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: headerPrefixCls,
    onSuperPrev: onPrevYear,
    onSuperNext: onNextYear
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("button", {
    type: "button",
    onClick: onYearClick,
    className: "".concat(prefixCls, "-year-btn")
  }, (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__.formatValue)(viewDate, {
    locale: locale,
    format: locale.yearFormat,
    generateConfig: generateConfig
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (MonthHeader);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/MonthPanel/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/MonthPanel/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _MonthHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MonthHeader */ "./node_modules/rc-picker/es/panels/MonthPanel/MonthHeader.js");
/* harmony import */ var _MonthBody__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MonthBody */ "./node_modules/rc-picker/es/panels/MonthPanel/MonthBody.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");






function MonthPanel(props) {
  var prefixCls = props.prefixCls,
      operationRef = props.operationRef,
      onViewDateChange = props.onViewDateChange,
      generateConfig = props.generateConfig,
      value = props.value,
      viewDate = props.viewDate,
      onPanelChange = props.onPanelChange,
      _onSelect = props.onSelect;
  var panelPrefixCls = "".concat(prefixCls, "-month-panel"); // ======================= Keyboard =======================

  operationRef.current = {
    onKeyDown: function onKeyDown(event) {
      return (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_4__.createKeyDownHandler)(event, {
        onLeftRight: function onLeftRight(diff) {
          _onSelect(generateConfig.addMonth(value || viewDate, diff), 'key');
        },
        onCtrlLeftRight: function onCtrlLeftRight(diff) {
          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');
        },
        onUpDown: function onUpDown(diff) {
          _onSelect(generateConfig.addMonth(value || viewDate, diff * _MonthBody__WEBPACK_IMPORTED_MODULE_3__.MONTH_COL_COUNT), 'key');
        },
        onEnter: function onEnter() {
          onPanelChange('date', value || viewDate);
        }
      });
    }
  }; // ==================== View Operation ====================

  var onYearChange = function onYearChange(diff) {
    var newDate = generateConfig.addYear(viewDate, diff);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: panelPrefixCls
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_MonthHeader__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    onPrevYear: function onPrevYear() {
      onYearChange(-1);
    },
    onNextYear: function onNextYear() {
      onYearChange(1);
    },
    onYearClick: function onYearClick() {
      onPanelChange('year', viewDate);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_MonthBody__WEBPACK_IMPORTED_MODULE_3__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    onSelect: function onSelect(date) {
      _onSelect(date, 'mouse');

      onPanelChange('date', date);
    }
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (MonthPanel);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/PanelBody.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/PanelBody.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ PanelBody; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");
/* harmony import */ var _utils_timeUtil__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/timeUtil */ "./node_modules/rc-picker/es/utils/timeUtil.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");







function PanelBody(_ref) {
  var prefixCls = _ref.prefixCls,
      disabledDate = _ref.disabledDate,
      onSelect = _ref.onSelect,
      picker = _ref.picker,
      rowNum = _ref.rowNum,
      colNum = _ref.colNum,
      prefixColumn = _ref.prefixColumn,
      rowClassName = _ref.rowClassName,
      baseDate = _ref.baseDate,
      getCellClassName = _ref.getCellClassName,
      getCellText = _ref.getCellText,
      getCellNode = _ref.getCellNode,
      getCellDate = _ref.getCellDate,
      generateConfig = _ref.generateConfig,
      titleCell = _ref.titleCell,
      headerCells = _ref.headerCells;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_4__.default),
      onDateMouseEnter = _React$useContext.onDateMouseEnter,
      onDateMouseLeave = _React$useContext.onDateMouseLeave,
      mode = _React$useContext.mode;

  var cellPrefixCls = "".concat(prefixCls, "-cell"); // =============================== Body ===============================

  var rows = [];

  for (var i = 0; i < rowNum; i += 1) {
    var row = [];
    var rowStartDate = void 0;

    var _loop = function _loop(j) {
      var _objectSpread2;

      var offset = i * colNum + j;
      var currentDate = getCellDate(baseDate, offset);
      var disabled = (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_6__.getCellDateDisabled)({
        cellDate: currentDate,
        mode: mode,
        disabledDate: disabledDate,
        generateConfig: generateConfig
      });

      if (j === 0) {
        rowStartDate = currentDate;

        if (prefixColumn) {
          row.push(prefixColumn(rowStartDate));
        }
      }

      var title = titleCell && titleCell(currentDate);
      row.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("td", {
        key: j,
        title: title,
        className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(cellPrefixCls, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__.default)((_objectSpread2 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_objectSpread2, "".concat(cellPrefixCls, "-disabled"), disabled), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_objectSpread2, "".concat(cellPrefixCls, "-start"), getCellText(currentDate) === 1 || picker === 'year' && Number(title) % 10 === 0), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_objectSpread2, "".concat(cellPrefixCls, "-end"), title === (0,_utils_timeUtil__WEBPACK_IMPORTED_MODULE_5__.getLastDay)(generateConfig, currentDate) || picker === 'year' && Number(title) % 10 === 9), _objectSpread2), getCellClassName(currentDate))),
        onClick: function onClick() {
          if (!disabled) {
            onSelect(currentDate);
          }
        },
        onMouseEnter: function onMouseEnter() {
          if (!disabled && onDateMouseEnter) {
            onDateMouseEnter(currentDate);
          }
        },
        onMouseLeave: function onMouseLeave() {
          if (!disabled && onDateMouseLeave) {
            onDateMouseLeave(currentDate);
          }
        }
      }, getCellNode ? getCellNode(currentDate) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        className: "".concat(cellPrefixCls, "-inner")
      }, getCellText(currentDate))));
    };

    for (var j = 0; j < colNum; j += 1) {
      _loop(j);
    }

    rows.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("tr", {
      key: i,
      className: rowClassName && rowClassName(rowStartDate)
    }, row));
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(prefixCls, "-body")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("table", {
    className: "".concat(prefixCls, "-content")
  }, headerCells && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("thead", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("tr", null, headerCells)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("tbody", null, rows)));
}

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/QuarterPanel/QuarterBody.js":
/*!**********************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/QuarterPanel/QuarterBody.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "QUARTER_COL_COUNT": function() { return /* binding */ QUARTER_COL_COUNT; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _RangeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../RangeContext */ "./node_modules/rc-picker/es/RangeContext.js");
/* harmony import */ var _hooks_useCellClassName__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useCellClassName */ "./node_modules/rc-picker/es/hooks/useCellClassName.js");
/* harmony import */ var _PanelBody__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PanelBody */ "./node_modules/rc-picker/es/panels/PanelBody.js");






var QUARTER_COL_COUNT = 4;
var QUARTER_ROW_COUNT = 1;

function QuarterBody(props) {
  var prefixCls = props.prefixCls,
      locale = props.locale,
      value = props.value,
      viewDate = props.viewDate,
      generateConfig = props.generateConfig;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_RangeContext__WEBPACK_IMPORTED_MODULE_3__.default),
      rangedValue = _React$useContext.rangedValue,
      hoverRangedValue = _React$useContext.hoverRangedValue;

  var cellPrefixCls = "".concat(prefixCls, "-cell");
  var getCellClassName = (0,_hooks_useCellClassName__WEBPACK_IMPORTED_MODULE_4__.default)({
    cellPrefixCls: cellPrefixCls,
    value: value,
    generateConfig: generateConfig,
    rangedValue: rangedValue,
    hoverRangedValue: hoverRangedValue,
    isSameCell: function isSameCell(current, target) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.isSameQuarter)(generateConfig, current, target);
    },
    isInView: function isInView() {
      return true;
    },
    offsetCell: function offsetCell(date, offset) {
      return generateConfig.addMonth(date, offset * 3);
    }
  });
  var baseQuarter = generateConfig.setDate(generateConfig.setMonth(viewDate, 0), 1);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_PanelBody__WEBPACK_IMPORTED_MODULE_5__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    rowNum: QUARTER_ROW_COUNT,
    colNum: QUARTER_COL_COUNT,
    baseDate: baseQuarter,
    getCellText: function getCellText(date) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.formatValue)(date, {
        locale: locale,
        format: locale.quarterFormat || '[Q]Q',
        generateConfig: generateConfig
      });
    },
    getCellClassName: getCellClassName,
    getCellDate: function getCellDate(date, offset) {
      return generateConfig.addMonth(date, offset * 3);
    },
    titleCell: function titleCell(date) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_2__.formatValue)(date, {
        locale: locale,
        format: 'YYYY-[Q]Q',
        generateConfig: generateConfig
      });
    }
  }));
}

/* harmony default export */ __webpack_exports__["default"] = (QuarterBody);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/QuarterPanel/QuarterHeader.js":
/*!************************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/QuarterPanel/QuarterHeader.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Header */ "./node_modules/rc-picker/es/panels/Header.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");






function QuarterHeader(props) {
  var prefixCls = props.prefixCls,
      generateConfig = props.generateConfig,
      locale = props.locale,
      viewDate = props.viewDate,
      onNextYear = props.onNextYear,
      onPrevYear = props.onPrevYear,
      onYearClick = props.onYearClick;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_3__.default),
      hideHeader = _React$useContext.hideHeader;

  if (hideHeader) {
    return null;
  }

  var headerPrefixCls = "".concat(prefixCls, "-header");
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Header__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: headerPrefixCls,
    onSuperPrev: onPrevYear,
    onSuperNext: onNextYear
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("button", {
    type: "button",
    onClick: onYearClick,
    className: "".concat(prefixCls, "-year-btn")
  }, (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__.formatValue)(viewDate, {
    locale: locale,
    format: locale.yearFormat,
    generateConfig: generateConfig
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (QuarterHeader);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/QuarterPanel/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/QuarterPanel/index.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _QuarterHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuarterHeader */ "./node_modules/rc-picker/es/panels/QuarterPanel/QuarterHeader.js");
/* harmony import */ var _QuarterBody__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuarterBody */ "./node_modules/rc-picker/es/panels/QuarterPanel/QuarterBody.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");






function QuarterPanel(props) {
  var prefixCls = props.prefixCls,
      operationRef = props.operationRef,
      onViewDateChange = props.onViewDateChange,
      generateConfig = props.generateConfig,
      value = props.value,
      viewDate = props.viewDate,
      onPanelChange = props.onPanelChange,
      _onSelect = props.onSelect;
  var panelPrefixCls = "".concat(prefixCls, "-quarter-panel"); // ======================= Keyboard =======================

  operationRef.current = {
    onKeyDown: function onKeyDown(event) {
      return (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_4__.createKeyDownHandler)(event, {
        onLeftRight: function onLeftRight(diff) {
          _onSelect(generateConfig.addMonth(value || viewDate, diff * 3), 'key');
        },
        onCtrlLeftRight: function onCtrlLeftRight(diff) {
          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');
        },
        onUpDown: function onUpDown(diff) {
          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');
        }
      });
    }
  }; // ==================== View Operation ====================

  var onYearChange = function onYearChange(diff) {
    var newDate = generateConfig.addYear(viewDate, diff);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: panelPrefixCls
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_QuarterHeader__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    onPrevYear: function onPrevYear() {
      onYearChange(-1);
    },
    onNextYear: function onNextYear() {
      onYearChange(1);
    },
    onYearClick: function onYearClick() {
      onPanelChange('year', viewDate);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_QuarterBody__WEBPACK_IMPORTED_MODULE_3__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    onSelect: function onSelect(date) {
      _onSelect(date, 'mouse');
    }
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (QuarterPanel);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/TimePanel/TimeBody.js":
/*!****************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/TimePanel/TimeBody.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_hooks_useMemo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/hooks/useMemo */ "./node_modules/rc-util/es/hooks/useMemo.js");
/* harmony import */ var _TimeUnitColumn__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TimeUnitColumn */ "./node_modules/rc-picker/es/panels/TimePanel/TimeUnitColumn.js");
/* harmony import */ var _utils_miscUtil__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/miscUtil */ "./node_modules/rc-picker/es/utils/miscUtil.js");
/* harmony import */ var _utils_timeUtil__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/timeUtil */ "./node_modules/rc-picker/es/utils/timeUtil.js");








function shouldUnitsUpdate(prevUnits, nextUnits) {
  if (prevUnits.length !== nextUnits.length) return true; // if any unit's disabled status is different, the units should be re-evaluted

  for (var i = 0; i < prevUnits.length; i += 1) {
    if (prevUnits[i].disabled !== nextUnits[i].disabled) return true;
  }

  return false;
}

function generateUnits(start, end, step, disabledUnits) {
  var units = [];

  for (var i = start; i <= end; i += step) {
    units.push({
      label: (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_5__.leftPad)(i, 2),
      value: i,
      disabled: (disabledUnits || []).includes(i)
    });
  }

  return units;
}

function TimeBody(props) {
  var generateConfig = props.generateConfig,
      prefixCls = props.prefixCls,
      operationRef = props.operationRef,
      activeColumnIndex = props.activeColumnIndex,
      value = props.value,
      showHour = props.showHour,
      showMinute = props.showMinute,
      showSecond = props.showSecond,
      use12Hours = props.use12Hours,
      _props$hourStep = props.hourStep,
      hourStep = _props$hourStep === void 0 ? 1 : _props$hourStep,
      _props$minuteStep = props.minuteStep,
      minuteStep = _props$minuteStep === void 0 ? 1 : _props$minuteStep,
      _props$secondStep = props.secondStep,
      secondStep = _props$secondStep === void 0 ? 1 : _props$secondStep,
      disabledHours = props.disabledHours,
      disabledMinutes = props.disabledMinutes,
      disabledSeconds = props.disabledSeconds,
      hideDisabledOptions = props.hideDisabledOptions,
      onSelect = props.onSelect;
  var columns = [];
  var contentPrefixCls = "".concat(prefixCls, "-content");
  var columnPrefixCls = "".concat(prefixCls, "-time-panel");
  var isPM;
  var originHour = value ? generateConfig.getHour(value) : -1;
  var hour = originHour;
  var minute = value ? generateConfig.getMinute(value) : -1;
  var second = value ? generateConfig.getSecond(value) : -1;

  var setTime = function setTime(isNewPM, newHour, newMinute, newSecond) {
    var newDate = value || generateConfig.getNow();
    var mergedHour = Math.max(0, newHour);
    var mergedMinute = Math.max(0, newMinute);
    var mergedSecond = Math.max(0, newSecond);
    newDate = (0,_utils_timeUtil__WEBPACK_IMPORTED_MODULE_6__.setTime)(generateConfig, newDate, !use12Hours || !isNewPM ? mergedHour : mergedHour + 12, mergedMinute, mergedSecond);
    return newDate;
  }; // ========================= Unit =========================


  var rawHours = generateUnits(0, 23, hourStep, disabledHours && disabledHours());
  var memorizedRawHours = (0,rc_util_es_hooks_useMemo__WEBPACK_IMPORTED_MODULE_3__.default)(function () {
    return rawHours;
  }, rawHours, shouldUnitsUpdate); // Should additional logic to handle 12 hours

  if (use12Hours) {
    isPM = hour >= 12; // -1 means should display AM

    hour %= 12;
  }

  var _React$useMemo = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(function () {
    if (!use12Hours) {
      return [false, false];
    }

    var AMPMDisabled = [true, true];
    memorizedRawHours.forEach(function (_ref) {
      var disabled = _ref.disabled,
          hourValue = _ref.value;
      if (disabled) return;

      if (hourValue >= 12) {
        AMPMDisabled[1] = false;
      } else {
        AMPMDisabled[0] = false;
      }
    });
    return AMPMDisabled;
  }, [use12Hours, memorizedRawHours]),
      _React$useMemo2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__.default)(_React$useMemo, 2),
      AMDisabled = _React$useMemo2[0],
      PMDisabled = _React$useMemo2[1];

  var hours = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(function () {
    if (!use12Hours) return memorizedRawHours;
    return memorizedRawHours.filter(isPM ? function (hourMeta) {
      return hourMeta.value >= 12;
    } : function (hourMeta) {
      return hourMeta.value < 12;
    }).map(function (hourMeta) {
      var hourValue = hourMeta.value % 12;
      var hourLabel = hourValue === 0 ? '12' : (0,_utils_miscUtil__WEBPACK_IMPORTED_MODULE_5__.leftPad)(hourValue, 2);
      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)({}, hourMeta), {}, {
        label: hourLabel,
        value: hourValue
      });
    });
  }, [use12Hours, isPM, memorizedRawHours]);
  var minutes = generateUnits(0, 59, minuteStep, disabledMinutes && disabledMinutes(originHour));
  var seconds = generateUnits(0, 59, secondStep, disabledSeconds && disabledSeconds(originHour, minute)); // ====================== Operations ======================

  operationRef.current = {
    onUpDown: function onUpDown(diff) {
      var column = columns[activeColumnIndex];

      if (column) {
        var valueIndex = column.units.findIndex(function (unit) {
          return unit.value === column.value;
        });
        var unitLen = column.units.length;

        for (var i = 1; i < unitLen; i += 1) {
          var nextUnit = column.units[(valueIndex + diff * i + unitLen) % unitLen];

          if (nextUnit.disabled !== true) {
            column.onSelect(nextUnit.value);
            break;
          }
        }
      }
    }
  }; // ======================== Render ========================

  function addColumnNode(condition, node, columnValue, units, onColumnSelect) {
    if (condition !== false) {
      columns.push({
        node: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.cloneElement(node, {
          prefixCls: columnPrefixCls,
          value: columnValue,
          active: activeColumnIndex === columns.length,
          onSelect: onColumnSelect,
          units: units,
          hideDisabledOptions: hideDisabledOptions
        }),
        onSelect: onColumnSelect,
        value: columnValue,
        units: units
      });
    }
  } // Hour


  addColumnNode(showHour, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TimeUnitColumn__WEBPACK_IMPORTED_MODULE_4__.default, {
    key: "hour"
  }), hour, hours, function (num) {
    onSelect(setTime(isPM, num, minute, second), 'mouse');
  }); // Minute

  addColumnNode(showMinute, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TimeUnitColumn__WEBPACK_IMPORTED_MODULE_4__.default, {
    key: "minute"
  }), minute, minutes, function (num) {
    onSelect(setTime(isPM, hour, num, second), 'mouse');
  }); // Second

  addColumnNode(showSecond, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TimeUnitColumn__WEBPACK_IMPORTED_MODULE_4__.default, {
    key: "second"
  }), second, seconds, function (num) {
    onSelect(setTime(isPM, hour, minute, num), 'mouse');
  }); // 12 Hours

  var PMIndex = -1;

  if (typeof isPM === 'boolean') {
    PMIndex = isPM ? 1 : 0;
  }

  addColumnNode(use12Hours === true, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TimeUnitColumn__WEBPACK_IMPORTED_MODULE_4__.default, {
    key: "12hours"
  }), PMIndex, [{
    label: 'AM',
    value: 0,
    disabled: AMDisabled
  }, {
    label: 'PM',
    value: 1,
    disabled: PMDisabled
  }], function (num) {
    onSelect(setTime(!!num, hour, minute, second), 'mouse');
  });
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: contentPrefixCls
  }, columns.map(function (_ref2) {
    var node = _ref2.node;
    return node;
  }));
}

/* harmony default export */ __webpack_exports__["default"] = (TimeBody);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/TimePanel/TimeHeader.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/TimePanel/TimeHeader.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Header */ "./node_modules/rc-picker/es/panels/Header.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");





function TimeHeader(props) {
  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_2__.default),
      hideHeader = _React$useContext.hideHeader;

  if (hideHeader) {
    return null;
  }

  var prefixCls = props.prefixCls,
      generateConfig = props.generateConfig,
      locale = props.locale,
      value = props.value,
      format = props.format;
  var headerPrefixCls = "".concat(prefixCls, "-header");
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Header__WEBPACK_IMPORTED_MODULE_1__.default, {
    prefixCls: headerPrefixCls
  }, value ? (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_3__.formatValue)(value, {
    locale: locale,
    format: format,
    generateConfig: generateConfig
  }) : "\xA0");
}

/* harmony default export */ __webpack_exports__["default"] = (TimeHeader);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/TimePanel/TimeUnitColumn.js":
/*!**********************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/TimePanel/TimeUnitColumn.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");







function TimeUnitColumn(props) {
  var prefixCls = props.prefixCls,
      units = props.units,
      onSelect = props.onSelect,
      value = props.value,
      active = props.active,
      hideDisabledOptions = props.hideDisabledOptions;
  var cellPrefixCls = "".concat(prefixCls, "-cell");

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_4__.default),
      open = _React$useContext.open;

  var ulRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
  var liRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());
  var scrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(); // `useLayoutEffect` here to avoid blink by duration is 0

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(function () {
    var li = liRefs.current.get(value);

    if (li && open !== false) {
      (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_3__.scrollTo)(ulRef.current, li.offsetTop, 120);
    }
  }, [value]);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(function () {
    if (open) {
      var li = liRefs.current.get(value);

      if (li) {
        scrollRef.current = (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_3__.waitElementReady)(li, function () {
          (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_3__.scrollTo)(ulRef.current, li.offsetTop, 0);
        });
      }
    }

    return function () {
      var _scrollRef$current;

      (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : _scrollRef$current.call(scrollRef);
    };
  }, [open]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("ul", {
    className: classnames__WEBPACK_IMPORTED_MODULE_2___default()("".concat(prefixCls, "-column"), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)({}, "".concat(prefixCls, "-column-active"), active)),
    ref: ulRef,
    style: {
      position: 'relative'
    }
  }, units.map(function (unit) {
    var _classNames2;

    if (hideDisabledOptions && unit.disabled) {
      return null;
    }

    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", {
      key: unit.value,
      ref: function ref(element) {
        liRefs.current.set(unit.value, element);
      },
      className: classnames__WEBPACK_IMPORTED_MODULE_2___default()(cellPrefixCls, (_classNames2 = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames2, "".concat(cellPrefixCls, "-disabled"), unit.disabled), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__.default)(_classNames2, "".concat(cellPrefixCls, "-selected"), value === unit.value), _classNames2)),
      onClick: function onClick() {
        if (unit.disabled) {
          return;
        }

        onSelect(unit.value);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
      className: "".concat(cellPrefixCls, "-inner")
    }, unit.label));
  }));
}

/* harmony default export */ __webpack_exports__["default"] = (TimeUnitColumn);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/TimePanel/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/TimePanel/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _TimeHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TimeHeader */ "./node_modules/rc-picker/es/panels/TimePanel/TimeHeader.js");
/* harmony import */ var _TimeBody__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TimeBody */ "./node_modules/rc-picker/es/panels/TimePanel/TimeBody.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");









var countBoolean = function countBoolean(boolList) {
  return boolList.filter(function (bool) {
    return bool !== false;
  }).length;
};

function TimePanel(props) {
  var generateConfig = props.generateConfig,
      _props$format = props.format,
      format = _props$format === void 0 ? 'HH:mm:ss' : _props$format,
      prefixCls = props.prefixCls,
      active = props.active,
      operationRef = props.operationRef,
      showHour = props.showHour,
      showMinute = props.showMinute,
      showSecond = props.showSecond,
      _props$use12Hours = props.use12Hours,
      use12Hours = _props$use12Hours === void 0 ? false : _props$use12Hours,
      onSelect = props.onSelect,
      value = props.value;
  var panelPrefixCls = "".concat(prefixCls, "-time-panel");
  var bodyOperationRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(); // ======================= Keyboard =======================

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState(-1),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__.default)(_React$useState, 2),
      activeColumnIndex = _React$useState2[0],
      setActiveColumnIndex = _React$useState2[1];

  var columnsCount = countBoolean([showHour, showMinute, showSecond, use12Hours]);
  operationRef.current = {
    onKeyDown: function onKeyDown(event) {
      return (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_7__.createKeyDownHandler)(event, {
        onLeftRight: function onLeftRight(diff) {
          setActiveColumnIndex((activeColumnIndex + diff + columnsCount) % columnsCount);
        },
        onUpDown: function onUpDown(diff) {
          if (activeColumnIndex === -1) {
            setActiveColumnIndex(0);
          } else if (bodyOperationRef.current) {
            bodyOperationRef.current.onUpDown(diff);
          }
        },
        onEnter: function onEnter() {
          onSelect(value || generateConfig.getNow(), 'key');
          setActiveColumnIndex(-1);
        }
      });
    },
    onBlur: function onBlur() {
      setActiveColumnIndex(-1);
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(panelPrefixCls, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(panelPrefixCls, "-active"), active))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_TimeHeader__WEBPACK_IMPORTED_MODULE_5__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    format: format,
    prefixCls: prefixCls
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_TimeBody__WEBPACK_IMPORTED_MODULE_6__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    activeColumnIndex: activeColumnIndex,
    operationRef: bodyOperationRef
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (TimePanel);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/WeekPanel/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/WeekPanel/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _DatePanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../DatePanel */ "./node_modules/rc-picker/es/panels/DatePanel/index.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");







function WeekPanel(props) {
  var prefixCls = props.prefixCls,
      generateConfig = props.generateConfig,
      locale = props.locale,
      value = props.value; // Render additional column

  var cellPrefixCls = "".concat(prefixCls, "-cell");

  var prefixColumn = function prefixColumn(date) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("td", {
      key: "week",
      className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(cellPrefixCls, "".concat(cellPrefixCls, "-week"))
    }, generateConfig.locale.getWeek(locale.locale, date));
  }; // Add row className


  var rowPrefixCls = "".concat(prefixCls, "-week-panel-row");

  var rowClassName = function rowClassName(date) {
    return classnames__WEBPACK_IMPORTED_MODULE_3___default()(rowPrefixCls, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(rowPrefixCls, "-selected"), (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_5__.isSameWeek)(generateConfig, locale.locale, value, date)));
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_DatePanel__WEBPACK_IMPORTED_MODULE_4__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    panelName: "week",
    prefixColumn: prefixColumn,
    rowClassName: rowClassName,
    keyboardConfig: {
      onLeftRight: null
    }
  }));
}

/* harmony default export */ __webpack_exports__["default"] = (WeekPanel);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/YearPanel/YearBody.js":
/*!****************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/YearPanel/YearBody.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "YEAR_COL_COUNT": function() { return /* binding */ YEAR_COL_COUNT; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! . */ "./node_modules/rc-picker/es/panels/YearPanel/index.js");
/* harmony import */ var _hooks_useCellClassName__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useCellClassName */ "./node_modules/rc-picker/es/hooks/useCellClassName.js");
/* harmony import */ var _utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/dateUtil */ "./node_modules/rc-picker/es/utils/dateUtil.js");
/* harmony import */ var _RangeContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../RangeContext */ "./node_modules/rc-picker/es/RangeContext.js");
/* harmony import */ var _PanelBody__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../PanelBody */ "./node_modules/rc-picker/es/panels/PanelBody.js");







var YEAR_COL_COUNT = 3;
var YEAR_ROW_COUNT = 4;

function YearBody(props) {
  var prefixCls = props.prefixCls,
      value = props.value,
      viewDate = props.viewDate,
      locale = props.locale,
      generateConfig = props.generateConfig;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_RangeContext__WEBPACK_IMPORTED_MODULE_5__.default),
      rangedValue = _React$useContext.rangedValue,
      hoverRangedValue = _React$useContext.hoverRangedValue;

  var yearPrefixCls = "".concat(prefixCls, "-cell"); // =============================== Year ===============================

  var yearNumber = generateConfig.getYear(viewDate);
  var startYear = Math.floor(yearNumber / ___WEBPACK_IMPORTED_MODULE_2__.YEAR_DECADE_COUNT) * ___WEBPACK_IMPORTED_MODULE_2__.YEAR_DECADE_COUNT;
  var endYear = startYear + ___WEBPACK_IMPORTED_MODULE_2__.YEAR_DECADE_COUNT - 1;
  var baseYear = generateConfig.setYear(viewDate, startYear - Math.ceil((YEAR_COL_COUNT * YEAR_ROW_COUNT - ___WEBPACK_IMPORTED_MODULE_2__.YEAR_DECADE_COUNT) / 2));

  var isInView = function isInView(date) {
    var currentYearNumber = generateConfig.getYear(date);
    return startYear <= currentYearNumber && currentYearNumber <= endYear;
  };

  var getCellClassName = (0,_hooks_useCellClassName__WEBPACK_IMPORTED_MODULE_3__.default)({
    cellPrefixCls: yearPrefixCls,
    value: value,
    generateConfig: generateConfig,
    rangedValue: rangedValue,
    hoverRangedValue: hoverRangedValue,
    isSameCell: function isSameCell(current, target) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__.isSameYear)(generateConfig, current, target);
    },
    isInView: isInView,
    offsetCell: function offsetCell(date, offset) {
      return generateConfig.addYear(date, offset);
    }
  });
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_PanelBody__WEBPACK_IMPORTED_MODULE_6__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    rowNum: YEAR_ROW_COUNT,
    colNum: YEAR_COL_COUNT,
    baseDate: baseYear,
    getCellText: generateConfig.getYear,
    getCellClassName: getCellClassName,
    getCellDate: generateConfig.addYear,
    titleCell: function titleCell(date) {
      return (0,_utils_dateUtil__WEBPACK_IMPORTED_MODULE_4__.formatValue)(date, {
        locale: locale,
        format: 'YYYY',
        generateConfig: generateConfig
      });
    }
  }));
}

/* harmony default export */ __webpack_exports__["default"] = (YearBody);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/YearPanel/YearHeader.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/YearPanel/YearHeader.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Header */ "./node_modules/rc-picker/es/panels/Header.js");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! . */ "./node_modules/rc-picker/es/panels/YearPanel/index.js");
/* harmony import */ var _PanelContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../PanelContext */ "./node_modules/rc-picker/es/PanelContext.js");






function YearHeader(props) {
  var prefixCls = props.prefixCls,
      generateConfig = props.generateConfig,
      viewDate = props.viewDate,
      onPrevDecade = props.onPrevDecade,
      onNextDecade = props.onNextDecade,
      onDecadeClick = props.onDecadeClick;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_PanelContext__WEBPACK_IMPORTED_MODULE_4__.default),
      hideHeader = _React$useContext.hideHeader;

  if (hideHeader) {
    return null;
  }

  var headerPrefixCls = "".concat(prefixCls, "-header");
  var yearNumber = generateConfig.getYear(viewDate);
  var startYear = Math.floor(yearNumber / ___WEBPACK_IMPORTED_MODULE_3__.YEAR_DECADE_COUNT) * ___WEBPACK_IMPORTED_MODULE_3__.YEAR_DECADE_COUNT;
  var endYear = startYear + ___WEBPACK_IMPORTED_MODULE_3__.YEAR_DECADE_COUNT - 1;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Header__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: headerPrefixCls,
    onSuperPrev: onPrevDecade,
    onSuperNext: onNextDecade
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("button", {
    type: "button",
    onClick: onDecadeClick,
    className: "".concat(prefixCls, "-decade-btn")
  }, startYear, "-", endYear));
}

/* harmony default export */ __webpack_exports__["default"] = (YearHeader);

/***/ }),

/***/ "./node_modules/rc-picker/es/panels/YearPanel/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-picker/es/panels/YearPanel/index.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "YEAR_DECADE_COUNT": function() { return /* binding */ YEAR_DECADE_COUNT; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _YearHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./YearHeader */ "./node_modules/rc-picker/es/panels/YearPanel/YearHeader.js");
/* harmony import */ var _YearBody__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./YearBody */ "./node_modules/rc-picker/es/panels/YearPanel/YearBody.js");
/* harmony import */ var _utils_uiUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/uiUtil */ "./node_modules/rc-picker/es/utils/uiUtil.js");





var YEAR_DECADE_COUNT = 10;

function YearPanel(props) {
  var prefixCls = props.prefixCls,
      operationRef = props.operationRef,
      onViewDateChange = props.onViewDateChange,
      generateConfig = props.generateConfig,
      value = props.value,
      viewDate = props.viewDate,
      sourceMode = props.sourceMode,
      _onSelect = props.onSelect,
      onPanelChange = props.onPanelChange;
  var panelPrefixCls = "".concat(prefixCls, "-year-panel"); // ======================= Keyboard =======================

  operationRef.current = {
    onKeyDown: function onKeyDown(event) {
      return (0,_utils_uiUtil__WEBPACK_IMPORTED_MODULE_4__.createKeyDownHandler)(event, {
        onLeftRight: function onLeftRight(diff) {
          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');
        },
        onCtrlLeftRight: function onCtrlLeftRight(diff) {
          _onSelect(generateConfig.addYear(value || viewDate, diff * YEAR_DECADE_COUNT), 'key');
        },
        onUpDown: function onUpDown(diff) {
          _onSelect(generateConfig.addYear(value || viewDate, diff * _YearBody__WEBPACK_IMPORTED_MODULE_3__.YEAR_COL_COUNT), 'key');
        },
        onEnter: function onEnter() {
          onPanelChange(sourceMode === 'date' ? 'date' : 'month', value || viewDate);
        }
      });
    }
  }; // ==================== View Operation ====================

  var onDecadeChange = function onDecadeChange(diff) {
    var newDate = generateConfig.addYear(viewDate, diff * 10);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    className: panelPrefixCls
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_YearHeader__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    onPrevDecade: function onPrevDecade() {
      onDecadeChange(-1);
    },
    onNextDecade: function onNextDecade() {
      onDecadeChange(1);
    },
    onDecadeClick: function onDecadeClick() {
      onPanelChange('decade', viewDate);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_YearBody__WEBPACK_IMPORTED_MODULE_3__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, props, {
    prefixCls: prefixCls,
    onSelect: function onSelect(date) {
      onPanelChange(sourceMode === 'date' ? 'date' : 'month', date);

      _onSelect(date, 'mouse');
    }
  })));
}

/* harmony default export */ __webpack_exports__["default"] = (YearPanel);

/***/ }),

/***/ "./node_modules/rc-picker/es/utils/dateUtil.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-picker/es/utils/dateUtil.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "WEEK_DAY_COUNT": function() { return /* binding */ WEEK_DAY_COUNT; },
/* harmony export */   "isNullEqual": function() { return /* binding */ isNullEqual; },
/* harmony export */   "isSameDecade": function() { return /* binding */ isSameDecade; },
/* harmony export */   "isSameYear": function() { return /* binding */ isSameYear; },
/* harmony export */   "getQuarter": function() { return /* binding */ getQuarter; },
/* harmony export */   "isSameQuarter": function() { return /* binding */ isSameQuarter; },
/* harmony export */   "isSameMonth": function() { return /* binding */ isSameMonth; },
/* harmony export */   "isSameDate": function() { return /* binding */ isSameDate; },
/* harmony export */   "isSameTime": function() { return /* binding */ isSameTime; },
/* harmony export */   "isSameWeek": function() { return /* binding */ isSameWeek; },
/* harmony export */   "isEqual": function() { return /* binding */ isEqual; },
/* harmony export */   "isInRange": function() { return /* binding */ isInRange; },
/* harmony export */   "getWeekStartDate": function() { return /* binding */ getWeekStartDate; },
/* harmony export */   "getClosingViewDate": function() { return /* binding */ getClosingViewDate; },
/* harmony export */   "formatValue": function() { return /* binding */ formatValue; },
/* harmony export */   "parseValue": function() { return /* binding */ parseValue; },
/* harmony export */   "getCellDateDisabled": function() { return /* binding */ getCellDateDisabled; }
/* harmony export */ });
/* harmony import */ var _panels_DecadePanel_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../panels/DecadePanel/index */ "./node_modules/rc-picker/es/panels/DecadePanel/index.js");

var WEEK_DAY_COUNT = 7;
function isNullEqual(value1, value2) {
  if (!value1 && !value2) {
    return true;
  }

  if (!value1 || !value2) {
    return false;
  }

  return undefined;
}
function isSameDecade(generateConfig, decade1, decade2) {
  var equal = isNullEqual(decade1, decade2);

  if (typeof equal === 'boolean') {
    return equal;
  }

  var num1 = Math.floor(generateConfig.getYear(decade1) / 10);
  var num2 = Math.floor(generateConfig.getYear(decade2) / 10);
  return num1 === num2;
}
function isSameYear(generateConfig, year1, year2) {
  var equal = isNullEqual(year1, year2);

  if (typeof equal === 'boolean') {
    return equal;
  }

  return generateConfig.getYear(year1) === generateConfig.getYear(year2);
}
function getQuarter(generateConfig, date) {
  var quota = Math.floor(generateConfig.getMonth(date) / 3);
  return quota + 1;
}
function isSameQuarter(generateConfig, quarter1, quarter2) {
  var equal = isNullEqual(quarter1, quarter2);

  if (typeof equal === 'boolean') {
    return equal;
  }

  return isSameYear(generateConfig, quarter1, quarter2) && getQuarter(generateConfig, quarter1) === getQuarter(generateConfig, quarter2);
}
function isSameMonth(generateConfig, month1, month2) {
  var equal = isNullEqual(month1, month2);

  if (typeof equal === 'boolean') {
    return equal;
  }

  return isSameYear(generateConfig, month1, month2) && generateConfig.getMonth(month1) === generateConfig.getMonth(month2);
}
function isSameDate(generateConfig, date1, date2) {
  var equal = isNullEqual(date1, date2);

  if (typeof equal === 'boolean') {
    return equal;
  }

  return generateConfig.getYear(date1) === generateConfig.getYear(date2) && generateConfig.getMonth(date1) === generateConfig.getMonth(date2) && generateConfig.getDate(date1) === generateConfig.getDate(date2);
}
function isSameTime(generateConfig, time1, time2) {
  var equal = isNullEqual(time1, time2);

  if (typeof equal === 'boolean') {
    return equal;
  }

  return generateConfig.getHour(time1) === generateConfig.getHour(time2) && generateConfig.getMinute(time1) === generateConfig.getMinute(time2) && generateConfig.getSecond(time1) === generateConfig.getSecond(time2);
}
function isSameWeek(generateConfig, locale, date1, date2) {
  var equal = isNullEqual(date1, date2);

  if (typeof equal === 'boolean') {
    return equal;
  }

  return generateConfig.locale.getWeek(locale, date1) === generateConfig.locale.getWeek(locale, date2);
}
function isEqual(generateConfig, value1, value2) {
  return isSameDate(generateConfig, value1, value2) && isSameTime(generateConfig, value1, value2);
}
/** Between in date but not equal of date */

function isInRange(generateConfig, startDate, endDate, current) {
  if (!startDate || !endDate || !current) {
    return false;
  }

  return !isSameDate(generateConfig, startDate, current) && !isSameDate(generateConfig, endDate, current) && generateConfig.isAfter(current, startDate) && generateConfig.isAfter(endDate, current);
}
function getWeekStartDate(locale, generateConfig, value) {
  var weekFirstDay = generateConfig.locale.getWeekFirstDay(locale);
  var monthStartDate = generateConfig.setDate(value, 1);
  var startDateWeekDay = generateConfig.getWeekDay(monthStartDate);
  var alignStartDate = generateConfig.addDate(monthStartDate, weekFirstDay - startDateWeekDay);

  if (generateConfig.getMonth(alignStartDate) === generateConfig.getMonth(value) && generateConfig.getDate(alignStartDate) > 1) {
    alignStartDate = generateConfig.addDate(alignStartDate, -7);
  }

  return alignStartDate;
}
function getClosingViewDate(viewDate, picker, generateConfig) {
  var offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;

  switch (picker) {
    case 'year':
      return generateConfig.addYear(viewDate, offset * 10);

    case 'quarter':
    case 'month':
      return generateConfig.addYear(viewDate, offset);

    default:
      return generateConfig.addMonth(viewDate, offset);
  }
}
function formatValue(value, _ref) {
  var generateConfig = _ref.generateConfig,
      locale = _ref.locale,
      format = _ref.format;
  return typeof format === 'function' ? format(value) : generateConfig.locale.format(locale.locale, value, format);
}
function parseValue(value, _ref2) {
  var generateConfig = _ref2.generateConfig,
      locale = _ref2.locale,
      formatList = _ref2.formatList;

  if (!value || typeof formatList[0] === 'function') {
    return null;
  }

  return generateConfig.locale.parse(locale.locale, value, formatList);
} // eslint-disable-next-line consistent-return

function getCellDateDisabled(_ref3) {
  var cellDate = _ref3.cellDate,
      mode = _ref3.mode,
      disabledDate = _ref3.disabledDate,
      generateConfig = _ref3.generateConfig;
  if (!disabledDate) return false; // Whether cellDate is disabled in range

  var getDisabledFromRange = function getDisabledFromRange(currentMode, start, end) {
    var current = start;

    while (current <= end) {
      var date = void 0;

      switch (currentMode) {
        case 'date':
          {
            date = generateConfig.setDate(cellDate, current);

            if (!disabledDate(date)) {
              return false;
            }

            break;
          }

        case 'month':
          {
            date = generateConfig.setMonth(cellDate, current);

            if (!getCellDateDisabled({
              cellDate: date,
              mode: 'month',
              generateConfig: generateConfig,
              disabledDate: disabledDate
            })) {
              return false;
            }

            break;
          }

        case 'year':
          {
            date = generateConfig.setYear(cellDate, current);

            if (!getCellDateDisabled({
              cellDate: date,
              mode: 'year',
              generateConfig: generateConfig,
              disabledDate: disabledDate
            })) {
              return false;
            }

            break;
          }
      }

      current += 1;
    }

    return true;
  };

  switch (mode) {
    case 'date':
    case 'week':
      {
        return disabledDate(cellDate);
      }

    case 'month':
      {
        var startDate = 1;
        var endDate = generateConfig.getDate(generateConfig.getEndDate(cellDate));
        return getDisabledFromRange('date', startDate, endDate);
      }

    case 'quarter':
      {
        var startMonth = Math.floor(generateConfig.getMonth(cellDate) / 3) * 3;
        var endMonth = startMonth + 2;
        return getDisabledFromRange('month', startMonth, endMonth);
      }

    case 'year':
      {
        return getDisabledFromRange('month', 0, 11);
      }

    case 'decade':
      {
        var year = generateConfig.getYear(cellDate);
        var startYear = Math.floor(year / _panels_DecadePanel_index__WEBPACK_IMPORTED_MODULE_0__.DECADE_UNIT_DIFF) * _panels_DecadePanel_index__WEBPACK_IMPORTED_MODULE_0__.DECADE_UNIT_DIFF;
        var endYear = startYear + _panels_DecadePanel_index__WEBPACK_IMPORTED_MODULE_0__.DECADE_UNIT_DIFF - 1;
        return getDisabledFromRange('year', startYear, endYear);
      }
  }
}

/***/ }),

/***/ "./node_modules/rc-picker/es/utils/getExtraFooter.js":
/*!***********************************************************!*\
  !*** ./node_modules/rc-picker/es/utils/getExtraFooter.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ getExtraFooter; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

function getExtraFooter(prefixCls, mode, renderExtraFooter) {
  if (!renderExtraFooter) {
    return null;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
    className: "".concat(prefixCls, "-footer-extra")
  }, renderExtraFooter(mode));
}

/***/ }),

/***/ "./node_modules/rc-picker/es/utils/getRanges.js":
/*!******************************************************!*\
  !*** ./node_modules/rc-picker/es/utils/getRanges.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ getRanges; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");

function getRanges(_ref) {
  var prefixCls = _ref.prefixCls,
      _ref$rangeList = _ref.rangeList,
      rangeList = _ref$rangeList === void 0 ? [] : _ref$rangeList,
      _ref$components = _ref.components,
      components = _ref$components === void 0 ? {} : _ref$components,
      needConfirmButton = _ref.needConfirmButton,
      onNow = _ref.onNow,
      onOk = _ref.onOk,
      okDisabled = _ref.okDisabled,
      showNow = _ref.showNow,
      locale = _ref.locale;
  var presetNode;
  var okNode;

  if (rangeList.length) {
    var Item = components.rangeItem || 'span';
    presetNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, rangeList.map(function (_ref2) {
      var label = _ref2.label,
          onClick = _ref2.onClick,
          onMouseEnter = _ref2.onMouseEnter,
          onMouseLeave = _ref2.onMouseLeave;
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("li", {
        key: label,
        className: "".concat(prefixCls, "-preset")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Item, {
        onClick: onClick,
        onMouseEnter: onMouseEnter,
        onMouseLeave: onMouseLeave
      }, label));
    }));
  }

  if (needConfirmButton) {
    var Button = components.button || 'button';

    if (onNow && !presetNode && showNow !== false) {
      presetNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("li", {
        className: "".concat(prefixCls, "-now")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("a", {
        className: "".concat(prefixCls, "-now-btn"),
        onClick: onNow
      }, locale.now));
    }

    okNode = needConfirmButton && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("li", {
      className: "".concat(prefixCls, "-ok")
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Button, {
      disabled: okDisabled,
      onClick: onOk
    }, locale.ok));
  }

  if (!presetNode && !okNode) {
    return null;
  }

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement("ul", {
    className: "".concat(prefixCls, "-ranges")
  }, presetNode, okNode);
}

/***/ }),

/***/ "./node_modules/rc-picker/es/utils/miscUtil.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-picker/es/utils/miscUtil.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "leftPad": function() { return /* binding */ leftPad; },
/* harmony export */   "tuple": function() { return /* binding */ tuple; },
/* harmony export */   "toArray": function() { return /* binding */ toArray; },
/* harmony export */   "default": function() { return /* binding */ getDataOrAriaProps; },
/* harmony export */   "getValue": function() { return /* binding */ getValue; },
/* harmony export */   "updateValues": function() { return /* binding */ updateValues; }
/* harmony export */ });
function leftPad(str, length) {
  var fill = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '0';
  var current = String(str);

  while (current.length < length) {
    current = "".concat(fill).concat(str);
  }

  return current;
}
var tuple = function tuple() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }

  return args;
};
function toArray(val) {
  if (val === null || val === undefined) {
    return [];
  }

  return Array.isArray(val) ? val : [val];
}
function getDataOrAriaProps(props) {
  var retProps = {};
  Object.keys(props).forEach(function (key) {
    if ((key.substr(0, 5) === 'data-' || key.substr(0, 5) === 'aria-' || key === 'role' || key === 'name') && key.substr(0, 7) !== 'data-__') {
      retProps[key] = props[key];
    }
  });
  return retProps;
}
function getValue(values, index) {
  return values ? values[index] : null;
}
function updateValues(values, value, index) {
  var newValues = [getValue(values, 0), getValue(values, 1)];
  newValues[index] = typeof value === 'function' ? value(newValues[index]) : value;

  if (!newValues[0] && !newValues[1]) {
    return null;
  }

  return newValues;
}

/***/ }),

/***/ "./node_modules/rc-picker/es/utils/timeUtil.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-picker/es/utils/timeUtil.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "setTime": function() { return /* binding */ setTime; },
/* harmony export */   "setDateTime": function() { return /* binding */ setDateTime; },
/* harmony export */   "getLowerBoundTime": function() { return /* binding */ getLowerBoundTime; },
/* harmony export */   "getLastDay": function() { return /* binding */ getLastDay; }
/* harmony export */ });
function setTime(generateConfig, date, hour, minute, second) {
  var nextTime = generateConfig.setHour(date, hour);
  nextTime = generateConfig.setMinute(nextTime, minute);
  nextTime = generateConfig.setSecond(nextTime, second);
  return nextTime;
}
function setDateTime(generateConfig, date, defaultDate) {
  if (!defaultDate) {
    return date;
  }

  var newDate = date;
  newDate = generateConfig.setHour(newDate, generateConfig.getHour(defaultDate));
  newDate = generateConfig.setMinute(newDate, generateConfig.getMinute(defaultDate));
  newDate = generateConfig.setSecond(newDate, generateConfig.getSecond(defaultDate));
  return newDate;
}
function getLowerBoundTime(hour, minute, second, hourStep, minuteStep, secondStep) {
  var lowerBoundHour = Math.floor(hour / hourStep) * hourStep;

  if (lowerBoundHour < hour) {
    return [lowerBoundHour, 60 - minuteStep, 60 - secondStep];
  }

  var lowerBoundMinute = Math.floor(minute / minuteStep) * minuteStep;

  if (lowerBoundMinute < minute) {
    return [lowerBoundHour, lowerBoundMinute, 60 - secondStep];
  }

  var lowerBoundSecond = Math.floor(second / secondStep) * secondStep;
  return [lowerBoundHour, lowerBoundMinute, lowerBoundSecond];
}
function getLastDay(generateConfig, date) {
  var year = generateConfig.getYear(date);
  var month = generateConfig.getMonth(date) + 1;
  var endDate = generateConfig.getEndDate(generateConfig.getFixedDate("".concat(year, "-").concat(month, "-01")));
  var lastDay = generateConfig.getDate(endDate);
  var monthShow = month < 10 ? "0".concat(month) : "".concat(month);
  return "".concat(year, "-").concat(monthShow, "-").concat(lastDay);
}

/***/ }),

/***/ "./node_modules/rc-picker/es/utils/uiUtil.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-picker/es/utils/uiUtil.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "waitElementReady": function() { return /* binding */ waitElementReady; },
/* harmony export */   "scrollTo": function() { return /* binding */ scrollTo; },
/* harmony export */   "createKeyDownHandler": function() { return /* binding */ createKeyDownHandler; },
/* harmony export */   "getDefaultFormat": function() { return /* binding */ getDefaultFormat; },
/* harmony export */   "getInputSize": function() { return /* binding */ getInputSize; },
/* harmony export */   "addGlobalMouseDownEvent": function() { return /* binding */ addGlobalMouseDownEvent; },
/* harmony export */   "getTargetFromEvent": function() { return /* binding */ getTargetFromEvent; },
/* harmony export */   "PickerModeMap": function() { return /* binding */ PickerModeMap; },
/* harmony export */   "elementsContains": function() { return /* binding */ elementsContains; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/KeyCode */ "./node_modules/rc-util/es/KeyCode.js");
/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/raf */ "./node_modules/rc-util/es/raf.js");
/* harmony import */ var rc_util_es_Dom_isVisible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/Dom/isVisible */ "./node_modules/rc-util/es/Dom/isVisible.js");




var scrollIds = new Map();
/** Trigger when element is visible in view */

function waitElementReady(element, callback) {
  var id;

  function tryOrNextFrame() {
    if ((0,rc_util_es_Dom_isVisible__WEBPACK_IMPORTED_MODULE_3__.default)(element)) {
      callback();
    } else {
      id = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_2__.default)(function () {
        tryOrNextFrame();
      });
    }
  }

  tryOrNextFrame();
  return function () {
    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_2__.default.cancel(id);
  };
}
/* eslint-disable no-param-reassign */

function scrollTo(element, to, duration) {
  if (scrollIds.get(element)) {
    cancelAnimationFrame(scrollIds.get(element));
  } // jump to target if duration zero


  if (duration <= 0) {
    scrollIds.set(element, requestAnimationFrame(function () {
      element.scrollTop = to;
    }));
    return;
  }

  var difference = to - element.scrollTop;
  var perTick = difference / duration * 10;
  scrollIds.set(element, requestAnimationFrame(function () {
    element.scrollTop += perTick;

    if (element.scrollTop !== to) {
      scrollTo(element, to, duration - 10);
    }
  }));
}
function createKeyDownHandler(event, _ref) {
  var onLeftRight = _ref.onLeftRight,
      onCtrlLeftRight = _ref.onCtrlLeftRight,
      onUpDown = _ref.onUpDown,
      onPageUpDown = _ref.onPageUpDown,
      onEnter = _ref.onEnter;
  var which = event.which,
      ctrlKey = event.ctrlKey,
      metaKey = event.metaKey;

  switch (which) {
    case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_1__.default.LEFT:
      if (ctrlKey || metaKey) {
        if (onCtrlLeftRight) {
          onCtrlLeftRight(-1);
          return true;
        }
      } else if (onLeftRight) {
        onLeftRight(-1);
        return true;
      }
      /* istanbul ignore next */


      break;

    case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_1__.default.RIGHT:
      if (ctrlKey || metaKey) {
        if (onCtrlLeftRight) {
          onCtrlLeftRight(1);
          return true;
        }
      } else if (onLeftRight) {
        onLeftRight(1);
        return true;
      }
      /* istanbul ignore next */


      break;

    case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_1__.default.UP:
      if (onUpDown) {
        onUpDown(-1);
        return true;
      }
      /* istanbul ignore next */


      break;

    case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_1__.default.DOWN:
      if (onUpDown) {
        onUpDown(1);
        return true;
      }
      /* istanbul ignore next */


      break;

    case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_1__.default.PAGE_UP:
      if (onPageUpDown) {
        onPageUpDown(-1);
        return true;
      }
      /* istanbul ignore next */


      break;

    case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_1__.default.PAGE_DOWN:
      if (onPageUpDown) {
        onPageUpDown(1);
        return true;
      }
      /* istanbul ignore next */


      break;

    case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_1__.default.ENTER:
      if (onEnter) {
        onEnter();
        return true;
      }
      /* istanbul ignore next */


      break;
  }

  return false;
} // ===================== Format =====================

function getDefaultFormat(format, picker, showTime, use12Hours) {
  var mergedFormat = format;

  if (!mergedFormat) {
    switch (picker) {
      case 'time':
        mergedFormat = use12Hours ? 'hh:mm:ss a' : 'HH:mm:ss';
        break;

      case 'week':
        mergedFormat = 'gggg-wo';
        break;

      case 'month':
        mergedFormat = 'YYYY-MM';
        break;

      case 'quarter':
        mergedFormat = 'YYYY-[Q]Q';
        break;

      case 'year':
        mergedFormat = 'YYYY';
        break;

      default:
        mergedFormat = showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
    }
  }

  return mergedFormat;
}
function getInputSize(picker, format, generateConfig) {
  var defaultSize = picker === 'time' ? 8 : 10;
  var length = typeof format === 'function' ? format(generateConfig.getNow()).length : format.length;
  return Math.max(defaultSize, length) + 2;
}
var globalClickFunc = null;
var clickCallbacks = new Set();
function addGlobalMouseDownEvent(callback) {
  if (!globalClickFunc && typeof window !== 'undefined' && window.addEventListener) {
    globalClickFunc = function globalClickFunc(e) {
      // Clone a new list to avoid repeat trigger events
      (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__.default)(clickCallbacks).forEach(function (queueFunc) {
        queueFunc(e);
      });
    };

    window.addEventListener('mousedown', globalClickFunc);
  }

  clickCallbacks.add(callback);
  return function () {
    clickCallbacks.delete(callback);

    if (clickCallbacks.size === 0) {
      window.removeEventListener('mousedown', globalClickFunc);
      globalClickFunc = null;
    }
  };
}
function getTargetFromEvent(e) {
  var target = e.target; // get target if in shadow dom

  if (e.composed && target.shadowRoot) {
    var _e$composedPath;

    return ((_e$composedPath = e.composedPath) === null || _e$composedPath === void 0 ? void 0 : _e$composedPath.call(e)[0]) || target;
  }

  return target;
} // ====================== Mode ======================

var getYearNextMode = function getYearNextMode(next) {
  if (next === 'month' || next === 'date') {
    return 'year';
  }

  return next;
};

var getMonthNextMode = function getMonthNextMode(next) {
  if (next === 'date') {
    return 'month';
  }

  return next;
};

var getQuarterNextMode = function getQuarterNextMode(next) {
  if (next === 'month' || next === 'date') {
    return 'quarter';
  }

  return next;
};

var getWeekNextMode = function getWeekNextMode(next) {
  if (next === 'date') {
    return 'week';
  }

  return next;
};

var PickerModeMap = {
  year: getYearNextMode,
  month: getMonthNextMode,
  quarter: getQuarterNextMode,
  week: getWeekNextMode,
  time: null,
  date: null
};
function elementsContains(elements, target) {
  return elements.some(function (ele) {
    return ele && ele.contains(target);
  });
}

/***/ })

}]);