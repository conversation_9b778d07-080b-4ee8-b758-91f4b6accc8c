var ue=Object.defineProperty;var V=Object.assign;var w=(A,S,y)=>(typeof S!="symbol"&&(S+=""),S in A?ue(A,S,{enumerable:!0,configurable:!0,writable:!0,value:y}):A[S]=y);var m=(A,S,y)=>new Promise((K,P)=>{var I=x=>{try{F(y.next(x))}catch(T){P(T)}},R=x=>{try{F(y.throw(x))}catch(T){P(T)}},F=x=>x.done?K(x.value):Promise.resolve(x.value).then(I,R);F((y=y.apply(A,S)).next())});(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8401],{78401:function(A,S,y){"use strict";y.r(S),y.d(S,{Adapter:function(){return k},CodeActionAdaptor:function(){return q},DefinitionAdapter:function(){return G},DiagnosticsAdapter:function(){return U},FormatAdapter:function(){return Y},FormatHelper:function(){return C},FormatOnTypeAdapter:function(){return Z},InlayHintsAdapter:function(){return te},Kind:function(){return c},LibFiles:function(){return B},OccurrencesAdapter:function(){return J},OutlineAdapter:function(){return X},QuickInfoAdapter:function(){return z},ReferenceAdapter:function(){return Q},RenameAdapter:function(){return ee},SignatureHelpAdapter:function(){return E},SuggestAdapter:function(){return O},WorkerManager:function(){return j},flattenDiagnosticMessageText:function(){return N},getJavaScriptWorker:function(){return oe},getTypeScriptWorker:function(){return ce},setupJavaScript:function(){return ae},setupTypeScript:function(){return ne}});var K=y(26565),P=y(39585);/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var I=Object.defineProperty,R=Object.getOwnPropertyDescriptor,F=Object.getOwnPropertyNames,x=Object.prototype.hasOwnProperty,T=(e,t,r)=>t in e?I(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ie=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of F(t))!x.call(e,a)&&(r||a!=="default")&&I(e,a,{get:()=>t[a],enumerable:!(n=R(t,a))||n.enumerable});return e},f=(e,t,r)=>(T(e,typeof t!="symbol"?t+"":t,r),r),i={};ie(i,K);var j=class{constructor(e,t){w(this,"_modeId");w(this,"_defaults");w(this,"_configChangeListener");w(this,"_updateExtraLibsToken");w(this,"_extraLibsChangeListener");w(this,"_worker");w(this,"_client");this._modeId=e,this._defaults=t,this._worker=null,this._client=null,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker()),this._updateExtraLibsToken=0,this._extraLibsChangeListener=this._defaults.onDidExtraLibsChange(()=>this._updateExtraLibs())}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){this._configChangeListener.dispose(),this._extraLibsChangeListener.dispose(),this._stopWorker()}_updateExtraLibs(){return m(this,null,function*(){if(!this._worker)return;const e=++this._updateExtraLibsToken,t=yield this._worker.getProxy();this._updateExtraLibsToken===e&&t.updateExtraLibs(this._defaults.getExtraLibs())})}_getClient(){if(!this._client){this._worker=i.editor.createWebWorker({moduleId:"vs/language/typescript/tsWorker",label:this._modeId,keepIdleModels:!0,createData:{compilerOptions:this._defaults.getCompilerOptions(),extraLibs:this._defaults.getExtraLibs(),customWorkerPath:this._defaults.workerOptions.customWorkerPath,inlayHintsOptions:this._defaults.inlayHintsOptions}});let e=this._worker.getProxy();this._defaults.getEagerModelSync()&&(e=e.then(t=>this._worker?this._worker.withSyncedResources(i.editor.getModels().filter(r=>r.getLanguageId()===this._modeId).map(r=>r.uri)):t)),this._client=e}return this._client}getLanguageServiceWorker(...e){let t;return this._getClient().then(r=>{t=r}).then(r=>{if(this._worker)return this._worker.withSyncedResources(e)}).then(r=>t)}},o={};o["lib.d.ts"]=!0,o["lib.dom.d.ts"]=!0,o["lib.dom.iterable.d.ts"]=!0,o["lib.es2015.collection.d.ts"]=!0,o["lib.es2015.core.d.ts"]=!0,o["lib.es2015.d.ts"]=!0,o["lib.es2015.generator.d.ts"]=!0,o["lib.es2015.iterable.d.ts"]=!0,o["lib.es2015.promise.d.ts"]=!0,o["lib.es2015.proxy.d.ts"]=!0,o["lib.es2015.reflect.d.ts"]=!0,o["lib.es2015.symbol.d.ts"]=!0,o["lib.es2015.symbol.wellknown.d.ts"]=!0,o["lib.es2016.array.include.d.ts"]=!0,o["lib.es2016.d.ts"]=!0,o["lib.es2016.full.d.ts"]=!0,o["lib.es2017.d.ts"]=!0,o["lib.es2017.full.d.ts"]=!0,o["lib.es2017.intl.d.ts"]=!0,o["lib.es2017.object.d.ts"]=!0,o["lib.es2017.sharedmemory.d.ts"]=!0,o["lib.es2017.string.d.ts"]=!0,o["lib.es2017.typedarrays.d.ts"]=!0,o["lib.es2018.asyncgenerator.d.ts"]=!0,o["lib.es2018.asynciterable.d.ts"]=!0,o["lib.es2018.d.ts"]=!0,o["lib.es2018.full.d.ts"]=!0,o["lib.es2018.intl.d.ts"]=!0,o["lib.es2018.promise.d.ts"]=!0,o["lib.es2018.regexp.d.ts"]=!0,o["lib.es2019.array.d.ts"]=!0,o["lib.es2019.d.ts"]=!0,o["lib.es2019.full.d.ts"]=!0,o["lib.es2019.object.d.ts"]=!0,o["lib.es2019.string.d.ts"]=!0,o["lib.es2019.symbol.d.ts"]=!0,o["lib.es2020.bigint.d.ts"]=!0,o["lib.es2020.d.ts"]=!0,o["lib.es2020.full.d.ts"]=!0,o["lib.es2020.intl.d.ts"]=!0,o["lib.es2020.promise.d.ts"]=!0,o["lib.es2020.sharedmemory.d.ts"]=!0,o["lib.es2020.string.d.ts"]=!0,o["lib.es2020.symbol.wellknown.d.ts"]=!0,o["lib.es2021.d.ts"]=!0,o["lib.es2021.full.d.ts"]=!0,o["lib.es2021.intl.d.ts"]=!0,o["lib.es2021.promise.d.ts"]=!0,o["lib.es2021.string.d.ts"]=!0,o["lib.es2021.weakref.d.ts"]=!0,o["lib.es5.d.ts"]=!0,o["lib.es6.d.ts"]=!0,o["lib.esnext.d.ts"]=!0,o["lib.esnext.full.d.ts"]=!0,o["lib.esnext.intl.d.ts"]=!0,o["lib.esnext.promise.d.ts"]=!0,o["lib.esnext.string.d.ts"]=!0,o["lib.esnext.weakref.d.ts"]=!0,o["lib.scripthost.d.ts"]=!0,o["lib.webworker.d.ts"]=!0,o["lib.webworker.importscripts.d.ts"]=!0,o["lib.webworker.iterable.d.ts"]=!0;function N(e,t,r=0){if(typeof e=="string")return e;if(e===void 0)return"";let n="";if(r){n+=t;for(let a=0;a<r;a++)n+="  "}if(n+=e.messageText,r++,e.next)for(const a of e.next)n+=N(a,t,r);return n}function v(e){return e?e.map(t=>t.text).join(""):""}var k=class{constructor(e){this._worker=e}_textSpanToRange(e,t){let r=e.getPositionAt(t.start),n=e.getPositionAt(t.start+t.length),{lineNumber:a,column:d}=r,{lineNumber:l,column:s}=n;return{startLineNumber:a,startColumn:d,endLineNumber:l,endColumn:s}}},B=class{constructor(e){w(this,"_libFiles");w(this,"_hasFetchedLibFiles");w(this,"_fetchLibFilesPromise");this._worker=e,this._libFiles={},this._hasFetchedLibFiles=!1,this._fetchLibFilesPromise=null}isLibFile(e){return e&&e.path.indexOf("/lib.")===0?!!o[e.path.slice(1)]:!1}getOrCreateModel(e){const t=i.Uri.parse(e),r=i.editor.getModel(t);if(r)return r;if(this.isLibFile(t)&&this._hasFetchedLibFiles)return i.editor.createModel(this._libFiles[t.path.slice(1)],"typescript",t);const n=P.typescriptDefaults.getExtraLibs()[e];return n?i.editor.createModel(n.content,"typescript",t):null}_containsLibFile(e){for(let t of e)if(this.isLibFile(t))return!0;return!1}fetchLibFilesIfNecessary(e){return m(this,null,function*(){!this._containsLibFile(e)||(yield this._fetchLibFiles())})}_fetchLibFiles(){return this._fetchLibFilesPromise||(this._fetchLibFilesPromise=this._worker().then(e=>e.getLibFiles()).then(e=>{this._hasFetchedLibFiles=!0,this._libFiles=e})),this._fetchLibFilesPromise}},U=class extends k{constructor(e,t,r,n){super(n);w(this,"_disposables",[]);w(this,"_listener",Object.create(null));this._libFiles=e,this._defaults=t,this._selector=r;const a=s=>{if(s.getLanguageId()!==r)return;const u=()=>{const{onlyVisible:b}=this._defaults.getDiagnosticsOptions();b?s.isAttachedToEditor()&&this._doValidate(s):this._doValidate(s)};let g;const p=s.onDidChangeContent(()=>{clearTimeout(g),g=window.setTimeout(u,500)}),h=s.onDidChangeAttached(()=>{const{onlyVisible:b}=this._defaults.getDiagnosticsOptions();b&&(s.isAttachedToEditor()?u():i.editor.setModelMarkers(s,this._selector,[]))});this._listener[s.uri.toString()]={dispose(){p.dispose(),h.dispose(),clearTimeout(g)}},u()},d=s=>{i.editor.setModelMarkers(s,this._selector,[]);const u=s.uri.toString();this._listener[u]&&(this._listener[u].dispose(),delete this._listener[u])};this._disposables.push(i.editor.onDidCreateModel(s=>a(s))),this._disposables.push(i.editor.onWillDisposeModel(d)),this._disposables.push(i.editor.onDidChangeModelLanguage(s=>{d(s.model),a(s.model)})),this._disposables.push({dispose(){for(const s of i.editor.getModels())d(s)}});const l=()=>{for(const s of i.editor.getModels())d(s),a(s)};this._disposables.push(this._defaults.onDidChange(l)),this._disposables.push(this._defaults.onDidExtraLibsChange(l)),i.editor.getModels().forEach(s=>a(s))}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables=[]}_doValidate(e){return m(this,null,function*(){const t=yield this._worker(e.uri);if(e.isDisposed())return;const r=[],{noSyntaxValidation:n,noSemanticValidation:a,noSuggestionDiagnostics:d}=this._defaults.getDiagnosticsOptions();n||r.push(t.getSyntacticDiagnostics(e.uri.toString())),a||r.push(t.getSemanticDiagnostics(e.uri.toString())),d||r.push(t.getSuggestionDiagnostics(e.uri.toString()));const l=yield Promise.all(r);if(!l||e.isDisposed())return;const s=l.reduce((g,p)=>p.concat(g),[]).filter(g=>(this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore||[]).indexOf(g.code)===-1),u=s.map(g=>g.relatedInformation||[]).reduce((g,p)=>p.concat(g),[]).map(g=>g.file?i.Uri.parse(g.file.fileName):null);yield this._libFiles.fetchLibFilesIfNecessary(u),!e.isDisposed()&&i.editor.setModelMarkers(e,this._selector,s.map(g=>this._convertDiagnostics(e,g)))})}_convertDiagnostics(e,t){const r=t.start||0,n=t.length||1,{lineNumber:a,column:d}=e.getPositionAt(r),{lineNumber:l,column:s}=e.getPositionAt(r+n),u=[];return t.reportsUnnecessary&&u.push(i.MarkerTag.Unnecessary),t.reportsDeprecated&&u.push(i.MarkerTag.Deprecated),{severity:this._tsDiagnosticCategoryToMarkerSeverity(t.category),startLineNumber:a,startColumn:d,endLineNumber:l,endColumn:s,message:N(t.messageText,`
`),code:t.code.toString(),tags:u,relatedInformation:this._convertRelatedInformation(e,t.relatedInformation)}}_convertRelatedInformation(e,t){if(!t)return[];const r=[];return t.forEach(n=>{let a=e;if(n.file&&(a=this._libFiles.getOrCreateModel(n.file.fileName)),!a)return;const d=n.start||0,l=n.length||1,{lineNumber:s,column:u}=a.getPositionAt(d),{lineNumber:g,column:p}=a.getPositionAt(d+l);r.push({resource:a.uri,startLineNumber:s,startColumn:u,endLineNumber:g,endColumn:p,message:N(n.messageText,`
`)})}),r}_tsDiagnosticCategoryToMarkerSeverity(e){switch(e){case 1:return i.MarkerSeverity.Error;case 3:return i.MarkerSeverity.Info;case 0:return i.MarkerSeverity.Warning;case 2:return i.MarkerSeverity.Hint}return i.MarkerSeverity.Info}},O=class extends k{get triggerCharacters(){return["."]}provideCompletionItems(e,t,r,n){return m(this,null,function*(){const a=e.getWordUntilPosition(t),d=new i.Range(t.lineNumber,a.startColumn,t.lineNumber,a.endColumn),l=e.uri,s=e.getOffsetAt(t),u=yield this._worker(l);if(e.isDisposed())return;const g=yield u.getCompletionsAtPosition(l.toString(),s);return!g||e.isDisposed()?void 0:{suggestions:g.entries.map(h=>{var L;let b=d;if(h.replacementSpan){const M=e.getPositionAt(h.replacementSpan.start),se=e.getPositionAt(h.replacementSpan.start+h.replacementSpan.length);b=new i.Range(M.lineNumber,M.column,se.lineNumber,se.column)}const D=[];return((L=h.kindModifiers)==null?void 0:L.indexOf("deprecated"))!==-1&&D.push(i.languages.CompletionItemTag.Deprecated),{uri:l,position:t,offset:s,range:b,label:h.name,insertText:h.name,sortText:h.sortText,kind:O.convertKind(h.kind),tags:D}})}})}resolveCompletionItem(e,t){return m(this,null,function*(){const r=e,n=r.uri,a=r.position,d=r.offset,s=yield(yield this._worker(n)).getCompletionEntryDetails(n.toString(),d,r.label);return s?{uri:n,position:a,label:s.name,kind:O.convertKind(s.kind),detail:v(s.displayParts),documentation:{value:O.createDocumentationString(s)}}:r})}static convertKind(e){switch(e){case c.primitiveType:case c.keyword:return i.languages.CompletionItemKind.Keyword;case c.variable:case c.localVariable:return i.languages.CompletionItemKind.Variable;case c.memberVariable:case c.memberGetAccessor:case c.memberSetAccessor:return i.languages.CompletionItemKind.Field;case c.function:case c.memberFunction:case c.constructSignature:case c.callSignature:case c.indexSignature:return i.languages.CompletionItemKind.Function;case c.enum:return i.languages.CompletionItemKind.Enum;case c.module:return i.languages.CompletionItemKind.Module;case c.class:return i.languages.CompletionItemKind.Class;case c.interface:return i.languages.CompletionItemKind.Interface;case c.warning:return i.languages.CompletionItemKind.File}return i.languages.CompletionItemKind.Property}static createDocumentationString(e){let t=v(e.documentation);if(e.tags)for(const r of e.tags)t+=`

${$(r)}`;return t}};function $(e){let t=`*@${e.name}*`;if(e.name==="param"&&e.text){const[r,...n]=e.text;t+=`\`${r.text}\``,n.length>0&&(t+=` \u2014 ${n.map(a=>a.text).join(" ")}`)}else Array.isArray(e.text)?t+=` \u2014 ${e.text.map(r=>r.text).join(" ")}`:e.text&&(t+=` \u2014 ${e.text}`);return t}var E=class extends k{constructor(){super(...arguments);w(this,"signatureHelpTriggerCharacters",["(",","])}static _toSignatureHelpTriggerReason(e){switch(e.triggerKind){case i.languages.SignatureHelpTriggerKind.TriggerCharacter:return e.triggerCharacter?e.isRetrigger?{kind:"retrigger",triggerCharacter:e.triggerCharacter}:{kind:"characterTyped",triggerCharacter:e.triggerCharacter}:{kind:"invoked"};case i.languages.SignatureHelpTriggerKind.ContentChange:return e.isRetrigger?{kind:"retrigger"}:{kind:"invoked"};case i.languages.SignatureHelpTriggerKind.Invoke:default:return{kind:"invoked"}}}provideSignatureHelp(e,t,r,n){return m(this,null,function*(){const a=e.uri,d=e.getOffsetAt(t),l=yield this._worker(a);if(e.isDisposed())return;const s=yield l.getSignatureHelpItems(a.toString(),d,{triggerReason:E._toSignatureHelpTriggerReason(n)});if(!s||e.isDisposed())return;const u={activeSignature:s.selectedItemIndex,activeParameter:s.argumentIndex,signatures:[]};return s.items.forEach(g=>{const p={label:"",parameters:[]};p.documentation={value:v(g.documentation)},p.label+=v(g.prefixDisplayParts),g.parameters.forEach((h,b,D)=>{const L=v(h.displayParts),M={label:L,documentation:{value:v(h.documentation)}};p.label+=L,p.parameters.push(M),b<D.length-1&&(p.label+=v(g.separatorDisplayParts))}),p.label+=v(g.suffixDisplayParts),u.signatures.push(p)}),{value:u,dispose(){}}})}},z=class extends k{provideHover(e,t,r){return m(this,null,function*(){const n=e.uri,a=e.getOffsetAt(t),d=yield this._worker(n);if(e.isDisposed())return;const l=yield d.getQuickInfoAtPosition(n.toString(),a);if(!l||e.isDisposed())return;const s=v(l.documentation),u=l.tags?l.tags.map(p=>$(p)).join(`  

`):"",g=v(l.displayParts);return{range:this._textSpanToRange(e,l.textSpan),contents:[{value:"```typescript\n"+g+"\n```\n"},{value:s+(u?`

`+u:"")}]}})}},J=class extends k{provideDocumentHighlights(e,t,r){return m(this,null,function*(){const n=e.uri,a=e.getOffsetAt(t),d=yield this._worker(n);if(e.isDisposed())return;const l=yield d.getOccurrencesAtPosition(n.toString(),a);if(!(!l||e.isDisposed()))return l.map(s=>({range:this._textSpanToRange(e,s.textSpan),kind:s.isWriteAccess?i.languages.DocumentHighlightKind.Write:i.languages.DocumentHighlightKind.Text}))})}},G=class extends k{constructor(e,t){super(t);this._libFiles=e}provideDefinition(e,t,r){return m(this,null,function*(){const n=e.uri,a=e.getOffsetAt(t),d=yield this._worker(n);if(e.isDisposed())return;const l=yield d.getDefinitionAtPosition(n.toString(),a);if(!l||e.isDisposed()||(yield this._libFiles.fetchLibFilesIfNecessary(l.map(u=>i.Uri.parse(u.fileName))),e.isDisposed()))return;const s=[];for(let u of l){const g=this._libFiles.getOrCreateModel(u.fileName);g&&s.push({uri:g.uri,range:this._textSpanToRange(g,u.textSpan)})}return s})}},Q=class extends k{constructor(e,t){super(t);this._libFiles=e}provideReferences(e,t,r,n){return m(this,null,function*(){const a=e.uri,d=e.getOffsetAt(t),l=yield this._worker(a);if(e.isDisposed())return;const s=yield l.getReferencesAtPosition(a.toString(),d);if(!s||e.isDisposed()||(yield this._libFiles.fetchLibFilesIfNecessary(s.map(g=>i.Uri.parse(g.fileName))),e.isDisposed()))return;const u=[];for(let g of s){const p=this._libFiles.getOrCreateModel(g.fileName);p&&u.push({uri:p.uri,range:this._textSpanToRange(p,g.textSpan)})}return u})}},X=class extends k{provideDocumentSymbols(e,t){return m(this,null,function*(){const r=e.uri,n=yield this._worker(r);if(e.isDisposed())return;const a=yield n.getNavigationBarItems(r.toString());if(!a||e.isDisposed())return;const d=(s,u,g)=>{let p={name:u.text,detail:"",kind:_[u.kind]||i.languages.SymbolKind.Variable,range:this._textSpanToRange(e,u.spans[0]),selectionRange:this._textSpanToRange(e,u.spans[0]),tags:[]};if(g&&(p.containerName=g),u.childItems&&u.childItems.length>0)for(let h of u.childItems)d(s,h,p.name);s.push(p)};let l=[];return a.forEach(s=>d(l,s)),l})}},c=class{};f(c,"unknown",""),f(c,"keyword","keyword"),f(c,"script","script"),f(c,"module","module"),f(c,"class","class"),f(c,"interface","interface"),f(c,"type","type"),f(c,"enum","enum"),f(c,"variable","var"),f(c,"localVariable","local var"),f(c,"function","function"),f(c,"localFunction","local function"),f(c,"memberFunction","method"),f(c,"memberGetAccessor","getter"),f(c,"memberSetAccessor","setter"),f(c,"memberVariable","property"),f(c,"constructorImplementation","constructor"),f(c,"callSignature","call"),f(c,"indexSignature","index"),f(c,"constructSignature","construct"),f(c,"parameter","parameter"),f(c,"typeParameter","type parameter"),f(c,"primitiveType","primitive type"),f(c,"label","label"),f(c,"alias","alias"),f(c,"const","const"),f(c,"let","let"),f(c,"warning","warning");var _=Object.create(null);_[c.module]=i.languages.SymbolKind.Module,_[c.class]=i.languages.SymbolKind.Class,_[c.enum]=i.languages.SymbolKind.Enum,_[c.interface]=i.languages.SymbolKind.Interface,_[c.memberFunction]=i.languages.SymbolKind.Method,_[c.memberVariable]=i.languages.SymbolKind.Property,_[c.memberGetAccessor]=i.languages.SymbolKind.Property,_[c.memberSetAccessor]=i.languages.SymbolKind.Property,_[c.variable]=i.languages.SymbolKind.Variable,_[c.const]=i.languages.SymbolKind.Variable,_[c.localVariable]=i.languages.SymbolKind.Variable,_[c.variable]=i.languages.SymbolKind.Variable,_[c.function]=i.languages.SymbolKind.Function,_[c.localFunction]=i.languages.SymbolKind.Function;var C=class extends k{static _convertOptions(e){return{ConvertTabsToSpaces:e.insertSpaces,TabSize:e.tabSize,IndentSize:e.tabSize,IndentStyle:2,NewLineCharacter:`
`,InsertSpaceAfterCommaDelimiter:!0,InsertSpaceAfterSemicolonInForStatements:!0,InsertSpaceBeforeAndAfterBinaryOperators:!0,InsertSpaceAfterKeywordsInControlFlowStatements:!0,InsertSpaceAfterFunctionKeywordForAnonymousFunctions:!0,InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis:!1,InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets:!1,InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces:!1,PlaceOpenBraceOnNewLineForControlBlocks:!1,PlaceOpenBraceOnNewLineForFunctions:!1}}_convertTextChanges(e,t){return{text:t.newText,range:this._textSpanToRange(e,t.span)}}},Y=class extends C{provideDocumentRangeFormattingEdits(e,t,r,n){return m(this,null,function*(){const a=e.uri,d=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),l=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),s=yield this._worker(a);if(e.isDisposed())return;const u=yield s.getFormattingEditsForRange(a.toString(),d,l,C._convertOptions(r));if(!(!u||e.isDisposed()))return u.map(g=>this._convertTextChanges(e,g))})}},Z=class extends C{get autoFormatTriggerCharacters(){return[";","}",`
`]}provideOnTypeFormattingEdits(e,t,r,n,a){return m(this,null,function*(){const d=e.uri,l=e.getOffsetAt(t),s=yield this._worker(d);if(e.isDisposed())return;const u=yield s.getFormattingEditsAfterKeystroke(d.toString(),l,r,C._convertOptions(n));if(!(!u||e.isDisposed()))return u.map(g=>this._convertTextChanges(e,g))})}},q=class extends C{provideCodeActions(e,t,r,n){return m(this,null,function*(){const a=e.uri,d=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),l=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),s=C._convertOptions(e.getOptions()),u=r.markers.filter(b=>b.code).map(b=>b.code).map(Number),g=yield this._worker(a);if(e.isDisposed())return;const p=yield g.getCodeFixesAtPosition(a.toString(),d,l,u,s);return!p||e.isDisposed()?{actions:[],dispose:()=>{}}:{actions:p.filter(b=>b.changes.filter(D=>D.isNewFile).length===0).map(b=>this._tsCodeFixActionToMonacoCodeAction(e,r,b)),dispose:()=>{}}})}_tsCodeFixActionToMonacoCodeAction(e,t,r){const n=[];for(const d of r.changes)for(const l of d.textChanges)n.push({resource:e.uri,edit:{range:this._textSpanToRange(e,l.span),text:l.newText}});return{title:r.description,edit:{edits:n},diagnostics:t.markers,kind:"quickfix"}}},ee=class extends k{constructor(e,t){super(t);this._libFiles=e}provideRenameEdits(e,t,r,n){return m(this,null,function*(){const a=e.uri,d=a.toString(),l=e.getOffsetAt(t),s=yield this._worker(a);if(e.isDisposed())return;const u=yield s.getRenameInfo(d,l,{allowRenameOfImportPath:!1});if(u.canRename===!1)return{edits:[],rejectReason:u.localizedErrorMessage};if(u.fileToRename!==void 0)throw new Error("Renaming files is not supported.");const g=yield s.findRenameLocations(d,l,!1,!1,!1);if(!g||e.isDisposed())return;const p=[];for(const h of g){const b=this._libFiles.getOrCreateModel(h.fileName);if(b)p.push({resource:b.uri,edit:{range:this._textSpanToRange(b,h.textSpan),text:r}});else throw new Error(`Unknown file ${h.fileName}.`)}return{edits:p}})}},te=class extends k{provideInlayHints(e,t,r){return m(this,null,function*(){const n=e.uri,a=n.toString(),d=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),l=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),s=yield this._worker(n);return e.isDisposed()?null:{hints:(yield s.provideInlayHints(a,d,l)).map(p=>V(V({},p),{label:p.text,position:e.getPositionAt(p.position),kind:this._convertHintKind(p.kind)})),dispose:()=>{}}})}_convertHintKind(e){switch(e){case"Parameter":return i.languages.InlayHintKind.Parameter;case"Type":return i.languages.InlayHintKind.Type;default:return i.languages.InlayHintKind.Type}}},H,W;function ne(e){W=re(e,"typescript")}function ae(e){H=re(e,"javascript")}function oe(){return new Promise((e,t)=>{if(!H)return t("JavaScript not registered!");e(H)})}function ce(){return new Promise((e,t)=>{if(!W)return t("TypeScript not registered!");e(W)})}function re(e,t){const r=new j(t,e),n=(...d)=>r.getLanguageServiceWorker(...d),a=new B(n);return i.languages.registerCompletionItemProvider(t,new O(n)),i.languages.registerSignatureHelpProvider(t,new E(n)),i.languages.registerHoverProvider(t,new z(n)),i.languages.registerDocumentHighlightProvider(t,new J(n)),i.languages.registerDefinitionProvider(t,new G(a,n)),i.languages.registerReferenceProvider(t,new Q(a,n)),i.languages.registerDocumentSymbolProvider(t,new X(n)),i.languages.registerDocumentRangeFormattingEditProvider(t,new Y(n)),i.languages.registerOnTypeFormattingEditProvider(t,new Z(n)),i.languages.registerCodeActionProvider(t,new q(n)),i.languages.registerRenameProvider(t,new ee(a,n)),i.languages.registerInlayHintsProvider(t,new te(n)),new U(a,e,t,n),n}}}]);
