(self["webpackChunkant_design_pro"] = self["webpackChunkant_design_pro"] || []).push([["mf-dep_vendors-node_modules_antd_es_input-number_index_js"],{

/***/ "./node_modules/@ant-design/icons-svg/es/asn/UpOutlined.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/UpOutlined.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
// This icon file is generated automatically.
var UpOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z" } }] }, "name": "up", "theme": "outlined" };
/* harmony default export */ __webpack_exports__["default"] = (UpOutlined);


/***/ }),

/***/ "./node_modules/@ant-design/icons/es/icons/UpOutlined.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/UpOutlined.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _ant_design_icons_svg_es_asn_UpOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/UpOutlined */ "./node_modules/@ant-design/icons-svg/es/asn/UpOutlined.js");
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AntdIcon */ "./node_modules/@ant-design/icons/es/components/AntdIcon.js");

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var UpOutlined = function UpOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__.default, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__.default)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_UpOutlined__WEBPACK_IMPORTED_MODULE_3__.default
  }));
};

UpOutlined.displayName = 'UpOutlined';
/* harmony default export */ __webpack_exports__["default"] = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(UpOutlined));

/***/ }),

/***/ "./node_modules/antd/es/input-number/index.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/input-number/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var rc_input_number__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-input-number */ "./node_modules/rc-input-number/es/index.js");
/* harmony import */ var _ant_design_icons_es_icons_UpOutlined__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ant-design/icons/es/icons/UpOutlined */ "./node_modules/@ant-design/icons/es/icons/UpOutlined.js");
/* harmony import */ var _ant_design_icons_es_icons_DownOutlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ant-design/icons/es/icons/DownOutlined */ "./node_modules/@ant-design/icons/es/icons/DownOutlined.js");
/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../config-provider */ "./node_modules/antd/es/config-provider/context.js");
/* harmony import */ var _config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../config-provider/SizeContext */ "./node_modules/antd/es/config-provider/SizeContext.js");



var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};








var InputNumber = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function (props, ref) {
  var _classNames;

  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_5__.ConfigContext),
      getPrefixCls = _React$useContext.getPrefixCls,
      direction = _React$useContext.direction;

  var size = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_config_provider_SizeContext__WEBPACK_IMPORTED_MODULE_6__.default);

  var className = props.className,
      customizeSize = props.size,
      customizePrefixCls = props.prefixCls,
      _props$bordered = props.bordered,
      bordered = _props$bordered === void 0 ? true : _props$bordered,
      readOnly = props.readOnly,
      others = __rest(props, ["className", "size", "prefixCls", "bordered", "readOnly"]);

  var prefixCls = getPrefixCls('input-number', customizePrefixCls);
  var upIcon = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons_es_icons_UpOutlined__WEBPACK_IMPORTED_MODULE_7__.default, {
    className: "".concat(prefixCls, "-handler-up-inner")
  });
  var downIcon = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons_es_icons_DownOutlined__WEBPACK_IMPORTED_MODULE_8__.default, {
    className: "".concat(prefixCls, "-handler-down-inner")
  });
  var mergeSize = customizeSize || size;
  var inputNumberClass = classnames__WEBPACK_IMPORTED_MODULE_3___default()((_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-lg"), mergeSize === 'large'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-sm"), mergeSize === 'small'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-rtl"), direction === 'rtl'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-readonly"), readOnly), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-borderless"), !bordered), _classNames), className);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(rc_input_number__WEBPACK_IMPORTED_MODULE_4__.default, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    ref: ref,
    className: inputNumberClass,
    upHandler: upIcon,
    downHandler: downIcon,
    prefixCls: prefixCls,
    readOnly: readOnly
  }, others));
});
/* harmony default export */ __webpack_exports__["default"] = (InputNumber);

/***/ }),

/***/ "./node_modules/rc-input-number/es/InputNumber.js":
/*!********************************************************!*\
  !*** ./node_modules/rc-input-number/es/InputNumber.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ "./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/KeyCode */ "./node_modules/rc-util/es/KeyCode.js");
/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/ref */ "./node_modules/rc-util/es/ref.js");
/* harmony import */ var _utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/MiniDecimal */ "./node_modules/rc-input-number/es/utils/MiniDecimal.js");
/* harmony import */ var _StepHandler__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./StepHandler */ "./node_modules/rc-input-number/es/StepHandler.js");
/* harmony import */ var _utils_numberUtil__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./utils/numberUtil */ "./node_modules/rc-input-number/es/utils/numberUtil.js");
/* harmony import */ var _hooks_useCursor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./hooks/useCursor */ "./node_modules/rc-input-number/es/hooks/useCursor.js");
/* harmony import */ var _hooks_useUpdateEffect__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useUpdateEffect */ "./node_modules/rc-input-number/es/hooks/useUpdateEffect.js");














/**
 * We support `stringMode` which need handle correct type when user call in onChange
 */

var getDecimalValue = function getDecimalValue(stringMode, decimalValue) {
  if (stringMode || decimalValue.isEmpty()) {
    return decimalValue.toString();
  }

  return decimalValue.toNumber();
};

var getDecimalIfValidate = function getDecimalIfValidate(value) {
  var decimal = (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.default)(value);
  return decimal.isInvalidate() ? null : decimal;
};

var InputNumber = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.forwardRef(function (props, ref) {
  var _classNames;

  var _props$prefixCls = props.prefixCls,
      prefixCls = _props$prefixCls === void 0 ? 'rc-input-number' : _props$prefixCls,
      className = props.className,
      style = props.style,
      min = props.min,
      max = props.max,
      _props$step = props.step,
      step = _props$step === void 0 ? 1 : _props$step,
      defaultValue = props.defaultValue,
      value = props.value,
      disabled = props.disabled,
      readOnly = props.readOnly,
      upHandler = props.upHandler,
      downHandler = props.downHandler,
      keyboard = props.keyboard,
      stringMode = props.stringMode,
      parser = props.parser,
      formatter = props.formatter,
      precision = props.precision,
      decimalSeparator = props.decimalSeparator,
      onChange = props.onChange,
      onInput = props.onInput,
      onPressEnter = props.onPressEnter,
      onStep = props.onStep,
      inputProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_4__.default)(props, ["prefixCls", "className", "style", "min", "max", "step", "defaultValue", "value", "disabled", "readOnly", "upHandler", "downHandler", "keyboard", "stringMode", "parser", "formatter", "precision", "decimalSeparator", "onChange", "onInput", "onPressEnter", "onStep"]);

  var inputClassName = "".concat(prefixCls, "-input");
  var inputRef = react__WEBPACK_IMPORTED_MODULE_5__.useRef(null);

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_5__.useState(false),
      _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(_React$useState, 2),
      focus = _React$useState2[0],
      setFocus = _React$useState2[1];

  var userTypingRef = react__WEBPACK_IMPORTED_MODULE_5__.useRef(false);
  var compositionRef = react__WEBPACK_IMPORTED_MODULE_5__.useRef(false); // ============================ Value =============================
  // Real value control

  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_5__.useState(function () {
    return (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.default)(value !== null && value !== void 0 ? value : defaultValue);
  }),
      _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(_React$useState3, 2),
      decimalValue = _React$useState4[0],
      setDecimalValue = _React$useState4[1];

  function setUncontrolledDecimalValue(newDecimal) {
    if (value === undefined) {
      setDecimalValue(newDecimal);
    }
  } // ====================== Parser & Formatter ======================

  /**
   * `precision` is used for formatter & onChange.
   * It will auto generate by `value` & `step`.
   * But it will not block user typing.
   *
   * Note: Auto generate `precision` is used for legacy logic.
   * We should remove this since we already support high precision with BigInt.
   *
   * @param number  Provide which number should calculate precision
   * @param userTyping  Change by user typing
   */


  var getPrecision = react__WEBPACK_IMPORTED_MODULE_5__.useCallback(function (numStr, userTyping) {
    if (userTyping) {
      return undefined;
    }

    if (precision >= 0) {
      return precision;
    }

    return Math.max((0,_utils_numberUtil__WEBPACK_IMPORTED_MODULE_11__.getNumberPrecision)(numStr), (0,_utils_numberUtil__WEBPACK_IMPORTED_MODULE_11__.getNumberPrecision)(step));
  }, [precision, step]); // >>> Parser

  var mergedParser = react__WEBPACK_IMPORTED_MODULE_5__.useCallback(function (num) {
    var numStr = String(num);

    if (parser) {
      return parser(numStr);
    }

    var parsedStr = numStr;

    if (decimalSeparator) {
      parsedStr = parsedStr.replace(decimalSeparator, '.');
    } // [Legacy] We still support auto convert `$ 123,456` to `123456`


    return parsedStr.replace(/[^\w.-]+/g, '');
  }, [parser, decimalSeparator]); // >>> Formatter

  var mergedFormatter = react__WEBPACK_IMPORTED_MODULE_5__.useCallback(function (number, userTyping) {
    if (formatter) {
      return formatter(number);
    }

    var str = typeof number === 'number' ? (0,_utils_numberUtil__WEBPACK_IMPORTED_MODULE_11__.num2str)(number) : number; // User typing will not auto format with precision directly

    if (!userTyping) {
      var mergedPrecision = getPrecision(str, userTyping);

      if ((0,_utils_numberUtil__WEBPACK_IMPORTED_MODULE_11__.validateNumber)(str) && (decimalSeparator || mergedPrecision >= 0)) {
        // Separator
        var separatorStr = decimalSeparator || '.';
        str = (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.toFixed)(str, separatorStr, mergedPrecision);
      }
    }

    return str;
  }, [formatter, getPrecision, decimalSeparator]); // ========================== InputValue ==========================

  /**
   * Input text value control
   *
   * User can not update input content directly. It update with follow rules by priority:
   *  1. controlled `value` changed
   *    * [SPECIAL] Typing like `1.` should not immediately convert to `1`
   *  2. User typing with format (not precision)
   *  3. Blur or Enter trigger revalidate
   */

  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_5__.useState(function () {
    var initValue = defaultValue !== null && defaultValue !== void 0 ? defaultValue : value;

    if (decimalValue.isInvalidate() && ['string', 'number'].includes((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__.default)(initValue))) {
      return Number.isNaN(initValue) ? '' : initValue;
    }

    return mergedFormatter(decimalValue.toString(), false);
  }),
      _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(_React$useState5, 2),
      inputValue = _React$useState6[0],
      setInternalInputValue = _React$useState6[1]; // Should always be string


  function setInputValue(newValue, userTyping) {
    setInternalInputValue(mergedFormatter( // Invalidate number is sometime passed by external control, we should let it go
    // Otherwise is controlled by internal interactive logic which check by userTyping
    // You can ref 'show limited value when input is not focused' test for more info.
    newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping), userTyping));
  } // >>> Max & Min limit


  var maxDecimal = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(function () {
    return getDecimalIfValidate(max);
  }, [max]);
  var minDecimal = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(function () {
    return getDecimalIfValidate(min);
  }, [min]);
  var upDisabled = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(function () {
    if (!maxDecimal || !decimalValue || decimalValue.isInvalidate()) {
      return false;
    }

    return maxDecimal.lessEquals(decimalValue);
  }, [maxDecimal, decimalValue]);
  var downDisabled = react__WEBPACK_IMPORTED_MODULE_5__.useMemo(function () {
    if (!minDecimal || !decimalValue || decimalValue.isInvalidate()) {
      return false;
    }

    return decimalValue.lessEquals(minDecimal);
  }, [minDecimal, decimalValue]); // Cursor controller

  var _useCursor = (0,_hooks_useCursor__WEBPACK_IMPORTED_MODULE_12__.default)(inputRef.current, focus),
      _useCursor2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__.default)(_useCursor, 2),
      recordCursor = _useCursor2[0],
      restoreCursor = _useCursor2[1]; // ============================= Data =============================

  /**
   * Find target value closet within range.
   * e.g. [11, 28]:
   *    3  => 11
   *    23 => 23
   *    99 => 28
   */


  var getRangeValue = function getRangeValue(target) {
    // target > max
    if (maxDecimal && !target.lessEquals(maxDecimal)) {
      return maxDecimal;
    } // target < min


    if (minDecimal && !minDecimal.lessEquals(target)) {
      return minDecimal;
    }

    return null;
  };
  /**
   * Check value is in [min, max] range
   */


  var isInRange = function isInRange(target) {
    return !getRangeValue(target);
  };
  /**
   * Trigger `onChange` if value validated and not equals of origin.
   * Return the value that re-align in range.
   */


  var triggerValueUpdate = function triggerValueUpdate(newValue, userTyping) {
    var updateValue = newValue;
    var isRangeValidate = isInRange(updateValue) || updateValue.isEmpty(); // Skip align value when trigger value is empty.
    // We just trigger onChange(null)
    // This should not block user typing

    if (!updateValue.isEmpty() && !userTyping) {
      // Revert value in range if needed
      updateValue = getRangeValue(updateValue) || updateValue;
      isRangeValidate = true;
    }

    if (!readOnly && !disabled && isRangeValidate) {
      var numStr = updateValue.toString();
      var mergedPrecision = getPrecision(numStr, userTyping);

      if (mergedPrecision >= 0) {
        updateValue = (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.default)((0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.toFixed)(numStr, '.', mergedPrecision));
      } // Trigger event


      if (!updateValue.equals(decimalValue)) {
        setUncontrolledDecimalValue(updateValue);
        onChange === null || onChange === void 0 ? void 0 : onChange(updateValue.isEmpty() ? null : getDecimalValue(stringMode, updateValue)); // Reformat input if value is not controlled

        if (value === undefined) {
          setInputValue(updateValue, userTyping);
        }
      }

      return updateValue;
    }

    return decimalValue;
  }; // ========================== User Input ==========================
  // >>> Collect input value


  var collectInputValue = function collectInputValue(inputStr) {
    recordCursor(); // Update inputValue incase input can not parse as number

    setInternalInputValue(inputStr); // Parse number

    if (!compositionRef.current) {
      var finalValue = mergedParser(inputStr);
      var finalDecimal = (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.default)(finalValue);

      if (!finalDecimal.isNaN()) {
        triggerValueUpdate(finalDecimal, true);
      }
    }
  }; // >>> Composition


  var onCompositionStart = function onCompositionStart() {
    compositionRef.current = true;
  };

  var onCompositionEnd = function onCompositionEnd() {
    compositionRef.current = false;
    collectInputValue(inputRef.current.value);
  }; // >>> Input


  var onInternalInput = function onInternalInput(e) {
    var inputStr = e.target.value; // optimize for chinese input experience
    // https://github.com/ant-design/ant-design/issues/8196

    if (!parser) {
      inputStr = inputStr.replace(/。/g, '.');
    }

    collectInputValue(inputStr); // Trigger onInput later to let user customize value if they want do handle something after onChange

    onInput === null || onInput === void 0 ? void 0 : onInput(inputStr);
  }; // ============================= Step =============================


  var onInternalStep = function onInternalStep(up) {
    var _inputRef$current;

    // Ignore step since out of range
    if (up && upDisabled || !up && downDisabled) {
      return;
    } // Clear typing status since it may caused by up & down key.
    // We should sync with input value.


    userTypingRef.current = false;
    var stepDecimal = (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.default)(step);

    if (!up) {
      stepDecimal = stepDecimal.negate();
    }

    var target = (decimalValue || (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.default)(0)).add(stepDecimal.toString());
    var updatedValue = triggerValueUpdate(target, false);
    onStep === null || onStep === void 0 ? void 0 : onStep(getDecimalValue(stringMode, updatedValue), {
      offset: step,
      type: up ? 'up' : 'down'
    });
    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus();
  }; // ============================ Flush =============================

  /**
   * Flush current input content to trigger value change & re-formatter input if needed
   */


  var flushInputValue = function flushInputValue(userTyping) {
    var parsedValue = (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.default)(mergedParser(inputValue));
    var formatValue = parsedValue;

    if (!parsedValue.isNaN()) {
      // Only validate value or empty value can be re-fill to inputValue
      // Reassign the formatValue within ranged of trigger control
      formatValue = triggerValueUpdate(parsedValue, userTyping);
    } else {
      formatValue = decimalValue;
    }

    if (value !== undefined) {
      // Reset back with controlled value first
      setInputValue(decimalValue, false);
    } else if (!formatValue.isNaN()) {
      // Reset input back since no validate value
      setInputValue(formatValue, false);
    }
  };

  var onKeyDown = function onKeyDown(event) {
    var which = event.which;
    userTypingRef.current = true;

    if (which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.ENTER) {
      if (!compositionRef.current) {
        userTypingRef.current = false;
      }

      flushInputValue(true);
      onPressEnter === null || onPressEnter === void 0 ? void 0 : onPressEnter(event);
    }

    if (keyboard === false) {
      return;
    } // Do step


    if (!compositionRef.current && [rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.UP, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.DOWN].includes(which)) {
      onInternalStep(rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_7__.default.UP === which);
      event.preventDefault();
    }
  };

  var onKeyUp = function onKeyUp() {
    userTypingRef.current = false;
  }; // >>> Focus & Blur


  var onBlur = function onBlur() {
    flushInputValue(false);
    setFocus(false);
    userTypingRef.current = false;
  }; // ========================== Controlled ==========================
  // Input by precision


  (0,_hooks_useUpdateEffect__WEBPACK_IMPORTED_MODULE_13__.default)(function () {
    if (!decimalValue.isInvalidate()) {
      setInputValue(decimalValue, false);
    }
  }, [precision]); // Input by value

  (0,_hooks_useUpdateEffect__WEBPACK_IMPORTED_MODULE_13__.default)(function () {
    var newValue = (0,_utils_MiniDecimal__WEBPACK_IMPORTED_MODULE_9__.default)(value);
    setDecimalValue(newValue); // When user typing from `1.2` to `1.`, we should not convert to `1` immediately.
    // But let it go if user set `formatter`

    if (newValue.isNaN() || !userTypingRef.current || formatter) {
      // Update value as effect
      setInputValue(newValue, false);
    }
  }, [value]); // ============================ Cursor ============================

  (0,_hooks_useUpdateEffect__WEBPACK_IMPORTED_MODULE_13__.default)(function () {
    if (formatter) {
      restoreCursor();
    }
  }, [inputValue]); // ============================ Render ============================

  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixCls, className, (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-focused"), focus), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-disabled"), disabled), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-readonly"), readOnly), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-not-a-number"), decimalValue.isNaN()), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)(_classNames, "".concat(prefixCls, "-out-of-range"), !decimalValue.isInvalidate() && !isInRange(decimalValue)), _classNames)),
    style: style,
    onFocus: function onFocus() {
      setFocus(true);
    },
    onBlur: onBlur,
    onKeyDown: onKeyDown,
    onKeyUp: onKeyUp,
    onCompositionStart: onCompositionStart,
    onCompositionEnd: onCompositionEnd
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_StepHandler__WEBPACK_IMPORTED_MODULE_10__.default, {
    prefixCls: prefixCls,
    upNode: upHandler,
    downNode: downHandler,
    upDisabled: upDisabled,
    downDisabled: downDisabled,
    onStep: onInternalStep
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "".concat(inputClassName, "-wrap")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("input", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({
    autoComplete: "off",
    role: "spinbutton",
    "aria-valuemin": min,
    "aria-valuemax": max,
    "aria-valuenow": decimalValue.isInvalidate() ? null : decimalValue.toString(),
    step: step
  }, inputProps, {
    ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_8__.composeRef)(inputRef, ref),
    className: inputClassName,
    value: inputValue,
    onChange: onInternalInput,
    disabled: disabled,
    readOnly: readOnly
  }))));
});
InputNumber.displayName = 'InputNumber';
/* harmony default export */ __webpack_exports__["default"] = (InputNumber);

/***/ }),

/***/ "./node_modules/rc-input-number/es/StepHandler.js":
/*!********************************************************!*\
  !*** ./node_modules/rc-input-number/es/StepHandler.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ StepHandler; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var rc_util_es_isMobile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/isMobile */ "./node_modules/rc-util/es/isMobile.js");





/**
 * When click and hold on a button - the speed of auto changing the value.
 */

var STEP_INTERVAL = 200;
/**
 * When click and hold on a button - the delay before auto changing the value.
 */

var STEP_DELAY = 600;
function StepHandler(_ref) {
  var prefixCls = _ref.prefixCls,
      upNode = _ref.upNode,
      downNode = _ref.downNode,
      upDisabled = _ref.upDisabled,
      downDisabled = _ref.downDisabled,
      onStep = _ref.onStep;
  // ======================== Step ========================
  var stepTimeoutRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef();
  var onStepRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef();
  onStepRef.current = onStep; // We will interval update step when hold mouse down

  var onStepMouseDown = function onStepMouseDown(e, up) {
    e.preventDefault();
    onStepRef.current(up); // Loop step for interval

    function loopStep() {
      onStepRef.current(up);
      stepTimeoutRef.current = setTimeout(loopStep, STEP_INTERVAL);
    } // First time press will wait some time to trigger loop step update


    stepTimeoutRef.current = setTimeout(loopStep, STEP_DELAY);
  };

  var onStopStep = function onStopStep() {
    clearTimeout(stepTimeoutRef.current);
  };

  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {
    return onStopStep;
  }, []); // ======================= Render =======================

  if ((0,rc_util_es_isMobile__WEBPACK_IMPORTED_MODULE_4__.default)()) {
    return null;
  }

  var handlerClassName = "".concat(prefixCls, "-handler");
  var upClassName = classnames__WEBPACK_IMPORTED_MODULE_3___default()(handlerClassName, "".concat(handlerClassName, "-up"), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(handlerClassName, "-up-disabled"), upDisabled));
  var downClassName = classnames__WEBPACK_IMPORTED_MODULE_3___default()(handlerClassName, "".concat(handlerClassName, "-down"), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__.default)({}, "".concat(handlerClassName, "-down-disabled"), downDisabled));
  var sharedHandlerProps = {
    unselectable: 'on',
    role: 'button',
    onMouseUp: onStopStep,
    onMouseLeave: onStopStep
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "".concat(handlerClassName, "-wrap")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, sharedHandlerProps, {
    onMouseDown: function onMouseDown(e) {
      onStepMouseDown(e, true);
    },
    "aria-label": "Increase Value",
    "aria-disabled": upDisabled,
    className: upClassName
  }), upNode || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
    unselectable: "on",
    className: "".concat(prefixCls, "-handler-up-inner")
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__.default)({}, sharedHandlerProps, {
    onMouseDown: function onMouseDown(e) {
      onStepMouseDown(e, false);
    },
    "aria-label": "Decrease Value",
    "aria-disabled": downDisabled,
    className: downClassName
  }), downNode || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
    unselectable: "on",
    className: "".concat(prefixCls, "-handler-down-inner")
  })));
}

/***/ }),

/***/ "./node_modules/rc-input-number/es/hooks/useCursor.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-input-number/es/hooks/useCursor.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useCursor; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/warning */ "./node_modules/rc-util/es/warning.js");


/**
 * Keep input cursor in the correct position if possible.
 * Is this necessary since we have `formatter` which may mass the content?
 */

function useCursor(input, focused) {
  var selectionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);

  function recordCursor() {
    // Record position
    try {
      var start = input.selectionStart,
          end = input.selectionEnd,
          value = input.value;
      var beforeTxt = value.substring(0, start);
      var afterTxt = value.substring(end);
      selectionRef.current = {
        start: start,
        end: end,
        value: value,
        beforeTxt: beforeTxt,
        afterTxt: afterTxt
      };
    } catch (e) {// Fix error in Chrome:
      // Failed to read the 'selectionStart' property from 'HTMLInputElement'
      // http://stackoverflow.com/q/21177489/3040605
    }
  }
  /**
   * Restore logic:
   *  1. back string same
   *  2. start string same
   */


  function restoreCursor() {
    if (input && selectionRef.current && focused) {
      try {
        var value = input.value;
        var _selectionRef$current = selectionRef.current,
            beforeTxt = _selectionRef$current.beforeTxt,
            afterTxt = _selectionRef$current.afterTxt,
            start = _selectionRef$current.start;
        var startPos = value.length;

        if (value.endsWith(afterTxt)) {
          startPos = value.length - selectionRef.current.afterTxt.length;
        } else if (value.startsWith(beforeTxt)) {
          startPos = beforeTxt.length;
        } else {
          var beforeLastChar = beforeTxt[start - 1];
          var newIndex = value.indexOf(beforeLastChar, start - 1);

          if (newIndex !== -1) {
            startPos = newIndex + 1;
          }
        }

        input.setSelectionRange(startPos, startPos);
      } catch (e) {
        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__.default)(false, "Something warning of cursor restore. Please fire issue about this: ".concat(e.message));
      }
    }
  }

  return [recordCursor, restoreCursor];
}

/***/ }),

/***/ "./node_modules/rc-input-number/es/hooks/useLayoutEffect.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-input-number/es/hooks/useLayoutEffect.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "useLayoutEffect": function() { return /* binding */ useLayoutEffect; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ "./node_modules/rc-util/es/Dom/canUseDom.js");


var useLayoutEffect = (0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__.default)() ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;

/***/ }),

/***/ "./node_modules/rc-input-number/es/hooks/useUpdateEffect.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-input-number/es/hooks/useUpdateEffect.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useUpdateEffect; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var _useLayoutEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useLayoutEffect */ "./node_modules/rc-input-number/es/hooks/useLayoutEffect.js");


/**
 * Work as `componentDidUpdate`
 */

function useUpdateEffect(callback, condition) {
  var initRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);
  (0,_useLayoutEffect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(function () {
    if (!initRef.current) {
      initRef.current = true;
      return undefined;
    }

    return callback();
  }, condition);
}

/***/ }),

/***/ "./node_modules/rc-input-number/es/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-input-number/es/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _InputNumber__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./InputNumber */ "./node_modules/rc-input-number/es/InputNumber.js");

/* harmony default export */ __webpack_exports__["default"] = (_InputNumber__WEBPACK_IMPORTED_MODULE_0__.default);

/***/ }),

/***/ "./node_modules/rc-input-number/es/utils/MiniDecimal.js":
/*!**************************************************************!*\
  !*** ./node_modules/rc-input-number/es/utils/MiniDecimal.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "NumberDecimal": function() { return /* binding */ NumberDecimal; },
/* harmony export */   "BigIntDecimal": function() { return /* binding */ BigIntDecimal; },
/* harmony export */   "default": function() { return /* binding */ getMiniDecimal; },
/* harmony export */   "toFixed": function() { return /* binding */ toFixed; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ "./node_modules/@babel/runtime/helpers/esm/classCallCheck.js");
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ "./node_modules/@babel/runtime/helpers/esm/createClass.js");
/* harmony import */ var _numberUtil__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./numberUtil */ "./node_modules/rc-input-number/es/utils/numberUtil.js");
/* harmony import */ var _supportUtil__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./supportUtil */ "./node_modules/rc-input-number/es/utils/supportUtil.js");



/* eslint-disable max-classes-per-file */


/**
 * We can remove this when IE not support anymore
 */

var NumberDecimal = /*#__PURE__*/function () {
  function NumberDecimal(value) {
    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__.default)(this, NumberDecimal);

    this.origin = '';

    if (!value && value !== 0 || !String(value).trim()) {
      this.empty = true;
      return;
    }

    this.origin = String(value);
    this.number = Number(value);
  }

  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__.default)(NumberDecimal, [{
    key: "negate",
    value: function negate() {
      return new NumberDecimal(-this.toNumber());
    }
  }, {
    key: "add",
    value: function add(value) {
      if (this.isInvalidate()) {
        return new NumberDecimal(value);
      }

      var target = Number(value);

      if (Number.isNaN(target)) {
        return this;
      }

      var number = this.number + target; // [Legacy] Back to safe integer

      if (number > Number.MAX_SAFE_INTEGER) {
        return new NumberDecimal(Number.MAX_SAFE_INTEGER);
      }

      if (number < Number.MIN_SAFE_INTEGER) {
        return new NumberDecimal(Number.MIN_SAFE_INTEGER);
      }

      var maxPrecision = Math.max((0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.getNumberPrecision)(this.number), (0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.getNumberPrecision)(target));
      return new NumberDecimal(number.toFixed(maxPrecision));
    }
  }, {
    key: "isEmpty",
    value: function isEmpty() {
      return this.empty;
    }
  }, {
    key: "isNaN",
    value: function isNaN() {
      return Number.isNaN(this.number);
    }
  }, {
    key: "isInvalidate",
    value: function isInvalidate() {
      return this.isEmpty() || this.isNaN();
    }
  }, {
    key: "equals",
    value: function equals(target) {
      return this.toNumber() === (target === null || target === void 0 ? void 0 : target.toNumber());
    }
  }, {
    key: "lessEquals",
    value: function lessEquals(target) {
      return this.add(target.negate().toString()).toNumber() <= 0;
    }
  }, {
    key: "toNumber",
    value: function toNumber() {
      return this.number;
    }
  }, {
    key: "toString",
    value: function toString() {
      var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;

      if (!safe) {
        return this.origin;
      }

      if (this.isInvalidate()) {
        return '';
      }

      return (0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.num2str)(this.number);
    }
  }]);

  return NumberDecimal;
}();
var BigIntDecimal = /*#__PURE__*/function () {
  function BigIntDecimal(value) {
    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__.default)(this, BigIntDecimal);

    this.origin = '';

    if (!value && value !== 0 || !String(value).trim()) {
      this.empty = true;
      return;
    }

    this.origin = String(value); // Act like Number convert

    if (value === '-') {
      this.nan = true;
      return;
    }

    var mergedValue = value; // We need convert back to Number since it require `toFixed` to handle this

    if ((0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.isE)(mergedValue)) {
      mergedValue = Number(mergedValue);
    }

    mergedValue = typeof mergedValue === 'string' ? mergedValue : (0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.num2str)(mergedValue);

    if ((0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.validateNumber)(mergedValue)) {
      var trimRet = (0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.trimNumber)(mergedValue);
      this.negative = trimRet.negative;
      var numbers = trimRet.trimStr.split('.');
      this.integer = BigInt(numbers[0]);
      var decimalStr = numbers[1] || '0';
      this.decimal = BigInt(decimalStr);
      this.decimalLen = decimalStr.length;
    } else {
      this.nan = true;
    }
  }

  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__.default)(BigIntDecimal, [{
    key: "getMark",
    value: function getMark() {
      return this.negative ? '-' : '';
    }
  }, {
    key: "getIntegerStr",
    value: function getIntegerStr() {
      return this.integer.toString();
    }
  }, {
    key: "getDecimalStr",
    value: function getDecimalStr() {
      return this.decimal.toString().padStart(this.decimalLen, '0');
    }
    /**
     * Align BigIntDecimal with same decimal length. e.g. 12.3 + 5 = 1230000
     * This is used for add function only.
     */

  }, {
    key: "alignDecimal",
    value: function alignDecimal(decimalLength) {
      var str = "".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(decimalLength, '0'));
      return BigInt(str);
    }
  }, {
    key: "negate",
    value: function negate() {
      var clone = new BigIntDecimal(this.toString());
      clone.negative = !clone.negative;
      return clone;
    }
  }, {
    key: "add",
    value: function add(value) {
      if (this.isInvalidate()) {
        return new BigIntDecimal(value);
      }

      var offset = new BigIntDecimal(value);

      if (offset.isInvalidate()) {
        return this;
      }

      var maxDecimalLength = Math.max(this.getDecimalStr().length, offset.getDecimalStr().length);
      var myAlignedDecimal = this.alignDecimal(maxDecimalLength);
      var offsetAlignedDecimal = offset.alignDecimal(maxDecimalLength);
      var valueStr = (myAlignedDecimal + offsetAlignedDecimal).toString(); // We need fill string length back to `maxDecimalLength` to avoid parser failed

      var _trimNumber = (0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.trimNumber)(valueStr),
          negativeStr = _trimNumber.negativeStr,
          trimStr = _trimNumber.trimStr;

      var hydrateValueStr = "".concat(negativeStr).concat(trimStr.padStart(maxDecimalLength + 1, '0'));
      return new BigIntDecimal("".concat(hydrateValueStr.slice(0, -maxDecimalLength), ".").concat(hydrateValueStr.slice(-maxDecimalLength)));
    }
  }, {
    key: "isEmpty",
    value: function isEmpty() {
      return this.empty;
    }
  }, {
    key: "isNaN",
    value: function isNaN() {
      return this.nan;
    }
  }, {
    key: "isInvalidate",
    value: function isInvalidate() {
      return this.isEmpty() || this.isNaN();
    }
  }, {
    key: "equals",
    value: function equals(target) {
      return this.toString() === (target === null || target === void 0 ? void 0 : target.toString());
    }
  }, {
    key: "lessEquals",
    value: function lessEquals(target) {
      return this.add(target.negate().toString()).toNumber() <= 0;
    }
  }, {
    key: "toNumber",
    value: function toNumber() {
      if (this.isNaN()) {
        return NaN;
      }

      return Number(this.toString());
    }
  }, {
    key: "toString",
    value: function toString() {
      var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;

      if (!safe) {
        return this.origin;
      }

      if (this.isInvalidate()) {
        return '';
      }

      return (0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.trimNumber)("".concat(this.getMark()).concat(this.getIntegerStr(), ".").concat(this.getDecimalStr())).fullStr;
    }
  }]);

  return BigIntDecimal;
}();
function getMiniDecimal(value) {
  // We use BigInt here.
  // Will fallback to Number if not support.
  if ((0,_supportUtil__WEBPACK_IMPORTED_MODULE_3__.supportBigInt)()) {
    return new BigIntDecimal(value);
  }

  return new NumberDecimal(value);
}
/**
 * Align the logic of toFixed to around like 1.5 => 2
 */

function toFixed(numStr, separatorStr, precision) {
  if (numStr === '') {
    return '';
  }

  var _trimNumber2 = (0,_numberUtil__WEBPACK_IMPORTED_MODULE_2__.trimNumber)(numStr),
      negativeStr = _trimNumber2.negativeStr,
      integerStr = _trimNumber2.integerStr,
      decimalStr = _trimNumber2.decimalStr;

  var precisionDecimalStr = "".concat(separatorStr).concat(decimalStr);
  var numberWithoutDecimal = "".concat(negativeStr).concat(integerStr);

  if (precision >= 0) {
    // We will get last + 1 number to check if need advanced number
    var advancedNum = Number(decimalStr[precision]);

    if (advancedNum >= 5) {
      var advancedDecimal = getMiniDecimal(numStr).add("0.".concat('0'.repeat(precision)).concat(10 - advancedNum));
      return toFixed(advancedDecimal.toString(), separatorStr, precision);
    }

    if (precision === 0) {
      return numberWithoutDecimal;
    }

    return "".concat(numberWithoutDecimal).concat(separatorStr).concat(decimalStr.padEnd(precision, '0').slice(0, precision));
  }

  if (precisionDecimalStr === '.0') {
    return numberWithoutDecimal;
  }

  return "".concat(numberWithoutDecimal).concat(precisionDecimalStr);
}

/***/ }),

/***/ "./node_modules/rc-input-number/es/utils/numberUtil.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-input-number/es/utils/numberUtil.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "trimNumber": function() { return /* binding */ trimNumber; },
/* harmony export */   "isE": function() { return /* binding */ isE; },
/* harmony export */   "getNumberPrecision": function() { return /* binding */ getNumberPrecision; },
/* harmony export */   "num2str": function() { return /* binding */ num2str; },
/* harmony export */   "validateNumber": function() { return /* binding */ validateNumber; }
/* harmony export */ });
/* harmony import */ var _supportUtil__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supportUtil */ "./node_modules/rc-input-number/es/utils/supportUtil.js");

/**
 * Format string number to readable number
 */

function trimNumber(numStr) {
  var str = numStr.trim();
  var negative = str.startsWith('-');

  if (negative) {
    str = str.slice(1);
  }

  str = str // Remove decimal 0. `1.000` => `1.`, `1.100` => `1.1`
  .replace(/(\.\d*[^0])0*$/, '$1') // Remove useless decimal. `1.` => `1`
  .replace(/\.0*$/, '') // Remove integer 0. `0001` => `1`, 000.1' => `.1`
  .replace(/^0+/, '');

  if (str.startsWith('.')) {
    str = "0".concat(str);
  }

  var trimStr = str || '0';
  var splitNumber = trimStr.split('.');
  var integerStr = splitNumber[0] || '0';
  var decimalStr = splitNumber[1] || '0';

  if (integerStr === '0' && decimalStr === '0') {
    negative = false;
  }

  var negativeStr = negative ? '-' : '';
  return {
    negative: negative,
    negativeStr: negativeStr,
    trimStr: trimStr,
    integerStr: integerStr,
    decimalStr: decimalStr,
    fullStr: "".concat(negativeStr).concat(trimStr)
  };
}
function isE(number) {
  var str = String(number);
  return !Number.isNaN(Number(str)) && str.includes('e');
}
/**
 * [Legacy] Convert 1e-9 to 0.000000001.
 * This may lose some precision if user really want 1e-9.
 */

function getNumberPrecision(number) {
  var numStr = String(number);

  if (isE(number)) {
    var precision = Number(numStr.slice(numStr.indexOf('e-') + 2));
    var decimalMatch = numStr.match(/\.(\d+)/);

    if (decimalMatch === null || decimalMatch === void 0 ? void 0 : decimalMatch[1]) {
      precision += decimalMatch[1].length;
    }

    return precision;
  }

  return numStr.includes('.') && validateNumber(numStr) ? numStr.length - numStr.indexOf('.') - 1 : 0;
}
/**
 * Convert number (includes scientific notation) to -xxx.yyy format
 */

function num2str(number) {
  var numStr = String(number);

  if (isE(number)) {
    if (number > Number.MAX_SAFE_INTEGER) {
      return String((0,_supportUtil__WEBPACK_IMPORTED_MODULE_0__.supportBigInt)() ? BigInt(number).toString() : Number.MAX_SAFE_INTEGER);
    }

    if (number < Number.MIN_SAFE_INTEGER) {
      return String((0,_supportUtil__WEBPACK_IMPORTED_MODULE_0__.supportBigInt)() ? BigInt(number).toString() : Number.MIN_SAFE_INTEGER);
    }

    numStr = number.toFixed(getNumberPrecision(numStr));
  }

  return trimNumber(numStr).fullStr;
}
function validateNumber(num) {
  if (typeof num === 'number') {
    return !Number.isNaN(num);
  } // Empty


  if (!num) {
    return false;
  }

  return (// Normal type: 11.28
    /^\s*-?\d+(\.\d+)?\s*$/.test(num) || // Pre-number: 1.
    /^\s*-?\d+\.\s*$/.test(num) || // Post-number: .1
    /^\s*-?\.\d+\s*$/.test(num)
  );
}

/***/ }),

/***/ "./node_modules/rc-input-number/es/utils/supportUtil.js":
/*!**************************************************************!*\
  !*** ./node_modules/rc-input-number/es/utils/supportUtil.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "supportBigInt": function() { return /* binding */ supportBigInt; }
/* harmony export */ });
function supportBigInt() {
  return typeof BigInt === 'function';
}

/***/ })

}]);